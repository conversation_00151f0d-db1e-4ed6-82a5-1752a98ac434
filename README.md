# HomesToCompare (pwb-pro-quasar)

On 26 Nov 2024 this branch
https://github.com/etewiah/pwb-pro-fe-h2c
was created off
https://github.com/etewiah/pwb-pro-fe-admin

HomesToCompare is a platform that uses AI to analyze property listings, providing detailed insights and enabling users to compare properties for informed real estate decisions. The platform serves buyers, sellers, and real estate agents with tailored features.

## Project Overview

HomesToCompare empowers users by:

- Providing AI-generated property comparables
- Offering detailed property dossiers with room-by-room insights
- Enabling data-driven real estate decisions
- Preserving privacy while delivering valuable market insights

## Installation

```bash
# Install dependencies
yarn
# or
npm install
```

## Development

### Start development server

```bash
# Start the app in development mode (hot-code reloading, error reporting)
quasar dev
```

### Code Quality

```bash
# Lint the files
yarn lint
# or
npm run lint

# Format the files
yarn format
# or
npm run format
```

### Build for Production

```bash
# Build the app for production
quasar build
```

## Deployment

The deployment process uses Dokku:

```bash
# From project root
./deploy/dokkubuild/build-prod-pwbprofeh2c-2025-apr.sh
```

This compiles and copies the build to `/Users/<USER>/dev/sites-2024-may/htz-deploy-app/`.

## Project Structure

```
.
├── public/             # Static assets
├── src/
│   ├── apps/           # Application-specific code
│   ├── boot/           # Quasar boot files
│   ├── components/     # Reusable Vue components
│   ├── concerns/       # Feature-specific code organized by domain
│   │   ├── gpt-experiments/
│   │   ├── legacy-h2c/
│   │   ├── marketing/
│   │   └── user-dash/
│   ├── compose/        # Vue composition API utilities
│   ├── h2c/            # HomesToCompare core functionality
│   ├── layouts/        # Quasar layouts
│   └── pages/          # Vue pages
├── quasar.config.js    # Quasar framework configuration
└── package.json        # Project dependencies and scripts
```

## Technologies Used

- [Vue.js](https://vuejs.org/) - Frontend framework
- [Quasar Framework](https://quasar.dev/) - Vue-based UI framework
- [Vite](https://vitejs.dev/) - Build tool
- [Axios](https://axios-http.com/) - HTTP client
- [ApexCharts](https://apexcharts.com/) - Interactive charts
- [OpenLayers](https://openlayers.org/) - Map integration

## Key Features

### For Buyers

- Create dream home blueprints from actual sold properties
- Compare and assess potential options confidently
- Make data-driven decisions using AI-powered insights

### For Sellers

- Price properties strategically using comparable data
- Generate accurate synthetic comparables
- Preserve privacy while gaining market insights

### For Agents

- Offer clients precise property insights
- Save time with quick property data generation
- Attract more clients with data-driven proposals

## Browser Support

HomesToCompare supports all major modern browsers including:

- Chrome
- Firefox
- Safari
- Edge

## License

Proprietary - All rights reserved

## Contact

For more information, contact <EMAIL>
