13 may 2025:

https://homestocompare.com/i/terms-of-service
- Currently to edit files like the one located above, I will need to reference below:
/Users/<USER>/dev/sites-2023-spt/htoc-fe-2023-spt/notes.txt


Last week I introduced Htoc2025 specific layout here:
/Users/<USER>/dev/sites-2023-spt/htoc-fe-2023-spt/src/apps/htoc-gate/layouts/HtocGate2025Layout.vue

Will now use this to allow me <NAME_EMAIL> as email in content pages:

/Users/<USER>/dev/sites-2023-spt/htoc-fe-2023-spt/src/pages/content/Htoc2025ContentPagesContainer.vue



apr 2025:

To deploy I run (from project root)
./deploy/dokkubuild/build-prod-pwbprofeh2c-2025-apr.sh

Will compile and copy to here:
/Users/<USER>/dev/sites-2024-may/htz-deploy-app/

From above I can run:
g push htz-dokku

Have some notes here:
/Users/<USER>/dev/sites-2024-may/htz-deploy-app/notes.txt
