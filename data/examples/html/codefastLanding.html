<html lang="en"
      data-theme="light"
      class="__className_af6c42">

<head>
  <meta charset="utf-8">
  <meta name="viewport"
        content="width=device-width, initial-scale=1">
  <link rel="preload"
        href="/_next/static/media/5455839c73f146e7-s.p.woff2"
        as="font"
        crossorigin=""
        type="font/woff2">
  <link rel="preload"
        as="image"
        href="https://www.facebook.com/tr?id=410683368646128&amp;ev=PageView&amp;noscript=1">
  <link rel="preload"
        as="image"
        imagesrcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fgear.1c9850d2.png&amp;w=256&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fgear.1c9850d2.png&amp;w=384&amp;q=75 2x"
        fetchpriority="high">
  <link rel="preload"
        as="image"
        imagesrcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fserver.41b0b400.png&amp;w=256&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fserver.41b0b400.png&amp;w=384&amp;q=75 2x"
        fetchpriority="high">
  <link rel="preload"
        as="image"
        imagesrcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcredit-card-reader.d1cc3127.png&amp;w=256&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcredit-card-reader.d1cc3127.png&amp;w=384&amp;q=75 2x"
        fetchpriority="high">
  <link rel="preload"
        as="image"
        imagesrcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fwizard.671ce364.png&amp;w=256&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fwizard.671ce364.png&amp;w=384&amp;q=75 2x"
        fetchpriority="high">
  <link rel="preload"
        as="image"
        imagesrcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ficon.ae943832.png&amp;w=32&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ficon.ae943832.png&amp;w=48&amp;q=75 2x"
        fetchpriority="high">
  <link rel="stylesheet"
        href="/_next/static/css/10e4dd1aec12afdf.css"
        data-precedence="next">
  <link rel="stylesheet"
        href="/_next/static/css/3545cad29b288095.css"
        data-precedence="next">
  <link rel="preload"
        as="script"
        fetchpriority="low"
        href="/_next/static/chunks/webpack-363d19520def0b88.js">
  <script src="https://connect.facebook.net/signals/config/410683368646128?v=2.9.176&amp;r=stable&amp;domain=codefa.st&amp;hme=872f04a0547459b3285cb03b0d7a47bfde40628f4b386809918a621e2688602f&amp;ex_m=70%2C121%2C107%2C111%2C61%2C4%2C100%2C69%2C16%2C97%2C89%2C51%2C54%2C172%2C175%2C187%2C183%2C184%2C186%2C29%2C101%2C53%2C77%2C185%2C167%2C170%2C180%2C181%2C188%2C131%2C41%2C189%2C190%2C34%2C143%2C15%2C50%2C195%2C194%2C133%2C18%2C40%2C1%2C43%2C65%2C66%2C67%2C71%2C93%2C17%2C14%2C96%2C92%2C91%2C108%2C52%2C110%2C39%2C109%2C30%2C94%2C26%2C168%2C171%2C140%2C86%2C56%2C84%2C33%2C73%2C0%2C95%2C32%2C28%2C82%2C83%2C88%2C47%2C46%2C87%2C37%2C11%2C12%2C13%2C6%2C7%2C25%2C22%2C23%2C57%2C62%2C64%2C75%2C102%2C27%2C76%2C9%2C8%2C80%2C48%2C21%2C104%2C103%2C105%2C98%2C10%2C20%2C3%2C38%2C74%2C19%2C5%2C90%2C81%2C44%2C35%2C85%2C2%2C36%2C63%2C42%2C106%2C45%2C79%2C68%2C112%2C60%2C59%2C31%2C99%2C58%2C55%2C49%2C78%2C72%2C24%2C113"
          async=""></script>
  <script async=""
          src="https://connect.facebook.net/en_US/fbevents.js"></script>
  <script src="/_next/static/chunks/fd9d1056-682c40cf9c938348.js"
          async=""></script>
  <script src="/_next/static/chunks/69-f12a2cf6be9beb40.js"
          async=""></script>
  <script src="/_next/static/chunks/main-app-f5c8d25c8479457f.js"
          async=""></script>
  <script src="/_next/static/chunks/126-2fe60ff12b81bcda.js"
          async=""></script>
  <script src="/_next/static/chunks/9-80daeb805727ea27.js"
          async=""></script>
  <script src="/_next/static/chunks/app/layout-aa134ab0782774d9.js"
          async=""></script>
  <link rel="preload"
        href="https://datafa.st/js/script.js"
        as="script">
  <link rel="preload"
        href="https://www.googletagmanager.com/gtag/js?id=G-SHPN0DPZNW"
        as="script">
  <link rel="preload"
        href="https://r.wdfl.co/rw.js"
        as="script">
  <meta name="theme-color"
        content="rgb(29, 29, 40)">
  <title>CodeFast | Learn to code in weeks, not months.</title>
  <meta name="description"
        content="CodeFast is the best coding course to learn how to turn your idea into an online business, fast.">
  <meta name="application-name"
        content="CodeFast">
  <meta name="keywords"
        content="CodeFast">
  <link rel="canonical"
        href="https://codefa.st">
  <meta property="og:title"
        content="CodeFast | Learn to code in weeks, not months.">
  <meta property="og:description"
        content="CodeFast is the best coding course to learn how to turn your idea into an online business, fast.">
  <meta property="og:url"
        content="https://codefa.st">
  <meta property="og:site_name"
        content="CodeFast | Learn to code in weeks, not months.">
  <meta property="og:locale"
        content="en_US">
  <meta property="og:image:type"
        content="image/png">
  <meta property="og:image:width"
        content="1200">
  <meta property="og:image:height"
        content="660">
  <meta property="og:image"
        content="https://codefa.st/opengraph-image.png?a87de873a8229e78">
  <meta property="og:type"
        content="website">
  <meta name="twitter:card"
        content="summary_large_image">
  <meta name="twitter:creator"
        content="@marc_louvion">
  <meta name="twitter:title"
        content="CodeFast | Learn to code in weeks, not months.">
  <meta name="twitter:description"
        content="CodeFast is the best coding course to learn how to turn your idea into an online business, fast.">
  <meta name="twitter:image:type"
        content="image/png">
  <meta name="twitter:image:width"
        content="1200">
  <meta name="twitter:image:height"
        content="660">
  <meta name="twitter:image"
        content="https://codefa.st/twitter-image.png?a87de873a8229e78">
  <link rel="icon"
        href="/icon.png?ae943832ec23612d"
        type="image/png"
        sizes="1080x1080">
  <meta name="next-size-adjust">
  <script>(self.__next_s = self.__next_s || []).push(["https://datafa.st/js/script.js", { "data-website-id": "66d5711aa2f1fb254ce42c0b", "data-domain": "codefa.st" }])</script>
  <noscript><img height="1"
         width="1"
         style="display:none"
         src="https://www.facebook.com/tr?id=410683368646128&amp;ev=PageView&amp;noscript=1" /></noscript>
  <script src="/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js"
          nomodule=""></script>
  <script data-website-id="66d5711aa2f1fb254ce42c0b"
          data-domain="codefa.st"
          src="https://datafa.st/js/script.js"></script>
  <script
          id="rewardful-queue">(function (w, r) { w._rwq = r; w[r] = w[r] || function () { (w[r].q = w[r].q || []).push(arguments) } })(window, 'rewardful');</script>
  <style id="_goober">
    @keyframes go2264125279 {
      from {
        transform: scale(0) rotate(45deg);
        opacity: 0;
      }

      to {
        transform: scale(1) rotate(45deg);
        opacity: 1;
      }
    }

    @keyframes go3020080000 {
      from {
        transform: scale(0);
        opacity: 0;
      }

      to {
        transform: scale(1);
        opacity: 1;
      }
    }

    @keyframes go463499852 {
      from {
        transform: scale(0) rotate(90deg);
        opacity: 0;
      }

      to {
        transform: scale(1) rotate(90deg);
        opacity: 1;
      }
    }

    @keyframes go1268368563 {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }

    @keyframes go1310225428 {
      from {
        transform: scale(0) rotate(45deg);
        opacity: 0;
      }

      to {
        transform: scale(1) rotate(45deg);
        opacity: 1;
      }
    }

    @keyframes go651618207 {
      0% {
        height: 0;
        width: 0;
        opacity: 0;
      }

      40% {
        height: 0;
        width: 6px;
        opacity: 1;
      }

      100% {
        opacity: 1;
        height: 10px;
      }
    }

    @keyframes go901347462 {
      from {
        transform: scale(0.6);
        opacity: 0.4;
      }

      to {
        transform: scale(1);
        opacity: 1;
      }
    }

    .go4109123758 {
      z-index: 9999;
    }

    .go4109123758>* {
      pointer-events: auto;
    }
  </style>
  <script src="https://www.gstatic.com/cv/js/sender/v1/cast_sender.js?loadCastFramework=1"></script>
  <style id="react-tooltip-core-styles"
         type="text/css">
    :root {
      --rt-color-white: #fff;
      --rt-color-dark: #222;
      --rt-color-success: #8dc572;
      --rt-color-error: #be6464;
      --rt-color-warning: #f0ad4e;
      --rt-color-info: #337ab7;
      --rt-opacity: 0.9;
      --rt-transition-show-delay: 0.15s;
      --rt-transition-closing-delay: 0.15s
    }

    .core-styles-module_tooltip__3vRRp {
      position: absolute;
      top: 0;
      left: 0;
      pointer-events: none;
      opacity: 0;
      will-change: opacity
    }

    .core-styles-module_fixed__pcSol {
      position: fixed
    }

    .core-styles-module_arrow__cvMwQ {
      position: absolute;
      background: inherit
    }

    .core-styles-module_noArrow__xock6 {
      display: none
    }

    .core-styles-module_clickable__ZuTTB {
      pointer-events: auto
    }

    .core-styles-module_show__Nt9eE {
      opacity: var(--rt-opacity);
      transition: opacity var(--rt-transition-show-delay)ease-out
    }

    .core-styles-module_closing__sGnxF {
      opacity: 0;
      transition: opacity var(--rt-transition-closing-delay)ease-in
    }
  </style>
  <style id="react-tooltip-base-styles"
         type="text/css">
    .styles-module_tooltip__mnnfp {
      padding: 8px 16px;
      border-radius: 3px;
      font-size: 90%;
      width: max-content
    }

    .styles-module_arrow__K0L3T {
      width: 8px;
      height: 8px
    }

    [class*='react-tooltip__place-top']>.styles-module_arrow__K0L3T {
      transform: rotate(45deg)
    }

    [class*='react-tooltip__place-right']>.styles-module_arrow__K0L3T {
      transform: rotate(135deg)
    }

    [class*='react-tooltip__place-bottom']>.styles-module_arrow__K0L3T {
      transform: rotate(225deg)
    }

    [class*='react-tooltip__place-left']>.styles-module_arrow__K0L3T {
      transform: rotate(315deg)
    }

    .styles-module_dark__xNqje {
      background: var(--rt-color-dark);
      color: var(--rt-color-white)
    }

    .styles-module_light__Z6W-X {
      background-color: var(--rt-color-white);
      color: var(--rt-color-dark)
    }

    .styles-module_success__A2AKt {
      background-color: var(--rt-color-success);
      color: var(--rt-color-white)
    }

    .styles-module_warning__SCK0X {
      background-color: var(--rt-color-warning);
      color: var(--rt-color-white)
    }

    .styles-module_error__JvumD {
      background-color: var(--rt-color-error);
      color: var(--rt-color-white)
    }

    .styles-module_info__BWdHW {
      background-color: var(--rt-color-info);
      color: var(--rt-color-white)
    }
  </style>
  <script src="//www.gstatic.com/cast/sdk/libs/sender/1.0/cast_framework.js"></script>
  <script src="//www.gstatic.com/eureka/clank/130/cast_sender.js"></script>
  <link rel="preload"
        as="image"
        fetchpriority="high"
        imagesrcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ficon.ae943832.png&amp;w=32&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ficon.ae943832.png&amp;w=64&amp;q=75 2x">
</head>

<body>
  <style>
    #nprogress {
      pointer-events: none
    }

    #nprogress .bar {
      background: rgb(29, 29, 40);
      position: fixed;
      z-index: 1600;
      top: 0;
      left: 0;
      width: 100%;
      height: 3px
    }

    #nprogress .peg {
      display: block;
      position: absolute;
      right: 0;
      width: 100px;
      height: 100%;
      box-shadow: 0 0 10px rgb(29, 29, 40), 0 0 5px rgb(29, 29, 40);
      opacity: 1;
      -webkit-transform: rotate(3deg) translate(0px, -4px);
      -ms-transform: rotate(3deg) translate(0px, -4px);
      transform: rotate(3deg) translate(0px, -4px)
    }

    #nprogress .spinner {
      display: block;
      position: fixed;
      z-index: 1600;
      top: 15px;
      right: 15px
    }

    #nprogress .spinner-icon {
      width: 18px;
      height: 18px;
      box-sizing: border-box;
      border: 2px solid transparent;
      border-top-color: rgb(29, 29, 40);
      border-left-color: rgb(29, 29, 40);
      border-radius: 50%;
      -webkit-animation: nprogress-spinner 400ms linear infinite;
      animation: nprogress-spinner 400ms linear infinite
    }

    .nprogress-custom-parent {
      overflow: hidden;
      position: relative
    }

    .nprogress-custom-parent #nprogress .bar,
    .nprogress-custom-parent #nprogress .spinner {
      position: absolute
    }

    @-webkit-keyframes nprogress-spinner {
      0% {
        -webkit-transform: rotate(0deg)
      }

      100% {
        -webkit-transform: rotate(360deg)
      }
    }

    @keyframes nprogress-spinner {
      0% {
        transform: rotate(0deg)
      }

      100% {
        transform: rotate(360deg)
      }
    }
  </style>
  <div data-theme="light">
    <header class="sticky top-0 z-50 border-b bg-base-100/50 backdrop-blur">
      <nav class="container flex items-center justify-between px-8 py-4 mx-auto"
           aria-label="Global">
        <div class="flex lg:flex-1"><a class="flex items-center gap-2 shrink-0"
             title="CodeFast homepage"
             href="/"><img alt="CodeFast logo"
                 fetchpriority="high"
                 width="32"
                 height="32"
                 decoding="async"
                 data-nimg="1"
                 class="size-8"
                 srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ficon.ae943832.png&amp;w=32&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ficon.ae943832.png&amp;w=64&amp;q=75 2x"
                 src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ficon.ae943832.png&amp;w=64&amp;q=75"
                 style="color: transparent;"><span class="font-extrabold text-lg">CodeFast</span></a></div>
        <div class="flex lg:hidden"><button type="button"
                  class="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5"><span class="sr-only">Open
              main menu</span><svg xmlns="http://www.w3.org/2000/svg"
                 width="24"
                 height="24"
                 viewBox="0 0 24 24"
                 fill="none"
                 stroke="currentColor"
                 stroke-width="2"
                 stroke-linecap="round"
                 stroke-linejoin="round"
                 class="lucide lucide-menu size-6">
              <line x1="4"
                    x2="20"
                    y1="12"
                    y2="12"></line>
              <line x1="4"
                    x2="20"
                    y1="6"
                    y2="6"></line>
              <line x1="4"
                    x2="20"
                    y1="18"
                    y2="18"></line>
            </svg></button></div>
        <div class="hidden lg:flex lg:justify-center lg:gap-12 lg:items-center"><a class="link link-hover"
             title="Reviews"
             href="/#testimonials">Reviews</a><a class="link link-hover"
             title="Pricing"
             href="/#pricing">Pricing</a><a class="link link-hover"
             title="FAQ"
             href="/#faq">FAQ</a></div>
        <div class="hidden lg:flex lg:justify-end lg:flex-1">
          <div class="flex flex-col lg:flex-row-reverse items-center gap-4"><a
               class="btn btn-primary group max-lg:btn-block btn-hover-orange lg:btn-sm"
               href="/#pricing">Get CodeFast</a><button class="btn btn-outline max-lg:btn-block lg:btn-sm">Sign
              in</button></div>
        </div>
      </nav>
      <div class="relative z-[60] hidden">
        <div
             class="fixed inset-y-0 right-0 z-10 w-full px-8 py-4 overflow-y-auto bg-base-100 sm:max-w-sm sm:ring-1 sm:ring-base-content transform origin-right transition ease-in-out duration-300">
          <div class="flex items-center justify-between"><a class="flex items-center gap-2 shrink-0"
               title="CodeFast homepage"
               href="/"><img alt="CodeFast logo"
                   fetchpriority="high"
                   width="32"
                   height="32"
                   decoding="async"
                   data-nimg="1"
                   class="size-8"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ficon.ae943832.png&amp;w=32&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ficon.ae943832.png&amp;w=64&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ficon.ae943832.png&amp;w=64&amp;q=75"
                   style="color: transparent;"><span class="font-extrabold text-lg">CodeFast</span></a><button
                    type="button"
                    class="-m-2.5 rounded-md p-2.5"><span class="sr-only">Close menu</span><svg
                   xmlns="http://www.w3.org/2000/svg"
                   width="24"
                   height="24"
                   viewBox="0 0 24 24"
                   fill="none"
                   stroke="currentColor"
                   stroke-width="2"
                   stroke-linecap="round"
                   stroke-linejoin="round"
                   class="lucide lucide-x size-6">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
              </svg></button></div>
          <div class="flow-root mt-6">
            <div class="py-4">
              <div class="flex flex-col gap-y-4 items-start"><a class="link link-hover"
                   title="Reviews"
                   href="/#testimonials">Reviews</a><a class="link link-hover"
                   title="Pricing"
                   href="/#pricing">Pricing</a><a class="link link-hover"
                   title="FAQ"
                   href="/#faq">FAQ</a></div>
            </div>
            <div class="py-4"></div>
            <div class="flex flex-col">
              <div class="flex flex-col lg:flex-row-reverse items-center gap-4"><a
                   class="btn btn-primary group max-lg:btn-block btn-hover-orange lg:btn-sm"
                   href="/#pricing">Get CodeFast</a><button class="btn btn-outline max-lg:btn-block lg:btn-sm">Sign
                  in</button></div>
            </div>
          </div>
        </div>
      </div>
    </header>
    <main class="max-w-[175rem] mx-auto">
      <section class=" bg-base-100 flex flex-col items-center justify-center gap-16 lg:gap-24 py-16 lg:pb-32 lg:pt-16">
        <div class="max-w-7xl mx-auto flex flex-col gap-10 items-center justify-center text-center px-8">
          <div class="flex gap-3 items-center mx-auto -mb-3"><img alt="Marc"
                 loading="lazy"
                 width="240"
                 height="240"
                 decoding="async"
                 data-nimg="1"
                 class="w-8 md:w-10 rounded-full border shadow-sm"
                 style="color:transparent"
                 srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmarc.be50937c.png&amp;w=256&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmarc.be50937c.png&amp;w=640&amp;q=75 2x"
                 src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmarc.be50937c.png&amp;w=640&amp;q=75">
            <div class="md:text-lg font-medium">By Marc Lou</div>
          </div>
          <h1 class="font-black text-5xl lg:text-6xl lg:text-[4rem] tracking-tight lg:tracking-[-0.035em] "><span
                  data-br=":R93au9la:"
                  data-brr="1"
                  style="display:inline-block;vertical-align:top;text-decoration:inherit;text-wrap:balance">Learn to
              code<!-- --> <span class="inline-block decoration-primary relative "><span class="relative z-10">in
                  weeks,</span><span
                      class="bottom-0  absolute bg-accent-pink h-4 md:h-6 md:-bottom-0.5 -inset-x-2 "></span></span> not
              months</span>
            <script>self.__wrap_n = self.__wrap_n || (self.CSS && CSS.supports("text-wrap", "balance") ? 1 : 2); self.__wrap_b = (e, t, a) => { let s = (a = a || document.querySelector(`[data-br="${e}"]`)).parentElement, r = e => a.style.maxWidth = e + "px"; a.style.maxWidth = ""; let l = s.clientWidth, i = s.clientHeight, n = l / 2 - .25, c = l + .5, o; if (l) { for (r(n), n = Math.max(a.scrollWidth, n); n + 1 < c;)r(o = Math.round((n + c) / 2)), s.clientHeight === i ? c = o : n = o; r(c * t + l * (1 - t)) } a.__wrap_o || "undefined" != typeof ResizeObserver && (a.__wrap_o = new ResizeObserver(() => { self.__wrap_b(0, +a.dataset.brr, a) })).observe(s) }; self.__wrap_n != 1 && self.__wrap_b(":R93au9la:", 1)</script>
          </h1>
          <p
             class="text-lg lg:text-2xl text-base-content-secondary leading-relaxed lg:leading-relaxed max-w-2xl mx-auto">
            Everything you need to build your SaaS or any online business—even as a complete beginner.</p>
          <div class="space-y-4"><a class="btn btn-primary group btn-pink btn-hover-pink btn-lg !text-lg btn-wide"
               href="/#pricing">Get instant access</a>
            <div class="flex text-base-content-secondary items-center justify-center gap-2">
              <p class="italic text-center"><span class="font-semibold text-base-content">277</span>
                <!-- -->entrepreneurs love the course</p>
            </div>
          </div>
        </div>
        <div class=" w-full  overflow-hidden">
          <div class="max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
            <div class="flex flex-col items-center text-center gap-4">
              <div class="relative"><img alt="Learn fundamentals"
                     fetchpriority="high"
                     width="192"
                     height="192"
                     decoding="async"
                     data-nimg="1"
                     class="w-48 scale-[0.60] translate-y-4"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fgear.1c9850d2.png&amp;w=256&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fgear.1c9850d2.png&amp;w=384&amp;q=75 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fgear.1c9850d2.png&amp;w=384&amp;q=75">
                <div class="absolute -bottom-6 left-1/2 -translate-x-1/2">
                  <div class="relative">
                    <div class="w-4 h-4 bg-primary rounded-full hidden lg:block"></div>
                    <div class="hidden lg:block absolute top-2 left-[-2000px] right-[-2000px] h-[1px] bg-primary"></div>
                  </div>
                </div>
              </div>
              <div class="lg:pt-8">
                <div class="font-bold text-lg">Day 1</div>
                <p class="text-base-content-secondary max-w-[20rem] mx-auto">Learn the fundamentals of coding</p>
              </div>
            </div>
            <div class="flex flex-col items-center text-center gap-4">
              <div class="relative"><img alt="Build SaaS"
                     fetchpriority="high"
                     width="192"
                     height="192"
                     decoding="async"
                     data-nimg="1"
                     class="w-48"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fserver.41b0b400.png&amp;w=256&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fserver.41b0b400.png&amp;w=384&amp;q=75 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fserver.41b0b400.png&amp;w=384&amp;q=75">
                <div class="absolute -bottom-6 left-1/2 -translate-x-1/2">
                  <div class="relative">
                    <div class="w-4 h-4 bg-primary rounded-full hidden lg:block"></div>
                  </div>
                </div>
              </div>
              <div class="lg:pt-8">
                <div class="font-bold text-lg">Day 4</div>
                <p class="text-base-content-secondary max-w-[20rem] mx-auto">Log in users and save in database</p>
              </div>
            </div>
            <div class="flex flex-col items-center text-center gap-4">
              <div class="relative"><img alt="AI coding"
                     fetchpriority="high"
                     width="192"
                     height="192"
                     decoding="async"
                     data-nimg="1"
                     class="w-48 scale-75 translate-y-4"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcredit-card-reader.d1cc3127.png&amp;w=256&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcredit-card-reader.d1cc3127.png&amp;w=384&amp;q=75 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcredit-card-reader.d1cc3127.png&amp;w=384&amp;q=75">
                <div class="absolute -bottom-6 left-1/2 -translate-x-1/2">
                  <div class="relative">
                    <div class="w-4 h-4 bg-primary rounded-full hidden lg:block"></div>
                  </div>
                </div>
              </div>
              <div class="lg:pt-8">
                <div class="font-bold text-lg">Day 9</div>
                <p class="text-base-content-secondary max-w-[20rem] mx-auto">Set up subscription payments</p>
              </div>
            </div>
            <div class="flex flex-col items-center text-center gap-4">
              <div class="relative"><img alt="Launch product"
                     fetchpriority="high"
                     width="192"
                     height="192"
                     decoding="async"
                     data-nimg="1"
                     class="w-48"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fwizard.671ce364.png&amp;w=256&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fwizard.671ce364.png&amp;w=384&amp;q=75 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fwizard.671ce364.png&amp;w=384&amp;q=75">
                <div class="absolute -bottom-6 left-1/2 -translate-x-1/2">
                  <div class="relative">
                    <div class="w-4 h-4 bg-primary rounded-full hidden lg:block"></div>
                  </div>
                </div>
              </div>
              <div class="lg:pt-8">
                <div class="font-bold text-lg">Day 14</div>
                <p class="text-base-content-secondary max-w-[20rem] mx-auto">Launch your idea!</p>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section data-theme="dark"
               class="text-center py-32 md:py-48 space-y-20 px-8">
        <div class="space-y-8 max-w-5xl mx-auto">
          <h2 class="text-4xl md:text-5xl font-extrabold !leading-tight"><span data-br=":R15au9la:"
                  data-brr="1"
                  style="display:inline-block;vertical-align:top;text-decoration:inherit;text-wrap:balance">Coding
              courses are designed for<!-- --> <span class="italic text-red-300">software engineers</span>, not <span
                    class="italic text-green-300">entrepreneurs</span></span>
            <script>self.__wrap_n = self.__wrap_n || (self.CSS && CSS.supports("text-wrap", "balance") ? 1 : 2); self.__wrap_b = (e, t, a) => { let s = (a = a || document.querySelector(`[data-br="${e}"]`)).parentElement, r = e => a.style.maxWidth = e + "px"; a.style.maxWidth = ""; let l = s.clientWidth, i = s.clientHeight, n = l / 2 - .25, c = l + .5, o; if (l) { for (r(n), n = Math.max(a.scrollWidth, n); n + 1 < c;)r(o = Math.round((n + c) / 2)), s.clientHeight === i ? c = o : n = o; r(c * t + l * (1 - t)) } a.__wrap_o || "undefined" != typeof ResizeObserver && (a.__wrap_o = new ResizeObserver(() => { self.__wrap_b(0, +a.dataset.brr, a) })).observe(s) }; self.__wrap_n != 1 && self.__wrap_b(":R15au9la:", 1)</script>
          </h2>
        </div>
        <div class="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto text-left">
          <div class="card rounded-box bg-base-100  border border-red-300 bg-red-950/10">
            <div class="card-body space-y-4">
              <h3 class="text-2xl font-bold flex items-center justify-between text-red-300">Coding as an
                employee<!-- --> <svg class="size-8"
                     viewBox="0 0 67 67"
                     fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path d="M33.3848 61.208C48.75 61.208 61.2061 48.752 61.2061 33.3867C61.2061 18.0214 48.75 5.56543 33.3848 5.56543C18.0195 5.56543 5.56348 18.0214 5.56348 33.3867C5.56348 48.752 18.0195 61.208 33.3848 61.208Z"
                        class="fill-red-300 stroke-red-300"
                        stroke-width="5.56426"
                        stroke-linecap="round"
                        stroke-linejoin="round"></path>
                  <path d="M41.7318 25.0391L25.0391 41.7318"
                        class="stroke-red-900"
                        stroke-width="5.56426"
                        stroke-linecap="round"
                        stroke-linejoin="round"></path>
                  <path d="M25.0391 25.0391L41.7318 41.7318"
                        class="stroke-red-900"
                        stroke-width="5.56426"
                        stroke-linecap="round"
                        stroke-linejoin="round"></path>
                </svg></h3>
              <ul class="space-y-3 text-base-content-secondary">
                <li>• Invert binary trees</li>
                <li>• Master 47 sorting algorithms you'll never implement</li>
                <li>• Memorize Big O notation to impress your interviewer</li>
                <li>• Read documentation longer than The Lord of the Rings</li>
                <li>• Write complex code when a simple AI prompt would do</li>
              </ul>
            </div>
          </div>
          <div class="card rounded-box  bg-base-100  border border-green-300 bg-green-950/10">
            <div class="card-body space-y-4">
              <h3 class="text-2xl font-bold flex items-center justify-between text-green-300">Coding as an
                entrepreneur<!-- --> <svg class="size-8"
                     viewBox="0 0 67 67"
                     fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                  <path d="M33.3856 61.207C48.7509 61.207 61.2069 48.751 61.2069 33.3857C61.2069 18.0205 48.7509 5.56445 33.3856 5.56445C18.0203 5.56445 5.56433 18.0205 5.56433 33.3857C5.56433 48.751 18.0203 61.207 33.3856 61.207Z"
                        class="fill-green-300 stroke-green-300"
                        stroke-width="5.56426"
                        stroke-linecap="round"
                        stroke-linejoin="round"></path>
                  <path d="M25.0391 33.3865L30.6034 38.9508L41.7319 27.8223"
                        class="stroke-green-900"
                        stroke-width="5.56426"
                        stroke-linecap="round"
                        stroke-linejoin="round"></path>
                </svg></h3>
              <ul class="space-y-3 text-base-content-secondary">
                <li>• Learn only the fundamentals</li>
                <li>• Use AI to code for you</li>
                <li>• Keep learning on the fly</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
      <section class="">
        <div class="px-8 pt-32 md:pt-48 pb-24 md:pb-36 space-y-16 ">
          <div
               class="text-xl md:text-2xl  text-red-700 text-center flex flex-col md:flex-row items-center justify-center gap-3 pb-24">
            <span class="text-4xl">🚫</span>
            <p class="italic">Instead of watching 100 hours of "JavaScript Basics" videos...</p>
          </div>
          <div class="max-sm:-mx-4">
            <div
                 class="relative flex justify-center items-center w-full max-md:max-w-[400px] md:w-[650px] aspect-[1/1] md:aspect-[3/2] rounded-full mx-auto">
              <div class="absolute top-2 flex flex-col items-center"><img alt="Book"
                     loading="lazy"
                     width="768"
                     height="768"
                     decoding="async"
                     data-nimg="1"
                     class="size-[70px] md:size-24 -rotate-3"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbook.3752ab35.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbook.3752ab35.png&amp;w=1920&amp;q=75 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbook.3752ab35.png&amp;w=1920&amp;q=75"><span
                      class="text-lg md:text-xl font-semibold">Learn what you need</span></div>
              <div class="absolute top-12 left-12 rotate-[135deg]"><svg class="size-12 md:size-14 fill-base-content"
                     viewBox="0 0 251 81">
                  <g>
                    <path
                          d="M14.4435 26.0257C16.3478 34.2205 18.0405 42.2052 20.1564 51.2405C14.6551 50.6101 11.4813 47.2481 10.2118 43.4659C6.40317 32.1193 2.80615 20.5625 0.267089 8.79558C-1.21404 2.07164 3.65251 -1.50048 10.2118 0.600755C21.2144 3.96273 32.0054 7.95508 43.0081 11.7373C43.6428 11.9474 44.4892 12.1576 44.7008 12.5778C45.7587 14.0487 46.3935 15.7296 47.2398 17.4106C45.7587 18.041 44.2776 19.5119 43.0081 19.3017C38.5647 18.6714 34.3329 17.6208 30.1011 16.7803C27.7737 16.36 25.6577 15.7297 22.2723 16.7803C24.5998 19.3018 26.9273 22.0333 29.2548 24.5548C79.6129 74.5642 155.15 85.0703 217.781 51.2405C225.821 46.8279 233.227 41.5748 241.055 36.742C243.806 35.061 246.557 33.5901 249.307 31.9092C249.942 32.3294 250.365 32.9598 251 33.38C250.365 35.2711 250.154 37.7926 248.673 39.0533C244.018 43.4659 239.363 47.8785 234.073 51.6607C181.599 89.4829 108.601 90.9538 52.1064 54.8126C41.3154 47.8785 31.7938 39.0533 21.8492 31.0686C19.7333 29.3876 18.0406 27.4966 16.1363 25.6054C15.7131 25.3953 15.0783 25.6054 14.4435 26.0257Z">
                    </path>
                  </g>
                </svg></div>
              <div class="absolute right-2 flex flex-col items-center"><img alt="Bolt"
                     loading="lazy"
                     width="768"
                     height="768"
                     decoding="async"
                     data-nimg="1"
                     class="size-[70px] md:size-24 rotate-3"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbolt.2105afa3.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbolt.2105afa3.png&amp;w=1920&amp;q=75 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbolt.2105afa3.png&amp;w=1920&amp;q=75"><span
                      class="text-lg md:text-xl font-semibold">Build features</span></div>
              <div class="absolute top-12 right-12 rotate-[-135deg]"><svg class="size-12 md:size-14 fill-base-content"
                     viewBox="0 0 251 81">
                  <g>
                    <path
                          d="M14.4435 26.0257C16.3478 34.2205 18.0405 42.2052 20.1564 51.2405C14.6551 50.6101 11.4813 47.2481 10.2118 43.4659C6.40317 32.1193 2.80615 20.5625 0.267089 8.79558C-1.21404 2.07164 3.65251 -1.50048 10.2118 0.600755C21.2144 3.96273 32.0054 7.95508 43.0081 11.7373C43.6428 11.9474 44.4892 12.1576 44.7008 12.5778C45.7587 14.0487 46.3935 15.7296 47.2398 17.4106C45.7587 18.041 44.2776 19.5119 43.0081 19.3017C38.5647 18.6714 34.3329 17.6208 30.1011 16.7803C27.7737 16.36 25.6577 15.7297 22.2723 16.7803C24.5998 19.3018 26.9273 22.0333 29.2548 24.5548C79.6129 74.5642 155.15 85.0703 217.781 51.2405C225.821 46.8279 233.227 41.5748 241.055 36.742C243.806 35.061 246.557 33.5901 249.307 31.9092C249.942 32.3294 250.365 32.9598 251 33.38C250.365 35.2711 250.154 37.7926 248.673 39.0533C244.018 43.4659 239.363 47.8785 234.073 51.6607C181.599 89.4829 108.601 90.9538 52.1064 54.8126C41.3154 47.8785 31.7938 39.0533 21.8492 31.0686C19.7333 29.3876 18.0406 27.4966 16.1363 25.6054C15.7131 25.3953 15.0783 25.6054 14.4435 26.0257Z">
                    </path>
                  </g>
                </svg></div>
              <div class="absolute bottom-2 flex flex-col items-center"><img alt="Rocket"
                     loading="lazy"
                     width="768"
                     height="768"
                     decoding="async"
                     data-nimg="1"
                     class="size-[70px] md:size-24 -rotate-6"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Frocket.e5ec330f.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Frocket.e5ec330f.png&amp;w=1920&amp;q=75 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Frocket.e5ec330f.png&amp;w=1920&amp;q=75"><span
                      class="text-lg md:text-xl font-semibold">Launch</span></div>
              <div class="absolute bottom-12 right-12 rotate-[-55deg]"><svg class="size-12 md:size-14 fill-base-content"
                     viewBox="0 0 251 81">
                  <g>
                    <path
                          d="M14.4435 26.0257C16.3478 34.2205 18.0405 42.2052 20.1564 51.2405C14.6551 50.6101 11.4813 47.2481 10.2118 43.4659C6.40317 32.1193 2.80615 20.5625 0.267089 8.79558C-1.21404 2.07164 3.65251 -1.50048 10.2118 0.600755C21.2144 3.96273 32.0054 7.95508 43.0081 11.7373C43.6428 11.9474 44.4892 12.1576 44.7008 12.5778C45.7587 14.0487 46.3935 15.7296 47.2398 17.4106C45.7587 18.041 44.2776 19.5119 43.0081 19.3017C38.5647 18.6714 34.3329 17.6208 30.1011 16.7803C27.7737 16.36 25.6577 15.7297 22.2723 16.7803C24.5998 19.3018 26.9273 22.0333 29.2548 24.5548C79.6129 74.5642 155.15 85.0703 217.781 51.2405C225.821 46.8279 233.227 41.5748 241.055 36.742C243.806 35.061 246.557 33.5901 249.307 31.9092C249.942 32.3294 250.365 32.9598 251 33.38C250.365 35.2711 250.154 37.7926 248.673 39.0533C244.018 43.4659 239.363 47.8785 234.073 51.6607C181.599 89.4829 108.601 90.9538 52.1064 54.8126C41.3154 47.8785 31.7938 39.0533 21.8492 31.0686C19.7333 29.3876 18.0406 27.4966 16.1363 25.6054C15.7131 25.3953 15.0783 25.6054 14.4435 26.0257Z">
                    </path>
                  </g>
                </svg></div>
              <div class="absolute left-2 flex flex-col items-center"><img alt="Bulb"
                     loading="lazy"
                     width="768"
                     height="768"
                     decoding="async"
                     data-nimg="1"
                     class="size-[70px] md:size-24"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbulb.b7adce6e.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbulb.b7adce6e.png&amp;w=1920&amp;q=75 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbulb.b7adce6e.png&amp;w=1920&amp;q=75"><span
                      class="text-lg md:text-xl font-semibold">Get feedback</span></div>
              <div class="absolute bottom-12 left-12 rotate-[45deg]"><svg class="size-12 md:size-14 fill-base-content"
                     viewBox="0 0 251 81">
                  <g>
                    <path
                          d="M14.4435 26.0257C16.3478 34.2205 18.0405 42.2052 20.1564 51.2405C14.6551 50.6101 11.4813 47.2481 10.2118 43.4659C6.40317 32.1193 2.80615 20.5625 0.267089 8.79558C-1.21404 2.07164 3.65251 -1.50048 10.2118 0.600755C21.2144 3.96273 32.0054 7.95508 43.0081 11.7373C43.6428 11.9474 44.4892 12.1576 44.7008 12.5778C45.7587 14.0487 46.3935 15.7296 47.2398 17.4106C45.7587 18.041 44.2776 19.5119 43.0081 19.3017C38.5647 18.6714 34.3329 17.6208 30.1011 16.7803C27.7737 16.36 25.6577 15.7297 22.2723 16.7803C24.5998 19.3018 26.9273 22.0333 29.2548 24.5548C79.6129 74.5642 155.15 85.0703 217.781 51.2405C225.821 46.8279 233.227 41.5748 241.055 36.742C243.806 35.061 246.557 33.5901 249.307 31.9092C249.942 32.3294 250.365 32.9598 251 33.38C250.365 35.2711 250.154 37.7926 248.673 39.0533C244.018 43.4659 239.363 47.8785 234.073 51.6607C181.599 89.4829 108.601 90.9538 52.1064 54.8126C41.3154 47.8785 31.7938 39.0533 21.8492 31.0686C19.7333 29.3876 18.0406 27.4966 16.1363 25.6054C15.7131 25.3953 15.0783 25.6054 14.4435 26.0257Z">
                    </path>
                  </g>
                </svg></div>
            </div>
          </div>
          <div class="flex flex-col items-center justify-center gap-8 md:gap-12 text-center">
            <div class="space-y-6 md:space-y-8">
              <h2 class="text-4xl sm:text-5xl md:text-7xl font-black leading-tight tracking-tight"><span
                      data-br=":Rn7au9la:"
                      data-brr="1"
                      style="display:inline-block;vertical-align:top;text-decoration:inherit;text-wrap:balance">Start a
                  learning<!-- --> <span class="inline-block decoration-primary relative "><span
                          class="relative z-10">flywheel</span><span
                          class="bottom-0  absolute bg-accent-green h-4 md:h-6 md:-bottom-0.5 -inset-x-2 "></span></span></span>
                <script>self.__wrap_n = self.__wrap_n || (self.CSS && CSS.supports("text-wrap", "balance") ? 1 : 2); self.__wrap_b = (e, t, a) => { let s = (a = a || document.querySelector(`[data-br="${e}"]`)).parentElement, r = e => a.style.maxWidth = e + "px"; a.style.maxWidth = ""; let l = s.clientWidth, i = s.clientHeight, n = l / 2 - .25, c = l + .5, o; if (l) { for (r(n), n = Math.max(a.scrollWidth, n); n + 1 < c;)r(o = Math.round((n + c) / 2)), s.clientHeight === i ? c = o : n = o; r(c * t + l * (1 - t)) } a.__wrap_o || "undefined" != typeof ResizeObserver && (a.__wrap_o = new ResizeObserver(() => { self.__wrap_b(0, +a.dataset.brr, a) })).observe(s) }; self.__wrap_n != 1 && self.__wrap_b(":Rn7au9la:", 1)</script>
              </h2>
              <div class="text-xl md:text-2xl max-w-xl mx-auto !leading-relaxed space-y-2">Code like an entrepreneur —
                build your idea in<!-- --> <span class="whitespace-nowrap">14 days</span> to get real-world feedback and
                keep learning as you go.</div>
            </div>
            <div class="space-y-4"><a
                 class="btn btn-primary group btn-green md:btn-lg md:!text-lg btn-block lg:btn-wide"
                 href="/#pricing">Get instant access</a>
              <div class="text-center text-base-content-secondary flex items-center justify-center gap-2"><svg
                     xmlns="http://www.w3.org/2000/svg"
                     viewBox="0 0 20 20"
                     fill="currentColor"
                     class="size-5">
                  <path fill-rule="evenodd"
                        d="M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z"
                        clip-rule="evenodd"></path>
                </svg>No experience needed</div>
            </div>
          </div>
        </div>
      </section>
      <section class="md:border-x ">
        <div data-theme="">
          <div class="py-24 md:py-36 flex flex-col items-center justify-center max-w-3xl mx-auto">
            <div class="flex flex-col md:flex-row justify-start items-start gap-8  max-md:px-8"><img alt="Jainil"
                   loading="lazy"
                   width="220"
                   height="220"
                   decoding="async"
                   data-nimg="1"
                   class="!aspect-square object-cover max-w-44 md:max-w-52 bg-accent-orange border shadow shrink-0"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fjainil.5d15e99a.jpeg&amp;w=256&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fjainil.5d15e99a.jpeg&amp;w=640&amp;q=100 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fjainil.5d15e99a.jpeg&amp;w=640&amp;q=100">
              <div class="relative flex flex-col justify-center items-start">
                <div class="mb-3"><svg xmlns="http://www.w3.org/2000/svg"
                       viewBox="0 0 24 24"
                       class="size-6 md:size-8">
                    <path d="M8.5 13.5C8.5 16.2614 6.26142 18.5 3.5 18.5C2.94772 18.5 2.5 18.9477 2.5 19.5C2.5 20.0523 2.94772 20.5 3.5 20.5C7.36599 20.5 10.5 17.366 10.5 13.5V5.75C10.5 4.23122 9.26878 3 7.75 3H3.75C2.23122 3 1 4.23122 1 5.75V9.75C1 11.2688 2.23122 12.5 3.75 12.5H7.75C8.01001 12.5 8.26158 12.4639 8.5 12.3965V13.5Z"
                          fill="currentColor"></path>
                    <path d="M20.5 13.5C20.5 16.2614 18.2614 18.5 15.5 18.5C14.9477 18.5 14.5 18.9477 14.5 19.5C14.5 20.0523 14.9477 20.5 15.5 20.5C19.366 20.5 22.5 17.366 22.5 13.5V5.75C22.5 4.23122 21.2688 3 19.75 3H15.75C14.2312 3 13 4.23122 13 5.75V9.75C13 11.2688 14.2312 12.5 15.75 12.5H19.75C20.01 12.5 20.2616 12.4639 20.5 12.3965V13.5Z"
                          fill="currentColor"></path>
                  </svg></div>
                <div class="text-base max-md:font-semibold  md:text-xl md:font-semibold mb-4 !leading-relaxed"><span
                        class="text-yellow-800 bg-yellow-100 px-1">First sale is definitely special,</span>
                  <!-- -->especially when it's your first self coded web-app. Thank you @marclou</div>
                <div class="italic md:text-lg">—<!-- -->Jainil</div>
              </div>
            </div>
            <div class="py-8 flex flex-row justify-center items-center -gap-4 max-md:px-8"><svg
                   class="w-14 rotate-90 fill-base-content-secondary"
                   viewBox="0 0 296 71"
                   fill="none"
                   xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_3_228)">
                  <path
                        d="M-3.10351e-06 71C13.102 44.3623 83.0496 8.91537 132.922 2.62301C186.809 -4.29859 217.873 1.57428 281.27 31.1484C280.425 26.9535 279.368 23.5976 278.945 20.4514C278.311 16.8857 277.677 13.1103 277.889 9.54461C277.889 8.49589 279.791 6.60818 281.059 6.39843C282.327 6.18869 284.44 7.6569 284.862 8.70563C288.666 18.7734 292.047 28.8412 295.64 39.1187C296.696 42.4746 295.428 44.9916 291.836 46.0403C278.1 49.3962 264.364 52.9619 250.417 56.1081C247.247 56.7373 243.443 55.8983 239.217 55.8983C242.598 46.8793 250.206 47.928 255.7 45.6208C261.406 43.1039 267.534 41.6357 274.93 39.1187C257.39 27.373 239.639 20.0319 220.832 15.4175C151.941 -1.78164 88.544 12.481 30.0078 50.6547C22.4002 55.6886 15.4265 61.5615 8.03027 66.8051C5.70572 67.8538 3.59247 68.9026 -3.10351e-06 71Z">
                  </path>
                </g>
              </svg>
              <p class="font-base italic text-base text-base-content-secondary">Built <u
                   class="select-all text-blue-700">confettisaas.pro</u>,<!-- --> <u
                   class="select-all text-blue-700">epicaitravelphotos.pro</u> <!-- -->&amp; more</p>
            </div>
            <div class="w-full relative mx-auto hidden lg:block"><img alt="Macbook"
                   loading="lazy"
                   width="687"
                   height="453"
                   decoding="async"
                   data-nimg="1"
                   class="w-full "
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmacbook.8ed77199.png&amp;w=750&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmacbook.8ed77199.png&amp;w=1920&amp;q=100 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmacbook.8ed77199.png&amp;w=1920&amp;q=100">
              <div
                   class="absolute right-[95px] left-[93px] top-[10px] bottom-[118px] flex items-center justify-center bg-primary rounded-t-[1.3rem] rounded-b-lg shadow-inner">
                <img alt="[object Object] screenshot"
                     loading="lazy"
                     width="800"
                     height="450"
                     decoding="async"
                     data-nimg="1"
                     class="w-full"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fepictravelphoto.ce3f492d.gif&amp;w=828&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fepictravelphoto.ce3f492d.gif&amp;w=1920&amp;q=100 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fepictravelphoto.ce3f492d.gif&amp;w=1920&amp;q=100">
              </div>
            </div>
            <div class="w-full relative mx-auto lg:hidden"><img alt="[object Object] screenshot"
                   loading="lazy"
                   width="800"
                   height="450"
                   decoding="async"
                   data-nimg="1"
                   class="w-full border shadow"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fepictravelphoto.ce3f492d.gif&amp;w=828&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fepictravelphoto.ce3f492d.gif&amp;w=1920&amp;q=100 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fepictravelphoto.ce3f492d.gif&amp;w=1920&amp;q=100">
            </div>
          </div>
        </div>
      </section>
      <section class="sm:px-8">
        <div class="max-w-5xl mx-auto">
          <div class="py-16 text-center px-8 space-y-8">
            <h2 class="text-3xl lg:text-5xl font-extrabold tracking-tight"><span data-br=":R5bau9la:"
                    data-brr="1"
                    style="display:inline-block;vertical-align:top;text-decoration:inherit;text-wrap:balance">From 0 to
                SaaS in<!-- --> <span class="inline-block decoration-primary relative "><span class="relative z-10">12
                    hours</span><span
                        class="bottom-0  absolute bg-accent-pink h-4 md:h-6 md:-bottom-0.5 -inset-x-2 "></span></span>
                <!-- -->of video</span>
              <script>self.__wrap_n = self.__wrap_n || (self.CSS && CSS.supports("text-wrap", "balance") ? 1 : 2); self.__wrap_b = (e, t, a) => { let s = (a = a || document.querySelector(`[data-br="${e}"]`)).parentElement, r = e => a.style.maxWidth = e + "px"; a.style.maxWidth = ""; let l = s.clientWidth, i = s.clientHeight, n = l / 2 - .25, c = l + .5, o; if (l) { for (r(n), n = Math.max(a.scrollWidth, n); n + 1 < c;)r(o = Math.round((n + c) / 2)), s.clientHeight === i ? c = o : n = o; r(c * t + l * (1 - t)) } a.__wrap_o || "undefined" != typeof ResizeObserver && (a.__wrap_o = new ResizeObserver(() => { self.__wrap_b(0, +a.dataset.brr, a) })).observe(s) }; self.__wrap_n != 1 && self.__wrap_b(":R5bau9la:", 1)</script>
            </h2>
            <p class="text-base-content-secondary text-lg italic">Everything you need to build an online business, even
              if you have no experience</p>
          </div>
          <div class="grid md:grid-cols-2 border-y sm:border-x">
            <div class="relative p-10 space-y-8  max-md:border-b md:border-r"><img alt="The Mindset"
                   loading="lazy"
                   width="768"
                   height="768"
                   decoding="async"
                   data-nimg="1"
                   class="size-48 "
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbulb.b7adce6e.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbulb.b7adce6e.png&amp;w=1920&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbulb.b7adce6e.png&amp;w=1920&amp;q=75">
              <div>
                <div class="flex items-center gap-4 mb-2">
                  <h3 class="text-2xl font-bold">1<!-- -->. <!-- -->The Mindset</h3><span
                        class="absolute top-0 right-0 text-sm text-base-content bg-accent-blue px-2 py-1 border-b border-l">15
                    minutes</span>
                </div>
                <p class="text-base-content-secondary mb-6">Learn to code as an entrepreneur, not an employee</p>
              </div>
              <ul class="grid gap-3">
                <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                       viewBox="0 0 16 16"
                       fill="currentColor"
                       class="w-4 h-4 shrink-0 opacity-80">
                    <path fill-rule="evenodd"
                          d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                          clip-rule="evenodd"></path>
                  </svg><span>How to ship products fast</span></li>
                <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                       viewBox="0 0 16 16"
                       fill="currentColor"
                       class="w-4 h-4 shrink-0 opacity-80">
                    <path fill-rule="evenodd"
                          d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                          clip-rule="evenodd"></path>
                  </svg><span>How to make money with code</span></li>
                <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                       viewBox="0 0 16 16"
                       fill="currentColor"
                       class="w-4 h-4 shrink-0 opacity-80">
                    <path fill-rule="evenodd"
                          d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                          clip-rule="evenodd"></path>
                  </svg><span>Finding motivation to build</span></li>
              </ul>
            </div>
            <div class="relative p-10 space-y-8  "><img alt="The Fundamentals"
                   loading="lazy"
                   width="1637"
                   height="1637"
                   decoding="async"
                   data-nimg="1"
                   class="size-48 "
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fhouse-construction.c0cbb345.png&amp;w=1920&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fhouse-construction.c0cbb345.png&amp;w=3840&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fhouse-construction.c0cbb345.png&amp;w=3840&amp;q=75">
              <div>
                <div class="flex items-center gap-4 mb-2">
                  <h3 class="text-2xl font-bold">2<!-- -->. <!-- -->The Fundamentals</h3><span
                        class="absolute top-0 right-0 text-sm text-base-content bg-accent-orange px-2 py-1 border-b border-l">1
                    hour 15 minutes</span>
                </div>
                <p class="text-base-content-secondary mb-6">Everything important about web development so you know how
                  to tell the AI what to code for you</p>
              </div>
              <ul class="grid gap-3">
                <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                       viewBox="0 0 16 16"
                       fill="currentColor"
                       class="w-4 h-4 shrink-0 opacity-80">
                    <path fill-rule="evenodd"
                          d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                          clip-rule="evenodd"></path>
                  </svg><span>How the internet works (HTTP, domain names)</span></li>
                <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                       viewBox="0 0 16 16"
                       fill="currentColor"
                       class="w-4 h-4 shrink-0 opacity-80">
                    <path fill-rule="evenodd"
                          d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                          clip-rule="evenodd"></path>
                  </svg><span>HTML</span></li>
                <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                       viewBox="0 0 16 16"
                       fill="currentColor"
                       class="w-4 h-4 shrink-0 opacity-80">
                    <path fill-rule="evenodd"
                          d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                          clip-rule="evenodd"></path>
                  </svg><span>CSS</span></li>
                <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                       viewBox="0 0 16 16"
                       fill="currentColor"
                       class="w-4 h-4 shrink-0 opacity-80">
                    <path fill-rule="evenodd"
                          d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                          clip-rule="evenodd"></path>
                  </svg><span>JavaScript</span></li>
              </ul>
            </div>
            <div class="relative p-10 space-y-8 md:col-span-2 border-t "><img alt="Your First SaaS"
                   loading="lazy"
                   width="768"
                   height="768"
                   decoding="async"
                   data-nimg="1"
                   class="size-48 size-60"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcastle.2622a990.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcastle.2622a990.png&amp;w=1920&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcastle.2622a990.png&amp;w=1920&amp;q=75">
              <div>
                <div class="flex items-center gap-4 mb-2">
                  <h3 class="text-2xl font-bold">3<!-- -->. <!-- -->Your First SaaS</h3><span
                        class="absolute top-0 right-0 text-sm text-base-content bg-accent-green px-2 py-1 border-b border-l">10
                    hours 26 minutes</span>
                </div>
                <p class="text-base-content-secondary mb-6">Build and launch a real SaaS business that makes money</p>
              </div>
              <div class="space-y-10">
                <div>
                  <h4 class="uppercase font-semibold tracking-wide text-base-content-secondary text-sm mb-3">🎨<!-- -->
                    <span class="opacity-80">Frontend Development</span></h4>
                  <ul class="grid md:grid-cols-3 gap-3">
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Next.js application</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>React components</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Tailwind CSS</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>UI/UX design</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>SVG &amp; icons</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Copywriting essentials</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Images</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Links</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Dynamic pages</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Responsive design</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Buttons</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Form inputs</span></li>
                  </ul>
                </div>
                <div>
                  <h4 class="uppercase font-semibold tracking-wide text-base-content-secondary text-sm mb-3">🔒<!-- -->
                    <span class="opacity-80">Backend &amp; Authentication</span></h4>
                  <ul class="grid md:grid-cols-3 gap-3">
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Magic link authentication</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Google OAuth login</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Database with MongoDB</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>API routes</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Backend VS. Frontend</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Cookies</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Data structures</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Private user dashboard</span></li>
                  </ul>
                </div>
                <div>
                  <h4 class="uppercase font-semibold tracking-wide text-base-content-secondary text-sm mb-3">🚀<!-- -->
                    <span class="opacity-80">Business &amp; Deployment</span></h4>
                  <ul class="grid md:grid-cols-3 gap-3">
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Stripe subscriptions</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>LemonSqueezy subscriptions</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Emails</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Domain names</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>DNS</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Server hosting</span></li>
                  </ul>
                </div>
                <div>
                  <h4 class="uppercase font-semibold tracking-wide text-base-content-secondary text-sm mb-3">🛠️<!-- -->
                    <span class="opacity-80">Developer Tools &amp; Skills</span></h4>
                  <ul class="grid md:grid-cols-3 gap-3">
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>SEO <span class="italic">(coming soon)</span></span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>AI-powered coding</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Git &amp; GitHub</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Node.js</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>AI-powered design</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Security best practices</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>npm</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>Terminal</span></li>
                    <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                           viewBox="0 0 16 16"
                           fill="currentColor"
                           class="w-4 h-4 shrink-0 opacity-80">
                        <path fill-rule="evenodd"
                              d="M12.416 3.376a.75.75 0 0 1 .208 1.04l-5 7.5a.75.75 0 0 1-1.154.114l-3-3a.75.75 0 0 1 1.06-1.06l2.353 2.353 4.493-6.74a.75.75 0 0 1 1.04-.207Z"
                              clip-rule="evenodd"></path>
                      </svg><span>VSCode extensions</span></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div class="py-20 flex items-center gap-8 justify-center"><button class="btn btn-outline">Watch a preview
              👀</button></div>
        </div>
      </section>
      <section class="md:border-x ">
        <div data-theme="">
          <div class="py-24 md:py-36 flex flex-col items-center justify-center max-w-3xl mx-auto">
            <div class="flex flex-col md:flex-row justify-start items-start gap-8  max-md:px-8"><img alt="Adsy"
                   loading="lazy"
                   width="220"
                   height="220"
                   decoding="async"
                   data-nimg="1"
                   class="!aspect-square object-cover max-w-44 md:max-w-52 bg-accent-green border shadow shrink-0"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fadscei_transparent.cb89d8c7.png&amp;w=256&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fadscei_transparent.cb89d8c7.png&amp;w=640&amp;q=100 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fadscei_transparent.cb89d8c7.png&amp;w=640&amp;q=100">
              <div class="relative flex flex-col justify-center items-start">
                <div class="mb-3"><svg xmlns="http://www.w3.org/2000/svg"
                       viewBox="0 0 24 24"
                       class="size-6 md:size-8">
                    <path d="M8.5 13.5C8.5 16.2614 6.26142 18.5 3.5 18.5C2.94772 18.5 2.5 18.9477 2.5 19.5C2.5 20.0523 2.94772 20.5 3.5 20.5C7.36599 20.5 10.5 17.366 10.5 13.5V5.75C10.5 4.23122 9.26878 3 7.75 3H3.75C2.23122 3 1 4.23122 1 5.75V9.75C1 11.2688 2.23122 12.5 3.75 12.5H7.75C8.01001 12.5 8.26158 12.4639 8.5 12.3965V13.5Z"
                          fill="currentColor"></path>
                    <path d="M20.5 13.5C20.5 16.2614 18.2614 18.5 15.5 18.5C14.9477 18.5 14.5 18.9477 14.5 19.5C14.5 20.0523 14.9477 20.5 15.5 20.5C19.366 20.5 22.5 17.366 22.5 13.5V5.75C22.5 4.23122 21.2688 3 19.75 3H15.75C14.2312 3 13 4.23122 13 5.75V9.75C13 11.2688 14.2312 12.5 15.75 12.5H19.75C20.01 12.5 20.2616 12.4639 20.5 12.3965V13.5Z"
                          fill="currentColor"></path>
                  </svg></div>
                <div class="text-base max-md:font-semibold  md:text-xl md:font-semibold mb-4 !leading-relaxed">I've
                  never finished Udemy courses...<!-- --> <span class="text-yellow-800 bg-yellow-100 px-1">Marc cuts the
                    BS</span> and teaches you only the most important parts of coding.</div>
                <div class="italic md:text-lg">—<!-- -->Adsy</div>
              </div>
            </div>
            <div class="py-8 flex flex-row justify-center items-center -gap-4 max-md:px-8"><svg
                   class="w-14 rotate-90 fill-base-content-secondary"
                   viewBox="0 0 296 71"
                   fill="none"
                   xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_3_228)">
                  <path
                        d="M-3.10351e-06 71C13.102 44.3623 83.0496 8.91537 132.922 2.62301C186.809 -4.29859 217.873 1.57428 281.27 31.1484C280.425 26.9535 279.368 23.5976 278.945 20.4514C278.311 16.8857 277.677 13.1103 277.889 9.54461C277.889 8.49589 279.791 6.60818 281.059 6.39843C282.327 6.18869 284.44 7.6569 284.862 8.70563C288.666 18.7734 292.047 28.8412 295.64 39.1187C296.696 42.4746 295.428 44.9916 291.836 46.0403C278.1 49.3962 264.364 52.9619 250.417 56.1081C247.247 56.7373 243.443 55.8983 239.217 55.8983C242.598 46.8793 250.206 47.928 255.7 45.6208C261.406 43.1039 267.534 41.6357 274.93 39.1187C257.39 27.373 239.639 20.0319 220.832 15.4175C151.941 -1.78164 88.544 12.481 30.0078 50.6547C22.4002 55.6886 15.4265 61.5615 8.03027 66.8051C5.70572 67.8538 3.59247 68.9026 -3.10351e-06 71Z">
                  </path>
                </g>
              </svg>
              <p class="font-base italic text-base text-base-content-secondary">Built <u
                   class="select-all text-blue-700">indielaunch.ch</u> &amp;<!-- --> <u
                   class="select-all text-blue-700">mrrbanner.com</u></p>
            </div>
            <div class="w-full relative mx-auto hidden lg:block"><img alt="Macbook"
                   loading="lazy"
                   width="687"
                   height="453"
                   decoding="async"
                   data-nimg="1"
                   class="w-full "
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmacbook.8ed77199.png&amp;w=750&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmacbook.8ed77199.png&amp;w=1920&amp;q=100 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmacbook.8ed77199.png&amp;w=1920&amp;q=100">
              <div
                   class="absolute right-[95px] left-[93px] top-[10px] bottom-[118px] flex items-center justify-center bg-primary rounded-t-[1.3rem] rounded-b-lg shadow-inner">
                <img alt="[object Object] screenshot"
                     loading="lazy"
                     width="800"
                     height="450"
                     decoding="async"
                     data-nimg="1"
                     class="w-full"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Findielaunch.4c5f03b8.gif&amp;w=828&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Findielaunch.4c5f03b8.gif&amp;w=1920&amp;q=100 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Findielaunch.4c5f03b8.gif&amp;w=1920&amp;q=100">
              </div>
            </div>
            <div class="w-full relative mx-auto lg:hidden"><img alt="[object Object] screenshot"
                   loading="lazy"
                   width="800"
                   height="450"
                   decoding="async"
                   data-nimg="1"
                   class="w-full border shadow"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Findielaunch.4c5f03b8.gif&amp;w=828&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Findielaunch.4c5f03b8.gif&amp;w=1920&amp;q=100 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Findielaunch.4c5f03b8.gif&amp;w=1920&amp;q=100">
            </div>
          </div>
        </div>
      </section>
      <section class="relative flex flex-col items-center gap-8 py-32 max-w-full w-full overflow-hidden text-center ">
        <p class="text-xl md:text-2xl"><span class="italic">Hey it's Marc, your teacher</span> 👋</p>
        <div class="pt-8"><span data-br=":R2fau9la:"
                data-brr="1"
                style="display:inline-block;vertical-align:top;text-decoration:inherit;text-wrap:balance">
            <ul class="space-y-8 text-lg md:text-xl max-w-3xl mx-auto text-center px-4">
              <li><img alt="Students learning to code"
                     loading="lazy"
                     width="800"
                     height="603"
                     decoding="async"
                     data-nimg="1"
                     class="w-full max-w-md mx-auto border shadow"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmarchello.3163f56a.gif&amp;w=828&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmarchello.3163f56a.gif&amp;w=1920&amp;q=100 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmarchello.3163f56a.gif&amp;w=1920&amp;q=100">
              </li>
              <li class="italic">I was fired from school and university.</li>
              <li>The way teachers taught was boring and impractical, so I didn’t care.</li>
              <li>In 2016, I faced the same issue trying to learn coding.<!-- --> <span class="font-semibold">Courses
                  were too long and made for people who want to get a job.</span> <span class="italic">I almost gave
                  up... </span> 😔</li>
              <li>So I skipped the theory, built tiny apps, and made my first $1,000 online with a few lines of
                code.<!-- --> </li>
              <li><img alt="Stripe dashboard"
                     loading="lazy"
                     width="848"
                     height="660"
                     decoding="async"
                     data-nimg="1"
                     class="w-full max-w-md mx-auto border shadow"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fvirallybot.674acc12.jpg&amp;w=1080&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fvirallybot.674acc12.jpg&amp;w=1920&amp;q=100 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fvirallybot.674acc12.jpg&amp;w=1920&amp;q=100">
              </li>
              <li><span class="font-semibold">I realized I didn’t need to know everything to create something
                  useful.</span></li>
              <li>Over time, I built<!-- --> <a href="https://marclou.com"
                   class=" link link-hover bg-accent-orange px-0.5"
                   target="_blank">25 tiny businesses</a> <!-- -->and earned over <span
                      class="font-semibold">$200,000</span> <!-- -->with my SaaS. Now, coding is even easier—AI writes
                and fixes code for you.</li>
              <li class="font-semibold">So I built CodeFast, the course<!-- --> <span class="font-semibold">I wish I
                  had</span> when I started:</li>
              <li>
                <ul class="list list-decimal max-w-[260px]  list-inside mx-auto text-left">
                  <li class="list-item">Short lessons</li>
                  <li class="list-item">Skip the fluff</li>
                  <li class="list-item">Build real businesses</li>
                </ul>
              </li>
              <li>This course took <span class="font-semibold">9 months</span> <!-- -->and was shaped by<!-- --> <span
                      class="font-semibold">50 students</span> in a beta. Some even<!-- --> <span
                      class="font-semibold">made money with their 1st app!</span> <!-- -->I hope you like it too.</li>
              <li><img alt="Students learning to code"
                     loading="lazy"
                     width="600"
                     height="600"
                     decoding="async"
                     data-nimg="1"
                     class="w-full max-w-md mx-auto border shadow"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdiscord-gif.0ce5ad99.gif&amp;w=640&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdiscord-gif.0ce5ad99.gif&amp;w=1200&amp;q=100 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fdiscord-gif.0ce5ad99.gif&amp;w=1200&amp;q=100">
              </li>
            </ul>
          </span>
          <script>self.__wrap_n = self.__wrap_n || (self.CSS && CSS.supports("text-wrap", "balance") ? 1 : 2); self.__wrap_b = (e, t, a) => { let s = (a = a || document.querySelector(`[data-br="${e}"]`)).parentElement, r = e => a.style.maxWidth = e + "px"; a.style.maxWidth = ""; let l = s.clientWidth, i = s.clientHeight, n = l / 2 - .25, c = l + .5, o; if (l) { for (r(n), n = Math.max(a.scrollWidth, n); n + 1 < c;)r(o = Math.round((n + c) / 2)), s.clientHeight === i ? c = o : n = o; r(c * t + l * (1 - t)) } a.__wrap_o || "undefined" != typeof ResizeObserver && (a.__wrap_o = new ResizeObserver(() => { self.__wrap_b(0, +a.dataset.brr, a) })).observe(s) }; self.__wrap_n != 1 && self.__wrap_b(":R2fau9la:", 1)</script>
        </div>
      </section>
      <section class="md:border-x ">
        <div data-theme="">
          <div class="py-24 md:py-36 flex flex-col items-center justify-center max-w-3xl mx-auto">
            <div class="flex flex-col md:flex-row justify-start items-start gap-8  max-md:px-8"><img alt="Andrei Bogdan"
                   loading="lazy"
                   width="220"
                   height="220"
                   decoding="async"
                   data-nimg="1"
                   class="!aspect-square object-cover max-w-44 md:max-w-52 bg-accent-blue border shadow shrink-0"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fandrei_transparent.856ee102.png&amp;w=256&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fandrei_transparent.856ee102.png&amp;w=640&amp;q=100 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fandrei_transparent.856ee102.png&amp;w=640&amp;q=100">
              <div class="relative flex flex-col justify-center items-start">
                <div class="mb-3"><svg xmlns="http://www.w3.org/2000/svg"
                       viewBox="0 0 24 24"
                       class="size-6 md:size-8">
                    <path d="M8.5 13.5C8.5 16.2614 6.26142 18.5 3.5 18.5C2.94772 18.5 2.5 18.9477 2.5 19.5C2.5 20.0523 2.94772 20.5 3.5 20.5C7.36599 20.5 10.5 17.366 10.5 13.5V5.75C10.5 4.23122 9.26878 3 7.75 3H3.75C2.23122 3 1 4.23122 1 5.75V9.75C1 11.2688 2.23122 12.5 3.75 12.5H7.75C8.01001 12.5 8.26158 12.4639 8.5 12.3965V13.5Z"
                          fill="currentColor"></path>
                    <path d="M20.5 13.5C20.5 16.2614 18.2614 18.5 15.5 18.5C14.9477 18.5 14.5 18.9477 14.5 19.5C14.5 20.0523 14.9477 20.5 15.5 20.5C19.366 20.5 22.5 17.366 22.5 13.5V5.75C22.5 4.23122 21.2688 3 19.75 3H15.75C14.2312 3 13 4.23122 13 5.75V9.75C13 11.2688 14.2312 12.5 15.75 12.5H19.75C20.01 12.5 20.2616 12.4639 20.5 12.3965V13.5Z"
                          fill="currentColor"></path>
                  </svg></div>
                <div class="text-base max-md:font-semibold  md:text-xl md:font-semibold mb-4 !leading-relaxed">I built
                  my app in almost a week. I shared on all social, and I guess people liked it. I got<!-- --> <span
                        class="text-yellow-800 bg-yellow-100 px-1">140 new users and 5 paying users ($162.5)</span>
                </div>
                <div class="italic md:text-lg">—<!-- -->Andrei Bogdan</div>
              </div>
            </div>
            <div class="py-8 flex flex-row justify-center items-center -gap-4 max-md:px-8"><svg
                   class="w-14 rotate-90 fill-base-content-secondary"
                   viewBox="0 0 296 71"
                   fill="none"
                   xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_3_228)">
                  <path
                        d="M-3.10351e-06 71C13.102 44.3623 83.0496 8.91537 132.922 2.62301C186.809 -4.29859 217.873 1.57428 281.27 31.1484C280.425 26.9535 279.368 23.5976 278.945 20.4514C278.311 16.8857 277.677 13.1103 277.889 9.54461C277.889 8.49589 279.791 6.60818 281.059 6.39843C282.327 6.18869 284.44 7.6569 284.862 8.70563C288.666 18.7734 292.047 28.8412 295.64 39.1187C296.696 42.4746 295.428 44.9916 291.836 46.0403C278.1 49.3962 264.364 52.9619 250.417 56.1081C247.247 56.7373 243.443 55.8983 239.217 55.8983C242.598 46.8793 250.206 47.928 255.7 45.6208C261.406 43.1039 267.534 41.6357 274.93 39.1187C257.39 27.373 239.639 20.0319 220.832 15.4175C151.941 -1.78164 88.544 12.481 30.0078 50.6547C22.4002 55.6886 15.4265 61.5615 8.03027 66.8051C5.70572 67.8538 3.59247 68.9026 -3.10351e-06 71Z">
                  </path>
                </g>
              </svg>
              <p class="font-base italic text-base text-base-content-secondary">Built <u
                   class="select-all text-blue-700">landingpro.ai</u>,<!-- --> <u
                   class="select-all text-blue-700">uireplicator.com</u> &amp;<!-- --> <u
                   class="select-all text-blue-700">instantseoaudit.com</u></p>
            </div>
            <div class="w-full relative mx-auto hidden lg:block"><img alt="Macbook"
                   loading="lazy"
                   width="687"
                   height="453"
                   decoding="async"
                   data-nimg="1"
                   class="w-full "
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmacbook.8ed77199.png&amp;w=750&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmacbook.8ed77199.png&amp;w=1920&amp;q=100 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmacbook.8ed77199.png&amp;w=1920&amp;q=100">
              <div
                   class="absolute right-[95px] left-[93px] top-[10px] bottom-[118px] flex items-center justify-center bg-primary rounded-t-[1.3rem] rounded-b-lg shadow-inner">
                <img alt="[object Object] screenshot"
                     loading="lazy"
                     width="800"
                     height="450"
                     decoding="async"
                     data-nimg="1"
                     class="w-full"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fuireplicator.3c381e2f.gif&amp;w=828&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fuireplicator.3c381e2f.gif&amp;w=1920&amp;q=100 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fuireplicator.3c381e2f.gif&amp;w=1920&amp;q=100">
              </div>
            </div>
            <div class="w-full relative mx-auto lg:hidden"><img alt="[object Object] screenshot"
                   loading="lazy"
                   width="800"
                   height="450"
                   decoding="async"
                   data-nimg="1"
                   class="w-full border shadow"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fuireplicator.3c381e2f.gif&amp;w=828&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fuireplicator.3c381e2f.gif&amp;w=1920&amp;q=100 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fuireplicator.3c381e2f.gif&amp;w=1920&amp;q=100">
            </div>
          </div>
        </div>
      </section>
      <section class="py-32 text-center space-y-16 ">
        <h2 class="text-4xl lg:text-5xl font-extrabold px-8">WARNING: You'll hate CodeFast if...</h2>
        <div class="relative w-full overflow-hidden group">
          <div class="absolute left-4 top-1/2 -translate-y-1/2 z-10 cursor-pointer">
            <div class="bg-base-100 p-2 shadow border transition-opacity"><svg xmlns="http://www.w3.org/2000/svg"
                   fill="none"
                   viewBox="0 0 24 24"
                   stroke-width="2.5"
                   stroke="currentColor"
                   class="w-6 h-6">
                <path stroke-linecap="round"
                      stroke-linejoin="round"
                      d="M15.75 19.5 8.25 12l7.5-7.5"></path>
              </svg></div>
          </div>
          <div class="absolute right-4 top-1/2 -translate-y-1/2 z-10 cursor-pointer">
            <div class="bg-base-100 p-2 shadow border transition-opacity"><svg xmlns="http://www.w3.org/2000/svg"
                   fill="none"
                   viewBox="0 0 24 24"
                   stroke-width="2.5"
                   stroke="currentColor"
                   class="w-6 h-6">
                <path stroke-linecap="round"
                      stroke-linejoin="round"
                      d="m8.25 4.5 7.5 7.5-7.5 7.5"></path>
              </svg></div>
          </div>
          <div class="flex overflow-x-auto snap-x snap-mandatory border-y">
            <div
                 class="bg-accent-blue border-r flex-shrink-0 w-[300px] rounded-none p-6 snap-center flex flex-col items-center gap-4">
              <h3 class="font-bold text-xl">You love watching 1,000 hours of video</h3><img
                   alt="You love watching 1,000 hours of video"
                   loading="lazy"
                   width="768"
                   height="768"
                   decoding="async"
                   data-nimg="1"
                   class="size-44"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbooks-.3539f55b.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbooks-.3539f55b.png&amp;w=1920&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbooks-.3539f55b.png&amp;w=1920&amp;q=75">
              <p class="text-sm text-base-content-secondary">CodeFast has<!-- --> <span
                      class="text-base-content font-medium">12 hours 26 minutes</span> <!-- -->of content, I respect
                your time.</p>
            </div>
            <div
                 class="bg-accent-orange border-r flex-shrink-0 w-[300px] rounded-none p-6 snap-center flex flex-col items-center gap-4">
              <h3 class="font-bold text-xl">You think you're not smart enough to code</h3><img
                   alt="You think you're not smart enough to code"
                   loading="lazy"
                   width="768"
                   height="768"
                   decoding="async"
                   data-nimg="1"
                   class="size-44"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fangry-teacher.3474e30d.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fangry-teacher.3474e30d.png&amp;w=1920&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fangry-teacher.3474e30d.png&amp;w=1920&amp;q=75">
              <p class="text-sm text-base-content-secondary">You'll be shocked to see how regular people build amazing
                apps.</p>
            </div>
            <div
                 class="bg-accent-green border-r flex-shrink-0 w-[300px] rounded-none p-6 snap-center flex flex-col items-center gap-4">
              <h3 class="font-bold text-xl">You like to memorize useless stuff</h3><img
                   alt="You like to memorize useless stuff"
                   loading="lazy"
                   width="768"
                   height="768"
                   decoding="async"
                   data-nimg="1"
                   class="size-44"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fvictorian-coronel-old-man-coronel-victorian-old-era.f0e7d079.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fvictorian-coronel-old-man-coronel-victorian-old-era.f0e7d079.png&amp;w=1920&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fvictorian-coronel-old-man-coronel-victorian-old-era.f0e7d079.png&amp;w=1920&amp;q=75">
              <p class="text-sm text-base-content-secondary">If knowing HTML was born in 1993 makes your heart race,
                sorry. We'll focus on the essentials.</p>
            </div>
            <div
                 class="bg-accent-blue border-r flex-shrink-0 w-[300px] rounded-none p-6 snap-center flex flex-col items-center gap-4">
              <h3 class="font-bold text-xl">You think AI will replace coders tomorrow</h3><img
                   alt="You think AI will replace coders tomorrow"
                   loading="lazy"
                   width="768"
                   height="768"
                   decoding="async"
                   data-nimg="1"
                   class="size-44"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbroken-robot.b55837aa.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbroken-robot.b55837aa.png&amp;w=1920&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbroken-robot.b55837aa.png&amp;w=1920&amp;q=75">
              <p class="text-sm text-base-content-secondary">Build apps with AI without knowing how to code is like
                buying a Ferrari without knowing how to drive.</p>
            </div>
            <div
                 class="bg-accent-green border-r flex-shrink-0 w-[300px] rounded-none p-6 snap-center flex flex-col items-center gap-4">
              <h3 class="font-bold text-xl">Or you're not ready to trust AI</h3><img
                   alt="Or you're not ready to trust AI"
                   loading="lazy"
                   width="1000"
                   height="1000"
                   decoding="async"
                   data-nimg="1"
                   class="size-44"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fai-not.78628b30.png&amp;w=1080&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fai-not.78628b30.png&amp;w=2048&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fai-not.78628b30.png&amp;w=2048&amp;q=75">
              <p class="text-sm text-base-content-secondary">If asking AI to code feels like cheating, you're missing
                out on the fastest way to launch your business.</p>
            </div>
            <div
                 class="bg-accent-pink border-r flex-shrink-0 w-[300px] rounded-none p-6 snap-center flex flex-col items-center gap-4">
              <h3 class="font-bold text-xl">You think coding takes years to master</h3><img
                   alt="You think coding takes years to master"
                   loading="lazy"
                   width="768"
                   height="768"
                   decoding="async"
                   data-nimg="1"
                   class="size-44"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcalendar.b9fda576.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcalendar.b9fda576.png&amp;w=1920&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fcalendar.b9fda576.png&amp;w=1920&amp;q=75">
              <p class="text-sm text-base-content-secondary">If you think you need a Computer Science degree to write
                code, you'll be disappointed to learn how easy it is.</p>
            </div>
            <div
                 class="bg-accent-orange border-r flex-shrink-0 w-[300px] rounded-none p-6 snap-center flex flex-col items-center gap-4">
              <h3 class="font-bold text-xl">You think the perfect tech stack matters</h3><img
                   alt="You think the perfect tech stack matters"
                   loading="lazy"
                   width="768"
                   height="768"
                   decoding="async"
                   data-nimg="1"
                   class="size-44"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fweight.667b3274.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fweight.667b3274.png&amp;w=1920&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fweight.667b3274.png&amp;w=1920&amp;q=75">
              <p class="text-sm text-base-content-secondary">If debating Kubernetes vs. Docker is your thing, have fun.
                We'll just pick what works and launch in days.</p>
            </div>
            <div
                 class="bg-accent-blue border-r flex-shrink-0 w-[300px] rounded-none p-6 snap-center flex flex-col items-center gap-4">
              <h3 class="font-bold text-xl">You want to be a software engineer</h3><img
                   alt="You want to be a software engineer"
                   loading="lazy"
                   width="768"
                   height="768"
                   decoding="async"
                   data-nimg="1"
                   class="size-44"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Foffice-cubicle.1be70c20.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Foffice-cubicle.1be70c20.png&amp;w=1920&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Foffice-cubicle.1be70c20.png&amp;w=1920&amp;q=75">
              <p class="text-sm text-base-content-secondary">If your dream is a desk job, I can't help you. I teach
                entrepreneurs how to quit their 9-5 jobs.</p>
            </div>
            <div
                 class="bg-accent-pink border-r flex-shrink-0 w-[300px] rounded-none p-6 snap-center flex flex-col items-center gap-4">
              <h3 class="font-bold text-xl">You prefer learning over doing</h3><img alt="You prefer learning over doing"
                   loading="lazy"
                   width="768"
                   height="768"
                   decoding="async"
                   data-nimg="1"
                   class="size-44"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbinge-watching.f285707e.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbinge-watching.f285707e.png&amp;w=1920&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fbinge-watching.f285707e.png&amp;w=1920&amp;q=75">
              <p class="text-sm text-base-content-secondary">If passively following course lectures excites you more
                than building your own SaaS, this isn't for you.</p>
            </div>
            <div
                 class="bg-accent-green border-r flex-shrink-0 w-[300px] rounded-none p-6 snap-center flex flex-col items-center gap-4">
              <h3 class="font-bold text-xl">You need a certificate to feel accomplished</h3><img
                   alt="You need a certificate to feel accomplished"
                   loading="lazy"
                   width="768"
                   height="768"
                   decoding="async"
                   data-nimg="1"
                   class="size-44"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fred-certificate.b6bcd544.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fred-certificate.b6bcd544.png&amp;w=1920&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fred-certificate.b6bcd544.png&amp;w=1920&amp;q=75">
              <p class="text-sm text-base-content-secondary">If a shiny certificate is your goal, sorry—I'd rather help
                you launch a real SaaS app instead.</p>
            </div>
            <div
                 class="bg-accent-blue border-r flex-shrink-0 w-[300px] rounded-none p-6 snap-center flex flex-col items-center gap-4">
              <h3 class="font-bold text-xl">You think every line of code needs to be perfect</h3><img
                   alt="You think every line of code needs to be perfect"
                   loading="lazy"
                   width="768"
                   height="768"
                   decoding="async"
                   data-nimg="1"
                   class="size-44"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fruler.50ab0a30.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fruler.50ab0a30.png&amp;w=1920&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fruler.50ab0a30.png&amp;w=1920&amp;q=75">
              <p class="text-sm text-base-content-secondary">If spending days debugging one semicolon excites you, this
                isn't your vibe. You'll ship, iterate, and move on.</p>
            </div>
          </div>
        </div>
        <div class="space-y-4 mt-16 px-8"><a
             class="btn btn-primary group btn-hover-green md:btn-lg !text-lg btn-block md:btn-wide"
             href="/#pricing">Nevermind, I'm ready to code fast</a>
          <div class="flex text-base-content-secondary items-center justify-center gap-2">
            <p class="italic text-center"><span class="font-semibold text-base-content">277</span> <!-- -->entrepreneurs
              love the course</p>
          </div>
        </div>
      </section>
      <section class="md:border-x ">
        <div data-theme="">
          <div class="py-24 md:py-36 flex flex-col items-center justify-center max-w-3xl mx-auto">
            <div class="flex flex-col md:flex-row justify-start items-start gap-8  max-md:px-8"><img alt="Juan"
                   loading="lazy"
                   width="220"
                   height="220"
                   decoding="async"
                   data-nimg="1"
                   class="!aspect-square object-cover max-w-44 md:max-w-52 bg-accent-orange border shadow shrink-0"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fjuan_transparent.3a8e808c.png&amp;w=256&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fjuan_transparent.3a8e808c.png&amp;w=640&amp;q=100 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fjuan_transparent.3a8e808c.png&amp;w=640&amp;q=100">
              <div class="relative flex flex-col justify-center items-start">
                <div class="mb-3"><svg xmlns="http://www.w3.org/2000/svg"
                       viewBox="0 0 24 24"
                       class="size-6 md:size-8">
                    <path d="M8.5 13.5C8.5 16.2614 6.26142 18.5 3.5 18.5C2.94772 18.5 2.5 18.9477 2.5 19.5C2.5 20.0523 2.94772 20.5 3.5 20.5C7.36599 20.5 10.5 17.366 10.5 13.5V5.75C10.5 4.23122 9.26878 3 7.75 3H3.75C2.23122 3 1 4.23122 1 5.75V9.75C1 11.2688 2.23122 12.5 3.75 12.5H7.75C8.01001 12.5 8.26158 12.4639 8.5 12.3965V13.5Z"
                          fill="currentColor"></path>
                    <path d="M20.5 13.5C20.5 16.2614 18.2614 18.5 15.5 18.5C14.9477 18.5 14.5 18.9477 14.5 19.5C14.5 20.0523 14.9477 20.5 15.5 20.5C19.366 20.5 22.5 17.366 22.5 13.5V5.75C22.5 4.23122 21.2688 3 19.75 3H15.75C14.2312 3 13 4.23122 13 5.75V9.75C13 11.2688 14.2312 12.5 15.75 12.5H19.75C20.01 12.5 20.2616 12.4639 20.5 12.3965V13.5Z"
                          fill="currentColor"></path>
                  </svg></div>
                <div class="text-base max-md:font-semibold  md:text-xl md:font-semibold mb-4 !leading-relaxed">I finally
                  created my first (coded 100% by myself) startup. I love CodeFast, I could never find something<!-- -->
                  <span class="text-yellow-800 bg-yellow-100 px-1">so straight to the point and easy to learn.</span>
                </div>
                <div class="italic md:text-lg">—<!-- -->Juan Belmont</div>
              </div>
            </div>
            <div class="py-8 flex flex-row justify-center items-center -gap-4 max-md:px-8"><svg
                   class="w-14 rotate-90 fill-base-content-secondary"
                   viewBox="0 0 296 71"
                   fill="none"
                   xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_3_228)">
                  <path
                        d="M-3.10351e-06 71C13.102 44.3623 83.0496 8.91537 132.922 2.62301C186.809 -4.29859 217.873 1.57428 281.27 31.1484C280.425 26.9535 279.368 23.5976 278.945 20.4514C278.311 16.8857 277.677 13.1103 277.889 9.54461C277.889 8.49589 279.791 6.60818 281.059 6.39843C282.327 6.18869 284.44 7.6569 284.862 8.70563C288.666 18.7734 292.047 28.8412 295.64 39.1187C296.696 42.4746 295.428 44.9916 291.836 46.0403C278.1 49.3962 264.364 52.9619 250.417 56.1081C247.247 56.7373 243.443 55.8983 239.217 55.8983C242.598 46.8793 250.206 47.928 255.7 45.6208C261.406 43.1039 267.534 41.6357 274.93 39.1187C257.39 27.373 239.639 20.0319 220.832 15.4175C151.941 -1.78164 88.544 12.481 30.0078 50.6547C22.4002 55.6886 15.4265 61.5615 8.03027 66.8051C5.70572 67.8538 3.59247 68.9026 -3.10351e-06 71Z">
                  </path>
                </g>
              </svg>
              <p class="font-base italic text-base text-base-content-secondary">Built <u
                   class="select-all text-blue-700">nerdmask.com</u></p>
            </div>
            <div class="w-full relative mx-auto hidden lg:block"><img alt="Macbook"
                   loading="lazy"
                   width="687"
                   height="453"
                   decoding="async"
                   data-nimg="1"
                   class="w-full "
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmacbook.8ed77199.png&amp;w=750&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmacbook.8ed77199.png&amp;w=1920&amp;q=100 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fmacbook.8ed77199.png&amp;w=1920&amp;q=100">
              <div
                   class="absolute right-[95px] left-[93px] top-[10px] bottom-[118px] flex items-center justify-center bg-primary rounded-t-[1.3rem] rounded-b-lg shadow-inner">
                <img alt="[object Object] screenshot"
                     loading="lazy"
                     width="800"
                     height="450"
                     decoding="async"
                     data-nimg="1"
                     class="w-full"
                     style="color:transparent"
                     srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnerdmask.0852e602.gif&amp;w=828&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnerdmask.0852e602.gif&amp;w=1920&amp;q=100 2x"
                     src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnerdmask.0852e602.gif&amp;w=1920&amp;q=100">
              </div>
            </div>
            <div class="w-full relative mx-auto lg:hidden"><img alt="[object Object] screenshot"
                   loading="lazy"
                   width="800"
                   height="450"
                   decoding="async"
                   data-nimg="1"
                   class="w-full border shadow"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnerdmask.0852e602.gif&amp;w=828&amp;q=100 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnerdmask.0852e602.gif&amp;w=1920&amp;q=100 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fnerdmask.0852e602.gif&amp;w=1920&amp;q=100"></div>
          </div>
        </div>
      </section>
      <section class="overflow-hidden"
               id="pricing">
        <div class="pb-12 pt-32 md:pt-48 px-8 max-w-5xl mx-auto">
          <div class="mb-12">
            <div class="max-w-lg lg:max-w-5xl mx-auto">
              <div class="p-4 shadow-xl border flex flex-col md:flex-row items-center justify-between gap-4">
                <div class="flex flex-col lg:flex-row items-center sm:items-start lg:items-center gap-2.5 lg:gap-4">
                  <div class="text-yellow-400 text-xl md:text-2xl font-bold flex items-center gap-1.5"><span
                          class="animate-pulse text-2xl md:text-3xl">⚡</span> BLACK FRIDAY</div>
                  <div class="text-lg md:text-xl"><span class="font-bold">50% OFF</span> — Limited time offer</div>
                </div>
                <div class="flex gap-4 items-center">
                  <div class="flex gap-2 text-sm md:text-base">
                    <div class="flex flex-col items-center">
                      <div
                           class="bg-base-content text-xl text-base-100 font-mono font-bold px-3 py-2 min-w-[2.5rem] text-center">
                        <number-flow-react aria-label="1"
                                           role="img"
                                           data="{&quot;pre&quot;:[],&quot;integer&quot;:[{&quot;type&quot;:&quot;integer&quot;,&quot;value&quot;:1,&quot;key&quot;:&quot;integer:0&quot;,&quot;place&quot;:0}],&quot;fraction&quot;:[],&quot;post&quot;:[],&quot;valueAsString&quot;:&quot;1&quot;,&quot;value&quot;:1}"></number-flow-react>
                      </div>
                      <div class="text-xs mt-1 text-base-content-secondary">days</div>
                    </div>
                    <div class="flex flex-col items-center">
                      <div
                           class="bg-base-content text-xl text-base-100 font-mono font-bold px-3 py-2 min-w-[2.5rem] text-center">
                        <number-flow-react aria-label="5"
                                           role="img"
                                           data="{&quot;pre&quot;:[],&quot;integer&quot;:[{&quot;type&quot;:&quot;integer&quot;,&quot;value&quot;:5,&quot;key&quot;:&quot;integer:0&quot;,&quot;place&quot;:0}],&quot;fraction&quot;:[],&quot;post&quot;:[],&quot;valueAsString&quot;:&quot;5&quot;,&quot;value&quot;:5}"></number-flow-react>
                      </div>
                      <div class="text-xs mt-1 text-base-content-secondary">hours</div>
                    </div>
                    <div class="flex flex-col items-center">
                      <div
                           class="bg-base-content text-xl text-base-100 font-mono font-bold px-3 py-2 min-w-[2.5rem] text-center">
                        <number-flow-react aria-label="50"
                                           role="img"
                                           data="{&quot;pre&quot;:[],&quot;integer&quot;:[{&quot;type&quot;:&quot;integer&quot;,&quot;value&quot;:5,&quot;key&quot;:&quot;integer:1&quot;,&quot;place&quot;:1},{&quot;type&quot;:&quot;integer&quot;,&quot;value&quot;:0,&quot;key&quot;:&quot;integer:0&quot;,&quot;place&quot;:0}],&quot;fraction&quot;:[],&quot;post&quot;:[],&quot;valueAsString&quot;:&quot;50&quot;,&quot;value&quot;:50}"></number-flow-react>
                      </div>
                      <div class="text-xs mt-1 text-base-content-secondary">minutes</div>
                    </div>
                    <div class="flex flex-col items-center">
                      <div
                           class="bg-base-content text-xl text-base-100 font-mono font-bold px-3 py-2 min-w-[2.5rem] text-center">
                        <number-flow-react aria-label="38"
                                           role="img"
                                           data="{&quot;pre&quot;:[],&quot;integer&quot;:[{&quot;type&quot;:&quot;integer&quot;,&quot;value&quot;:3,&quot;key&quot;:&quot;integer:1&quot;,&quot;place&quot;:1},{&quot;type&quot;:&quot;integer&quot;,&quot;value&quot;:8,&quot;key&quot;:&quot;integer:0&quot;,&quot;place&quot;:0}],&quot;fraction&quot;:[],&quot;post&quot;:[],&quot;valueAsString&quot;:&quot;38&quot;,&quot;value&quot;:38}"></number-flow-react>
                      </div>
                      <div class="text-xs mt-1 text-base-content-secondary">seconds</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="flex flex-col text-center w-full mb-20">
            <h2 class="text-4xl md:text-6xl font-black leading-tight"><span data-br=":R2nau9la:"
                    data-brr="1"
                    style="display:inline-block;vertical-align:top;text-decoration:inherit;text-wrap:balance">Code your
                idea fast, build your<!-- --> <span class="inline-block decoration-primary relative "><span
                        class="relative z-10">freedom</span><span
                        class="bottom-0  absolute bg-accent-blue h-4 md:h-6 md:-bottom-0.5 -inset-x-2 "></span></span></span>
              <script>self.__wrap_n = self.__wrap_n || (self.CSS && CSS.supports("text-wrap", "balance") ? 1 : 2); self.__wrap_b = (e, t, a) => { let s = (a = a || document.querySelector(`[data-br="${e}"]`)).parentElement, r = e => a.style.maxWidth = e + "px"; a.style.maxWidth = ""; let l = s.clientWidth, i = s.clientHeight, n = l / 2 - .25, c = l + .5, o; if (l) { for (r(n), n = Math.max(a.scrollWidth, n); n + 1 < c;)r(o = Math.round((n + c) / 2)), s.clientHeight === i ? c = o : n = o; r(c * t + l * (1 - t)) } a.__wrap_o || "undefined" != typeof ResizeObserver && (a.__wrap_o = new ResizeObserver(() => { self.__wrap_b(0, +a.dataset.brr, a) })).observe(s) }; self.__wrap_n != 1 && self.__wrap_b(":R2nau9la:", 1)</script>
            </h2>
          </div>
          <div class="relative flex justify-center flex-col lg:flex-row items-center lg:items-center max-lg:gap-8">
            <div class="relative w-full max-w-lg lg:max-w-none"
                 data-theme="light">
              <div class="relative flex flex-col h-full gap-5 lg:gap-8 z-10 bg-base-100 border lg:border-r-0 p-8">
                <div class="flex justify-between items-center gap-4">
                  <div>
                    <p class="text-lg lg:text-xl font-bold">CodeFast Course</p>
                    <p class="text-base-content-secondary mt-2">Learn to build your ideas, fast</p>
                  </div>
                </div>
                <div class="flex gap-2">
                  <div class="flex flex-col justify-end mb-[4px] text-lg ">
                    <p class="relative"><span
                            class="absolute bg-base-content h-[1.5px] inset-x-0 top-[53%]"></span><span
                            class="text-base-content-secondary">$<!-- -->299</span></p>
                  </div>
                  <p class="text-5xl tracking-tight font-extrabold">$<!-- -->149</p>
                  <div class="flex flex-col justify-end mb-[4px]">
                    <p class="text-xs text-base-content-secondary opacity-80 uppercase font-semibold">USD</p>
                  </div>
                </div>
                <ul class="space-y-3 text-base flex-1">
                  <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                         viewBox="0 0 20 20"
                         fill="currentColor"
                         class="w-[18px] h-[18px] opacity-80 shrink-0">
                      <path fill-rule="evenodd"
                            d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                            clip-rule="evenodd"></path>
                    </svg><span>12 hours of content (211 videos)<!-- --> </span></li>
                  <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                         viewBox="0 0 20 20"
                         fill="currentColor"
                         class="w-[18px] h-[18px] opacity-80 shrink-0">
                      <path fill-rule="evenodd"
                            d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                            clip-rule="evenodd"></path>
                    </svg><span>3 modules: The Mindset 💡, The Fundamentals 🏠, Your First SaaS 🏰<!-- --> </span></li>
                  <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                         viewBox="0 0 20 20"
                         fill="currentColor"
                         class="w-[18px] h-[18px] opacity-80 shrink-0">
                      <path fill-rule="evenodd"
                            d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                            clip-rule="evenodd"></path>
                    </svg><span>Private Discord community<!-- --> </span></li>
                  <li class="flex items-center gap-2"><svg xmlns="http://www.w3.org/2000/svg"
                         viewBox="0 0 20 20"
                         fill="currentColor"
                         class="w-[18px] h-[18px] opacity-80 shrink-0">
                      <path fill-rule="evenodd"
                            d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                            clip-rule="evenodd"></path>
                    </svg><span>Lifetime updates<!-- --> </span></li>
                </ul>
                <div class="space-y-2"><button class="btn btn-primary group btn-block btn-hover-blue">Learn to code,
                    now</button>
                  <p class="text-sm text-base-content-secondary text-center">Access forever (no subscription)</p>
                </div>
              </div>
            </div>
            <div class="relative w-full max-w-lg lg:max-w-none"
                 data-theme="dark">
              <div class="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 z-20 bg-transparent"><span
                      class="shadow badge badge-lg border border-base-content tracking-wide">BUNDLE</span></div>
              <div class="relative flex flex-col h-full gap-5 lg:gap-8 z-10 bg-base-100 p-8 lg:px-12 lg:py-20">
                <div class="flex justify-between items-center gap-4">
                  <div>
                    <p class="text-lg lg:text-xl font-bold">CodeFast Course <span class="text-yellow-400">+
                        ShipFast</span></p>
                    <p class="text-base-content-secondary mt-2">Ship your ideas even faster</p>
                  </div>
                </div>
                <div class="flex gap-2">
                  <div class="flex flex-col justify-end mb-[4px] text-lg ">
                    <p class="relative"><span
                            class="absolute bg-base-content h-[1.5px] inset-x-0 top-[53%]"></span><span
                            class="text-base-content-secondary">$<!-- -->648</span></p>
                  </div>
                  <p class="text-5xl tracking-tight font-extrabold">$<!-- -->299</p>
                  <div class="flex flex-col justify-end mb-[4px]">
                    <p class="text-xs text-base-content-secondary opacity-80 uppercase font-semibold">USD</p>
                  </div>
                </div>
                <div class="space-y-3 flex-1">
                  <div class=" flex items-center gap-2"><span class=""><svg xmlns="http://www.w3.org/2000/svg"
                           fill="none"
                           viewBox="0 0 24 24"
                           stroke-width="2"
                           stroke="currentColor"
                           class="size-4 rotate-90 md:rotate-0">
                        <path stroke-linecap="round"
                              stroke-linejoin="round"
                              d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18"></path>
                      </svg></span><span class="italic">Everything in the course, and...</span></div><a
                     href="https://shipfa.st?ref=codefast_pricing"
                     target="_blank"
                     class="group block p-4 space-y-2 border ssshadow border-yellow-400 shadow-yellow-500 bg-[#FDE04703] hover:bg-[#FDE04710] duration-200">
                    <div class="flex gap-2.5 items-center text-yellow-400"><img alt="ShipFast"
                           loading="lazy"
                           width="512"
                           height="512"
                           decoding="async"
                           data-nimg="1"
                           class="size-6 md:size-7 group-hover:scale-110 duration-200 group-hover:rotate-6"
                           style="color:transparent"
                           srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fshipfast-icon_transparent.2aa1e5a5.png&amp;w=640&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fshipfast-icon_transparent.2aa1e5a5.png&amp;w=1080&amp;q=75 2x"
                           src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fshipfast-icon_transparent.2aa1e5a5.png&amp;w=1080&amp;q=75"><span
                            class="md:text-lg font-semibold">ShipFast | All-in Tier ($349)</span></div>
                    <div class="leading-snug text-yellow-50">The codebase used by<!-- --> <span
                            class="link group-hover:text-yellow-400 duration-100">5,000+ developers</span> <!-- -->to
                      ship startups in days, not weeks</div>
                  </a>
                </div>
                <div class="space-y-2"><button class="btn btn-primary group btn-block btn-hover-blue">Get CodeFast +
                    ShipFast</button>
                  <p class="text-sm text-base-content-secondary text-center">Access forever (no subscription)</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section id="faq">
        <div class="py-20 px-8 max-w-7xl mx-auto ">
          <p class="text-3xl lg:text-4xl font-extrabold pb-16 text-center ">Frequently Asked Questions</p>
          <ul class="divide-y max-w-xl mx-auto">
            <li class=""><button
                      class="relative flex gap-2 items-center w-full py-6 text-base font-semibold text-left md:text-lg"
                      aria-expanded="false"><span class="flex-1 text-base-content ">What do I get exactly?</span><svg
                     class="flex-shrink-0 size-4 ml-auto fill-current"
                     viewBox="0 0 16 16"
                     xmlns="http://www.w3.org/2000/svg">
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center transition duration-200 ease-out false"></rect>
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center rotate-90 transition duration-200 ease-out false"></rect>
                </svg></button>
              <div class="transition-all duration-300 ease-in-out text-base-content-secondary overflow-hidden"
                   style="max-height:0;opacity:0">
                <div class="pb-6 leading-relaxed">
                  <div>You get lifetime access to the CodeFast course, which includes step-by-step lessons, real-world
                    projects (SaaS), and guidance to build and launch your own online business, even if you're a
                    complete beginner.</div>
                </div>
              </div>
            </li>
            <li class=""><button
                      class="relative flex gap-2 items-center w-full py-6 text-base font-semibold text-left md:text-lg"
                      aria-expanded="false"><span class="flex-1 text-base-content ">Who is this course for?</span><svg
                     class="flex-shrink-0 size-4 ml-auto fill-current"
                     viewBox="0 0 16 16"
                     xmlns="http://www.w3.org/2000/svg">
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center transition duration-200 ease-out false"></rect>
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center rotate-90 transition duration-200 ease-out false"></rect>
                </svg></button>
              <div class="transition-all duration-300 ease-in-out text-base-content-secondary overflow-hidden"
                   style="max-height:0;opacity:0">
                <div class="pb-6 leading-relaxed">
                  <div>This course is for anyone who wants to learn to code and build their own apps, whether you’re a
                    beginner or someone tired of long, theory-heavy courses made for the pre-AI world</div>
                </div>
              </div>
            </li>
            <li class=""><button
                      class="relative flex gap-2 items-center w-full py-6 text-base font-semibold text-left md:text-lg"
                      aria-expanded="false"><span class="flex-1 text-base-content ">Is there a refund policy?</span><svg
                     class="flex-shrink-0 size-4 ml-auto fill-current"
                     viewBox="0 0 16 16"
                     xmlns="http://www.w3.org/2000/svg">
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center transition duration-200 ease-out false"></rect>
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center rotate-90 transition duration-200 ease-out false"></rect>
                </svg></button>
              <div class="transition-all duration-300 ease-in-out text-base-content-secondary overflow-hidden"
                   style="max-height:0;opacity:0">
                <div class="pb-6 leading-relaxed">
                  <div>Yes, you can get a refund if you’ve completed less than 10% of the course. Just<!-- --> <a
                       class="link"
                       href="mailto:marc%40codefa.st?subject=I%20want%20a%20refund%20for%20CodeFast"
                       target="_blank">contact me</a> <!-- -->within 7 days of purchase.</div>
                </div>
              </div>
            </li>
            <li class=""><button
                      class="relative flex gap-2 items-center w-full py-6 text-base font-semibold text-left md:text-lg"
                      aria-expanded="false"><span class="flex-1 text-base-content ">What tech stack will I
                  learn?</span><svg class="flex-shrink-0 size-4 ml-auto fill-current"
                     viewBox="0 0 16 16"
                     xmlns="http://www.w3.org/2000/svg">
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center transition duration-200 ease-out false"></rect>
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center rotate-90 transition duration-200 ease-out false"></rect>
                </svg></button>
              <div class="transition-all duration-300 ease-in-out text-base-content-secondary overflow-hidden"
                   style="max-height:0;opacity:0">
                <div class="pb-6 leading-relaxed">
                  <div>Frontend: React, Next.js, Tailwind CSS (+ daisyUI)<br>Backend: Next.js API, Stripe for payments
                    (or Lemon Squeezy), Resend for emails, MongoDB for database</div>
                </div>
              </div>
            </li>
            <li class=""><button
                      class="relative flex gap-2 items-center w-full py-6 text-base font-semibold text-left md:text-lg"
                      aria-expanded="false"><span class="flex-1 text-base-content ">What kind of apps will I
                  build?</span><svg class="flex-shrink-0 size-4 ml-auto fill-current"
                     viewBox="0 0 16 16"
                     xmlns="http://www.w3.org/2000/svg">
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center transition duration-200 ease-out false"></rect>
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center rotate-90 transition duration-200 ease-out false"></rect>
                </svg></button>
              <div class="transition-all duration-300 ease-in-out text-base-content-secondary overflow-hidden"
                   style="max-height:0;opacity:0">
                <div class="pb-6 leading-relaxed">
                  <div>You’ll build practical projects like a YouTube clone, a SaaS app with payments, and a
                    live-deployed business-ready app.</div>
                </div>
              </div>
            </li>
            <li class=""><button
                      class="relative flex gap-2 items-center w-full py-6 text-base font-semibold text-left md:text-lg"
                      aria-expanded="false"><span class="flex-1 text-base-content ">What if I get stuck during the
                  course?</span><svg class="flex-shrink-0 size-4 ml-auto fill-current"
                     viewBox="0 0 16 16"
                     xmlns="http://www.w3.org/2000/svg">
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center transition duration-200 ease-out false"></rect>
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center rotate-90 transition duration-200 ease-out false"></rect>
                </svg></button>
              <div class="transition-all duration-300 ease-in-out text-base-content-secondary overflow-hidden"
                   style="max-height:0;opacity:0">
                <div class="pb-6 leading-relaxed">
                  <div>You’ll have access to a support community where you can ask questions and get help from others
                    taking the course. I'll also teach you how to use AI tools to debug your code easily.</div>
                </div>
              </div>
            </li>
            <li class=""><button
                      class="relative flex gap-2 items-center w-full py-6 text-base font-semibold text-left md:text-lg"
                      aria-expanded="false"><span class="flex-1 text-base-content ">Will I learn how to deploy my app
                  live?</span><svg class="flex-shrink-0 size-4 ml-auto fill-current"
                     viewBox="0 0 16 16"
                     xmlns="http://www.w3.org/2000/svg">
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center transition duration-200 ease-out false"></rect>
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center rotate-90 transition duration-200 ease-out false"></rect>
                </svg></button>
              <div class="transition-all duration-300 ease-in-out text-base-content-secondary overflow-hidden"
                   style="max-height:0;opacity:0">
                <div class="pb-6 leading-relaxed">
                  <div>Yes, the course includes lessons on deploying your app so it’s accessible to millions of users on
                    the internet!</div>
                </div>
              </div>
            </li>
            <li class=""><button
                      class="relative flex gap-2 items-center w-full py-6 text-base font-semibold text-left md:text-lg"
                      aria-expanded="false"><span class="flex-1 text-base-content ">What makes CodeFast different from
                  other courses?</span><svg class="flex-shrink-0 size-4 ml-auto fill-current"
                     viewBox="0 0 16 16"
                     xmlns="http://www.w3.org/2000/svg">
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center transition duration-200 ease-out false"></rect>
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center rotate-90 transition duration-200 ease-out false"></rect>
                </svg></button>
              <div class="transition-all duration-300 ease-in-out text-base-content-secondary overflow-hidden"
                   style="max-height:0;opacity:0">
                <div class="pb-6 leading-relaxed">
                  <div>CodeFast is designed to be short, practical, and focused on building apps quickly, unlike
                    traditional courses that are long, theory-heavy for the pre-AI world.</div>
                </div>
              </div>
            </li>
            <li class=""><button
                      class="relative flex gap-2 items-center w-full py-6 text-base font-semibold text-left md:text-lg"
                      aria-expanded="false"><span class="flex-1 text-base-content ">Can I use PayPal?</span><svg
                     class="flex-shrink-0 size-4 ml-auto fill-current"
                     viewBox="0 0 16 16"
                     xmlns="http://www.w3.org/2000/svg">
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center transition duration-200 ease-out false"></rect>
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center rotate-90 transition duration-200 ease-out false"></rect>
                </svg></button>
              <div class="transition-all duration-300 ease-in-out text-base-content-secondary overflow-hidden"
                   style="max-height:0;opacity:0">
                <div class="pb-6 leading-relaxed">
                  <div class="space-y-2 leading-relaxed">
                    <p>Yes! You can send over the USD equivalent of the plan you want to purchase (<!-- -->$149 or
                      $299<!-- -->) to my PayPal account:<!-- --> <a href="https://www.paypal.com/paypalme/marclouvion"
                         target="_blank"
                         class="link link-primary">paypal.me/marclouvion</a></p>
                    <p>Once done, please<!-- --> <a
                         href="mailto:<EMAIL>?subject=PayPal%20payment%20for%20CodeFast"
                         target="_blank"
                         class="link link-primary">email me</a> <!-- -->with your purchase email so I can give you
                      access to the course. Please allow a few hours to get access.</p>
                    <p>135k people trust me on<!-- --> <a href="https://twitter.com/marc_louvion"
                         target="_blank"
                         class="link">Twitter</a>. I'm not running away with the money 😊</p>
                  </div>
                </div>
              </div>
            </li>
            <li class=""><button
                      class="relative flex gap-2 items-center w-full py-6 text-base font-semibold text-left md:text-lg"
                      aria-expanded="false"><span class="flex-1 text-base-content ">I have another question</span><svg
                     class="flex-shrink-0 size-4 ml-auto fill-current"
                     viewBox="0 0 16 16"
                     xmlns="http://www.w3.org/2000/svg">
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center transition duration-200 ease-out false"></rect>
                  <rect y="7"
                        width="16"
                        height="2"
                        rx="1"
                        class="transform origin-center rotate-90 transition duration-200 ease-out false"></rect>
                </svg></button>
              <div class="transition-all duration-300 ease-in-out text-base-content-secondary overflow-hidden"
                   style="max-height:0;opacity:0">
                <div class="pb-6 leading-relaxed">
                  <p>Cool, contact me by<!-- --> <a class="link"
                       href="mailto:marc%40codefa.st?subject=Need%20help%20with%20CodeFast"
                       target="_blank">email</a></p>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </section>
      <section class="max-md:border-b-0 my-24 md:my-32"
               id="testimonials">
        <div class="max-w-5xl mx-auto">
          <h3 class="text-center text-3xl md:text-5xl font-extrabold py-12 px-8"><span data-br=":R1rau9la:"
                  data-brr="1"
                  style="display:inline-block;vertical-align:top;text-decoration:inherit;text-wrap:balance">277<!-- -->
              students learned to code, fast</span>
            <script>self.__wrap_n = self.__wrap_n || (self.CSS && CSS.supports("text-wrap", "balance") ? 1 : 2); self.__wrap_b = (e, t, a) => { let s = (a = a || document.querySelector(`[data-br="${e}"]`)).parentElement, r = e => a.style.maxWidth = e + "px"; a.style.maxWidth = ""; let l = s.clientWidth, i = s.clientHeight, n = l / 2 - .25, c = l + .5, o; if (l) { for (r(n), n = Math.max(a.scrollWidth, n); n + 1 < c;)r(o = Math.round((n + c) / 2)), s.clientHeight === i ? c = o : n = o; r(c * t + l * (1 - t)) } a.__wrap_o || "undefined" != typeof ResizeObserver && (a.__wrap_o = new ResizeObserver(() => { self.__wrap_b(0, +a.dataset.brr, a) })).observe(s) }; self.__wrap_n != 1 && self.__wrap_b(":R1rau9la:", 1)</script>
          </h3>
          <div
               class="max-md:px-8 max-md:gap-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 md:border-t md:border-l overflow-hidden w-full max-w-full">
            <div class="bg-base-content max-md:border md:border-r border-b overflow-hidden   ">
              <div class="font-semibold p-4 relative z-10 border-b bg-accent-blue -mb-20">"<!-- -->I've never finished
                Udemy courses... Marc cuts the BS<!-- -->"</div>
              <div class="bg-base-content scale-[1.04]">
                <div class="next-video-container">
                  <style>
                    .next-video-container {
                      position: relative;
                      width: 100%;
                      aspect-ratio: 16 / 9;
                    }

                    [data-next-video] {
                      position: relative;
                      width: 100%;
                      height: 100%;
                      display: inline-block;
                      line-height: 0;
                    }

                    [data-next-video] img {
                      object-fit: var(--media-object-fit, contain);
                      object-position: var(--media-object-position, center);
                      background: center / var(--media-object-fit, contain) no-repeat transparent;
                      width: 100%;
                      height: 100%;
                    }
                  </style><mux-player player-software-name="mux-player-react"
                              player-software-version="2.4.0"
                              stream-type="on-demand"
                              default-hidden-captions=""
                              accent-color="#BDE0FF"
                              primary-color="#FFFFFF"
                              id="X6MCc7QxXHwNxTq012mITjIO01a02BigBkWnpWdwDAlLvo"
                              style="aspect-ratio:9/16"
                              playback-id="X6MCc7QxXHwNxTq012mITjIO01a02BigBkWnpWdwDAlLvo"></mux-player>
                  <style>
                    .next-video-alert {
                      position: absolute;
                      inset: 1em;
                      bottom: auto;
                      padding: .75rem 1rem;
                      border-radius: 1rem;
                      color: hsl(0, 0%, 100%);
                      background-color: hsl(240 10% 3.9% / .7);
                      border: 1px solid hsl(240 3.7% 15.9%);
                      transition: visibility 0s, opacity .25s;
                      visibility: visible;
                      opacity: 1;
                    }

                    .next-video-alert[hidden] {
                      display: block;
                      transition: visibility 1s, opacity 1s;
                      visibility: hidden;
                      opacity: 0;
                    }

                    .next-video-alert svg {
                      position: absolute;
                    }

                    .next-video-alert h5 {
                      line-height: 1;
                      font-weight: 500;
                      margin-bottom: 0.25rem;
                      padding-left: 1.75rem;
                      font-size: inherit;
                    }

                    .next-video-alert div {
                      padding-left: 1.75rem;
                      font-size: 0.875rem;
                      line-height: 1.25rem;
                    }
                  </style>
                  <div role="alert"
                       class="next-video-alert next-video-alert-undefined"
                       hidden=""><svg width="15"
                         height="15"
                         viewBox="0 0 15 15"
                         fill="none">
                      <path d="M8.4449 0.608765C8.0183 -0.107015 6.9817 -0.107015 6.55509 0.608766L0.161178 11.3368C-0.275824 12.07 0.252503 13 1.10608 13H13.8939C14.7475 13 15.2758 12.07 14.8388 11.3368L8.4449 0.608765ZM7.4141 1.12073C7.45288 1.05566 7.54712 1.05566 7.5859 1.12073L13.9798 11.8488C14.0196 11.9154 13.9715 12 13.8939 12H1.10608C1.02849 12 0.980454 11.9154 1.02018 11.8488L7.4141 1.12073ZM6.8269 4.48611C6.81221 4.10423 7.11783 3.78663 7.5 3.78663C7.88217 3.78663 8.18778 4.10423 8.1731 4.48612L8.01921 8.48701C8.00848 8.766 7.7792 8.98664 7.5 8.98664C7.2208 8.98664 6.99151 8.766 6.98078 8.48701L6.8269 4.48611ZM8.24989 10.476C8.24989 10.8902 7.9141 11.226 7.49989 11.226C7.08567 11.226 6.74989 10.8902 6.74989 10.476C6.74989 10.0618 7.08567 9.72599 7.49989 9.72599C7.9141 9.72599 8.24989 10.0618 8.24989 10.476Z"
                            fill="currentColor"
                            fill-rule="evenodd"
                            clip-rule="evenodd"></path>
                    </svg>
                    <h5>Upload in progress...</h5>
                    <div>Your video file is being uploaded. The currently loaded video is the source file.</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-base-content max-md:border md:border-r border-b overflow-hidden   ">
              <div class="font-semibold p-4 relative z-10 border-b bg-accent-pink -mb-20">"<!-- -->I built my second app
                in a week<!-- -->"</div>
              <div class="bg-base-content scale-[1.04]">
                <div class="next-video-container">
                  <style>
                    .next-video-container {
                      position: relative;
                      width: 100%;
                      aspect-ratio: 16 / 9;
                    }

                    [data-next-video] {
                      position: relative;
                      width: 100%;
                      height: 100%;
                      display: inline-block;
                      line-height: 0;
                    }

                    [data-next-video] img {
                      object-fit: var(--media-object-fit, contain);
                      object-position: var(--media-object-position, center);
                      background: center / var(--media-object-fit, contain) no-repeat transparent;
                      width: 100%;
                      height: 100%;
                    }
                  </style><mux-player player-software-name="mux-player-react"
                              player-software-version="2.4.0"
                              stream-type="on-demand"
                              default-hidden-captions=""
                              accent-color="#f9dcf8"
                              primary-color="#FFFFFF"
                              id="fW7GSv1z02Q8QrYVE49lb00QrVmOH68I73gQKew902UTYc"
                              style="aspect-ratio:9/16"
                              playback-id="fW7GSv1z02Q8QrYVE49lb00QrVmOH68I73gQKew902UTYc"></mux-player>
                  <style>
                    .next-video-alert {
                      position: absolute;
                      inset: 1em;
                      bottom: auto;
                      padding: .75rem 1rem;
                      border-radius: 1rem;
                      color: hsl(0, 0%, 100%);
                      background-color: hsl(240 10% 3.9% / .7);
                      border: 1px solid hsl(240 3.7% 15.9%);
                      transition: visibility 0s, opacity .25s;
                      visibility: visible;
                      opacity: 1;
                    }

                    .next-video-alert[hidden] {
                      display: block;
                      transition: visibility 1s, opacity 1s;
                      visibility: hidden;
                      opacity: 0;
                    }

                    .next-video-alert svg {
                      position: absolute;
                    }

                    .next-video-alert h5 {
                      line-height: 1;
                      font-weight: 500;
                      margin-bottom: 0.25rem;
                      padding-left: 1.75rem;
                      font-size: inherit;
                    }

                    .next-video-alert div {
                      padding-left: 1.75rem;
                      font-size: 0.875rem;
                      line-height: 1.25rem;
                    }
                  </style>
                  <div role="alert"
                       class="next-video-alert next-video-alert-undefined"
                       hidden=""><svg width="15"
                         height="15"
                         viewBox="0 0 15 15"
                         fill="none">
                      <path d="M8.4449 0.608765C8.0183 -0.107015 6.9817 -0.107015 6.55509 0.608766L0.161178 11.3368C-0.275824 12.07 0.252503 13 1.10608 13H13.8939C14.7475 13 15.2758 12.07 14.8388 11.3368L8.4449 0.608765ZM7.4141 1.12073C7.45288 1.05566 7.54712 1.05566 7.5859 1.12073L13.9798 11.8488C14.0196 11.9154 13.9715 12 13.8939 12H1.10608C1.02849 12 0.980454 11.9154 1.02018 11.8488L7.4141 1.12073ZM6.8269 4.48611C6.81221 4.10423 7.11783 3.78663 7.5 3.78663C7.88217 3.78663 8.18778 4.10423 8.1731 4.48612L8.01921 8.48701C8.00848 8.766 7.7792 8.98664 7.5 8.98664C7.2208 8.98664 6.99151 8.766 6.98078 8.48701L6.8269 4.48611ZM8.24989 10.476C8.24989 10.8902 7.9141 11.226 7.49989 11.226C7.08567 11.226 6.74989 10.8902 6.74989 10.476C6.74989 10.0618 7.08567 9.72599 7.49989 9.72599C7.9141 9.72599 8.24989 10.0618 8.24989 10.476Z"
                            fill="currentColor"
                            fill-rule="evenodd"
                            clip-rule="evenodd"></path>
                    </svg>
                    <h5>Upload in progress...</h5>
                    <div>Your video file is being uploaded. The currently loaded video is the source file.</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-base-content max-md:border md:border-r border-b overflow-hidden   ">
              <div class="font-semibold p-4 relative z-10 border-b bg-accent-orange -mb-20">"<!-- -->From knowing
                nothing to writing JavaScript<!-- -->"</div>
              <div class="bg-base-content scale-[1.04]">
                <div class="next-video-container">
                  <style>
                    .next-video-container {
                      position: relative;
                      width: 100%;
                      aspect-ratio: 16 / 9;
                    }

                    [data-next-video] {
                      position: relative;
                      width: 100%;
                      height: 100%;
                      display: inline-block;
                      line-height: 0;
                    }

                    [data-next-video] img {
                      object-fit: var(--media-object-fit, contain);
                      object-position: var(--media-object-position, center);
                      background: center / var(--media-object-fit, contain) no-repeat transparent;
                      width: 100%;
                      height: 100%;
                    }
                  </style><mux-player player-software-name="mux-player-react"
                              player-software-version="2.4.0"
                              stream-type="on-demand"
                              default-hidden-captions=""
                              accent-color="#FFD3AC"
                              primary-color="#FFFFFF"
                              id="E5NOm2rPdOEs6SWDfeyQ028It7s4huOvc8xzExhjgsPU"
                              style="aspect-ratio:9/16"
                              playback-id="E5NOm2rPdOEs6SWDfeyQ028It7s4huOvc8xzExhjgsPU"></mux-player>
                  <style>
                    .next-video-alert {
                      position: absolute;
                      inset: 1em;
                      bottom: auto;
                      padding: .75rem 1rem;
                      border-radius: 1rem;
                      color: hsl(0, 0%, 100%);
                      background-color: hsl(240 10% 3.9% / .7);
                      border: 1px solid hsl(240 3.7% 15.9%);
                      transition: visibility 0s, opacity .25s;
                      visibility: visible;
                      opacity: 1;
                    }

                    .next-video-alert[hidden] {
                      display: block;
                      transition: visibility 1s, opacity 1s;
                      visibility: hidden;
                      opacity: 0;
                    }

                    .next-video-alert svg {
                      position: absolute;
                    }

                    .next-video-alert h5 {
                      line-height: 1;
                      font-weight: 500;
                      margin-bottom: 0.25rem;
                      padding-left: 1.75rem;
                      font-size: inherit;
                    }

                    .next-video-alert div {
                      padding-left: 1.75rem;
                      font-size: 0.875rem;
                      line-height: 1.25rem;
                    }
                  </style>
                  <div role="alert"
                       class="next-video-alert next-video-alert-undefined"
                       hidden=""><svg width="15"
                         height="15"
                         viewBox="0 0 15 15"
                         fill="none">
                      <path d="M8.4449 0.608765C8.0183 -0.107015 6.9817 -0.107015 6.55509 0.608766L0.161178 11.3368C-0.275824 12.07 0.252503 13 1.10608 13H13.8939C14.7475 13 15.2758 12.07 14.8388 11.3368L8.4449 0.608765ZM7.4141 1.12073C7.45288 1.05566 7.54712 1.05566 7.5859 1.12073L13.9798 11.8488C14.0196 11.9154 13.9715 12 13.8939 12H1.10608C1.02849 12 0.980454 11.9154 1.02018 11.8488L7.4141 1.12073ZM6.8269 4.48611C6.81221 4.10423 7.11783 3.78663 7.5 3.78663C7.88217 3.78663 8.18778 4.10423 8.1731 4.48612L8.01921 8.48701C8.00848 8.766 7.7792 8.98664 7.5 8.98664C7.2208 8.98664 6.99151 8.766 6.98078 8.48701L6.8269 4.48611ZM8.24989 10.476C8.24989 10.8902 7.9141 11.226 7.49989 11.226C7.08567 11.226 6.74989 10.8902 6.74989 10.476C6.74989 10.0618 7.08567 9.72599 7.49989 9.72599C7.9141 9.72599 8.24989 10.0618 8.24989 10.476Z"
                            fill="currentColor"
                            fill-rule="evenodd"
                            clip-rule="evenodd"></path>
                    </svg>
                    <h5>Upload in progress...</h5>
                    <div>Your video file is being uploaded. The currently loaded video is the source file.</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-base-content max-md:border md:border-r border-b overflow-hidden   ">
              <div class="font-semibold p-4 relative z-10 border-b bg-accent-green -mb-20">"<!-- -->I built my first
                SaaS in 2-3 weeks<!-- -->"</div>
              <div class="bg-base-content scale-[1.04]">
                <div class="next-video-container">
                  <style>
                    .next-video-container {
                      position: relative;
                      width: 100%;
                      aspect-ratio: 16 / 9;
                    }

                    [data-next-video] {
                      position: relative;
                      width: 100%;
                      height: 100%;
                      display: inline-block;
                      line-height: 0;
                    }

                    [data-next-video] img {
                      object-fit: var(--media-object-fit, contain);
                      object-position: var(--media-object-position, center);
                      background: center / var(--media-object-fit, contain) no-repeat transparent;
                      width: 100%;
                      height: 100%;
                    }
                  </style><mux-player player-software-name="mux-player-react"
                              player-software-version="2.4.0"
                              stream-type="on-demand"
                              default-hidden-captions=""
                              accent-color="#93E2C8"
                              primary-color="#FFFFFF"
                              id="HHL7MnMMldmkRPVmlRi1lOI3ivYYPMKBGxm1cfDWwOk"
                              style="aspect-ratio:9/16"
                              playback-id="HHL7MnMMldmkRPVmlRi1lOI3ivYYPMKBGxm1cfDWwOk"></mux-player>
                  <style>
                    .next-video-alert {
                      position: absolute;
                      inset: 1em;
                      bottom: auto;
                      padding: .75rem 1rem;
                      border-radius: 1rem;
                      color: hsl(0, 0%, 100%);
                      background-color: hsl(240 10% 3.9% / .7);
                      border: 1px solid hsl(240 3.7% 15.9%);
                      transition: visibility 0s, opacity .25s;
                      visibility: visible;
                      opacity: 1;
                    }

                    .next-video-alert[hidden] {
                      display: block;
                      transition: visibility 1s, opacity 1s;
                      visibility: hidden;
                      opacity: 0;
                    }

                    .next-video-alert svg {
                      position: absolute;
                    }

                    .next-video-alert h5 {
                      line-height: 1;
                      font-weight: 500;
                      margin-bottom: 0.25rem;
                      padding-left: 1.75rem;
                      font-size: inherit;
                    }

                    .next-video-alert div {
                      padding-left: 1.75rem;
                      font-size: 0.875rem;
                      line-height: 1.25rem;
                    }
                  </style>
                  <div role="alert"
                       class="next-video-alert next-video-alert-undefined"
                       hidden=""><svg width="15"
                         height="15"
                         viewBox="0 0 15 15"
                         fill="none">
                      <path d="M8.4449 0.608765C8.0183 -0.107015 6.9817 -0.107015 6.55509 0.608766L0.161178 11.3368C-0.275824 12.07 0.252503 13 1.10608 13H13.8939C14.7475 13 15.2758 12.07 14.8388 11.3368L8.4449 0.608765ZM7.4141 1.12073C7.45288 1.05566 7.54712 1.05566 7.5859 1.12073L13.9798 11.8488C14.0196 11.9154 13.9715 12 13.8939 12H1.10608C1.02849 12 0.980454 11.9154 1.02018 11.8488L7.4141 1.12073ZM6.8269 4.48611C6.81221 4.10423 7.11783 3.78663 7.5 3.78663C7.88217 3.78663 8.18778 4.10423 8.1731 4.48612L8.01921 8.48701C8.00848 8.766 7.7792 8.98664 7.5 8.98664C7.2208 8.98664 6.99151 8.766 6.98078 8.48701L6.8269 4.48611ZM8.24989 10.476C8.24989 10.8902 7.9141 11.226 7.49989 11.226C7.08567 11.226 6.74989 10.8902 6.74989 10.476C6.74989 10.0618 7.08567 9.72599 7.49989 9.72599C7.9141 9.72599 8.24989 10.0618 8.24989 10.476Z"
                            fill="currentColor"
                            fill-rule="evenodd"
                            clip-rule="evenodd"></path>
                    </svg>
                    <h5>Upload in progress...</h5>
                    <div>Your video file is being uploaded. The currently loaded video is the source file.</div>
                  </div>
                </div>
              </div>
            </div>
            <div
                 class="bg-base-content max-md:border md:border-r border-b overflow-hidden md:col-span-full lg:col-span-3  ">
              <div class="font-semibold p-4 relative z-10 border-b bg-accent-green md:-mb-12 md:text-lg md:py-6">
                "<!-- -->After CodeFast, I learned that coding is much faster and easier than I thought<!-- -->"</div>
              <div class="bg-base-content scale-[1.04]">
                <div class="next-video-container">
                  <style>
                    .next-video-container {
                      position: relative;
                      width: 100%;
                      aspect-ratio: 16 / 9;
                    }

                    [data-next-video] {
                      position: relative;
                      width: 100%;
                      height: 100%;
                      display: inline-block;
                      line-height: 0;
                    }

                    [data-next-video] img {
                      object-fit: var(--media-object-fit, contain);
                      object-position: var(--media-object-position, center);
                      background: center / var(--media-object-fit, contain) no-repeat transparent;
                      width: 100%;
                      height: 100%;
                    }
                  </style><mux-player player-software-name="mux-player-react"
                              player-software-version="2.4.0"
                              stream-type="on-demand"
                              default-hidden-captions=""
                              accent-color="#93E2C8"
                              primary-color="#FFFFFF"
                              id="LGcN6J02VTvRICqZ9MZ01JPd00LvalgmLH00zR3adHznhX4"
                              poster="https://image.mux.com/LGcN6J02VTvRICqZ9MZ01JPd00LvalgmLH00zR3adHznhX4/thumbnail.png?width=428&amp;height=242&amp;time=33"
                              style="aspect-ratio:16/9"
                              playback-id="LGcN6J02VTvRICqZ9MZ01JPd00LvalgmLH00zR3adHznhX4"></mux-player>
                  <style>
                    .next-video-alert {
                      position: absolute;
                      inset: 1em;
                      bottom: auto;
                      padding: .75rem 1rem;
                      border-radius: 1rem;
                      color: hsl(0, 0%, 100%);
                      background-color: hsl(240 10% 3.9% / .7);
                      border: 1px solid hsl(240 3.7% 15.9%);
                      transition: visibility 0s, opacity .25s;
                      visibility: visible;
                      opacity: 1;
                    }

                    .next-video-alert[hidden] {
                      display: block;
                      transition: visibility 1s, opacity 1s;
                      visibility: hidden;
                      opacity: 0;
                    }

                    .next-video-alert svg {
                      position: absolute;
                    }

                    .next-video-alert h5 {
                      line-height: 1;
                      font-weight: 500;
                      margin-bottom: 0.25rem;
                      padding-left: 1.75rem;
                      font-size: inherit;
                    }

                    .next-video-alert div {
                      padding-left: 1.75rem;
                      font-size: 0.875rem;
                      line-height: 1.25rem;
                    }
                  </style>
                  <div role="alert"
                       class="next-video-alert next-video-alert-undefined"
                       hidden=""><svg width="15"
                         height="15"
                         viewBox="0 0 15 15"
                         fill="none">
                      <path d="M8.4449 0.608765C8.0183 -0.107015 6.9817 -0.107015 6.55509 0.608766L0.161178 11.3368C-0.275824 12.07 0.252503 13 1.10608 13H13.8939C14.7475 13 15.2758 12.07 14.8388 11.3368L8.4449 0.608765ZM7.4141 1.12073C7.45288 1.05566 7.54712 1.05566 7.5859 1.12073L13.9798 11.8488C14.0196 11.9154 13.9715 12 13.8939 12H1.10608C1.02849 12 0.980454 11.9154 1.02018 11.8488L7.4141 1.12073ZM6.8269 4.48611C6.81221 4.10423 7.11783 3.78663 7.5 3.78663C7.88217 3.78663 8.18778 4.10423 8.1731 4.48612L8.01921 8.48701C8.00848 8.766 7.7792 8.98664 7.5 8.98664C7.2208 8.98664 6.99151 8.766 6.98078 8.48701L6.8269 4.48611ZM8.24989 10.476C8.24989 10.8902 7.9141 11.226 7.49989 11.226C7.08567 11.226 6.74989 10.8902 6.74989 10.476C6.74989 10.0618 7.08567 9.72599 7.49989 9.72599C7.9141 9.72599 8.24989 10.0618 8.24989 10.476Z"
                            fill="currentColor"
                            fill-rule="evenodd"
                            clip-rule="evenodd"></path>
                    </svg>
                    <h5>Upload in progress...</h5>
                    <div>Your video file is being uploaded. The currently loaded video is the source file.</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-base-content max-md:border md:border-r border-b overflow-hidden   ">
              <div class="font-semibold p-4 relative z-10 border-b bg-accent-pink -mb-20">"<!-- -->I thought coding was
                hard, CodeFast changed that.<!-- -->"</div>
              <div class="bg-base-content scale-[1.04]">
                <div class="next-video-container">
                  <style>
                    .next-video-container {
                      position: relative;
                      width: 100%;
                      aspect-ratio: 16 / 9;
                    }

                    [data-next-video] {
                      position: relative;
                      width: 100%;
                      height: 100%;
                      display: inline-block;
                      line-height: 0;
                    }

                    [data-next-video] img {
                      object-fit: var(--media-object-fit, contain);
                      object-position: var(--media-object-position, center);
                      background: center / var(--media-object-fit, contain) no-repeat transparent;
                      width: 100%;
                      height: 100%;
                    }
                  </style><mux-player player-software-name="mux-player-react"
                              player-software-version="2.4.0"
                              stream-type="on-demand"
                              default-hidden-captions=""
                              accent-color="#f9dcf8"
                              primary-color="#FFFFFF"
                              id="5f7thV02acgCS8gGvYjTEjqQVw4ZQLBDMWX5w56M00Uvo"
                              style="aspect-ratio:9/16"
                              playback-id="5f7thV02acgCS8gGvYjTEjqQVw4ZQLBDMWX5w56M00Uvo"></mux-player>
                  <style>
                    .next-video-alert {
                      position: absolute;
                      inset: 1em;
                      bottom: auto;
                      padding: .75rem 1rem;
                      border-radius: 1rem;
                      color: hsl(0, 0%, 100%);
                      background-color: hsl(240 10% 3.9% / .7);
                      border: 1px solid hsl(240 3.7% 15.9%);
                      transition: visibility 0s, opacity .25s;
                      visibility: visible;
                      opacity: 1;
                    }

                    .next-video-alert[hidden] {
                      display: block;
                      transition: visibility 1s, opacity 1s;
                      visibility: hidden;
                      opacity: 0;
                    }

                    .next-video-alert svg {
                      position: absolute;
                    }

                    .next-video-alert h5 {
                      line-height: 1;
                      font-weight: 500;
                      margin-bottom: 0.25rem;
                      padding-left: 1.75rem;
                      font-size: inherit;
                    }

                    .next-video-alert div {
                      padding-left: 1.75rem;
                      font-size: 0.875rem;
                      line-height: 1.25rem;
                    }
                  </style>
                  <div role="alert"
                       class="next-video-alert next-video-alert-undefined"
                       hidden=""><svg width="15"
                         height="15"
                         viewBox="0 0 15 15"
                         fill="none">
                      <path d="M8.4449 0.608765C8.0183 -0.107015 6.9817 -0.107015 6.55509 0.608766L0.161178 11.3368C-0.275824 12.07 0.252503 13 1.10608 13H13.8939C14.7475 13 15.2758 12.07 14.8388 11.3368L8.4449 0.608765ZM7.4141 1.12073C7.45288 1.05566 7.54712 1.05566 7.5859 1.12073L13.9798 11.8488C14.0196 11.9154 13.9715 12 13.8939 12H1.10608C1.02849 12 0.980454 11.9154 1.02018 11.8488L7.4141 1.12073ZM6.8269 4.48611C6.81221 4.10423 7.11783 3.78663 7.5 3.78663C7.88217 3.78663 8.18778 4.10423 8.1731 4.48612L8.01921 8.48701C8.00848 8.766 7.7792 8.98664 7.5 8.98664C7.2208 8.98664 6.99151 8.766 6.98078 8.48701L6.8269 4.48611ZM8.24989 10.476C8.24989 10.8902 7.9141 11.226 7.49989 11.226C7.08567 11.226 6.74989 10.8902 6.74989 10.476C6.74989 10.0618 7.08567 9.72599 7.49989 9.72599C7.9141 9.72599 8.24989 10.0618 8.24989 10.476Z"
                            fill="currentColor"
                            fill-rule="evenodd"
                            clip-rule="evenodd"></path>
                    </svg>
                    <h5>Upload in progress...</h5>
                    <div>Your video file is being uploaded. The currently loaded video is the source file.</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-base-content max-md:border md:border-r border-b overflow-hidden  xl:border-b-0 ">
              <div class="font-semibold p-4 relative z-10 border-b bg-accent-blue -mb-20">"<!-- -->Exactly what you need
                to build a small SaaS<!-- -->"</div>
              <div class="bg-base-content scale-[1.04]">
                <div class="next-video-container">
                  <style>
                    .next-video-container {
                      position: relative;
                      width: 100%;
                      aspect-ratio: 16 / 9;
                    }

                    [data-next-video] {
                      position: relative;
                      width: 100%;
                      height: 100%;
                      display: inline-block;
                      line-height: 0;
                    }

                    [data-next-video] img {
                      object-fit: var(--media-object-fit, contain);
                      object-position: var(--media-object-position, center);
                      background: center / var(--media-object-fit, contain) no-repeat transparent;
                      width: 100%;
                      height: 100%;
                    }
                  </style><mux-player player-software-name="mux-player-react"
                              player-software-version="2.4.0"
                              stream-type="on-demand"
                              default-hidden-captions=""
                              accent-color="#BDE0FF"
                              primary-color="#FFFFFF"
                              id="aE1F8TPmup1ccp8fu7Cvgh9DF65PL1acZkJO02oLGfjs"
                              style="aspect-ratio:9/16"
                              playback-id="aE1F8TPmup1ccp8fu7Cvgh9DF65PL1acZkJO02oLGfjs"></mux-player>
                  <style>
                    .next-video-alert {
                      position: absolute;
                      inset: 1em;
                      bottom: auto;
                      padding: .75rem 1rem;
                      border-radius: 1rem;
                      color: hsl(0, 0%, 100%);
                      background-color: hsl(240 10% 3.9% / .7);
                      border: 1px solid hsl(240 3.7% 15.9%);
                      transition: visibility 0s, opacity .25s;
                      visibility: visible;
                      opacity: 1;
                    }

                    .next-video-alert[hidden] {
                      display: block;
                      transition: visibility 1s, opacity 1s;
                      visibility: hidden;
                      opacity: 0;
                    }

                    .next-video-alert svg {
                      position: absolute;
                    }

                    .next-video-alert h5 {
                      line-height: 1;
                      font-weight: 500;
                      margin-bottom: 0.25rem;
                      padding-left: 1.75rem;
                      font-size: inherit;
                    }

                    .next-video-alert div {
                      padding-left: 1.75rem;
                      font-size: 0.875rem;
                      line-height: 1.25rem;
                    }
                  </style>
                  <div role="alert"
                       class="next-video-alert next-video-alert-undefined"
                       hidden=""><svg width="15"
                         height="15"
                         viewBox="0 0 15 15"
                         fill="none">
                      <path d="M8.4449 0.608765C8.0183 -0.107015 6.9817 -0.107015 6.55509 0.608766L0.161178 11.3368C-0.275824 12.07 0.252503 13 1.10608 13H13.8939C14.7475 13 15.2758 12.07 14.8388 11.3368L8.4449 0.608765ZM7.4141 1.12073C7.45288 1.05566 7.54712 1.05566 7.5859 1.12073L13.9798 11.8488C14.0196 11.9154 13.9715 12 13.8939 12H1.10608C1.02849 12 0.980454 11.9154 1.02018 11.8488L7.4141 1.12073ZM6.8269 4.48611C6.81221 4.10423 7.11783 3.78663 7.5 3.78663C7.88217 3.78663 8.18778 4.10423 8.1731 4.48612L8.01921 8.48701C8.00848 8.766 7.7792 8.98664 7.5 8.98664C7.2208 8.98664 6.99151 8.766 6.98078 8.48701L6.8269 4.48611ZM8.24989 10.476C8.24989 10.8902 7.9141 11.226 7.49989 11.226C7.08567 11.226 6.74989 10.8902 6.74989 10.476C6.74989 10.0618 7.08567 9.72599 7.49989 9.72599C7.9141 9.72599 8.24989 10.0618 8.24989 10.476Z"
                            fill="currentColor"
                            fill-rule="evenodd"
                            clip-rule="evenodd"></path>
                    </svg>
                    <h5>Upload in progress...</h5>
                    <div>Your video file is being uploaded. The currently loaded video is the source file.</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-base-content max-md:border md:border-r border-b overflow-hidden  xl:border-b-0 ">
              <div class="font-semibold p-4 relative z-10 border-b bg-accent-orange -mb-20">"<!-- -->I never found a
                course to become a startup founder<!-- -->"</div>
              <div class="bg-base-content scale-[1.04]">
                <div class="next-video-container">
                  <style>
                    .next-video-container {
                      position: relative;
                      width: 100%;
                      aspect-ratio: 16 / 9;
                    }

                    [data-next-video] {
                      position: relative;
                      width: 100%;
                      height: 100%;
                      display: inline-block;
                      line-height: 0;
                    }

                    [data-next-video] img {
                      object-fit: var(--media-object-fit, contain);
                      object-position: var(--media-object-position, center);
                      background: center / var(--media-object-fit, contain) no-repeat transparent;
                      width: 100%;
                      height: 100%;
                    }
                  </style><mux-player player-software-name="mux-player-react"
                              player-software-version="2.4.0"
                              stream-type="on-demand"
                              default-hidden-captions=""
                              accent-color="#FFD3AC"
                              primary-color="#FFFFFF"
                              id="2kY4UYOYb9leJnhPAt4DEY202VWPBEBAcRHgaG6Ykwz00"
                              style="aspect-ratio:9/16"
                              playback-id="2kY4UYOYb9leJnhPAt4DEY202VWPBEBAcRHgaG6Ykwz00"></mux-player>
                  <style>
                    .next-video-alert {
                      position: absolute;
                      inset: 1em;
                      bottom: auto;
                      padding: .75rem 1rem;
                      border-radius: 1rem;
                      color: hsl(0, 0%, 100%);
                      background-color: hsl(240 10% 3.9% / .7);
                      border: 1px solid hsl(240 3.7% 15.9%);
                      transition: visibility 0s, opacity .25s;
                      visibility: visible;
                      opacity: 1;
                    }

                    .next-video-alert[hidden] {
                      display: block;
                      transition: visibility 1s, opacity 1s;
                      visibility: hidden;
                      opacity: 0;
                    }

                    .next-video-alert svg {
                      position: absolute;
                    }

                    .next-video-alert h5 {
                      line-height: 1;
                      font-weight: 500;
                      margin-bottom: 0.25rem;
                      padding-left: 1.75rem;
                      font-size: inherit;
                    }

                    .next-video-alert div {
                      padding-left: 1.75rem;
                      font-size: 0.875rem;
                      line-height: 1.25rem;
                    }
                  </style>
                  <div role="alert"
                       class="next-video-alert next-video-alert-undefined"
                       hidden=""><svg width="15"
                         height="15"
                         viewBox="0 0 15 15"
                         fill="none">
                      <path d="M8.4449 0.608765C8.0183 -0.107015 6.9817 -0.107015 6.55509 0.608766L0.161178 11.3368C-0.275824 12.07 0.252503 13 1.10608 13H13.8939C14.7475 13 15.2758 12.07 14.8388 11.3368L8.4449 0.608765ZM7.4141 1.12073C7.45288 1.05566 7.54712 1.05566 7.5859 1.12073L13.9798 11.8488C14.0196 11.9154 13.9715 12 13.8939 12H1.10608C1.02849 12 0.980454 11.9154 1.02018 11.8488L7.4141 1.12073ZM6.8269 4.48611C6.81221 4.10423 7.11783 3.78663 7.5 3.78663C7.88217 3.78663 8.18778 4.10423 8.1731 4.48612L8.01921 8.48701C8.00848 8.766 7.7792 8.98664 7.5 8.98664C7.2208 8.98664 6.99151 8.766 6.98078 8.48701L6.8269 4.48611ZM8.24989 10.476C8.24989 10.8902 7.9141 11.226 7.49989 11.226C7.08567 11.226 6.74989 10.8902 6.74989 10.476C6.74989 10.0618 7.08567 9.72599 7.49989 9.72599C7.9141 9.72599 8.24989 10.0618 8.24989 10.476Z"
                            fill="currentColor"
                            fill-rule="evenodd"
                            clip-rule="evenodd"></path>
                    </svg>
                    <h5>Upload in progress...</h5>
                    <div>Your video file is being uploaded. The currently loaded video is the source file.</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-base-content max-md:border md:border-r border-b overflow-hidden  xl:border-b-0 ">
              <div class="font-semibold p-4 relative z-10 border-b bg-accent-pink -mb-20">"<!-- -->CodeFast helped me
                build my micro-SaaS<!-- -->"</div>
              <div class="bg-base-content scale-[1.04]">
                <div class="next-video-container">
                  <style>
                    .next-video-container {
                      position: relative;
                      width: 100%;
                      aspect-ratio: 16 / 9;
                    }

                    [data-next-video] {
                      position: relative;
                      width: 100%;
                      height: 100%;
                      display: inline-block;
                      line-height: 0;
                    }

                    [data-next-video] img {
                      object-fit: var(--media-object-fit, contain);
                      object-position: var(--media-object-position, center);
                      background: center / var(--media-object-fit, contain) no-repeat transparent;
                      width: 100%;
                      height: 100%;
                    }
                  </style><mux-player player-software-name="mux-player-react"
                              player-software-version="2.4.0"
                              stream-type="on-demand"
                              default-hidden-captions=""
                              accent-color="#f9dcf8"
                              primary-color="#FFFFFF"
                              id="xp2X6i01F7Wqvi02H02XRXooAKRb4MYcWPNt3Le01KxzRQ8"
                              style="aspect-ratio:9/16"
                              playback-id="xp2X6i01F7Wqvi02H02XRXooAKRb4MYcWPNt3Le01KxzRQ8"></mux-player>
                  <style>
                    .next-video-alert {
                      position: absolute;
                      inset: 1em;
                      bottom: auto;
                      padding: .75rem 1rem;
                      border-radius: 1rem;
                      color: hsl(0, 0%, 100%);
                      background-color: hsl(240 10% 3.9% / .7);
                      border: 1px solid hsl(240 3.7% 15.9%);
                      transition: visibility 0s, opacity .25s;
                      visibility: visible;
                      opacity: 1;
                    }

                    .next-video-alert[hidden] {
                      display: block;
                      transition: visibility 1s, opacity 1s;
                      visibility: hidden;
                      opacity: 0;
                    }

                    .next-video-alert svg {
                      position: absolute;
                    }

                    .next-video-alert h5 {
                      line-height: 1;
                      font-weight: 500;
                      margin-bottom: 0.25rem;
                      padding-left: 1.75rem;
                      font-size: inherit;
                    }

                    .next-video-alert div {
                      padding-left: 1.75rem;
                      font-size: 0.875rem;
                      line-height: 1.25rem;
                    }
                  </style>
                  <div role="alert"
                       class="next-video-alert next-video-alert-undefined"
                       hidden=""><svg width="15"
                         height="15"
                         viewBox="0 0 15 15"
                         fill="none">
                      <path d="M8.4449 0.608765C8.0183 -0.107015 6.9817 -0.107015 6.55509 0.608766L0.161178 11.3368C-0.275824 12.07 0.252503 13 1.10608 13H13.8939C14.7475 13 15.2758 12.07 14.8388 11.3368L8.4449 0.608765ZM7.4141 1.12073C7.45288 1.05566 7.54712 1.05566 7.5859 1.12073L13.9798 11.8488C14.0196 11.9154 13.9715 12 13.8939 12H1.10608C1.02849 12 0.980454 11.9154 1.02018 11.8488L7.4141 1.12073ZM6.8269 4.48611C6.81221 4.10423 7.11783 3.78663 7.5 3.78663C7.88217 3.78663 8.18778 4.10423 8.1731 4.48612L8.01921 8.48701C8.00848 8.766 7.7792 8.98664 7.5 8.98664C7.2208 8.98664 6.99151 8.766 6.98078 8.48701L6.8269 4.48611ZM8.24989 10.476C8.24989 10.8902 7.9141 11.226 7.49989 11.226C7.08567 11.226 6.74989 10.8902 6.74989 10.476C6.74989 10.0618 7.08567 9.72599 7.49989 9.72599C7.9141 9.72599 8.24989 10.0618 8.24989 10.476Z"
                            fill="currentColor"
                            fill-rule="evenodd"
                            clip-rule="evenodd"></path>
                    </svg>
                    <h5>Upload in progress...</h5>
                    <div>Your video file is being uploaded. The currently loaded video is the source file.</div>
                  </div>
                </div>
              </div>
            </div>
            <div
                 class="hidden lg:flex bg-base-100 max-md:border max-md:border-b overflow-hidden justify-center items-center text-center text-lg italic">
              You..?</div>
          </div>
        </div>
      </section>
      <section class="pb-48 pt-20">
        <div class="px-8">
          <div class="relative bg-base-100 border text-base-content w-full mx-auto max-w-5xl">
            <div class="absolute left-1/2 -translate-x-1/2 top-0 -translate-y-[90%] md:-translate-y-[70%] z-20"><img
                   alt="A wizard coding"
                   loading="lazy"
                   width="768"
                   height="768"
                   decoding="async"
                   data-nimg="1"
                   class="w-80"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fwizard.671ce364.png&amp;w=828&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fwizard.671ce364.png&amp;w=1920&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fwizard.671ce364.png&amp;w=1920&amp;q=75"></div>
            <div class=" flex flex-col items-center justify-center text-center gap-8 p-8 md:p-32 shadow-xl">
              <h2 class="font-black text-4xl md:text-6xl tracking-tight lg:tracking-[-0.035em]"><span
                      data-br=":R6tau9la:"
                      data-brr="1"
                      style="display:inline-block;vertical-align:top;text-decoration:inherit;text-wrap:balance">Learn to
                  code<!-- --> <span class="inline-block decoration-primary relative "><span class="relative z-10">in
                      weeks,</span><span
                          class="bottom-0  absolute bg-accent-pink h-4 md:h-6 md:-bottom-0.5 -inset-x-2 "></span></span>
                  <!-- -->not months</span>
                <script>self.__wrap_n = self.__wrap_n || (self.CSS && CSS.supports("text-wrap", "balance") ? 1 : 2); self.__wrap_b = (e, t, a) => { let s = (a = a || document.querySelector(`[data-br="${e}"]`)).parentElement, r = e => a.style.maxWidth = e + "px"; a.style.maxWidth = ""; let l = s.clientWidth, i = s.clientHeight, n = l / 2 - .25, c = l + .5, o; if (l) { for (r(n), n = Math.max(a.scrollWidth, n); n + 1 < c;)r(o = Math.round((n + c) / 2)), s.clientHeight === i ? c = o : n = o; r(c * t + l * (1 - t)) } a.__wrap_o || "undefined" != typeof ResizeObserver && (a.__wrap_o = new ResizeObserver(() => { self.__wrap_b(0, +a.dataset.brr, a) })).observe(s) }; self.__wrap_n != 1 && self.__wrap_b(":R6tau9la:", 1)</script>
              </h2>
              <p
                 class="text-base-content-secondary text-lg md:text-xl lg:leading-relaxed px-4 md:px-16 mx-auto max-w-lg">
                Everything you need to turn your idea into an online business, fast.</p>
              <div class="space-y-4"><a class="btn btn-primary group btn-pink md:btn-wide max-md:btn-block"
                   href="/#pricing">Get instant access</a>
                <div class="flex text-base-content-secondary items-center justify-center gap-2">
                  <p class="italic text-center"><span class="font-semibold text-base-content">277</span>
                    <!-- -->entrepreneurs love the course</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
    <footer class="bg-base-100 border-t"
            data-theme="dark">
      <div class="max-w-7xl mx-auto px-8 py-24">
        <div class="flex lg:items-start md:flex-row md:flex-nowrap flex-wrap flex-col">
          <div class="mx-auto flex-shrink-0 text-center md:mx-0 md:w-96 md:pr-12 md:text-left"><a aria-current="page"
               class="flex gap-2 justify-center md:justify-start items-center"
               href="/#"><img alt="CodeFast logo"
                   fetchpriority="high"
                   width="24"
                   height="24"
                   decoding="async"
                   data-nimg="1"
                   class="size-6"
                   style="color:transparent"
                   srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ficon.ae943832.png&amp;w=32&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ficon.ae943832.png&amp;w=48&amp;q=75 2x"
                   src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ficon.ae943832.png&amp;w=48&amp;q=75"><strong
                      class="font-extrabold tracking-tight text-base md:text-lg">CodeFast</strong></a>
            <p class="mt-3 text-sm text-base-content-secondary">Learn to code in weeks, not months.</p>
            <p class="text-base-content-secondary mt-1 mb-3 text-sm">Made with ☕️ and 🥐 by<!-- --> <a
                 href="https://marclou.com"
                 target="_blank"
                 class="link">Marc</a></p><a href="https://shipfa.st/?ref=codefast_badge"
               target="_blank"
               class="inline-block cursor-pointer rounded bg-base-content px-2 py-1 text-sm text-base-100 ring-1 ring-base-content/10 duration-200 hover:ring-yellow-500">
              <div class="flex items-center gap-1"><span>Built with</span><span
                      class="flex items-center gap-0.5 font-semibold tracking-tight"><img alt="ShipFast logo"
                       loading="lazy"
                       width="20"
                       height="20"
                       decoding="async"
                       data-nimg="1"
                       class="h-5 w-5"
                       style="color:transparent"
                       srcset="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fshipfast-icon_transparent.2aa1e5a5.png&amp;w=32&amp;q=75 1x, /_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fshipfast-icon_transparent.2aa1e5a5.png&amp;w=48&amp;q=75 2x"
                       src="/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fshipfast-icon_transparent.2aa1e5a5.png&amp;w=48&amp;q=75">ShipFast</span>
              </div>
            </a>
            <p class="mt-2 text-sm text-base-content-secondary opacity-80">Copyright © <!-- -->2024<!-- --> - All rights
              reserved</p>
          </div>
          <div class="-mb-10 mt-10 flex flex-grow flex-wrap justify-center text-center md:mt-0 md:text-left">
            <div class="w-full px-4 md:w-1/2 lg:w-1/3">
              <div class="footer-title font-semibold text-base-content tracking-widest text-sm md:text-left mb-3">LINKS
              </div>
              <div class="flex flex-col justify-center items-center md:items-start gap-2 mb-10 text-sm"><a
                   href="mailto:<EMAIL>"
                   target="_blank"
                   class="link link-hover"
                   aria-label="Contact Support">Support</a><a class="link link-hover"
                   href="/#pricing">Pricing</a><a class="link link-hover"
                   href="/course">Course</a><a class="link link-hover"
                   href="/affiliates">Affiliate (Earn up to $<!-- -->150<!-- -->)</a></div>
            </div>
            <div class="w-full px-4 md:w-1/2 lg:w-1/3">
              <div class="footer-title font-semibold text-base-content tracking-widest text-sm md:text-left mb-3">LEGAL
              </div>
              <div class="flex flex-col justify-center items-center md:items-start gap-2 mb-10 text-sm"><a
                   class="link link-hover"
                   href="/tos">Terms of services</a><a class="link link-hover"
                   href="/privacy-policy">Privacy policy</a></div>
            </div>
            <div class="w-full px-4 md:w-1/2 lg:w-1/3">
              <div class="footer-title mb-3 text-sm font-semibold tracking-widest text-base-content md:text-left">By the
                maker of this site</div>
              <div class="mb-10 flex flex-col items-center justify-center gap-2 text-sm md:items-start"><a
                   href="https://marclou.beehiiv.com/"
                   target="_blank"
                   class="link-hover link">Newsletter for makers</a><a href="https://byedispute.com/"
                   target="_blank"
                   class="link-hover link">ByeDispute</a><a href="https://indiepa.ge/"
                   target="_blank"
                   class="link-hover link">IndiePage</a><a href="https://zenvoice.io/"
                   target="_blank"
                   class="link-hover link">ZenVoice</a><a href="https://gamifylist.com/"
                   target="_blank"
                   class="link-hover link">GamifyList</a><a href="https://workbookpdf.com/"
                   target="_blank"
                   class="link-hover link">WorkbookPDF</a><a href="https://habitsgarden.com/"
                   target="_blank"
                   class="link-hover link">HabitsGarden</a><a href="https://shipfa.st/"
                   target="_blank"
                   class="link-hover link">ShipFast</a><a href="https://DataFa.st/"
                   target="_blank"
                   class="link-hover link">DataFast</a></div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
  <div style="position:fixed;z-index:9999;top:16px;left:16px;right:16px;bottom:16px;pointer-events:none"></div>
  <script>(self.__next_s = self.__next_s || []).push([0, { "children": "(function(w,r){w._rwq=r;w[r]=w[r]||function(){(w[r].q=w[r].q||[]).push(arguments)}})(window,'rewardful');", "id": "rewardful-queue" }])</script>
  <script src="/_next/static/chunks/webpack-363d19520def0b88.js"
          async=""></script>
  <script>(self.__next_f = self.__next_f || []).push([0]); self.__next_f.push([2, null])</script>
  <script>self.__next_f.push([1, "1:HL[\"/_next/static/media/5455839c73f146e7-s.p.woff2\",\"font\",{\"crossOrigin\":\"\",\"type\":\"font/woff2\"}]\n2:HL[\"/_next/static/css/10e4dd1aec12afdf.css\",\"style\"]\n0:\"$L3\"\n"])</script>
  <script>self.__next_f.push([1, "4:HL[\"/_next/static/css/3545cad29b288095.css\",\"style\"]\n"])</script>
  <script>self.__next_f.push([1, "5:I[7690,[],\"\"]\n8:I[5935,[\"126\",\"static/chunks/126-2fe60ff12b81bcda.js\",\"9\",\"static/chunks/9-80daeb805727ea27.js\",\"437\",\"static/chunks/437-b5b41b3a2e4ac36e.js\",\"185\",\"static/chunks/app/layout-aa134ab0782774d9.js\"],\"\"]\n9:I[4805,[\"126\",\"static/chunks/126-2fe60ff12b81bcda.js\",\"9\",\"static/chunks/9-80daeb805727ea27.js\",\"437\",\"static/chunks/437-b5b41b3a2e4ac36e.js\",\"185\",\"static/chunks/app/layout-aa134ab0782774d9.js\"],\"\"]\na:I[5613,[],\"\"]\nb:I[625,[\"250\",\"static/chunks/250-a9b98f1bc94173a5.js\",\"357\",\"static/chunks/357-cc984f4311d669e9.js\",\"601\",\"static/chunks/app/error-6fd5238d438ab1e4.js\"],\"\"]\nc:I[1778,[],\"\"]\n13:I[5250,[\"404\",\"static/chunks/a4634e51-fb28818a37ff4dfe.js\",\"250\",\"static/chunks/250-a9b98f1bc94173a5.js\",\"126\",\"static/chunks/126-2fe60ff12b81bcda.js\",\"9\",\"static/chunks/9-80daeb805727ea27.js\",\"908\",\"static/chunks/908-74eb929365e91f0a.js\",\"476\",\"static/chunks/476-8c43f6546ab92211.js\",\"749\",\"static/chunks/749-6c64d4afa6aa0b64.js\",\"385\",\"static/chunks/385-5053c6cd3fa449ec.js\",\"448\",\"static/chunks/448-def631ca554081a4.js\",\"931\",\"static/chunks/app/page-1a1970330440f1bc.js\"],\"\"]\n14:I[16,[\"250\",\"static/chunks/250-a9b98f1bc94173a5.js\",\"357\",\"static/chunks/357-cc984f4311d669e9.js\",\"160\",\"static/chunks/app/not-found-8fafa9212480884d.js\"],\"\"]\n16:I[8955,[],\"\"]\nd:T627,M697.636 545.355c-4.711-5.95-6.637-7.343-11.284-13.347q-56.765-73.417-106.708-151.793-33.924-53.23-64.483-108.504-14.549-26.278-28.3-52.969-10.67-20.695-20.864-41.638a841.984 841.984 0 0 1-5.711-12.009c-4.428-9.442-8.774-18.93-13.44-28.244-5.317-10.616-11.789-21.745-21.552-28.877a29.405 29.405 0 0 0-15.319-5.895c-7.948-.513-15.282 2.769-22.176 6.353-50.438 26.301-97.659 59.276-140.37 96.798A730.778 730.778 0 0 0 133.39 331.82c-1.009 1.44-3.393.064-2.375-1.384q6.01-8.498 12.257-16.813a734.817 734.817 0 0 1 187.6-174.986q18.248-11.825 37.182-22.542c6.362-3.603 12.752-7.16 19.251-10.497 6.372-3.272 13.137-6.215 20.416-6.325 24.77-.385 37.595 27.667 46.405 46.542q4.153 8.911 8.406 17.767 16.075 33.62 33.388 66.628 10.684 20.379 21.837 40.52 34.707 62.717 73.778 12"])</script>
  <script>self.__next_f.push([1, "2.896c34.506 53.143 68.737 100.089 108.046 149.785 1.082 1.375-.852 3.337-1.944 1.943ZM244.982 191.378c-1.44-1.604-2.87-3.209-4.318-4.813-11.422-12.632-23.679-25.118-39.364-32.36a57.11 57.11 0 0 0-23.927-5.547c-8.562.028-16.932 2.274-24.843 5.418-3.74 1.494-7.399 3.19-11.001 4.996-4.116 2.072-8.16 4.281-12.183 6.51q-11.332 6.27-22.369 13.09-21.96 13.572-42.545 29.216-10.671 8.113-20.902 16.758-9.516 8.03-18.646 16.492c-1.302 1.201-3.245-.742-1.944-1.943a441.255 441.255 0 0 1 4.85-4.446q6.875-6.216 13.971-12.193 12.94-10.918 26.549-20.993 21.162-15.676 43.782-29.226 11.304-6.765 22.919-12.962a198.735 198.735 0 0 1 7.095-3.621 113.116 113.116 0 0 1 16.868-6.867 60.006 60.006 0 0 1 25.476-2.502 66.327 66.327 0 0 1 23.505 8.131c15.401 8.608 27.346 21.92 38.97 34.91 1.174 1.32-.76 3.272-1.943 1.952Ze:T43d,M851.011 92.728a.982.982 0 0 1-.302-.047C586.303 9.063 353.265 19.998 204.33 43.895a1294.017 1294.017 0 0 0-60.403 11.161 1196.246 1196.246 0 0 0-15.597 3.378 1023.104 1023.104 0 0 0-18.532 4.306q-3.873.917-7.595 1.849a972.21 972.21 0 0 0-11.66 2.957 930.173 930.173 0 0 0-13.797 3.671.442.442 0 0 1-.051.015v.001a926.363 926.363 0 0 0-15.323 4.325c-2.698.78-5.304 1.548-7.8 2.307-.278.077-.525.151-.776.227l-.536.164c-.31.094-.617.187-.924.275l-.02.006h.001l-.811.253c-.968.293-1.912.579-2.841.864C23.119 87.22 9.626 92.604 9.493 92.656a1 1 0 1 1-.744-1.856c.134-.053 13.693-5.463 38.327-13.058.932-.286 1.88-.572 2.85-.866l.754-.235c.026-.01.051-.017.078-.025.305-.087.61-.18.92-.273l.536-.164c.268-.08.532-.16.802-.235a593.8 593.8 0 0 1 7.797-2.307 932.235 932.235 0 0 1 15.334-4.328c.017-.006.033-.01.05-.014v-.001a941.379 941.379 0 0 1 13.844-3.685 993.766 993.766 0 0 1 11.68-2.962q3.738-.93 7.61-1.852a1026.011 1026.011 0 0 1 18.563-4.313c5.299-1.183 10.555-2.322 15.622-3.383a1295.424 1295.424 0 0 1 60.497-11.178c149.149-23.932 382.52-34.884 647.299 48.854a1 1 0 0 1-.3 1.953Zf:Ta15,"])</script>
  <script>self.__next_f.push([1, "M262.989 419.84a6.73 6.73 0 0 0-1.7-2.67 6.43 6.43 0 0 0-.92-.71c-2.61-1.74-6.51-2.13-8.99 0a5.81 5.81 0 0 0-.69.71q-1.11 1.365-2.28 2.67a88.226 88.226 0 0 1-3.96 4.24c-.39.38-.78.77-1.18 1.15-.23.23-.46.45-.69.67-.88.84-1.78 1.65-2.69 2.45-.48.43-.96.85-1.45 1.26-.73.61-1.46 1.22-2.2 1.81-.07.05-.14.1-.21.16-.02.01-.03.03-.05.04-.01 0-.02 0-.03.02a.179.179 0 0 0-.07.05c-.22.15-.37.25-.48.34.04-.02.08-.05.12-.07-.18.14-.37.28-.55.42a92.853 92.853 0 0 1-5.37 3.69 99.21 99.21 0 0 1-14.22 7.55c-.33.13-.67.27-1.01.4a85.97 85.97 0 0 1-40.85 6.02q-2.13-.165-4.26-.45c-1.64-.24-3.27-.53-4.89-.86a97.932 97.932 0 0 1-18.02-5.44 118.652 118.652 0 0 1-20.66-12.12c-1-.71-2.01-1.42-3.02-2.11 1.15-2.82 2.28-5.64 3.38-8.48.55-1.37 1.08-2.74 1.6-4.12 4.09-10.63 7.93-21.36 11.61-32.13q5.58-16.365 10.53-32.92.51-1.68.99-3.36 2.595-8.745 4.98-17.53c.15-.57.31-1.13.45-1.7q.69-2.52 1.35-5.04c1-3.79-1.26-8.32-5.24-9.23a7.634 7.634 0 0 0-9.22 5.24c-.43 1.62-.86 3.23-1.3 4.85q-3.165 11.745-6.66 23.41l-1.02 3.36q-7.71 25.41-16.93 50.31-1.11 3.015-2.25 6.01c-.37.98-.74 1.96-1.12 2.94-.73 1.93-1.48 3.86-2.23 5.79-.43 1.13-.87 2.26-1.31 3.38-.29.71-.57 1.42-.85 2.12a41.81 41.81 0 0 0-8.81-2.12l-.48-.06a27.397 27.397 0 0 0-7.01.06 23.914 23.914 0 0 0-17.24 10.66c-4.77 7.51-4.71 18.25 1.98 24.63 6.89 6.57 17.32 6.52 25.43 2.41a28.351 28.351 0 0 0 10.52-9.86 50.57 50.57 0 0 0 2.74-4.65c.21.14.42.28.63.43.8.56 1.6 1.13 2.39 1.69a111.738 111.738 0 0 0 14.51 8.91 108.359 108.359 0 0 0 34.62 10.47c.27.03.53.07.8.1 1.33.17 2.67.3 4.01.41a103.782 103.782 0 0 0 55.58-11.36q2.175-1.125 4.31-2.36 3.315-1.92 6.48-4.08c1.15-.78 2.27-1.57 3.38-2.4a101.042 101.042 0 0 0 13.51-11.95q2.355-2.475 4.51-5.11a8.061 8.061 0 0 0 2.2-5.3 7.564 7.564 0 0 0-.5-2.64Zm-165.59 23.82c.21-.15.42-.31.62-.47-.06.15-.35.32-.62.47Zm3.21-3.23c-.23.26-.44.52-.67.78a23.366 23.366 0 0 1-2.25 2.2c-.11.1-.23.2-.35.29a.01.01 0 0 0-.01.01 3.804 3.804 0 0 0-.42.22q-.645.39-1.32.72a17.005 17.005 0 0 1-2.71.75 16.8 16.8 0 0 1-2.13.02h-.02a14.823 14.823 0 0 1-1.45-.4c-.24-.12-.47-.26-.7-.4-.09-.08-.17-.16-.22-.21a2.44 2.44 0 0 1-.27-.29.01.01 0 0 0-.01-.01c-.11-.2-.23-.4-.34-.6a.031.031 0 0 1-.01-.02c-.08-.25-.15-.51-.21-.77a12.51 12.51 0 0 1 .01-1.37 13.467 13.467 0 0 1 .54-1.88 11.068 11.068 0 0 1 .69-1.26c.02-.04.12-.2.23-.38.01-.01.01-.01.01-.02.15-.17.3-.35.46-.51.27-.3.56-.56.85-.83a18.022 18.022 0 0 1 1.75-1.01 19.48 19.48 0 0 1 2.93-.79 24.99 24.99 0 0 1 4.41.04 30.301 30.301 0 0 1 4.1 1.01 36.945 36.945 0 0 1-2.77 4.54c-.04.06-.08.12-.12.17Zm-11.12-3.29a2.18 2.18 0 0 1-.31.39 1.409 1.409 0 0 1 .31-.39Z"])</script>
  <script>self.__next_f.push([1, "10:T9d9,"])</script>
  <script>self.__next_f.push([1, "m232.929 317.71-.27 9.42q-.285 10.455-.59 20.92-.315 11.775-.66 23.54-.165 6.075-.34 12.15-.465 16.365-.92 32.72c-.03 1.13-.07 2.25-.1 3.38l-.45 16.23q-.255 8.805-.5 17.61-.18 6.6-.37 13.21l-2.7 95.79a7.648 7.648 0 0 1-7.5 7.5 7.561 7.561 0 0 1-7.5-7.5q.75-26.94 1.52-53.88.675-24.36 1.37-48.72l.45-16.06q.345-12.09.68-24.18c.03-1.13.07-2.25.1-3.38.02-.99.05-1.97.08-2.96l1.32-46.96q.27-9.24.52-18.49l.6-21.08c.09-3.09.17-6.17.26-9.26a7.648 7.648 0 0 1 7.5-7.5 7.561 7.561 0 0 1 7.5 7.5ZM644.357 319.791a893.238 893.238 0 0 1-28.161 87.941c-3.007 7.947-6.083 15.877-9.372 23.712l.756-1.791a54.583 54.583 0 0 1-5.59 10.612q-.229.32-.466.636 1.166-1.49.443-.589c-.254.3-.505.602-.768.895a23.664 23.664 0 0 1-2.249 2.204q-.301.257-.612.504l.938-.73c-.109.258-.873.598-1.11.744a18.254 18.254 0 0 1-2.405 1.218l1.791-.756a19.086 19.086 0 0 1-4.23 1.16l1.993-.267a17.02 17.02 0 0 1-4.298.046l1.994.268a14.002 14.002 0 0 1-3.405-.917l1.791.756a12.012 12.012 0 0 1-1.678-.896c-.272-.177-1.106-.809-.015.024 1.133.866.145.075-.088-.155-.194-.192-.37-.4-.56-.595-.882-.905.997 1.556.397.498a18.182 18.182 0 0 1-.878-1.637l.756 1.792a11.925 11.925 0 0 1-.728-2.651l.268 1.993a13.651 13.651 0 0 1-.003-3.404l-.268 1.993a15.964 15.964 0 0 1 .995-3.68l-.756 1.792a16.73 16.73 0 0 1 1.178-2.299 6.73 6.73 0 0 1 .728-1.071c.05.016-1.268 1.513-.57.757.184-.198.355-.406.54-.602.296-.314.613-.6.925-.898 1.045-.994-1.461.966-.256.18a19.049 19.049 0 0 1 2.75-1.5l-1.792.756a20.311 20.311 0 0 1 4.995-1.34l-1.994.268a25.628 25.628 0 0 1 6.46.076l-1.993-.267a33.21 33.21 0 0 1 7.892 2.22l-1.792-.757c5.39 2.314 10.163 5.75 14.928 9.118a111.95 111.95 0 0 0 14.506 8.907 108.388 108.388 0 0 0 34.622 10.474 103.933 103.933 0 0 0 92.586-36.752 8.078 8.078 0 0 0 2.197-5.304 7.632 7.632 0 0 0-2.197-5.303c-2.752-2.526-7.95-3.239-10.607 0a95.636 95.636 0 0 1-8.106 8.727q-2.018 1.914-4.143 3.71-1.213 1.026-2.46 2.011c-.394.31-1.62 1.138.263-.197-.432.306-.845.64-1.27.954a99.269 99.269 0 0 1-20.333 11.565l1.792-.756a96.836 96.836 0 0 1-24.172 6.623l1.994-.268a97.643 97.643 0 0 1-25.753-.038l1.993.268a99.8 99.8 0 0 1-24.857-6.77l1.792.755a116.025 116.025 0 0 1-21.736-12.59 86.877 86.877 0 0 0-11.113-6.995 42.824 42.824 0 0 0-14.438-4.388c-9.44-1.111-19.057 2.565-24.247 10.72-4.775 7.505-4.714 18.244 1.974 24.625 6.888 6.573 17.319 6.517 25.436 2.406 7.817-3.96 12.513-12.186 15.815-19.942 7.43-17.455 14.01-35.314 20.14-53.263q9.096-26.637 16.498-53.813.917-3.366 1.807-6.74c1.001-3.788-1.261-8.32-5.238-9.225a7.633 7.633 0 0 0-9.226 5.238Z"])</script>
  <script>self.__next_f.push([1, "11:Ta2f,"])</script>
  <script>self.__next_f.push([1, "M519.887 390.06c-8.609-16.792-21.946-30.92-37.632-41.303a114.237 114.237 0 0 0-52.563-18.38q-3.69-.335-7.399-.393c-2.921-.043-46.866 12.632-61.587 22.982a114.295 114.295 0 0 0-35.333 39.527 102.5 102.5 0 0 0-12.126 51.634 113.564 113.564 0 0 0 14.703 51.476 110.475 110.475 0 0 0 36.444 38.745c15.338 9.787 30.745 35.736 48.855 36.652 18.246.923 39.054-23.555 55.695-30.987a104.425 104.425 0 0 0 41.725-34.005 110.25 110.25 0 0 0 19.6-48.948c2.573-18.083 1.374-36.733-4.802-54.016a111.86 111.86 0 0 0-5.58-12.983c-1.78-3.506-6.996-4.796-10.261-2.691a7.68 7.68 0 0 0-2.691 10.261q1.568 3.088 2.915 6.278l-.756-1.792a101.15 101.15 0 0 1 6.877 25.539l-.268-1.994a109.229 109.229 0 0 1-.066 28.682l.267-1.994a109.734 109.734 0 0 1-7.554 27.675l.756-1.792a104.212 104.212 0 0 1-6.672 13.098q-1.923 3.186-4.08 6.222c-.632.888-1.283 1.761-1.94 2.631-.855 1.136 1.168-1.483.283-.37-.15.19-.3.38-.452.57q-.681.852-1.382 1.688a93.613 93.613 0 0 1-10.176 10.383q-1.366 1.193-2.778 2.331c-.469.379-.932.773-1.42 1.125.018-.013 1.579-1.2.655-.51-.29.216-.579.435-.87.651q-2.91 2.156-5.974 4.092a103.485 103.485 0 0 1-14.756 7.713l1.792-.756a109.215 109.215 0 0 1-27.597 7.552l1.994-.268a108.154 108.154 0 0 1-28.589.05l1.994.268a99.835 99.835 0 0 1-25.096-6.784l1.792.756a93.643 93.643 0 0 1-13.416-6.991q-3.174-2-6.184-4.248c-.286-.213-.57-.43-.855-.645-.915-.691.658.51.67.518a19.169 19.169 0 0 1-1.534-1.225q-1.454-1.184-2.862-2.422a101.99 101.99 0 0 1-10.493-10.71q-1.213-1.433-2.374-2.91c-.335-.426-.946-1.29.404.53-.177-.24-.362-.475-.541-.713q-.647-.858-1.276-1.728-2.203-3.048-4.188-6.246a109.29 109.29 0 0 1-7.805-15.108l.756 1.791a106.588 106.588 0 0 1-7.34-26.837l.267 1.994a97.866 97.866 0 0 1-.048-25.636l-.268 1.994a94.673 94.673 0 0 1 6.595-23.959l-.757 1.792a101.557 101.557 0 0 1 7.196-13.857q2.065-3.323 4.377-6.484.526-.719 1.063-1.428c.324-.428 1.215-1.494-.306.388.15-.184.293-.374.44-.56q1.269-1.608 2.6-3.165a107.402 107.402 0 0 1 10.883-11.02q1.474-1.293 2.994-2.53.691-.562 1.391-1.113c.187-.147.376-.29.562-.438-1.998 1.59-.555.432-.102.092q3.134-2.348 6.436-4.46a103.644 103.644 0 0 1 15.386-8.109l-1.791.756c7.76-3.258 42.14-10.949 48.394-10.11l-1.994-.267a106.225 106.225 0 0 1 26.72 7.382l-1.792-.756a110.313 110.313 0 0 1 12.6 6.33q3.044 1.783 5.968 3.762 1.383.936 2.738 1.915.677.489 1.346.989c.248.185.494.372.741.558 1.04.779-1.431-1.129-.342-.267a110.843 110.843 0 0 1 10.368 9.253q2.401 2.445 4.637 5.045 1.147 1.335 2.246 2.708c.365.455 1.605 ***********.372.493.747.983 1.114 1.48a97.977 97.977 0 0 1 8.392 13.537c1.793 3.498 6.987 4.802 10.261 2.691a7.677 7.677 0 0 0 2.69-10.261Z"])</script>
  <script>self.__next_f.push([1, "12:T61e,M432.497 512.456a3.78 3.78 0 0 1-2.74-6.552l.26-1.03-.103-.247c-3.48-8.297-25.685 14.834-26.645 22.632a30.029 30.029 0 0 0 .527 10.328 120.392 120.392 0 0 1-10.952-50.003 116.202 116.202 0 0 1 .72-12.963q.598-5.293 1.658-10.51a121.787 121.787 0 0 1 24.151-51.617c6.874.383 12.898-.664 13.48-13.986.103-2.37 1.86-4.421 2.248-6.756a30.72 30.72 0 0 1-1.98.183l-.623.032-.077.004a3.745 3.745 0 0 1-3.076-6.101l.85-1.046c.43-.538.872-1.065 1.302-1.603a1.865 1.865 0 0 0 .14-.161c.495-.613.99-1.216 1.485-1.829a10.83 10.83 0 0 0-3.55-3.432c-4.96-2.904-11.802-.893-15.384 3.593-3.593 4.486-4.271 10.78-3.023 16.385a43.398 43.398 0 0 0 6.003 13.383c-.27.344-.549.677-.818 1.022a122.574 122.574 0 0 0-12.793 20.268c1.016-7.939-11.412-36.608-16.218-42.68-5.773-7.295-17.611-4.112-18.628 5.135l-.03.268q1.072.604 2.097 1.283a5.127 5.127 0 0 1-2.067 9.33l-.104.016c-9.556 13.644 21.077 49.155 28.745 41.182a125.11 125.11 0 0 0-6.735 31.692 118.664 118.664 0 0 0 .086 19.16l-.032-.226c-1.704-13.882-30.931-34.522-39.466-32.803-4.917.99-9.76.765-9.013 5.725l.036.237a34.442 34.442 0 0 1 3.862 1.861q1.07.605 2.096 1.283a5.127 5.127 0 0 1-2.067 9.33l-.104.016-.215.033c-4.35 14.966 27.907 39.12 47.517 31.434h.011a125.075 125.075 0 0 0 8.402 24.528h30.015c.107-.333.204-.678.301-1.011a34.102 34.102 0 0 1-8.305-.495c2.227-2.732 4.454-5.486 6.68-8.219a1.861 1.861 0 0 0 .14-.161c1.13-1.399 2.27-2.787 3.4-4.185v-.002a49.952 49.952 0 0 0-1.463-12.725Zm-34.37-67.613.015-.022-.016.043Zm-6.65 59.932-.257-.58c.01-.42.01-.84 0-1.27 0-.119-.022-.237-.022-.355.097.742.183 1.484.29 2.227Z17:[]\n"])</script>
  <script>self.__next_f.push([1, "3:[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/10e4dd1aec12afdf.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]],[\"$\",\"$L5\",null,{\"buildId\":\"I7HRvCMyxnlsA_D3EvvnB\",\"assetPrefix\":\"\",\"initialCanonicalUrl\":\"/\",\"initialTree\":[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"__PAGE__\",{},[\"$L6\",\"$L7\",null]]},[null,[\"$\",\"html\",null,{\"lang\":\"en\",\"data-theme\":\"light\",\"className\":\"__className_af6c42\",\"children\":[[\"$\",\"head\",null,{\"children\":[[\"$\",\"$L8\",null,{\"strategy\":\"beforeInteractive\",\"data-website-id\":\"66d5711aa2f1fb254ce42c0b\",\"data-domain\":\"codefa.st\",\"src\":\"https://datafa.st/js/script.js\"}],[\"$\",\"noscript\",null,{\"children\":[\"$\",\"img\",null,{\"height\":\"1\",\"width\":\"1\",\"style\":{\"display\":\"none\"},\"src\":\"https://www.facebook.com/tr?id=410683368646128\u0026ev=PageView\u0026noscript=1\"}]}],[\"$\",\"$L8\",null,{\"id\":\"facebook-pixel\",\"strategy\":\"afterInteractive\",\"children\":\"\\n          !function(f,b,e,v,n,t,s)\\n          {if(f.fbq)return;n=f.fbq=function(){n.callMethod?\\n          n.callMethod.apply(n,arguments):n.queue.push(arguments)};\\n          if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';\\n          n.queue=[];t=b.createElement(e);t.async=!0;\\n          t.src=v;s=b.getElementsByTagName(e)[0];\\n          s.parentNode.insertBefore(t,s)}(window, document,'script',\\n          'https://connect.facebook.net/en_US/fbevents.js');\\n          fbq('init', '410683368646128');\\n          fbq('track', 'PageView');\\n        \"}],[\"$\",\"$L8\",null,{\"strategy\":\"afterInteractive\",\"src\":\"https://www.googletagmanager.com/gtag/js?id=G-SHPN0DPZNW\"}],[\"$\",\"$L8\",null,{\"id\":\"google-analytics\",\"strategy\":\"afterInteractive\",\"dangerouslySetInnerHTML\":{\"__html\":\"\\n            window.dataLayer = window.dataLayer || [];\\n            function gtag(){dataLayer.push(arguments);}\\n            gtag('js', new Date());\\n            gtag('config', 'G-SHPN0DPZNW');\\n          \"}}]]}],[\"$\",\"body\",null,{\"children\":[[\"$\",\"$L9\",null,{\"children\":[\"$\",\"$La\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"loading\":\"$undefined\",\"loadingStyles\":\"$undefined\",\"loadingScripts\":\"$undefined\",\"hasLoading\":false,\"error\":\"$b\",\"errorStyles\":[],\"errorScripts\":[],\"template\":[\"$\",\"$Lc\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[\"$\",\"section\",null,{\"className\":\"relative bg-base-100 text-base-content h-screen w-full flex flex-col justify-center gap-8 items-center p-10\",\"children\":[[\"$\",\"div\",null,{\"className\":\"p-6 bg-white rounded-xl\",\"children\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"className\":\"w-56 h-56\",\"viewBox\":\"0 0 860.13137 571.14799\",\"children\":[[\"$\",\"path\",null,{\"fill\":\"#f2f2f2\",\"d\":\"M435.735 160.527c-7.669-12.684-16.757-26.228-30.99-30.37-16.481-4.796-33.412 4.732-47.774 14.135a1392.157 1392.157 0 0 0-123.893 91.283l.043.493 92.451-6.376c22.265-1.535 45.296-3.283 64.972-13.816 7.467-3.996 14.745-9.335 23.206-9.707 10.511-.463 19.677 6.879 26.88 14.549 42.607 45.37 54.937 114.754 102.738 154.616a1516.995 1516.995 0 0 0-107.633-214.807Z\"}],[\"$\",\"path\",null,{\"fill\":\"#e4e4e4\",\"d\":\"$d\"}],[\"$\",\"path\",null,{\"fill\":\"#e4e4e4\",\"d\":\"m560.542 322.285 36.905-13.498 18.323-6.702c5.968-2.183 11.921-4.667 18.09-6.23a28.539 28.539 0 0 1 16.374.208 37.738 37.738 0 0 1 12.77 7.917 103.64 103.64 0 0 1 10.475 11.186c3.99 4.795 7.92 9.64 11.868 14.467q24.442 29.891 48.563 60.042 24.121 30.15 47.92 60.556 23.857 30.48 47.386 61.216 2.882 3.765 5.76 7.534c1.059 1.388 3.449.02 2.374-1.388q-23.702-31.045-47.735-61.835-24.092-30.864-48.516-61.466-24.425-30.601-49.179-60.937-6.167-7.558-12.354-15.099c-3.48-4.24-6.92-8.527-10.737-12.474-7.005-7.245-15.757-13.648-26.234-13.822-6.16-.102-12.121 1.853-17.844 3.923-6.17 2.232-12.325 4.506-18.486 6.76l-37.163 13.592-9.29 3.398c-1.65.603-.937 3.262.73 2.652Z\"}],[\"$\",\"path\",null,{\"fill\":\"#f2f2f2\",\"d\":\"M196.443 170.1c-18.754-9.639-42.771-7.75-60.005 4.291a855.847 855.847 0 0 1 97.37 22.726c-13.282-7.784-23.672-19.98-37.365-27.017ZM136.253 174.358l-3.61 2.935a53.444 53.444 0 0 1 3.795-2.902c-.062-.01-.123-.022-.185-.033ZM661.615 322.42c-3.633-4.422-7.56-9.052-12.994-10.849l-5.073.2a575.436 575.436 0 0 0 153.267 175.221l-135.2-164.572ZM346.15 285.94a37.481 37.481 0 0 0 14.93 20.96c2.82 1.92 6.157 3.761 7.122 7.034a8.379 8.379 0 0 1-.873 6.15 24.884 24.884 0 0 1-3.862 5.041l-.136.512c-6.999-4.147-13.657-9.393-17.523-16.551s-4.405-16.539.342-23.146M579.15 488.94a37.481 37.481 0 0 0 14.93 20.96c2.82 1.92 6.157 3.761 7.122 7.034a8.379 8.379 0 0 1-.873 6.15 24.884 24.884 0 0 1-3.862 5.041l-.136.512c-6.999-4.147-13.657-9.393-17.523-16.551s-4.405-16.539.342-23.146M114.15 474.94a37.481 37.481 0 0 0 14.93 20.96c2.82 1.92 6.157 3.761 7.122 7.034a8.379 8.379 0 0 1-.873 6.15 24.884 24.884 0 0 1-3.862 5.041l-.136.512c-6.999-4.147-13.657-9.393-17.523-16.551s-4.405-16.539.342-23.146\"}],[\"$\",\"circle\",null,{\"cx\":649.249,\"cy\":51,\"r\":51,\"className\":\"fill-primary\"}],[\"$\",\"path\",null,{\"fill\":\"#f0f0f0\",\"d\":\"M741.284 11.87c-24.717-3.34-52.935 10.02-59.341 34.124a21.597 21.597 0 0 0-41.094 2.109l2.83 2.026a372.275 372.275 0 0 0 160.659-.726C787.145 31.334 766 15.21 741.284 11.87ZM635.284 79.87c-24.717-3.34-52.935 10.02-59.341 34.124a21.597 21.597 0 0 0-41.094 2.109l2.83 2.026a372.275 372.275 0 0 0 160.659-.726C681.145 99.334 660 83.21 635.284 79.87Z\"}],[\"$\",\"path\",null,{\"fill\":\"#ccc\",\"d\":\"$e\"}],[\"$\",\"path\",null,{\"fill\":\"#3f3d56\",\"d\":\"$f\"}],[\"$\",\"path\",null,{\"fill\":\"#3f3d56\",\"d\":\"$10\"}],[\"$\",\"path\",null,{\"fill\":\"#3f3d56\",\"d\":\"m719.19 317.71-2.7 95.793-2.686 95.294-1.518 53.883a7.565 7.565 0 0 0 7.5 7.5 7.65 7.65 0 0 0 7.5-7.5l2.7-95.793 2.685-95.294 1.518-53.883a7.565 7.565 0 0 0-7.5-7.5 7.65 7.65 0 0 0-7.5 7.5Z\"}],[\"$\",\"path\",null,{\"d\":\"M459.591 535.935h2.33V429.893h54.328v-2.322H461.92v-44.745h41.956q-.923-1.173-1.899-2.317H461.92v-29.553a65.378 65.378 0 0 0-2.329-.943v30.496H413.94v-37.865c-.782.036-1.552.09-2.329.155v37.71h-36.42v-28.25a54.63 54.63 0 0 0-2.317 1.092v27.158h-30.615v2.317h30.615v44.744h-30.615v2.323h30.615v106.042h2.317V429.893a36.413 36.413 0 0 1 36.42 36.42v69.622h2.33V429.893h45.651Zm-84.4-108.365v-44.744h36.42v44.745Zm38.748 0v-44.744h.914a44.741 44.741 0 0 1 44.738 44.745Z\",\"opacity\":0.2}],[\"$\",\"path\",null,{\"fill\":\"#3f3d56\",\"d\":\"M445.369 504.14a63.059 63.059 0 0 1-20.05 33.7c-.74.64-1.48 1.26-2.25 1.87q-2.805.255-5.57.52c-1.53.14-3.04.29-4.54.43l-.27.03-.19-1.64-.76-6.64a37.623 37.623 0 0 1-3.3-32.44c2.64-7.12 7.42-13.41 12.12-19.65 6.49-8.62 12.8-17.14 13.03-27.65a60.544 60.544 0 0 1 7.9 13.33 16.432 16.432 0 0 0-5.12 3.77c-.41.45-.82 1.08-.54 1.62.24.46.84.57 1.36.63l3.76.39c1 .11 2 .21 3 .32a63.99 63.99 0 0 1 2.45 12.18 61.189 61.189 0 0 1-1.03 19.23Z\"}],[\"$\",\"path\",null,{\"className\":\"fill-primary\",\"d\":\"M478.569 477.93c-5.9 4.29-9.35 10.46-12.03 17.26a16.628 16.628 0 0 0-7.17 4.58c-.41.45-.82 1.08-.54 1.62.24.46.84.57 1.36.63l3.76.39c-2.68 8.04-5.14 16.36-9.88 23.15a36.99 36.99 0 0 1-12.03 10.91 38.492 38.492 0 0 1-4.02 1.99q-7.62.585-14.95 1.25-2.805.255-5.57.52c-1.53.14-3.04.29-4.54.43q-.015-.825 0-1.65a63.304 63.304 0 0 1 15.25-39.86c.45-.52.91-1.03 1.38-1.54a61.792 61.792 0 0 1 16.81-12.7 62.654 62.654 0 0 1 32.17-6.98Z\"}],[\"$\",\"path\",null,{\"className\":\"fill-primary\",\"d\":\"m419.229 535.1-1.15 3.4-.58 1.73c-1.53.14-3.04.29-4.54.43l-.27.03-4.96.51c-.43-.5-.86-1.01-1.28-1.53a62.03 62.03 0 0 1 8.07-87.11c-1.32 6.91.22 13.53 2.75 20.1-.27.11-.53.22-.78.34a16.432 16.432 0 0 0-5.12 3.77c-.41.45-.82 1.08-.54 1.62.24.46.84.57 1.36.63l3.76.39c1 .11 2 .21 3 .32l1.41.15c.07.15.13.29.2.44 2.85 6.18 5.92 12.39 7.65 18.83a43.666 43.666 0 0 1 1.02 4.91 37.604 37.604 0 0 1-10 31.04Z\"}],[\"$\",\"path\",null,{\"fill\":\"#3f3d56\",\"d\":\"$11\"}],[\"$\",\"path\",null,{\"fill\":\"#3f3d56\",\"d\":\"$12\"}],[\"$\",\"circle\",null,{\"cx\":95.249,\"cy\":439,\"r\":11,\"fill\":\"#3f3d56\"}],[\"$\",\"circle\",null,{\"cx\":227.249,\"cy\":559,\"r\":11,\"fill\":\"#3f3d56\"}],[\"$\",\"circle\",null,{\"cx\":728.249,\"cy\":559,\"r\":11,\"fill\":\"#3f3d56\"}],[\"$\",\"circle\",null,{\"cx\":755.249,\"cy\":419,\"r\":11,\"fill\":\"#3f3d56\"}],[\"$\",\"circle\",null,{\"cx\":723.249,\"cy\":317,\"r\":11,\"fill\":\"#3f3d56\"}],[\"$\",\"path\",null,{\"fill\":\"#3f3d56\",\"d\":\"M264.249 419a10.949 10.949 0 1 1-.21-2.16 10.992 10.992 0 0 1 .21 2.16Z\"}],[\"$\",\"circle\",null,{\"cx\":484.249,\"cy\":349,\"r\":11,\"fill\":\"#3f3d56\"}],[\"$\",\"path\",null,{\"fill\":\"#3f3d56\",\"d\":\"M375.249 349a10.949 10.949 0 1 1-.21-2.16 10.992 10.992 0 0 1 .21 2.16ZM233.249 317a10.949 10.949 0 1 1-.21-2.16 10.992 10.992 0 0 1 .21 2.16Z\"}],[\"$\",\"circle\",null,{\"cx\":599.249,\"cy\":443,\"r\":11,\"fill\":\"#3f3d56\"}],[\"$\",\"circle\",null,{\"cx\":426.249,\"cy\":338,\"r\":16,\"fill\":\"#3f3d56\"}],[\"$\",\"path\",null,{\"fill\":\"#cacaca\",\"d\":\"m858.94 570.84-857.75.308a1.19 1.19 0 1 1 0-2.381l857.75-.308a1.19 1.19 0 0 1 0 2.382Z\"}]]}]}],[\"$\",\"p\",null,{\"className\":\"text-lg md:text-xl font-semibold\",\"children\":\"This page doesn't exist 😅\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-wrap gap-4 justify-center\",\"children\":[[\"$\",\"$L13\",null,{\"href\":\"/\",\"className\":\"btn btn-sm btn-outline\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-home w-5 h-5\",\"children\":[[\"$\",\"path\",\"y5dka4\",{\"d\":\"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"}],[\"$\",\"polyline\",\"e2us08\",{\"points\":\"9 22 9 12 15 12 15 22\"}],\"$undefined\"]}],\"Home\"]}],[\"$\",\"$L13\",null,{\"href\":\"/course\",\"className\":\"btn btn-sm btn-outline\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"width\":24,\"height\":24,\"viewBox\":\"0 0 24 24\",\"fill\":\"none\",\"stroke\":\"currentColor\",\"strokeWidth\":2,\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"className\":\"lucide lucide-graduation-cap w-5 h-5\",\"children\":[[\"$\",\"path\",\"j76jl0\",{\"d\":\"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z\"}],[\"$\",\"path\",\"1lu8f3\",{\"d\":\"M22 10v6\"}],[\"$\",\"path\",\"1r8lef\",{\"d\":\"M6 12.5V16a6 3 0 0 0 12 0v-3.5\"}],\"$undefined\"]}],\"Course\"]}],[\"$\",\"$L14\",null,{}]]}]]}],\"notFoundStyles\":[],\"styles\":[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/3545cad29b288095.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\"}]]}]}],[\"$\",\"$L8\",null,{\"src\":\"https://r.wdfl.co/rw.js\",\"data-rewardful\":\"184f84\"}],[\"$\",\"$L8\",null,{\"id\":\"rewardful-queue\",\"strategy\":\"beforeInteractive\",\"children\":\"(function(w,r){w._rwq=r;w[r]=w[r]||function(){(w[r].q=w[r].q||[]).push(arguments)}})(window,'rewardful');\"}]]}]]}],null]],\"initialHead\":[false,\"$L15\"],\"globalErrorComponent\":\"$16\",\"missingSlots\":\"$W17\"}]]\n"])</script>
  <script>self.__next_f.push([1, "15:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"name\":\"theme-color\",\"content\":\"rgb(29, 29, 40)\"}],[\"$\",\"meta\",\"2\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"3\",{\"children\":\"CodeFast | Learn to code in weeks, not months.\"}],[\"$\",\"meta\",\"4\",{\"name\":\"description\",\"content\":\"CodeFast is the best coding course to learn how to turn your idea into an online business, fast.\"}],[\"$\",\"meta\",\"5\",{\"name\":\"application-name\",\"content\":\"CodeFast\"}],[\"$\",\"meta\",\"6\",{\"name\":\"keywords\",\"content\":\"CodeFast\"}],[\"$\",\"link\",\"7\",{\"rel\":\"canonical\",\"href\":\"https://codefa.st\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:title\",\"content\":\"CodeFast | Learn to code in weeks, not months.\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:description\",\"content\":\"CodeFast is the best coding course to learn how to turn your idea into an online business, fast.\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:url\",\"content\":\"https://codefa.st\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:site_name\",\"content\":\"CodeFast | Learn to code in weeks, not months.\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:locale\",\"content\":\"en_US\"}],[\"$\",\"meta\",\"13\",{\"property\":\"og:image:type\",\"content\":\"image/png\"}],[\"$\",\"meta\",\"14\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"15\",{\"property\":\"og:image:height\",\"content\":\"660\"}],[\"$\",\"meta\",\"16\",{\"property\":\"og:image\",\"content\":\"https://codefa.st/opengraph-image.png?a87de873a8229e78\"}],[\"$\",\"meta\",\"17\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:creator\",\"content\":\"@marc_louvion\"}],[\"$\",\"meta\",\"20\",{\"name\":\"twitter:title\",\"content\":\"CodeFast | Learn to code in weeks, not months.\"}],[\"$\",\"meta\",\"21\",{\"name\":\"twitter:description\",\"content\":\"CodeFast is the best coding course to learn how to turn your idea into an online business, fast.\"}],[\"$\",\"meta\",\"22\",{\"name\":\"twitter:image:type\",\"content\":\"image/png\"}],[\"$\",\"meta\",\"23\",{\"name\":\"twitter:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"24\",{\"name\":\"twitter:image:height\",\"content\":\"660\"}],[\"$\",\"meta\",\"25\",{\"name\":\"twitter:image\",\"content\":\"https://codefa.st/twitter-image.png?a87de873a8229e78\"}],[\"$\",\"link\",\"26\",{\"rel\":\"icon\",\"href\":\"/icon.png?ae943832ec23612d\",\"type\":\"image/png\",\"sizes\":\"1080x1080\"}],[\"$\",\"meta\",\"27\",{\"name\":\"next-size-adjust\"}]]\n"])</script>
  <script>self.__next_f.push([1, "6:null\n"])</script>
  <script>self.__next_f.push([1, "18:I[7836,[\"404\",\"static/chunks/a4634e51-fb28818a37ff4dfe.js\",\"250\",\"static/chunks/250-a9b98f1bc94173a5.js\",\"126\",\"static/chunks/126-2fe60ff12b81bcda.js\",\"9\",\"static/chunks/9-80daeb805727ea27.js\",\"908\",\"static/chunks/908-74eb929365e91f0a.js\",\"476\",\"static/chunks/476-8c43f6546ab92211.js\",\"749\",\"static/chunks/749-6c64d4afa6aa0b64.js\",\"385\",\"static/chunks/385-5053c6cd3fa449ec.js\",\"448\",\"static/chunks/448-def631ca554081a4.js\",\"931\",\"static/chunks/app/page-1a1970330440f1bc.js\"],\"\"]\n19:\"$Sreact.suspense\"\n1a:I[1979,[\"404\",\"static/chunks/a4634e51-fb28818a37ff4dfe.js\",\"250\",\"static/chunks/250-a9b98f1bc94173a5.js\",\"126\",\"static/chunks/126-2fe60ff12b81bcda.js\",\"9\",\"static/chunks/9-80daeb805727ea27.js\",\"908\",\"static/chunks/908-74eb929365e91f0a.js\",\"476\",\"static/chunks/476-8c43f6546ab92211.js\",\"749\",\"static/chunks/749-6c64d4afa6aa0b64.js\",\"385\",\"static/chunks/385-5053c6cd3fa449ec.js\",\"448\",\"static/chunks/448-def631ca554081a4.js\",\"931\",\"static/chunks/app/page-1a1970330440f1bc.js\"],\"\"]\n1b:I[1749,[\"404\",\"static/chunks/a4634e51-fb28818a37ff4dfe.js\",\"250\",\"static/chunks/250-a9b98f1bc94173a5.js\",\"126\",\"static/chunks/126-2fe60ff12b81bcda.js\",\"9\",\"static/chunks/9-80daeb805727ea27.js\",\"908\",\"static/chunks/908-74eb929365e91f0a.js\",\"476\",\"static/chunks/476-8c43f6546ab92211.js\",\"749\",\"static/chunks/749-6c64d4afa6aa0b64.js\",\"385\",\"static/chunks/385-5053c6cd3fa449ec.js\",\"448\",\"static/chunks/448-def631ca554081a4.js\",\"931\",\"static/chunks/app/page-1a1970330440f1bc.js\"],\"Image\"]\n1c:I[9221,[\"404\",\"static/chunks/a4634e51-fb28818a37ff4dfe.js\",\"250\",\"static/chunks/250-a9b98f1bc94173a5.js\",\"126\",\"static/chunks/126-2fe60ff12b81bcda.js\",\"9\",\"static/chunks/9-80daeb805727ea27.js\",\"908\",\"static/chunks/908-74eb929365e91f0a.js\",\"476\",\"static/chunks/476-8c43f6546ab92211.js\",\"749\",\"static/chunks/749-6c64d4afa6aa0b64.js\",\"385\",\"static/chunks/385-5053c6cd3fa449ec.js\",\"448\",\"static/chunks/448-def631ca554081a4.js\",\"931\",\"static/chunks/app/page-1a1970330440f1bc.js\"],\"\"]\n1d:I[5912,[\"404\",\"static/chunks/a4634e51-fb28818a37ff4dfe.js\",\"250\",\"static/chunks/250-a9b98f"])</script>
  <script>self.__next_f.push([1, "1bc94173a5.js\",\"126\",\"static/chunks/126-2fe60ff12b81bcda.js\",\"9\",\"static/chunks/9-80daeb805727ea27.js\",\"908\",\"static/chunks/908-74eb929365e91f0a.js\",\"476\",\"static/chunks/476-8c43f6546ab92211.js\",\"749\",\"static/chunks/749-6c64d4afa6aa0b64.js\",\"385\",\"static/chunks/385-5053c6cd3fa449ec.js\",\"448\",\"static/chunks/448-def631ca554081a4.js\",\"931\",\"static/chunks/app/page-1a1970330440f1bc.js\"],\"\"]\n1f:I[9665,[\"404\",\"static/chunks/a4634e51-fb28818a37ff4dfe.js\",\"250\",\"static/chunks/250-a9b98f1bc94173a5.js\",\"126\",\"static/chunks/126-2fe60ff12b81bcda.js\",\"9\",\"static/chunks/9-80daeb805727ea27.js\",\"908\",\"static/chunks/908-74eb929365e91f0a.js\",\"476\",\"static/chunks/476-8c43f6546ab92211.js\",\"749\",\"static/chunks/749-6c64d4afa6aa0b64.js\",\"385\",\"static/chunks/385-5053c6cd3fa449ec.js\",\"448\",\"static/chunks/448-def631ca554081a4.js\",\"931\",\"static/chunks/app/page-1a1970330440f1bc.js\"],\"\"]\n23:I[5035,[\"404\",\"static/chunks/a4634e51-fb28818a37ff4dfe.js\",\"250\",\"static/chunks/250-a9b98f1bc94173a5.js\",\"126\",\"static/chunks/126-2fe60ff12b81bcda.js\",\"9\",\"static/chunks/9-80daeb805727ea27.js\",\"908\",\"static/chunks/908-74eb929365e91f0a.js\",\"476\",\"static/chunks/476-8c43f6546ab92211.js\",\"749\",\"static/chunks/749-6c64d4afa6aa0b64.js\",\"385\",\"static/chunks/385-5053c6cd3fa449ec.js\",\"448\",\"static/chunks/448-def631ca554081a4.js\",\"931\",\"static/chunks/app/page-1a1970330440f1bc.js\"],\"\"]\n25:I[5840,[\"404\",\"static/chunks/a4634e51-fb28818a37ff4dfe.js\",\"250\",\"static/chunks/250-a9b98f1bc94173a5.js\",\"126\",\"static/chunks/126-2fe60ff12b81bcda.js\",\"9\",\"static/chunks/9-80daeb805727ea27.js\",\"908\",\"static/chunks/908-74eb929365e91f0a.js\",\"476\",\"static/chunks/476-8c43f6546ab92211.js\",\"749\",\"static/chunks/749-6c64d4afa6aa0b64.js\",\"385\",\"static/chunks/385-5053c6cd3fa449ec.js\",\"448\",\"static/chunks/448-def631ca554081a4.js\",\"931\",\"static/chunks/app/page-1a1970330440f1bc.js\"],\"\"]\n26:I[1965,[\"404\",\"static/chunks/a4634e51-fb28818a37ff4dfe.js\",\"250\",\"static/chunks/250-a9b98f1bc94173a5.js\",\"126\",\"static/chunks/126-2fe60ff12b81bcda.js\",\"9\",\"static/chunks/9-80daeb805727ea27.js\",\"908\",\"static/chu"])</script>
  <script>self.__next_f.push([1, "nks/908-74eb929365e91f0a.js\",\"476\",\"static/chunks/476-8c43f6546ab92211.js\",\"749\",\"static/chunks/749-6c64d4afa6aa0b64.js\",\"385\",\"static/chunks/385-5053c6cd3fa449ec.js\",\"448\",\"static/chunks/448-def631ca554081a4.js\",\"931\",\"static/chunks/app/page-1a1970330440f1bc.js\"],\"\"]\n27:I[747,[\"404\",\"static/chunks/a4634e51-fb28818a37ff4dfe.js\",\"250\",\"static/chunks/250-a9b98f1bc94173a5.js\",\"126\",\"static/chunks/126-2fe60ff12b81bcda.js\",\"9\",\"static/chunks/9-80daeb805727ea27.js\",\"908\",\"static/chunks/908-74eb929365e91f0a.js\",\"476\",\"static/chunks/476-8c43f6546ab92211.js\",\"749\",\"static/chunks/749-6c64d4afa6aa0b64.js\",\"385\",\"static/chunks/385-5053c6cd3fa449ec.js\",\"448\",\"static/chunks/448-def631ca554081a4.js\",\"931\",\"static/chunks/app/page-1a1970330440f1bc.js\"],\"\"]\n1e:{\"src\":\"/_next/static/media/epictravelphoto.ce3f492d.gif\",\"height\":450,\"width\":800,\"blurWidth\":0,\"blurHeight\":0}\n20:{\"src\":\"/_next/static/media/macbook.8ed77199.png\",\"height\":453,\"width\":687,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAAJ1BMVEXs7e2nq65tcHRydXlnaW2vs7aMj5OWmp6RlZmvs7e8vcB6fYHBw8UG6FvNAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAKklEQVR4nAXBhwEAMAjDMIcwuv6/txKoapeANdIM0C31AuwIXziRmeH3AQxuAJeiZl7jAAAAAElFTkSuQmCC\",\"blurWidth\":8,\"blurHeight\":5}\n21:{\"src\":\"/_next/static/media/indielaunch.4c5f03b8.gif\",\"height\":450,\"width\":800,\"blurWidth\":0,\"blurHeight\":0}\n22:{\"src\":\"/_next/static/media/uireplicator.3c381e2f.gif\",\"height\":450,\"width\":800,\"blurWidth\":0,\"blurHeight\":0}\n24:{\"src\":\"/_next/static/media/nerdmask.0852e602.gif\",\"height\":450,\"width\":800,\"blurWidth\":0,\"blurHeight\":0}\n28:{\"src\":\"/_next/static/media/wizard.671ce364.png\",\"height\":768,\"width\":768,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAS1BMVEVMaXFcTW5RKGZ3UEY9LU2ZgnVMO1wmGDd1XlQ1Fk3/7mfkv1PktUZQLHpmP6EoETl5SK9sO6H93VpuTlf/31qwr7LEljy4qZq4qpvESUCQAAAAFXRSTlMAqlKv/vykRvoQp/xRhTRZydqZ+pMbDWOpAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPUlEQVR4nB2JRw6AMAzA3Jl0sGnh/y9FwRdLNsCyYhQ5dinAFlOKYim/T/6fG8OZ/aWqHgiztRmAftd6dj42QwG1h2g3FQAAAABJRU5ErkJggg==\",\"blurWidt"])</script>
  <script>self.__next_f.push([1, "h\":8,\"blurHeight\":8}\n29:{\"src\":\"/_next/static/media/shipfast-icon_transparent.2aa1e5a5.png\",\"height\":512,\"width\":512,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAQlBMVEVMaXG2iBBUPggcEAQAAAB2VwsAAAAdFgKheA87LAWOaQ2GZAw+MATdoxTHnRRBMQfQnhN2Vwr/1Bv9yRnzuRe+kBIcVWd7AAAAEnRSTlMA53Y+CKkYK8Cbwb41/Ppt95IauPaQAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nCWLSQ7AIBDDTAsMaxeW/38VMeQSK1YASkTjTdC2qR34ezf5Be9Cbc+9J/nGpU6mOy+JFlgssQFEVvqcWAAAAABJRU5ErkJggg==\",\"blurWidth\":8,\"blurHeight\":8}\n"])</script>
  <script>self.__next_f.push([1, "7:[[\"$\",\"$L18\",null,{}],[\"$\",\"div\",null,{\"data-theme\":\"light\",\"children\":[[\"$\",\"$19\",null,{\"fallback\":[\"$\",\"div\",null,{\"className\":\"bg-base-100 h-[64px] md:h-[74px]\"}],\"children\":[\"$\",\"$L1a\",null,{}]}],[\"$\",\"main\",null,{\"className\":\"max-w-[175rem] mx-auto\",\"children\":[[\"$\",\"section\",null,{\"className\":\" bg-base-100 flex flex-col items-center justify-center gap-16 lg:gap-24 py-16 lg:pb-32 lg:pt-16\",\"children\":[[\"$\",\"div\",null,{\"className\":\"max-w-7xl mx-auto flex flex-col gap-10 items-center justify-center text-center px-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex gap-3 items-center mx-auto -mb-3\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/marc.be50937c.png\",\"height\":240,\"width\":240,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAASFBMVEX/wGnQnF2zdXCbZF/6tWL8rlzkrGP/xWz+uGP5u2e1e3tkQzRPLCi7g4fqtGXFkWWfaXW9h2DrtW+7klSuhVCFWVe1jE8oFBBS7RYpAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAP0lEQVR4nAXBBwKAIAwEsCvdBcTt/39qAg0QSSjC+vi6BfwdrY3b4fTM6pcDZ81iAzJb8eEJwdp5g0AjySD6A0tbAf9G6c+eAAAAAElFTkSuQmCC\",\"blurWidth\":8,\"blurHeight\":8},\"alt\":\"Marc\",\"className\":\"w-8 md:w-10 rounded-full border shadow-sm\"}],[\"$\",\"div\",null,{\"className\":\"md:text-lg font-medium\",\"children\":\"By Marc Lou\"}]]}],[\"$\",\"h1\",null,{\"className\":\"font-black text-5xl lg:text-6xl lg:text-[4rem] tracking-tight lg:tracking-[-0.035em] \",\"children\":[\"$\",\"$L1c\",null,{\"children\":[\"Learn to code\",\" \",[\"$\",\"span\",null,{\"className\":\"inline-block decoration-primary relative \",\"children\":[[\"$\",\"span\",null,{\"className\":\"relative z-10\",\"children\":\"in weeks,\"}],[\"$\",\"span\",null,{\"className\":\"bottom-0  absolute bg-accent-pink h-4 md:h-6 md:-bottom-0.5 -inset-x-2 \"}]]}],\" not months\"]}]}],[\"$\",\"p\",null,{\"className\":\"text-lg lg:text-2xl text-base-content-secondary leading-relaxed lg:leading-relaxed max-w-2xl mx-auto\",\"children\":\"Everything you need to build your SaaS or any online business—even as a complete beginner.\"}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"$L1d\",null,{\"extraStyle\":\"btn-pink btn-hover-pink btn-lg !text-lg btn-wide\",\"text\":\"Get instant access\"}],[\"$\",\"div\",null,{\"className\":\"flex text-base-content-secondary items-center justify-center gap-2\",\"children\":[\"$\",\"p\",null,{\"className\":\"italic text-center\",\"children\":[[\"$\",\"span\",null,{\"className\":\"font-semibold text-base-content\",\"children\":\"277\"}],\" \",\"entrepreneurs love the course\"]}]}]]}]]}],[\"$\",\"div\",null,{\"className\":\" w-full  overflow-hidden\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex flex-col items-center text-center gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/gear.1c9850d2.png\",\"height\":768,\"width\":768,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAVFBMVEVMaXGziEeBak3Ur22idTmecTWUckZ/f3+VcUGncC7/76LSrGCPeVe5llmklXrqzI67sZiWaC6ZcT5qXU5uZFaojV+qj2CymXC0nXCRgGeOiHyqfkDlvDuXAAAAGXRSTlMAaLRNbkfnAtUiISX2/vxg/Sx+o87p5t7ewkbCwwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAEFJREFUeJwFwYcBgCAQALFDytPBLrD/niYIsbWIgH7n/DSke/Y+n4QatZQ6FGbktfIwBH95f/oA1m2bsyAEc+zID3GqAuLJX3PeAAAAAElFTkSuQmCC\",\"blurWidth\":8,\"blurHeight\":8},\"alt\":\"Learn fundamentals\",\"className\":\"w-48 scale-[0.60] translate-y-4\",\"priority\":true,\"width\":192,\"height\":192}],[\"$\",\"div\",null,{\"className\":\"absolute -bottom-6 left-1/2 -translate-x-1/2\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-4 h-4 bg-primary rounded-full hidden lg:block\"}],[\"$\",\"div\",null,{\"className\":\"hidden lg:block absolute top-2 left-[-2000px] right-[-2000px] h-[1px] bg-primary\"}]]}]}]]}],[\"$\",\"div\",null,{\"className\":\"lg:pt-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-bold text-lg\",\"children\":\"Day 1\"}],[\"$\",\"p\",null,{\"className\":\"text-base-content-secondary max-w-[20rem] mx-auto\",\"children\":\"Learn the fundamentals of coding\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col items-center text-center gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/server.41b0b400.png\",\"height\":768,\"width\":768,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAQlBMVEVMaXFmcXbC0eCMm6c9QkBfa3cqNzwzPEI/SlY1PjpLVmJiaWnH0t2qqriqudBygIxpdXw3QUFxf4pJVV5YZW+UoavePkYVAAAAEXRSTlMA/jJr/W/7OzD8WPMuEiFv9W1QzLIAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA7SURBVHicLcrBEYAgDADBAwMkimJQ+2/VQfntY2GvMR4Ay3OGTQd6dilA6yI+kIL5j9vsw3rNgxZVeAE/HgG5+zk+xgAAAABJRU5ErkJggg==\",\"blurWidth\":8,\"blurHeight\":8},\"alt\":\"Build SaaS\",\"className\":\"w-48\",\"priority\":true,\"width\":192,\"height\":192}],[\"$\",\"div\",null,{\"className\":\"absolute -bottom-6 left-1/2 -translate-x-1/2\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative\",\"children\":[\"$\",\"div\",null,{\"className\":\"w-4 h-4 bg-primary rounded-full hidden lg:block\"}]}]}]]}],[\"$\",\"div\",null,{\"className\":\"lg:pt-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-bold text-lg\",\"children\":\"Day 4\"}],[\"$\",\"p\",null,{\"className\":\"text-base-content-secondary max-w-[20rem] mx-auto\",\"children\":\"Log in users and save in database\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col items-center text-center gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/credit-card-reader.d1cc3127.png\",\"height\":768,\"width\":768,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAb1BMVEVMaXEYHSV1gYVudX2itLR0hpCYo6suISxmVVVkfY0xOkFVa3yXnaEnLDGptLvOzs86QUXe3t4qMjfv7++Xm6FjY2WzqJ5cfoKEj282PkciMDd7gYZrYGVVYGeKmZ2dmZVeZGhHXG0+Qkdscnawmpxih9M6AAAAHnRSTlMALkPUBv78HA/+q/zrzf3rRtJgELHk/H/pPUrydMzM5PxcAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAQ0lEQVR4nB3LRxKAIBAAwVGBBXPOgun/b7TKvjegOgsQ9U2Vx0D7lO5KLNS3dpmeQBWn8X4WkDSM5t0BGcJ2LH9cBT5lDQMOdnGHzwAAAABJRU5ErkJggg==\",\"blurWidth\":8,\"blurHeight\":8},\"alt\":\"AI coding\",\"className\":\"w-48 scale-75 translate-y-4\",\"priority\":true,\"width\":192,\"height\":192}],[\"$\",\"div\",null,{\"className\":\"absolute -bottom-6 left-1/2 -translate-x-1/2\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative\",\"children\":[\"$\",\"div\",null,{\"className\":\"w-4 h-4 bg-primary rounded-full hidden lg:block\"}]}]}]]}],[\"$\",\"div\",null,{\"className\":\"lg:pt-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-bold text-lg\",\"children\":\"Day 9\"}],[\"$\",\"p\",null,{\"className\":\"text-base-content-secondary max-w-[20rem] mx-auto\",\"children\":\"Set up subscription payments\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col items-center text-center gap-4\",\"children\":[[\"$\",\"div\",null,{\"className\":\"relative\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/wizard.671ce364.png\",\"height\":768,\"width\":768,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAS1BMVEVMaXFcTW5RKGZ3UEY9LU2ZgnVMO1wmGDd1XlQ1Fk3/7mfkv1PktUZQLHpmP6EoETl5SK9sO6H93VpuTlf/31qwr7LEljy4qZq4qpvESUCQAAAAFXRSTlMAqlKv/vykRvoQp/xRhTRZydqZ+pMbDWOpAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAPUlEQVR4nB2JRw6AMAzA3Jl0sGnh/y9FwRdLNsCyYhQ5dinAFlOKYim/T/6fG8OZ/aWqHgiztRmAftd6dj42QwG1h2g3FQAAAABJRU5ErkJggg==\",\"blurWidth\":8,\"blurHeight\":8},\"alt\":\"Launch product\",\"className\":\"w-48\",\"priority\":true,\"width\":192,\"height\":192}],[\"$\",\"div\",null,{\"className\":\"absolute -bottom-6 left-1/2 -translate-x-1/2\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative\",\"children\":[\"$\",\"div\",null,{\"className\":\"w-4 h-4 bg-primary rounded-full hidden lg:block\"}]}]}]]}],[\"$\",\"div\",null,{\"className\":\"lg:pt-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"font-bold text-lg\",\"children\":\"Day 14\"}],[\"$\",\"p\",null,{\"className\":\"text-base-content-secondary max-w-[20rem] mx-auto\",\"children\":\"Launch your idea!\"}]]}]]}]]}]}]]}],[\"$\",\"section\",null,{\"data-theme\":\"dark\",\"className\":\"text-center py-32 md:py-48 space-y-20 px-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"space-y-8 max-w-5xl mx-auto\",\"children\":[\"$\",\"h2\",null,{\"className\":\"text-4xl md:text-5xl font-extrabold !leading-tight\",\"children\":[\"$\",\"$L1c\",null,{\"children\":[\"Coding courses are designed for\",\" \",[\"$\",\"span\",null,{\"className\":\"italic text-red-300\",\"children\":\"software engineers\"}],\", not \",[\"$\",\"span\",null,{\"className\":\"italic text-green-300\",\"children\":\"entrepreneurs\"}]]}]}]}],[\"$\",\"div\",null,{\"className\":\"grid md:grid-cols-2 gap-8 max-w-4xl mx-auto text-left\",\"children\":[[\"$\",\"div\",null,{\"className\":\"card rounded-box bg-base-100  border border-red-300 bg-red-950/10\",\"children\":[\"$\",\"div\",null,{\"className\":\"card-body space-y-4\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-2xl font-bold flex items-center justify-between text-red-300\",\"children\":[\"Coding as an employee\",\" \",[\"$\",\"svg\",null,{\"className\":\"size-8\",\"viewBox\":\"0 0 67 67\",\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[[\"$\",\"path\",null,{\"d\":\"M33.3848 61.208C48.75 61.208 61.2061 48.752 61.2061 33.3867C61.2061 18.0214 48.75 5.56543 33.3848 5.56543C18.0195 5.56543 5.56348 18.0214 5.56348 33.3867C5.56348 48.752 18.0195 61.208 33.3848 61.208Z\",\"className\":\"fill-red-300 stroke-red-300\",\"strokeWidth\":\"5.56426\",\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\"}],[\"$\",\"path\",null,{\"d\":\"M41.7318 25.0391L25.0391 41.7318\",\"className\":\"stroke-red-900\",\"strokeWidth\":\"5.56426\",\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\"}],[\"$\",\"path\",null,{\"d\":\"M25.0391 25.0391L41.7318 41.7318\",\"className\":\"stroke-red-900\",\"strokeWidth\":\"5.56426\",\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\"}]]}]]}],[\"$\",\"ul\",null,{\"className\":\"space-y-3 text-base-content-secondary\",\"children\":[[\"$\",\"li\",null,{\"children\":\"• Invert binary trees\"}],[\"$\",\"li\",null,{\"children\":\"• Master 47 sorting algorithms you'll never implement\"}],[\"$\",\"li\",null,{\"children\":\"• Memorize Big O notation to impress your interviewer\"}],[\"$\",\"li\",null,{\"children\":\"• Read documentation longer than The Lord of the Rings\"}],[\"$\",\"li\",null,{\"children\":\"• Write complex code when a simple AI prompt would do\"}]]}]]}]}],[\"$\",\"div\",null,{\"className\":\"card rounded-box  bg-base-100  border border-green-300 bg-green-950/10\",\"children\":[\"$\",\"div\",null,{\"className\":\"card-body space-y-4\",\"children\":[[\"$\",\"h3\",null,{\"className\":\"text-2xl font-bold flex items-center justify-between text-green-300\",\"children\":[\"Coding as an entrepreneur\",\" \",[\"$\",\"svg\",null,{\"className\":\"size-8\",\"viewBox\":\"0 0 67 67\",\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[[\"$\",\"path\",null,{\"d\":\"M33.3856 61.207C48.7509 61.207 61.2069 48.751 61.2069 33.3857C61.2069 18.0205 48.7509 5.56445 33.3856 5.56445C18.0203 5.56445 5.56433 18.0205 5.56433 33.3857C5.56433 48.751 18.0203 61.207 33.3856 61.207Z\",\"className\":\"fill-green-300 stroke-green-300\",\"strokeWidth\":\"5.56426\",\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\"}],[\"$\",\"path\",null,{\"d\":\"M25.0391 33.3865L30.6034 38.9508L41.7319 27.8223\",\"className\":\"stroke-green-900\",\"strokeWidth\":\"5.56426\",\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\"}]]}]]}],[\"$\",\"ul\",null,{\"className\":\"space-y-3 text-base-content-secondary\",\"children\":[[\"$\",\"li\",null,{\"children\":\"• Learn only the fundamentals\"}],[\"$\",\"li\",null,{\"children\":\"• Use AI to code for you\"}],[\"$\",\"li\",null,{\"children\":\"• Keep learning on the fly\"}]]}]]}]}]]}]]}],[\"$\",\"section\",null,{\"className\":\"\",\"children\":[\"$\",\"div\",null,{\"className\":\"px-8 pt-32 md:pt-48 pb-24 md:pb-36 space-y-16 \",\"children\":[[\"$\",\"div\",null,{\"className\":\"text-xl md:text-2xl  text-red-700 text-center flex flex-col md:flex-row items-center justify-center gap-3 pb-24\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-4xl\",\"children\":\"🚫\"}],\" \",[\"$\",\"p\",null,{\"className\":\"italic\",\"children\":\"Instead of watching 100 hours of \\\"JavaScript Basics\\\" videos...\"}]]}],[\"$\",\"div\",null,{\"className\":\"max-sm:-mx-4\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative flex justify-center items-center w-full max-md:max-w-[400px] md:w-[650px] aspect-[1/1] md:aspect-[3/2] rounded-full mx-auto\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute top-2 flex flex-col items-center\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/book.3752ab35.png\",\"height\":768,\"width\":768,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAATlBMVEVMaXGmeUK0lWhuUCvx5sLcxZju2q7u4bzk1a7Yx5717MfCrIXNuZF8XjqPcEpyUzDs4LzXx6Lm1qy5kmT46r/cuonq3LX//de5pHz57cRzP+bHAAAAF3RSTlMACGk0k0JL/fr8+v7+hHWZf2DbIeIl+bMsdOMAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA9SURBVHicPcs3EsAgEANAEXVHdIAB//+j7th+gSP6+/IBMGMp12MwSRXKi/gllcQAW8reOVu42npv1Z39A0T1Af1LFWRCAAAAAElFTkSuQmCC\",\"blurWidth\":8,\"blurHeight\":8},\"alt\":\"Book\",\"className\":\"size-[70px] md:size-24 -rotate-3\"}],[\"$\",\"span\",null,{\"className\":\"text-lg md:text-xl font-semibold\",\"children\":\"Learn what you need\"}]]}],[\"$\",\"div\",null,{\"className\":\"absolute top-12 left-12 rotate-[135deg]\",\"children\":[\"$\",\"svg\",null,{\"className\":\"size-12 md:size-14 fill-base-content\",\"viewBox\":\"0 0 251 81\",\"children\":[\"$\",\"g\",null,{\"children\":[\"$\",\"path\",null,{\"d\":\"M14.4435 26.0257C16.3478 34.2205 18.0405 42.2052 20.1564 51.2405C14.6551 50.6101 11.4813 47.2481 10.2118 43.4659C6.40317 32.1193 2.80615 20.5625 0.267089 8.79558C-1.21404 2.07164 3.65251 -1.50048 10.2118 0.600755C21.2144 3.96273 32.0054 7.95508 43.0081 11.7373C43.6428 11.9474 44.4892 12.1576 44.7008 12.5778C45.7587 14.0487 46.3935 15.7296 47.2398 17.4106C45.7587 18.041 44.2776 19.5119 43.0081 19.3017C38.5647 18.6714 34.3329 17.6208 30.1011 16.7803C27.7737 16.36 25.6577 15.7297 22.2723 16.7803C24.5998 19.3018 26.9273 22.0333 29.2548 24.5548C79.6129 74.5642 155.15 85.0703 217.781 51.2405C225.821 46.8279 233.227 41.5748 241.055 36.742C243.806 35.061 246.557 33.5901 249.307 31.9092C249.942 32.3294 250.365 32.9598 251 33.38C250.365 35.2711 250.154 37.7926 248.673 39.0533C244.018 43.4659 239.363 47.8785 234.073 51.6607C181.599 89.4829 108.601 90.9538 52.1064 54.8126C41.3154 47.8785 31.7938 39.0533 21.8492 31.0686C19.7333 29.3876 18.0406 27.4966 16.1363 25.6054C15.7131 25.3953 15.0783 25.6054 14.4435 26.0257Z\"}]}]}]}],[\"$\",\"div\",null,{\"className\":\"absolute right-2 flex flex-col items-center\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/bolt.2105afa3.png\",\"height\":768,\"width\":768,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEVMaXHTexL421nlpyH0zELpox7uuCr33l3zxjT30k388HHq6l3CAAAAC3RSTlMACeNMrimXq4Kn6p3XkNYAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAsSURBVHicLYvJDQAwDIPI1Wv/gas2/hiEZADK3sLOph2vFp9dyCUZIQmd+LwMYQBK2GuuGQAAAABJRU5ErkJggg==\",\"blurWidth\":8,\"blurHeight\":8},\"alt\":\"Bolt\",\"className\":\"size-[70px] md:size-24 rotate-3\"}],[\"$\",\"span\",null,{\"className\":\"text-lg md:text-xl font-semibold\",\"children\":\"Build features\"}]]}],[\"$\",\"div\",null,{\"className\":\"absolute top-12 right-12 rotate-[-135deg]\",\"children\":[\"$\",\"svg\",null,{\"className\":\"size-12 md:size-14 fill-base-content\",\"viewBox\":\"0 0 251 81\",\"children\":[\"$\",\"g\",null,{\"children\":[\"$\",\"path\",null,{\"d\":\"M14.4435 26.0257C16.3478 34.2205 18.0405 42.2052 20.1564 51.2405C14.6551 50.6101 11.4813 47.2481 10.2118 43.4659C6.40317 32.1193 2.80615 20.5625 0.267089 8.79558C-1.21404 2.07164 3.65251 -1.50048 10.2118 0.600755C21.2144 3.96273 32.0054 7.95508 43.0081 11.7373C43.6428 11.9474 44.4892 12.1576 44.7008 12.5778C45.7587 14.0487 46.3935 15.7296 47.2398 17.4106C45.7587 18.041 44.2776 19.5119 43.0081 19.3017C38.5647 18.6714 34.3329 17.6208 30.1011 16.7803C27.7737 16.36 25.6577 15.7297 22.2723 16.7803C24.5998 19.3018 26.9273 22.0333 29.2548 24.5548C79.6129 74.5642 155.15 85.0703 217.781 51.2405C225.821 46.8279 233.227 41.5748 241.055 36.742C243.806 35.061 246.557 33.5901 249.307 31.9092C249.942 32.3294 250.365 32.9598 251 33.38C250.365 35.2711 250.154 37.7926 248.673 39.0533C244.018 43.4659 239.363 47.8785 234.073 51.6607C181.599 89.4829 108.601 90.9538 52.1064 54.8126C41.3154 47.8785 31.7938 39.0533 21.8492 31.0686C19.7333 29.3876 18.0406 27.4966 16.1363 25.6054C15.7131 25.3953 15.0783 25.6054 14.4435 26.0257Z\"}]}]}]}],[\"$\",\"div\",null,{\"className\":\"absolute bottom-2 flex flex-col items-center\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/rocket.e5ec330f.png\",\"height\":768,\"width\":768,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAP1BMVEVMaXGUdl6DAgCJbmqeGg6nmpCcjH+OX1W4mIF5ZWHKgHGNnaDd7u7jYT//onq2OyOVODPGqoamqKLh8PDNs6ncHluGAAAAEXRSTlMAf1v4O8FOCnE35uOK3G7XNmw6hMkAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAsSURBVHicY2CAAHYIxcfLAubxiPCDRFi4hLk5QQIcQswQWpCRCayAlQ1VIwAlLAD0cnZI2QAAAABJRU5ErkJggg==\",\"blurWidth\":8,\"blurHeight\":8},\"alt\":\"Rocket\",\"className\":\"size-[70px] md:size-24 -rotate-6\"}],[\"$\",\"span\",null,{\"className\":\"text-lg md:text-xl font-semibold\",\"children\":\"Launch\"}]]}],[\"$\",\"div\",null,{\"className\":\"absolute bottom-12 right-12 rotate-[-55deg]\",\"children\":[\"$\",\"svg\",null,{\"className\":\"size-12 md:size-14 fill-base-content\",\"viewBox\":\"0 0 251 81\",\"children\":[\"$\",\"g\",null,{\"children\":[\"$\",\"path\",null,{\"d\":\"M14.4435 26.0257C16.3478 34.2205 18.0405 42.2052 20.1564 51.2405C14.6551 50.6101 11.4813 47.2481 10.2118 43.4659C6.40317 32.1193 2.80615 20.5625 0.267089 8.79558C-1.21404 2.07164 3.65251 -1.50048 10.2118 0.600755C21.2144 3.96273 32.0054 7.95508 43.0081 11.7373C43.6428 11.9474 44.4892 12.1576 44.7008 12.5778C45.7587 14.0487 46.3935 15.7296 47.2398 17.4106C45.7587 18.041 44.2776 19.5119 43.0081 19.3017C38.5647 18.6714 34.3329 17.6208 30.1011 16.7803C27.7737 16.36 25.6577 15.7297 22.2723 16.7803C24.5998 19.3018 26.9273 22.0333 29.2548 24.5548C79.6129 74.5642 155.15 85.0703 217.781 51.2405C225.821 46.8279 233.227 41.5748 241.055 36.742C243.806 35.061 246.557 33.5901 249.307 31.9092C249.942 32.3294 250.365 32.9598 251 33.38C250.365 35.2711 250.154 37.7926 248.673 39.0533C244.018 43.4659 239.363 47.8785 234.073 51.6607C181.599 89.4829 108.601 90.9538 52.1064 54.8126C41.3154 47.8785 31.7938 39.0533 21.8492 31.0686C19.7333 29.3876 18.0406 27.4966 16.1363 25.6054C15.7131 25.3953 15.0783 25.6054 14.4435 26.0257Z\"}]}]}]}],[\"$\",\"div\",null,{\"className\":\"absolute left-2 flex flex-col items-center\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/bulb.b7adce6e.png\",\"height\":768,\"width\":768,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAM1BMVEVMaXG0n2rUtmbTv4zUyK/LsGjMt4dDOSWSch6pm4PZvGS3m1C1mFJxbGPsylX/+br/8Xsb8sW7AAAADnRSTlMAGMXJt2m1Gr4v1/PfEOMaVSQAAAAJcEhZcwAACxMAAAsTAQCanBgAAAAwSURBVHicY2BAAE4WFk4wg5mfnw3M4BIQYAIzWPn4WMEMRm4eqGoODiiDnZ2BgQEAHKIAyrV9RTgAAAAASUVORK5CYII=\",\"blurWidth\":8,\"blurHeight\":8},\"alt\":\"Bulb\",\"className\":\"size-[70px] md:size-24\"}],[\"$\",\"span\",null,{\"className\":\"text-lg md:text-xl font-semibold\",\"children\":\"Get feedback\"}]]}],[\"$\",\"div\",null,{\"className\":\"absolute bottom-12 left-12 rotate-[45deg]\",\"children\":[\"$\",\"svg\",null,{\"className\":\"size-12 md:size-14 fill-base-content\",\"viewBox\":\"0 0 251 81\",\"children\":[\"$\",\"g\",null,{\"children\":[\"$\",\"path\",null,{\"d\":\"M14.4435 26.0257C16.3478 34.2205 18.0405 42.2052 20.1564 51.2405C14.6551 50.6101 11.4813 47.2481 10.2118 43.4659C6.40317 32.1193 2.80615 20.5625 0.267089 8.79558C-1.21404 2.07164 3.65251 -1.50048 10.2118 0.600755C21.2144 3.96273 32.0054 7.95508 43.0081 11.7373C43.6428 11.9474 44.4892 12.1576 44.7008 12.5778C45.7587 14.0487 46.3935 15.7296 47.2398 17.4106C45.7587 18.041 44.2776 19.5119 43.0081 19.3017C38.5647 18.6714 34.3329 17.6208 30.1011 16.7803C27.7737 16.36 25.6577 15.7297 22.2723 16.7803C24.5998 19.3018 26.9273 22.0333 29.2548 24.5548C79.6129 74.5642 155.15 85.0703 217.781 51.2405C225.821 46.8279 233.227 41.5748 241.055 36.742C243.806 35.061 246.557 33.5901 249.307 31.9092C249.942 32.3294 250.365 32.9598 251 33.38C250.365 35.2711 250.154 37.7926 248.673 39.0533C244.018 43.4659 239.363 47.8785 234.073 51.6607C181.599 89.4829 108.601 90.9538 52.1064 54.8126C41.3154 47.8785 31.7938 39.0533 21.8492 31.0686C19.7333 29.3876 18.0406 27.4966 16.1363 25.6054C15.7131 25.3953 15.0783 25.6054 14.4435 26.0257Z\"}]}]}]}]]}]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col items-center justify-center gap-8 md:gap-12 text-center\",\"children\":[[\"$\",\"div\",null,{\"className\":\"space-y-6 md:space-y-8\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"text-4xl sm:text-5xl md:text-7xl font-black leading-tight tracking-tight\",\"children\":[\"$\",\"$L1c\",null,{\"children\":[\"Start a learning\",\" \",[\"$\",\"span\",null,{\"className\":\"inline-block decoration-primary relative \",\"children\":[[\"$\",\"span\",null,{\"className\":\"relative z-10\",\"children\":\"flywheel\"}],[\"$\",\"span\",null,{\"className\":\"bottom-0  absolute bg-accent-green h-4 md:h-6 md:-bottom-0.5 -inset-x-2 \"}]]}]]}]}],[\"$\",\"div\",null,{\"className\":\"text-xl md:text-2xl max-w-xl mx-auto !leading-relaxed space-y-2\",\"children\":[\"Code like an entrepreneur — build your idea in\",\" \",[\"$\",\"span\",null,{\"className\":\"whitespace-nowrap\",\"children\":\"14 days\"}],\" to get real-world feedback and keep learning as you go.\"]}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"$L1d\",null,{\"extraStyle\":\"btn-green md:btn-lg md:!text-lg btn-block lg:btn-wide\",\"text\":\"Get instant access\"}],[\"$\",\"div\",null,{\"className\":\"text-center text-base-content-secondary flex items-center justify-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 20 20\",\"fill\":\"currentColor\",\"className\":\"size-5\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"d\":\"M16.704 4.153a.75.75 0 0 1 .143 1.052l-8 10.5a.75.75 0 0 1-1.127.075l-4.5-4.5a.75.75 0 0 1 1.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 0 1 1.05-.143Z\",\"clipRule\":\"evenodd\"}]}],\"No experience needed\"]}]]}]]}]]}]}],[\"$\",\"section\",null,{\"className\":\"md:border-x \",\"children\":[\"$\",\"div\",null,{\"data-theme\":\"\",\"children\":[\"$\",\"div\",null,{\"className\":\"py-24 md:py-36 flex flex-col items-center justify-center max-w-3xl mx-auto\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex flex-col md:flex-row justify-start items-start gap-8  max-md:px-8\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/jainil.5d15e99a.jpeg\",\"height\":965,\"width\":965,\"blurDataURL\":\"data:image/jpeg;base64,/9j/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIfIiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/2wBDAQoLCw4NDhwQEBw7KCIoOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozv/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAT/xAAVAQEBAAAAAAAAAAAAAAAAAAABAv/aAAwDAQACEAMQAAABrBf/xAAVEAEBAAAAAAAAAAAAAAAAAAACEv/aAAgBAQABBQJUH//EABYRAQEBAAAAAAAAAAAAAAAAAAIAAf/aAAgBAwEBPwEA7f/EABURAQEAAAAAAAAAAAAAAAAAAAEA/9oACAECAQE/AVb/xAAXEAADAQAAAAAAAAAAAAAAAAABETEA/9oACAEBAAY/AkjIN//EABYQAQEBAAAAAAAAAAAAAAAAAAEAEf/aAAgBAQABPyHFDglj/9oADAMBAAIAAwAAABAP/8QAFhEBAQEAAAAAAAAAAAAAAAAAAQAh/9oACAEDAQE/EANL/8QAFhEBAQEAAAAAAAAAAAAAAAAAAREA/9oACAECAQE/EDSO/8QAFxABAAMAAAAAAAAAAAAAAAAAAQAhMf/aAAgBAQABPxBWQyRDRQZ//9k=\",\"blurWidth\":8,\"blurHeight\":8},\"className\":\"!aspect-square object-cover max-w-44 md:max-w-52 bg-accent-orange border shadow shrink-0\",\"alt\":\"Jainil\",\"quality\":100,\"width\":220,\"height\":220}],[\"$\",\"div\",null,{\"className\":\"relative flex flex-col justify-center items-start\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-3\",\"children\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 24 24\",\"className\":\"size-6 md:size-8\",\"children\":[[\"$\",\"path\",null,{\"d\":\"M8.5 13.5C8.5 16.2614 6.26142 18.5 3.5 18.5C2.94772 18.5 2.5 18.9477 2.5 19.5C2.5 20.0523 2.94772 20.5 3.5 20.5C7.36599 20.5 10.5 17.366 10.5 13.5V5.75C10.5 4.23122 9.26878 3 7.75 3H3.75C2.23122 3 1 4.23122 1 5.75V9.75C1 11.2688 2.23122 12.5 3.75 12.5H7.75C8.01001 12.5 8.26158 12.4639 8.5 12.3965V13.5Z\",\"fill\":\"currentColor\"}],[\"$\",\"path\",null,{\"d\":\"M20.5 13.5C20.5 16.2614 18.2614 18.5 15.5 18.5C14.9477 18.5 14.5 18.9477 14.5 19.5C14.5 20.0523 14.9477 20.5 15.5 20.5C19.366 20.5 22.5 17.366 22.5 13.5V5.75C22.5 4.23122 21.2688 3 19.75 3H15.75C14.2312 3 13 4.23122 13 5.75V9.75C13 11.2688 14.2312 12.5 15.75 12.5H19.75C20.01 12.5 20.2616 12.4639 20.5 12.3965V13.5Z\",\"fill\":\"currentColor\"}]]}]}],[\"$\",\"div\",null,{\"className\":\"text-base max-md:font-semibold  md:text-xl md:font-semibold mb-4 !leading-relaxed\",\"children\":[[\"$\",\"span\",null,{\"className\":\"text-yellow-800 bg-yellow-100 px-1\",\"children\":\"First sale is definitely special,\"}],\" \",\"especially when it's your first self coded web-app. Thank you @marclou\"]}],[\"$\",\"div\",null,{\"className\":\"italic md:text-lg\",\"children\":[\"—\",\"Jainil\"]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"py-8 flex flex-row justify-center items-center -gap-4 max-md:px-8\",\"children\":[[\"$\",\"svg\",null,{\"className\":\"w-14 rotate-90 fill-base-content-secondary\",\"viewBox\":\"0 0 296 71\",\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"g\",null,{\"clipPath\":\"url(#clip0_3_228)\",\"children\":[\"$\",\"path\",null,{\"d\":\"M-3.10351e-06 71C13.102 44.3623 83.0496 8.91537 132.922 2.62301C186.809 -4.29859 217.873 1.57428 281.27 31.1484C280.425 26.9535 279.368 23.5976 278.945 20.4514C278.311 16.8857 277.677 13.1103 277.889 9.54461C277.889 8.49589 279.791 6.60818 281.059 6.39843C282.327 6.18869 284.44 7.6569 284.862 8.70563C288.666 18.7734 292.047 28.8412 295.64 39.1187C296.696 42.4746 295.428 44.9916 291.836 46.0403C278.1 49.3962 264.364 52.9619 250.417 56.1081C247.247 56.7373 243.443 55.8983 239.217 55.8983C242.598 46.8793 250.206 47.928 255.7 45.6208C261.406 43.1039 267.534 41.6357 274.93 39.1187C257.39 27.373 239.639 20.0319 220.832 15.4175C151.941 -1.78164 88.544 12.481 30.0078 50.6547C22.4002 55.6886 15.4265 61.5615 8.03027 66.8051C5.70572 67.8538 3.59247 68.9026 -3.10351e-06 71Z\"}]}]}],[\"$\",\"p\",null,{\"className\":\"font-base italic text-base text-base-content-secondary\",\"children\":[\"Built \",[[\"$\",\"u\",null,{\"className\":\"select-all text-blue-700\",\"children\":\"confettisaas.pro\"}],\",\",\" \",[\"$\",\"u\",null,{\"className\":\"select-all text-blue-700\",\"children\":\"epicaitravelphotos.pro\"}],\" \",\"\u0026 more\"]]}]]}],[\"$\",\"div\",null,{\"className\":\"w-full relative mx-auto hidden lg:block\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/macbook.8ed77199.png\",\"height\":453,\"width\":687,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAFCAMAAABPT11nAAAAJ1BMVEXs7e2nq65tcHRydXlnaW2vs7aMj5OWmp6RlZmvs7e8vcB6fYHBw8UG6FvNAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAKklEQVR4nAXBhwEAMAjDMIcwuv6/txKoapeANdIM0C31AuwIXziRmeH3AQxuAJeiZl7jAAAAAElFTkSuQmCC\",\"blurWidth\":8,\"blurHeight\":5},\"alt\":\"Macbook\",\"className\":\"w-full \",\"quality\":100}],[\"$\",\"div\",null,{\"className\":\"absolute right-[95px] left-[93px] top-[10px] bottom-[118px] flex items-center justify-center bg-primary rounded-t-[1.3rem] rounded-b-lg shadow-inner\",\"children\":[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/epictravelphoto.ce3f492d.gif\",\"height\":450,\"width\":800,\"blurWidth\":0,\"blurHeight\":0},\"className\":\"w-full\",\"alt\":\"[object Object] screenshot\",\"quality\":100}]}]]}],[\"$\",\"div\",null,{\"className\":\"w-full relative mx-auto lg:hidden\",\"children\":[\"$\",\"$L1b\",null,{\"src\":\"$1e\",\"className\":\"w-full border shadow\",\"alt\":\"[object Object] screenshot\",\"quality\":100}]}]]}]}]}],[\"$\",\"$L1f\",null,{}],[\"$\",\"section\",null,{\"className\":\"md:border-x \",\"children\":[\"$\",\"div\",null,{\"data-theme\":\"\",\"children\":[\"$\",\"div\",null,{\"className\":\"py-24 md:py-36 flex flex-col items-center justify-center max-w-3xl mx-auto\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex flex-col md:flex-row justify-start items-start gap-8  max-md:px-8\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/adscei_transparent.cb89d8c7.png\",\"height\":447,\"width\":446,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAXVBMVEVMaXE6R1E8QEhAMi5fW1tBQURUVVhBSFCPZVB1Tk4xLzevcU/8rnBgTkhNT1OMfHOmdFzBg135j2TDlniJdGl9f4VqTT1XSkdCNTE6Q0tNU1pBTFdbWl1lRztBPUI+GB3yAAAAGXRSTlMAW5+rMc7t/v0EhWpf2diaSJAwEemIga76Bm2zbAAAAAlwSFlzAAALEwAACxMBAJqcGAAAAEFJREFUeJwFwQcCgCAMALFjFtxbC+j/n2lCxowLGTI2rezQb10auDxpnt7jvB2xla+0GBARFdGHUEW1WocvorUYfmRJAzepmKNCAAAAAElFTkSuQmCC\",\"blurWidth\":8,\"blurHeight\":8},\"className\":\"!aspect-square object-cover max-w-44 md:max-w-52 bg-accent-green border shadow shrink-0\",\"alt\":\"Adsy\",\"quality\":100,\"width\":220,\"height\":220}],[\"$\",\"div\",null,{\"className\":\"relative flex flex-col justify-center items-start\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-3\",\"children\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 24 24\",\"className\":\"size-6 md:size-8\",\"children\":[[\"$\",\"path\",null,{\"d\":\"M8.5 13.5C8.5 16.2614 6.26142 18.5 3.5 18.5C2.94772 18.5 2.5 18.9477 2.5 19.5C2.5 20.0523 2.94772 20.5 3.5 20.5C7.36599 20.5 10.5 17.366 10.5 13.5V5.75C10.5 4.23122 9.26878 3 7.75 3H3.75C2.23122 3 1 4.23122 1 5.75V9.75C1 11.2688 2.23122 12.5 3.75 12.5H7.75C8.01001 12.5 8.26158 12.4639 8.5 12.3965V13.5Z\",\"fill\":\"currentColor\"}],[\"$\",\"path\",null,{\"d\":\"M20.5 13.5C20.5 16.2614 18.2614 18.5 15.5 18.5C14.9477 18.5 14.5 18.9477 14.5 19.5C14.5 20.0523 14.9477 20.5 15.5 20.5C19.366 20.5 22.5 17.366 22.5 13.5V5.75C22.5 4.23122 21.2688 3 19.75 3H15.75C14.2312 3 13 4.23122 13 5.75V9.75C13 11.2688 14.2312 12.5 15.75 12.5H19.75C20.01 12.5 20.2616 12.4639 20.5 12.3965V13.5Z\",\"fill\":\"currentColor\"}]]}]}],[\"$\",\"div\",null,{\"className\":\"text-base max-md:font-semibold  md:text-xl md:font-semibold mb-4 !leading-relaxed\",\"children\":[\"I've never finished Udemy courses...\",\" \",[\"$\",\"span\",null,{\"className\":\"text-yellow-800 bg-yellow-100 px-1\",\"children\":\"Marc cuts the BS\"}],\" and teaches you only the most important parts of coding.\"]}],[\"$\",\"div\",null,{\"className\":\"italic md:text-lg\",\"children\":[\"—\",\"Adsy\"]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"py-8 flex flex-row justify-center items-center -gap-4 max-md:px-8\",\"children\":[[\"$\",\"svg\",null,{\"className\":\"w-14 rotate-90 fill-base-content-secondary\",\"viewBox\":\"0 0 296 71\",\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"g\",null,{\"clipPath\":\"url(#clip0_3_228)\",\"children\":[\"$\",\"path\",null,{\"d\":\"M-3.10351e-06 71C13.102 44.3623 83.0496 8.91537 132.922 2.62301C186.809 -4.29859 217.873 1.57428 281.27 31.1484C280.425 26.9535 279.368 23.5976 278.945 20.4514C278.311 16.8857 277.677 13.1103 277.889 9.54461C277.889 8.49589 279.791 6.60818 281.059 6.39843C282.327 6.18869 284.44 7.6569 284.862 8.70563C288.666 18.7734 292.047 28.8412 295.64 39.1187C296.696 42.4746 295.428 44.9916 291.836 46.0403C278.1 49.3962 264.364 52.9619 250.417 56.1081C247.247 56.7373 243.443 55.8983 239.217 55.8983C242.598 46.8793 250.206 47.928 255.7 45.6208C261.406 43.1039 267.534 41.6357 274.93 39.1187C257.39 27.373 239.639 20.0319 220.832 15.4175C151.941 -1.78164 88.544 12.481 30.0078 50.6547C22.4002 55.6886 15.4265 61.5615 8.03027 66.8051C5.70572 67.8538 3.59247 68.9026 -3.10351e-06 71Z\"}]}]}],[\"$\",\"p\",null,{\"className\":\"font-base italic text-base text-base-content-secondary\",\"children\":[\"Built \",[[\"$\",\"u\",null,{\"className\":\"select-all text-blue-700\",\"children\":\"indielaunch.ch\"}],\" \u0026\",\" \",[\"$\",\"u\",null,{\"className\":\"select-all text-blue-700\",\"children\":\"mrrbanner.com\"}]]]}]]}],[\"$\",\"div\",null,{\"className\":\"w-full relative mx-auto hidden lg:block\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":\"$20\",\"alt\":\"Macbook\",\"className\":\"w-full \",\"quality\":100}],[\"$\",\"div\",null,{\"className\":\"absolute right-[95px] left-[93px] top-[10px] bottom-[118px] flex items-center justify-center bg-primary rounded-t-[1.3rem] rounded-b-lg shadow-inner\",\"children\":[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/indielaunch.4c5f03b8.gif\",\"height\":450,\"width\":800,\"blurWidth\":0,\"blurHeight\":0},\"className\":\"w-full\",\"alt\":\"[object Object] screenshot\",\"quality\":100}]}]]}],[\"$\",\"div\",null,{\"className\":\"w-full relative mx-auto lg:hidden\",\"children\":[\"$\",\"$L1b\",null,{\"src\":\"$21\",\"className\":\"w-full border shadow\",\"alt\":\"[object Object] screenshot\",\"quality\":100}]}]]}]}]}],[\"$\",\"section\",null,{\"className\":\"relative flex flex-col items-center gap-8 py-32 max-w-full w-full overflow-hidden text-center \",\"children\":[[\"$\",\"p\",null,{\"className\":\"text-xl md:text-2xl\",\"children\":[[\"$\",\"span\",null,{\"className\":\"italic\",\"children\":\"Hey it's Marc, your teacher\"}],\" 👋\"]}],[\"$\",\"div\",null,{\"className\":\"pt-8\",\"children\":[\"$\",\"$L1c\",null,{\"children\":[\"$\",\"ul\",null,{\"className\":\"space-y-8 text-lg md:text-xl max-w-3xl mx-auto text-center px-4\",\"children\":[[\"$\",\"li\",null,{\"children\":[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/marchello.3163f56a.gif\",\"height\":603,\"width\":800,\"blurWidth\":0,\"blurHeight\":0},\"alt\":\"Students learning to code\",\"className\":\"w-full max-w-md mx-auto border shadow\",\"quality\":100}]}],[\"$\",\"li\",null,{\"className\":\"italic\",\"children\":\"I was fired from school and university.\"}],[\"$\",\"li\",null,{\"children\":\"The way teachers taught was boring and impractical, so I didn’t care.\"}],[\"$\",\"li\",null,{\"children\":[\"In 2016, I faced the same issue trying to learn coding.\",\" \",[\"$\",\"span\",null,{\"className\":\"font-semibold\",\"children\":\"Courses were too long and made for people who want to get a job.\"}],\" \",[\"$\",\"span\",null,{\"className\":\"italic\",\"children\":\"I almost gave up... \"}],\" 😔\"]}],[\"$\",\"li\",null,{\"children\":[\"So I skipped the theory, built tiny apps, and made my first $1,000 online with a few lines of code.\",\" \"]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/virallybot.674acc12.jpg\",\"height\":660,\"width\":848,\"blurDataURL\":\"data:image/jpeg;base64,/9j/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIfIiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/2wBDAQoLCw4NDhwQEBw7KCIoOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozv/wgARCAAGAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAb/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIQAxAAAAGvB//EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEAAQUCf//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQMBAT8Bf//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQIBAT8Bf//EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEABj8Cf//EABgQAAIDAAAAAAAAAAAAAAAAAAABESGh/9oACAEBAAE/IUrnD//aAAwDAQACAAMAAAAQ9//EABURAQEAAAAAAAAAAAAAAAAAAAAB/9oACAEDAQE/EK//xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oACAECAQE/EH//xAAXEAEBAQEAAAAAAAAAAAAAAAABEQAh/9oACAEBAAE/EIxUKgHm/9k=\",\"blurWidth\":8,\"blurHeight\":6},\"alt\":\"Stripe dashboard\",\"className\":\"w-full max-w-md mx-auto border shadow\",\"quality\":100}]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"span\",null,{\"className\":\"font-semibold\",\"children\":\"I realized I didn’t need to know everything to create something useful.\"}]}],[\"$\",\"li\",null,{\"children\":[\"Over time, I built\",\" \",[\"$\",\"a\",null,{\"href\":\"https://marclou.com\",\"className\":\" link link-hover bg-accent-orange px-0.5\",\"target\":\"_blank\",\"children\":\"25 tiny businesses\"}],\" \",\"and earned over \",[\"$\",\"span\",null,{\"className\":\"font-semibold\",\"children\":\"$$200,000\"}],\" \",\"with my SaaS. Now, coding is even easier—AI writes and fixes code for you.\"]}],[\"$\",\"li\",null,{\"className\":\"font-semibold\",\"children\":[\"So I built CodeFast, the course\",\" \",[\"$\",\"span\",null,{\"className\":\"font-semibold\",\"children\":\"I wish I had\"}],\" when I started:\"]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"ul\",null,{\"className\":\"list list-decimal max-w-[260px]  list-inside mx-auto text-left\",\"children\":[[\"$\",\"li\",null,{\"className\":\"list-item\",\"children\":\"Short lessons\"}],[\"$\",\"li\",null,{\"className\":\"list-item\",\"children\":\"Skip the fluff\"}],[\"$\",\"li\",null,{\"className\":\"list-item\",\"children\":\"Build real businesses\"}]]}]}],[\"$\",\"li\",null,{\"children\":[\"This course took \",[\"$\",\"span\",null,{\"className\":\"font-semibold\",\"children\":\"9 months\"}],\" \",\"and was shaped by\",\" \",[\"$\",\"span\",null,{\"className\":\"font-semibold\",\"children\":\"50 students\"}],\" in a beta. Some even\",\" \",[\"$\",\"span\",null,{\"className\":\"font-semibold\",\"children\":\"made money with their 1st app!\"}],\" \",\"I hope you like it too.\"]}],[\"$\",\"li\",null,{\"children\":[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/discord-gif.0ce5ad99.gif\",\"height\":600,\"width\":600,\"blurWidth\":0,\"blurHeight\":0},\"alt\":\"Students learning to code\",\"className\":\"w-full max-w-md mx-auto border shadow\",\"quality\":100}]}]]}]}]}]]}],[\"$\",\"section\",null,{\"className\":\"md:border-x \",\"children\":[\"$\",\"div\",null,{\"data-theme\":\"\",\"children\":[\"$\",\"div\",null,{\"className\":\"py-24 md:py-36 flex flex-col items-center justify-center max-w-3xl mx-auto\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex flex-col md:flex-row justify-start items-start gap-8  max-md:px-8\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/andrei_transparent.856ee102.png\",\"height\":1000,\"width\":1000,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAP1BMVEVtXU02Li0iKDZ0V1EdJDIABQVZST0YHy93aVejcFwzKSGRclyLa17Nqn1OSEokLDuKaFZ8WES2gWWXZle9kXKwjVNHAAAAEXRSTlMBRTj9sz+M+A35Y4x5OX7K8KYx3acAAAAJcEhZcwAACxMAAAsTAQCanBgAAAA4SURBVHicHcZJEsAgDASxBgy2s0Py/7emanQSQO/INmdRRq6hHG/uSuQ6lfJ8VwDmrfkdUM3dKj8qeQFGa5m13wAAAABJRU5ErkJggg==\",\"blurWidth\":8,\"blurHeight\":8},\"className\":\"!aspect-square object-cover max-w-44 md:max-w-52 bg-accent-blue border shadow shrink-0\",\"alt\":\"Andrei Bogdan\",\"quality\":100,\"width\":220,\"height\":220}],[\"$\",\"div\",null,{\"className\":\"relative flex flex-col justify-center items-start\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-3\",\"children\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 24 24\",\"className\":\"size-6 md:size-8\",\"children\":[[\"$\",\"path\",null,{\"d\":\"M8.5 13.5C8.5 16.2614 6.26142 18.5 3.5 18.5C2.94772 18.5 2.5 18.9477 2.5 19.5C2.5 20.0523 2.94772 20.5 3.5 20.5C7.36599 20.5 10.5 17.366 10.5 13.5V5.75C10.5 4.23122 9.26878 3 7.75 3H3.75C2.23122 3 1 4.23122 1 5.75V9.75C1 11.2688 2.23122 12.5 3.75 12.5H7.75C8.01001 12.5 8.26158 12.4639 8.5 12.3965V13.5Z\",\"fill\":\"currentColor\"}],[\"$\",\"path\",null,{\"d\":\"M20.5 13.5C20.5 16.2614 18.2614 18.5 15.5 18.5C14.9477 18.5 14.5 18.9477 14.5 19.5C14.5 20.0523 14.9477 20.5 15.5 20.5C19.366 20.5 22.5 17.366 22.5 13.5V5.75C22.5 4.23122 21.2688 3 19.75 3H15.75C14.2312 3 13 4.23122 13 5.75V9.75C13 11.2688 14.2312 12.5 15.75 12.5H19.75C20.01 12.5 20.2616 12.4639 20.5 12.3965V13.5Z\",\"fill\":\"currentColor\"}]]}]}],[\"$\",\"div\",null,{\"className\":\"text-base max-md:font-semibold  md:text-xl md:font-semibold mb-4 !leading-relaxed\",\"children\":[\"I built my app in almost a week. I shared on all social, and I guess people liked it. I got\",\" \",[\"$\",\"span\",null,{\"className\":\"text-yellow-800 bg-yellow-100 px-1\",\"children\":\"140 new users and 5 paying users ($162.5)\"}]]}],[\"$\",\"div\",null,{\"className\":\"italic md:text-lg\",\"children\":[\"—\",\"Andrei Bogdan\"]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"py-8 flex flex-row justify-center items-center -gap-4 max-md:px-8\",\"children\":[[\"$\",\"svg\",null,{\"className\":\"w-14 rotate-90 fill-base-content-secondary\",\"viewBox\":\"0 0 296 71\",\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"g\",null,{\"clipPath\":\"url(#clip0_3_228)\",\"children\":[\"$\",\"path\",null,{\"d\":\"M-3.10351e-06 71C13.102 44.3623 83.0496 8.91537 132.922 2.62301C186.809 -4.29859 217.873 1.57428 281.27 31.1484C280.425 26.9535 279.368 23.5976 278.945 20.4514C278.311 16.8857 277.677 13.1103 277.889 9.54461C277.889 8.49589 279.791 6.60818 281.059 6.39843C282.327 6.18869 284.44 7.6569 284.862 8.70563C288.666 18.7734 292.047 28.8412 295.64 39.1187C296.696 42.4746 295.428 44.9916 291.836 46.0403C278.1 49.3962 264.364 52.9619 250.417 56.1081C247.247 56.7373 243.443 55.8983 239.217 55.8983C242.598 46.8793 250.206 47.928 255.7 45.6208C261.406 43.1039 267.534 41.6357 274.93 39.1187C257.39 27.373 239.639 20.0319 220.832 15.4175C151.941 -1.78164 88.544 12.481 30.0078 50.6547C22.4002 55.6886 15.4265 61.5615 8.03027 66.8051C5.70572 67.8538 3.59247 68.9026 -3.10351e-06 71Z\"}]}]}],[\"$\",\"p\",null,{\"className\":\"font-base italic text-base text-base-content-secondary\",\"children\":[\"Built \",[[\"$\",\"u\",null,{\"className\":\"select-all text-blue-700\",\"children\":\"landingpro.ai\"}],\",\",\" \",[\"$\",\"u\",null,{\"className\":\"select-all text-blue-700\",\"children\":\"uireplicator.com\"}],\" \u0026\",\" \",[\"$\",\"u\",null,{\"className\":\"select-all text-blue-700\",\"children\":\"instantseoaudit.com\"}]]]}]]}],[\"$\",\"div\",null,{\"className\":\"w-full relative mx-auto hidden lg:block\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":\"$20\",\"alt\":\"Macbook\",\"className\":\"w-full \",\"quality\":100}],[\"$\",\"div\",null,{\"className\":\"absolute right-[95px] left-[93px] top-[10px] bottom-[118px] flex items-center justify-center bg-primary rounded-t-[1.3rem] rounded-b-lg shadow-inner\",\"children\":[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/uireplicator.3c381e2f.gif\",\"height\":450,\"width\":800,\"blurWidth\":0,\"blurHeight\":0},\"className\":\"w-full\",\"alt\":\"[object Object] screenshot\",\"quality\":100}]}]]}],[\"$\",\"div\",null,{\"className\":\"w-full relative mx-auto lg:hidden\",\"children\":[\"$\",\"$L1b\",null,{\"src\":\"$22\",\"className\":\"w-full border shadow\",\"alt\":\"[object Object] screenshot\",\"quality\":100}]}]]}]}]}],[\"$\",\"$L23\",null,{\"totalStudentsNow\":277}],[\"$\",\"section\",null,{\"className\":\"md:border-x \",\"children\":[\"$\",\"div\",null,{\"data-theme\":\"\",\"children\":[\"$\",\"div\",null,{\"className\":\"py-24 md:py-36 flex flex-col items-center justify-center max-w-3xl mx-auto\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex flex-col md:flex-row justify-start items-start gap-8  max-md:px-8\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/juan_transparent.3a8e808c.png\",\"height\":640,\"width\":640,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAARVBMVEVMaXEQFyMQEx8XHCkTGio5MjJBMSgbIjFvSkoSEhIiIy8QFCStfmZNOS6ZfWtcUEh2X1slKjTLn4pMSksYHSrfr5e1j3g0tgn2AAAAFXRSTlMAxO3Z/hRV/gQKnH9hhNzLs/y2/LMQlHeyAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAO0lEQVR4nEXGSRaAIAwE0UIDnTjhhPc/qk821qY+/JU1+mN7Cv7pbEtH7K3OGbh1HZrAk0waHZJJNuQXLx4Be9YvOuQAAAAASUVORK5CYII=\",\"blurWidth\":8,\"blurHeight\":8},\"className\":\"!aspect-square object-cover max-w-44 md:max-w-52 bg-accent-orange border shadow shrink-0\",\"alt\":\"Juan\",\"quality\":100,\"width\":220,\"height\":220}],[\"$\",\"div\",null,{\"className\":\"relative flex flex-col justify-center items-start\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-3\",\"children\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 24 24\",\"className\":\"size-6 md:size-8\",\"children\":[[\"$\",\"path\",null,{\"d\":\"M8.5 13.5C8.5 16.2614 6.26142 18.5 3.5 18.5C2.94772 18.5 2.5 18.9477 2.5 19.5C2.5 20.0523 2.94772 20.5 3.5 20.5C7.36599 20.5 10.5 17.366 10.5 13.5V5.75C10.5 4.23122 9.26878 3 7.75 3H3.75C2.23122 3 1 4.23122 1 5.75V9.75C1 11.2688 2.23122 12.5 3.75 12.5H7.75C8.01001 12.5 8.26158 12.4639 8.5 12.3965V13.5Z\",\"fill\":\"currentColor\"}],[\"$\",\"path\",null,{\"d\":\"M20.5 13.5C20.5 16.2614 18.2614 18.5 15.5 18.5C14.9477 18.5 14.5 18.9477 14.5 19.5C14.5 20.0523 14.9477 20.5 15.5 20.5C19.366 20.5 22.5 17.366 22.5 13.5V5.75C22.5 4.23122 21.2688 3 19.75 3H15.75C14.2312 3 13 4.23122 13 5.75V9.75C13 11.2688 14.2312 12.5 15.75 12.5H19.75C20.01 12.5 20.2616 12.4639 20.5 12.3965V13.5Z\",\"fill\":\"currentColor\"}]]}]}],[\"$\",\"div\",null,{\"className\":\"text-base max-md:font-semibold  md:text-xl md:font-semibold mb-4 !leading-relaxed\",\"children\":[\"I finally created my first (coded 100% by myself) startup. I love CodeFast, I could never find something\",\" \",[\"$\",\"span\",null,{\"className\":\"text-yellow-800 bg-yellow-100 px-1\",\"children\":\"so straight to the point and easy to learn.\"}]]}],[\"$\",\"div\",null,{\"className\":\"italic md:text-lg\",\"children\":[\"—\",\"Juan Belmont\"]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"py-8 flex flex-row justify-center items-center -gap-4 max-md:px-8\",\"children\":[[\"$\",\"svg\",null,{\"className\":\"w-14 rotate-90 fill-base-content-secondary\",\"viewBox\":\"0 0 296 71\",\"fill\":\"none\",\"xmlns\":\"http://www.w3.org/2000/svg\",\"children\":[\"$\",\"g\",null,{\"clipPath\":\"url(#clip0_3_228)\",\"children\":[\"$\",\"path\",null,{\"d\":\"M-3.10351e-06 71C13.102 44.3623 83.0496 8.91537 132.922 2.62301C186.809 -4.29859 217.873 1.57428 281.27 31.1484C280.425 26.9535 279.368 23.5976 278.945 20.4514C278.311 16.8857 277.677 13.1103 277.889 9.54461C277.889 8.49589 279.791 6.60818 281.059 6.39843C282.327 6.18869 284.44 7.6569 284.862 8.70563C288.666 18.7734 292.047 28.8412 295.64 39.1187C296.696 42.4746 295.428 44.9916 291.836 46.0403C278.1 49.3962 264.364 52.9619 250.417 56.1081C247.247 56.7373 243.443 55.8983 239.217 55.8983C242.598 46.8793 250.206 47.928 255.7 45.6208C261.406 43.1039 267.534 41.6357 274.93 39.1187C257.39 27.373 239.639 20.0319 220.832 15.4175C151.941 -1.78164 88.544 12.481 30.0078 50.6547C22.4002 55.6886 15.4265 61.5615 8.03027 66.8051C5.70572 67.8538 3.59247 68.9026 -3.10351e-06 71Z\"}]}]}],[\"$\",\"p\",null,{\"className\":\"font-base italic text-base text-base-content-secondary\",\"children\":[\"Built \",[\"$\",\"u\",null,{\"className\":\"select-all text-blue-700\",\"children\":\"nerdmask.com\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"w-full relative mx-auto hidden lg:block\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":\"$20\",\"alt\":\"Macbook\",\"className\":\"w-full \",\"quality\":100}],[\"$\",\"div\",null,{\"className\":\"absolute right-[95px] left-[93px] top-[10px] bottom-[118px] flex items-center justify-center bg-primary rounded-t-[1.3rem] rounded-b-lg shadow-inner\",\"children\":[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/nerdmask.0852e602.gif\",\"height\":450,\"width\":800,\"blurWidth\":0,\"blurHeight\":0},\"className\":\"w-full\",\"alt\":\"[object Object] screenshot\",\"quality\":100}]}]]}],[\"$\",\"div\",null,{\"className\":\"w-full relative mx-auto lg:hidden\",\"children\":[\"$\",\"$L1b\",null,{\"src\":\"$24\",\"className\":\"w-full border shadow\",\"alt\":\"[object Object] screenshot\",\"quality\":100}]}]]}]}]}],[\"$\",\"section\",null,{\"className\":\"overflow-hidden\",\"id\":\"pricing\",\"children\":[\"$\",\"div\",null,{\"className\":\"pb-12 pt-32 md:pt-48 px-8 max-w-5xl mx-auto\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mb-12\",\"children\":[\"$\",\"$L25\",null,{}]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col text-center w-full mb-20\",\"children\":[\"$\",\"h2\",null,{\"className\":\"text-4xl md:text-6xl font-black leading-tight\",\"children\":[\"$\",\"$L1c\",null,{\"children\":[\"Code your idea fast, build your\",\" \",[\"$\",\"span\",null,{\"className\":\"inline-block decoration-primary relative \",\"children\":[[\"$\",\"span\",null,{\"className\":\"relative z-10\",\"children\":\"freedom\"}],[\"$\",\"span\",null,{\"className\":\"bottom-0  absolute bg-accent-blue h-4 md:h-6 md:-bottom-0.5 -inset-x-2 \"}]]}]]}]}]}],[\"$\",\"div\",null,{\"className\":\"relative flex justify-center flex-col lg:flex-row items-center lg:items-center max-lg:gap-8\",\"children\":[[\"$\",\"div\",\"price_1QPcF2AmBJS84zbxfTbLokya\",{\"className\":\"relative w-full max-w-lg lg:max-w-none\",\"data-theme\":\"light\",\"children\":[\"$undefined\",[\"$\",\"div\",null,{\"className\":\"relative flex flex-col h-full gap-5 lg:gap-8 z-10 bg-base-100 border lg:border-r-0 p-8\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex justify-between items-center gap-4\",\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"p\",null,{\"className\":\"text-lg lg:text-xl font-bold\",\"children\":\"CodeFast Course\"}],[\"$\",\"p\",null,{\"className\":\"text-base-content-secondary mt-2\",\"children\":\"Learn to build your ideas, fast\"}]]}]}],[\"$\",\"div\",null,{\"className\":\"flex gap-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex flex-col justify-end mb-[4px] text-lg \",\"children\":[\"$\",\"p\",null,{\"className\":\"relative\",\"children\":[[\"$\",\"span\",null,{\"className\":\"absolute bg-base-content h-[1.5px] inset-x-0 top-[53%]\"}],[\"$\",\"span\",null,{\"className\":\"text-base-content-secondary\",\"children\":[\"$$\",299]}]]}]}],[\"$\",\"p\",null,{\"className\":\"text-5xl tracking-tight font-extrabold\",\"children\":[\"$$\",149]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col justify-end mb-[4px]\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-xs text-base-content-secondary opacity-80 uppercase font-semibold\",\"children\":\"USD\"}]}]]}],[\"$\",\"ul\",null,{\"className\":\"space-y-3 text-base flex-1\",\"children\":[[\"$\",\"li\",\"0\",{\"className\":\"flex items-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 20 20\",\"fill\":\"currentColor\",\"className\":\"w-[18px] h-[18px] opacity-80 shrink-0\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"d\":\"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z\",\"clipRule\":\"evenodd\"}]}],[\"$\",\"span\",null,{\"children\":[\"12 hours of content (211 videos)\",\" \"]}]]}],[\"$\",\"li\",\"1\",{\"className\":\"flex items-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 20 20\",\"fill\":\"currentColor\",\"className\":\"w-[18px] h-[18px] opacity-80 shrink-0\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"d\":\"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z\",\"clipRule\":\"evenodd\"}]}],[\"$\",\"span\",null,{\"children\":[\"3 modules: The Mindset 💡, The Fundamentals 🏠, Your First SaaS 🏰\",\" \"]}]]}],[\"$\",\"li\",\"2\",{\"className\":\"flex items-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 20 20\",\"fill\":\"currentColor\",\"className\":\"w-[18px] h-[18px] opacity-80 shrink-0\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"d\":\"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z\",\"clipRule\":\"evenodd\"}]}],[\"$\",\"span\",null,{\"children\":[\"Private Discord community\",\" \"]}]]}],[\"$\",\"li\",\"3\",{\"className\":\"flex items-center gap-2\",\"children\":[[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"viewBox\":\"0 0 20 20\",\"fill\":\"currentColor\",\"className\":\"w-[18px] h-[18px] opacity-80 shrink-0\",\"children\":[\"$\",\"path\",null,{\"fillRule\":\"evenodd\",\"d\":\"M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z\",\"clipRule\":\"evenodd\"}]}],[\"$\",\"span\",null,{\"children\":[\"Lifetime updates\",\" \"]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"$L1d\",null,{\"text\":\"$undefined\",\"priceId\":\"price_1QPcF2AmBJS84zbxfTbLokya\",\"extraStyle\":\"btn-block btn-hover-blue\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-base-content-secondary text-center\",\"children\":\"Access forever (no subscription)\"}]]}]]}]]}],[\"$\",\"div\",\"price_1QPcGlAmBJS84zbxhrcUQg3v\",{\"className\":\"relative w-full max-w-lg lg:max-w-none\",\"data-theme\":\"dark\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2 z-20 bg-transparent\",\"children\":[\"$\",\"span\",null,{\"className\":\"shadow badge badge-lg border border-base-content tracking-wide\",\"children\":\"BUNDLE\"}]}],[\"$\",\"div\",null,{\"className\":\"relative flex flex-col h-full gap-5 lg:gap-8 z-10 bg-base-100 p-8 lg:px-12 lg:py-20\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex justify-between items-center gap-4\",\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"p\",null,{\"className\":\"text-lg lg:text-xl font-bold\",\"children\":[\"CodeFast Course \",[\"$\",\"span\",null,{\"className\":\"text-yellow-400\",\"children\":\"+ ShipFast\"}]]}],[\"$\",\"p\",null,{\"className\":\"text-base-content-secondary mt-2\",\"children\":\"Ship your ideas even faster\"}]]}]}],[\"$\",\"div\",null,{\"className\":\"flex gap-2\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex flex-col justify-end mb-[4px] text-lg \",\"children\":[\"$\",\"p\",null,{\"className\":\"relative\",\"children\":[[\"$\",\"span\",null,{\"className\":\"absolute bg-base-content h-[1.5px] inset-x-0 top-[53%]\"}],[\"$\",\"span\",null,{\"className\":\"text-base-content-secondary\",\"children\":[\"$$\",648]}]]}]}],[\"$\",\"p\",null,{\"className\":\"text-5xl tracking-tight font-extrabold\",\"children\":[\"$$\",299]}],[\"$\",\"div\",null,{\"className\":\"flex flex-col justify-end mb-[4px]\",\"children\":[\"$\",\"p\",null,{\"className\":\"text-xs text-base-content-secondary opacity-80 uppercase font-semibold\",\"children\":\"USD\"}]}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-3 flex-1\",\"children\":[[\"$\",\"div\",null,{\"className\":\" flex items-center gap-2\",\"children\":[[\"$\",\"span\",null,{\"className\":\"\",\"children\":[\"$\",\"svg\",null,{\"xmlns\":\"http://www.w3.org/2000/svg\",\"fill\":\"none\",\"viewBox\":\"0 0 24 24\",\"strokeWidth\":2,\"stroke\":\"currentColor\",\"className\":\"size-4 rotate-90 md:rotate-0\",\"children\":[\"$\",\"path\",null,{\"strokeLinecap\":\"round\",\"strokeLinejoin\":\"round\",\"d\":\"M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18\"}]}]}],[\"$\",\"span\",null,{\"className\":\"italic\",\"children\":\"Everything in the course, and...\"}]]}],[\"$\",\"a\",null,{\"href\":\"https://shipfa.st?ref=codefast_pricing\",\"target\":\"_blank\",\"className\":\"group block p-4 space-y-2 border ssshadow border-yellow-400 shadow-yellow-500 bg-[#FDE04703] hover:bg-[#FDE04710] duration-200\",\"children\":[[\"$\",\"div\",null,{\"className\":\"flex gap-2.5 items-center text-yellow-400\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/shipfast-icon_transparent.2aa1e5a5.png\",\"height\":512,\"width\":512,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAQlBMVEVMaXG2iBBUPggcEAQAAAB2VwsAAAAdFgKheA87LAWOaQ2GZAw+MATdoxTHnRRBMQfQnhN2Vwr/1Bv9yRnzuRe+kBIcVWd7AAAAEnRSTlMA53Y+CKkYK8Cbwb41/Ppt95IauPaQAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAOklEQVR4nCWLSQ7AIBDDTAsMaxeW/38VMeQSK1YASkTjTdC2qR34ezf5Be9Cbc+9J/nGpU6mOy+JFlgssQFEVvqcWAAAAABJRU5ErkJggg==\",\"blurWidth\":8,\"blurHeight\":8},\"alt\":\"ShipFast\",\"className\":\"size-6 md:size-7 group-hover:scale-110 duration-200 group-hover:rotate-6\"}],[\"$\",\"span\",null,{\"className\":\"md:text-lg font-semibold\",\"children\":\"ShipFast | All-in Tier ($349)\"}]]}],[\"$\",\"div\",null,{\"className\":\"leading-snug text-yellow-50\",\"children\":[\"The codebase used by\",\" \",[\"$\",\"span\",null,{\"className\":\"link group-hover:text-yellow-400 duration-100\",\"children\":\"5,000+ developers\"}],\" \",\"to ship startups in days, not weeks\"]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"space-y-2\",\"children\":[[\"$\",\"$L1d\",null,{\"text\":\"Get CodeFast + ShipFast\",\"priceId\":\"price_1QPcGlAmBJS84zbxhrcUQg3v\",\"extraStyle\":\"btn-block btn-hover-blue\"}],[\"$\",\"p\",null,{\"className\":\"text-sm text-base-content-secondary text-center\",\"children\":\"Access forever (no subscription)\"}]]}]]}]]}]]}]]}]}],[\"$\",\"$L26\",null,{}],[\"$\",\"$L27\",null,{\"totalStudents\":277}],[\"$\",\"section\",null,{\"className\":\"pb-48 pt-20\",\"children\":[\"$\",\"div\",null,{\"className\":\"px-8\",\"children\":[\"$\",\"div\",null,{\"className\":\"relative bg-base-100 border text-base-content w-full mx-auto max-w-5xl\",\"children\":[[\"$\",\"div\",null,{\"className\":\"absolute left-1/2 -translate-x-1/2 top-0 -translate-y-[90%] md:-translate-y-[70%] z-20\",\"children\":[\"$\",\"$L1b\",null,{\"alt\":\"A wizard coding\",\"src\":\"$28\",\"className\":\"w-80\"}]}],[\"$\",\"div\",null,{\"className\":\" flex flex-col items-center justify-center text-center gap-8 p-8 md:p-32 shadow-xl\",\"children\":[[\"$\",\"h2\",null,{\"className\":\"font-black text-4xl md:text-6xl tracking-tight lg:tracking-[-0.035em]\",\"children\":[\"$\",\"$L1c\",null,{\"children\":[\"Learn to code\",\" \",[\"$\",\"span\",null,{\"className\":\"inline-block decoration-primary relative \",\"children\":[[\"$\",\"span\",null,{\"className\":\"relative z-10\",\"children\":\"in weeks,\"}],[\"$\",\"span\",null,{\"className\":\"bottom-0  absolute bg-accent-pink h-4 md:h-6 md:-bottom-0.5 -inset-x-2 \"}]]}],\" \",\"not months\"]}]}],[\"$\",\"p\",null,{\"className\":\"text-base-content-secondary text-lg md:text-xl lg:leading-relaxed px-4 md:px-16 mx-auto max-w-lg\",\"children\":\"Everything you need to turn your idea into an online business, fast.\"}],[\"$\",\"div\",null,{\"className\":\"space-y-4\",\"children\":[[\"$\",\"$L1d\",null,{\"extraStyle\":\"btn-pink md:btn-wide max-md:btn-block\",\"text\":\"Get instant access\"}],[\"$\",\"div\",null,{\"className\":\"flex text-base-content-secondary items-center justify-center gap-2\",\"children\":[\"$\",\"p\",null,{\"className\":\"italic text-center\",\"children\":[[\"$\",\"span\",null,{\"className\":\"font-semibold text-base-content\",\"children\":\"277\"}],\" \",\"entrepreneurs love the course\"]}]}]]}]]}]]}]}]}]]}],[\"$\",\"footer\",null,{\"className\":\"bg-base-100 border-t\",\"data-theme\":\"dark\",\"children\":[\"$\",\"div\",null,{\"className\":\"max-w-7xl mx-auto px-8 py-24\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex lg:items-start md:flex-row md:flex-nowrap flex-wrap flex-col\",\"children\":[[\"$\",\"div\",null,{\"className\":\"mx-auto flex-shrink-0 text-center md:mx-0 md:w-96 md:pr-12 md:text-left\",\"children\":[[\"$\",\"$L13\",null,{\"href\":\"/#\",\"aria-current\":\"page\",\"className\":\"flex gap-2 justify-center md:justify-start items-center\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":{\"src\":\"/_next/static/media/icon.ae943832.png\",\"height\":1080,\"width\":1080,\"blurDataURL\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAM1BMVEUAAAAwMDC2trZ4eHgGBgYAAAAjIyMSEhIAAAAAAABiYmJJSUlwcHCmpqZ9fX2np6c9PT2J4EczAAAACnRSTlPZ/////f///j77RAtZ/wAAAAlwSFlzAAALEwAACxMBAJqcGAAAADlJREFUeJw1yzESwCAMA8EDj2wEJPn/bxmKdNssA0ligKxZAueuL9JUbytar4tYF873qZ0G1ZQFfz8zMgFu0rEABQAAAABJRU5ErkJggg==\",\"blurWidth\":8,\"blurHeight\":8},\"alt\":\"CodeFast logo\",\"priority\":true,\"className\":\"size-6\",\"width\":24,\"height\":24}],[\"$\",\"strong\",null,{\"className\":\"font-extrabold tracking-tight text-base md:text-lg\",\"children\":\"CodeFast\"}]]}],[\"$\",\"p\",null,{\"className\":\"mt-3 text-sm text-base-content-secondary\",\"children\":\"Learn to code in weeks, not months.\"}],[\"$\",\"p\",null,{\"className\":\"text-base-content-secondary mt-1 mb-3 text-sm\",\"children\":[\"Made with ☕️ and 🥐 by\",\" \",[\"$\",\"a\",null,{\"href\":\"https://marclou.com\",\"target\":\"_blank\",\"className\":\"link\",\"children\":\"Marc\"}]]}],[\"$\",\"a\",null,{\"href\":\"https://shipfa.st/?ref=codefast_badge\",\"target\":\"_blank\",\"className\":\"inline-block cursor-pointer rounded bg-base-content px-2 py-1 text-sm text-base-100 ring-1 ring-base-content/10 duration-200 hover:ring-yellow-500\",\"children\":[\"$\",\"div\",null,{\"className\":\"flex items-center gap-1\",\"children\":[[\"$\",\"span\",null,{\"children\":\"Built with\"}],[\"$\",\"span\",null,{\"className\":\"flex items-center gap-0.5 font-semibold tracking-tight\",\"children\":[[\"$\",\"$L1b\",null,{\"src\":\"$29\",\"alt\":\"ShipFast logo\",\"className\":\"h-5 w-5\",\"width\":20,\"height\":20}],\"ShipFast\"]}]]}]}],[\"$\",\"p\",null,{\"className\":\"mt-2 text-sm text-base-content-secondary opacity-80\",\"children\":[\"Copyright © \",2024,\" - All rights reserved\"]}]]}],[\"$\",\"div\",null,{\"className\":\"-mb-10 mt-10 flex flex-grow flex-wrap justify-center text-center md:mt-0 md:text-left\",\"children\":[[\"$\",\"div\",null,{\"className\":\"w-full px-4 md:w-1/2 lg:w-1/3\",\"children\":[[\"$\",\"div\",null,{\"className\":\"footer-title font-semibold text-base-content tracking-widest text-sm md:text-left mb-3\",\"children\":\"LINKS\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-col justify-center items-center md:items-start gap-2 mb-10 text-sm\",\"children\":[[\"$\",\"a\",null,{\"href\":\"mailto:<EMAIL>\",\"target\":\"_blank\",\"className\":\"link link-hover\",\"aria-label\":\"Contact Support\",\"children\":\"Support\"}],[\"$\",\"$L13\",null,{\"href\":\"/#pricing\",\"className\":\"link link-hover\",\"children\":\"Pricing\"}],[\"$\",\"$L13\",null,{\"href\":\"/course\",\"className\":\"link link-hover\",\"children\":\"Course\"}],[\"$\",\"$L13\",null,{\"href\":\"/affiliates\",\"className\":\"link link-hover\",\"children\":[\"Affiliate (Earn up to $\",\"150\",\")\"]}]]}]]}],[\"$\",\"div\",null,{\"className\":\"w-full px-4 md:w-1/2 lg:w-1/3\",\"children\":[[\"$\",\"div\",null,{\"className\":\"footer-title font-semibold text-base-content tracking-widest text-sm md:text-left mb-3\",\"children\":\"LEGAL\"}],[\"$\",\"div\",null,{\"className\":\"flex flex-col justify-center items-center md:items-start gap-2 mb-10 text-sm\",\"children\":[[\"$\",\"$L13\",null,{\"href\":\"/tos\",\"className\":\"link link-hover\",\"children\":\"Terms of services\"}],[\"$\",\"$L13\",null,{\"href\":\"/privacy-policy\",\"className\":\"link link-hover\",\"children\":\"Privacy policy\"}]]}]]}],[\"$\",\"div\",null,{\"className\":\"w-full px-4 md:w-1/2 lg:w-1/3\",\"children\":[[\"$\",\"div\",null,{\"className\":\"footer-title mb-3 text-sm font-semibold tracking-widest text-base-content md:text-left\",\"children\":\"By the maker of this site\"}],[\"$\",\"div\",null,{\"className\":\"mb-10 flex flex-col items-center justify-center gap-2 text-sm md:items-start\",\"children\":[[\"$\",\"a\",null,{\"href\":\"https://marclou.beehiiv.com/\",\"target\":\"_blank\",\"className\":\"link-hover link\",\"children\":\"Newsletter for makers\"}],[\"$\",\"a\",null,{\"href\":\"https://byedispute.com/\",\"target\":\"_blank\",\"className\":\"link-hover link\",\"children\":\"ByeDispute\"}],[\"$\",\"a\",null,{\"href\":\"https://indiepa.ge/\",\"target\":\"_blank\",\"className\":\"link-hover link\",\"children\":\"IndiePage\"}],[\"$\",\"a\",null,{\"href\":\"https://zenvoice.io/\",\"target\":\"_blank\",\"className\":\"link-hover link\",\"children\":\"ZenVoice\"}],[\"$\",\"a\",null,{\"href\":\"https://gamifylist.com/\",\"target\":\"_blank\",\"className\":\"link-hover link\",\"children\":\"GamifyList\"}],[\"$\",\"a\",null,{\"href\":\"https://workbookpdf.com/\",\"target\":\"_blank\",\"className\":\"link-hover link\",\"children\":\"WorkbookPDF\"}],[\"$\",\"a\",null,{\"href\":\"https://habitsgarden.com/\",\"target\":\"_blank\",\"className\":\"link-hover link\",\"children\":\"HabitsGarden\"}],[\"$\",\"a\",null,{\"href\":\"https://shipfa.st/\",\"target\":\"_blank\",\"className\":\"link-hover link\",\"children\":\"ShipFast\"}],[\"$\",\"a\",null,{\"href\":\"https://DataFa.st/\",\"target\":\"_blank\",\"className\":\"link-hover link\",\"children\":\"DataFast\"}]]}]]}]]}]]}]}]}]]}]]\n"])</script>
  <script>self.__next_f.push([1, ""])</script>
  <script id="facebook-pixel"
          data-nscript="afterInteractive">
            !function (f, b, e, v, n, t, s) {
              if (f.fbq) return; n = f.fbq = function () {
                n.callMethod ?
                n.callMethod.apply(n, arguments) : n.queue.push(arguments)
              };
              if (!f._fbq) f._fbq = n; n.push = n; n.loaded = !0; n.version = '2.0';
              n.queue = []; t = b.createElement(e); t.async = !0;
              t.src = v; s = b.getElementsByTagName(e)[0];
              s.parentNode.insertBefore(t, s)
            }(window, document, 'script',
              'https://connect.facebook.net/en_US/fbevents.js');
            fbq('init', '410683368646128');
            fbq('track', 'PageView');
          </script>
  <script src="https://www.googletagmanager.com/gtag/js?id=G-SHPN0DPZNW"
          data-nscript="afterInteractive"></script>
  <script id="google-analytics"
          data-nscript="afterInteractive">
            window.dataLayer = window.dataLayer || [];
            function gtag() { dataLayer.push(arguments); }
            gtag('js', new Date());
            gtag('config', 'G-SHPN0DPZNW');
          </script>
  <script src="https://r.wdfl.co/rw.js"
          data-rewardful="184f84"
          data-nscript="afterInteractive"></script><next-route-announcer
                        style="position: absolute;"></next-route-announcer>
</body>

</html>