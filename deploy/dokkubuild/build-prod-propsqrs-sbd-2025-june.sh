#!/bin/zsh

# https://stackoverflow.com/questions/36273665/what-does-set-x-do
# set -x

#
# chmod 700 deploy/dokkubuild/build-prod-propsqrs-sbd-2025-june.sh
# ./deploy/dokkubuild/build-prod-propsqrs-sbd-2025-june.sh

# ENV_FILE=../../../.env-dokku-hhh-july-2023
ENV_FILE=.env.local.propsqrs-sbd-2025-june-prod
# ../../../.env-prod-htoc-sbd-spt-2023

if [ ! -f "$ENV_FILE" ]; then
  echo "File $ENV_FILE does not exist"
  read err_msg
  exit
fi

# [ ! -f ../../.env ] || export $(grep -v '^#' ../../.env | xargs)
# Above would silently fail if .env file not found
export $(grep -v '^#' $ENV_FILE | xargs)
# Above will make variables in .env file available
# Mainly used for quasar build

(exec "./deploy/dokkubuild/shared-build-only-dokku-prod.sh")

# # as of july 2023 I need to copy from the default build dir to the dokku deploy one after building:
# printf "%s" "Confirm copying of build dir to dokku deploy dir"
# read copy_confirmation

# ----
# dist/ssr
# NEW_BUILD_DIR=/Users/<USER>/dev/sites-2024-may/pwb-pro-fe-admin/dist/ssr
# pwb-pro-fe-admin was the project I copied for this.  Below is new name
NEW_BUILD_DIR=/Users/<USER>/dev/sites-2024-may/pwb-pro-fe-h2c/dist/ssr
DOKKU_DEPLOY_DIR=/Users/<USER>/dev/sites-2024-may/htz-deploy-app/node-apps/dist-propsqrs-sbd-2025-june
DOKKU_DEPLOY_TO_REMOVE=/Users/<USER>/dev/sites-2024-may/htz-deploy-app/node-apps/dist-propsqrs-sbd-2025-june

# as of july 2023 I need to copy from the default build dir to the dokku deploy one after building:
printf "%s" "Confirm moving of build dir to dokku deploy dir"
echo "NEW_BUILD_DIR is ${NEW_BUILD_DIR}"
echo "DOKKU_DEPLOY_DIR is ${DOKKU_DEPLOY_DIR}"
echo "DOKKU_DEPLOY_TO_REMOVE is ${DOKKU_DEPLOY_TO_REMOVE}"
read copy_confirmation

if [ -d "$DOKKU_DEPLOY_TO_REMOVE" ]; then
  echo "Removing $DOKKU_DEPLOY_TO_REMOVE"
  rm -r $DOKKU_DEPLOY_TO_REMOVE
fi
if [ ! -f "$DOKKU_DEPLOY_DIR/ssr" ]; then
  echo "Making $DOKKU_DEPLOY_DIR/ssr"
  mkdir -p $DOKKU_DEPLOY_DIR/ssr
fi
mv -f $NEW_BUILD_DIR $DOKKU_DEPLOY_DIR
# # Oct 2023 update - instead of copying as below, will remove old dir and move new one there..
# cp -r $NEW_BUILD_DIR $DOKKU_DEPLOY_DIR
# ----

# (exec "./shared-post-build-log.sh")

# cp -r /Users/<USER>/dev/sites-2022-dec/homestocompare-fe/dist-htoc-sbd-spt-2023 /Users/<USER>/dev/sites-2024-may/htz-deploy-app/node-apps/

# Aug 2023 - commenting this out for now:
# cd /Users/<USER>/dev/sites-2024-may/htz-deploy-app/node-apps
# dokku_deploy_git_rev=$(git log --pretty=format:"%h %s" -n 1)
# export DOKKU_DEPLOY_GIT_REV=$dokku_deploy_git_rev
# date_now=$(date +"%Y-%m-%d")
# time_now=$(date +"%H:%M")

# echo "dokku_deploy_git_rev is ${dokku_deploy_git_rev} on ${date_now} at ${time_now}" >>dokku_deploy_git_rev.txt
# # above works but SRC_GIT_REV not available here so can't log quasar git rev for each build - yet......
# echo "built ${SERVICE_NAME} rev: ${SRC_GIT_REV} on ${date_now} at ${time_now}" >>quasar_builds.txt
