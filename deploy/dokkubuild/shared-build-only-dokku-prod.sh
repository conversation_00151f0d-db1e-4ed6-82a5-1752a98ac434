#!/bin/zsh

# chmod 700 shared-build-only-dokku-prod.sh
# ./shared-build-only-dokku-prod.sh

# [ ! -f ../../.env ] || export $(grep -v '^#' ../../.env | xargs)
# Above would silently fail if .env file not found
# export $(grep -v '^#' ../../.env | xargs)
# Above will make variables in .env file available
# Mainly used for quasar build

# SERVICE_NAME="florida-1"
# printf "%s" "Enter SERVICE_NAME: "
# read SERVICE_NAME
current_git_rev_of_source=$(git log --pretty=format:"%h %s" -n 1)
export SRC_GIT_REV=$(git log --pretty=format:"%h" -n 1)
date_now=$(date +"%Y-%m-%d")
time_now=$(date +"%H:%M")
# all_deploys_log_file="../../deploy/gcloud/services/all-depoys.txt"
build_log_file="./services/${SERVICE_NAME}-$date_now-build.result.txt"
# single_deploy_log_file="../../deploy/gcloud/services/${SERVICE_NAME}-$date_now-deploy.result.txt"
echo "Dist dir is ${DIST_DIR_NAME}"
echo "Service name is ${SERVICE_NAME}"
echo "PRODUCT_NAME is ${PRODUCT_NAME}"
echo "ROOT_FOLDER_NAME is ${ROOT_FOLDER_NAME}"
echo "Current Date: ${date_now}"

# exec >>$log_file 2>&1 && tail $log_file

# below errors:
# make 2>&1 | tee -a file.txt
# below just hangs
# exec &> >(tee -a $log_file)
# below 2 do not show output on screen:
# exec >>$log_file 2>&1 && tail $log_file
# exec &>>$log_file && tail $log_file

# exec 2>&1 | tee -a $log_file

printf "%s" "Confirm build only with vars above:"
read deploy_confirmation

quasar build -m ssr 2>&1 | tee -a $build_log_file

# echo "copying ./services/source-app-ssr.yaml "
echo "Built app in $DIST_DIR_NAME"
echo "Current working dir: $PWD"

# # echo "On $date_now at $time_now:" 2>&1 | tee -a $single_deploy_log_file
# echo "cd-ing to ../../../${DIST_DIR_NAME}"
# # below dir will be created by quasar build command..
# cd "../../../${DIST_DIR_NAME}"
# # I need to cd to it to be able to git add and commit it
# git add .
# git commit -m "For deploy to ${SERVICE_NAME} of git rev $current_git_rev_of_source"
# # touch $single_deploy_log_file
# # touch $all_deploys_log_file
# git push digital-o-vps
