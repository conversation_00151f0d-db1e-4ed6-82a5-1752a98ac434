<html>

<head>
  <base href="https://www.homie.com/">
  <meta charset="UTF-8">
  <meta name="viewport"
        content="width=device-width, initial-scale=1.0">
  <title>Homie: Your Modern Real Estate Solution</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Poppins', sans-serif;
    }

    :root {
      --primary: #FF385C;
      --secondary: #1C3D5A;
    }

    body {
      line-height: 1.6;
    }

    .hero {
      min-height: 100vh;
      background: linear-gradient(135deg, #fff 50%, #f8f9fa 50%);
      padding: 2rem;
    }

    nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 0;
    }

    .logo {
      font-size: 2rem;
      font-weight: bold;
      color: var(--primary);
    }

    .nav-links {
      display: flex;
      gap: 2rem;
    }

    .nav-links a {
      text-decoration: none;
      color: var(--secondary);
      font-weight: 500;
    }

    .hero-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 4rem;
    }

    .hero-text {
      max-width: 600px;
    }

    h1 {
      font-size: 3.5rem;
      color: var(--secondary);
      margin-bottom: 1.5rem;
    }

    .cta-buttons {
      display: flex;
      gap: 1rem;
      margin-top: 2rem;
    }

    .btn {
      padding: 1rem 2rem;
      border-radius: 30px;
      text-decoration: none;
      font-weight: 600;
      transition: transform 0.3s ease;
    }

    .btn:hover {
      transform: translateY(-3px);
    }

    .btn-primary {
      background: var(--primary);
      color: white;
    }

    .btn-secondary {
      border: 2px solid var(--primary);
      color: var(--primary);
    }

    .house-illustration {
      width: 500px;
      height: 500px;
    }

    .features {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;
      margin-top: 4rem;
    }

    .feature-card {
      background: white;
      padding: 2rem;
      border-radius: 15px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      text-align: center;
    }

    .feature-icon {
      width: 60px;
      height: 60px;
      margin-bottom: 1rem;
    }

    @keyframes float {
      0% {
        transform: translateY(0px);
      }

      50% {
        transform: translateY(-20px);
      }

      100% {
        transform: translateY(0px);
      }
    }

    .floating {
      animation: float 6s ease-in-out infinite;
    }
  </style>
</head>

<body>
  <div class="hero">
    <nav>
      <div class="logo">Homie</div>
      <div class="nav-links">
        <a href="https://www.homie.com/buy">Buy</a>
        <a href="https://www.homie.com/sell">Sell</a>
        <a href="https://www.homie.com/agents">Agents</a>
        <a href="https://www.homie.com/about">About</a>
        <a href="https://www.homie.com/contact">Contact</a>
      </div>
    </nav>

    <div class="hero-content">
      <div class="hero-text">
        <h1>Modern Real Estate Made Simple</h1>
        <p>Save thousands when you buy or sell your home with Homie. Our tech-powered platform and expert agents make
          real estate transactions seamless and affordable.</p>
        <div class="cta-buttons">
          <a href="https://www.homie.com/get-started"
             class="btn btn-primary">Get Started</a>
          <a href="https://www.homie.com/how-it-works"
             class="btn btn-secondary">Learn More</a>
        </div>
      </div>

      <svg class="house-illustration floating"
           viewBox="0 0 200 200">
        <!-- House SVG -->
        <path d="M100 20L180 80V180H20V80L100 20Z"
              fill="#FF385C"
              opacity="0.1" />
        <path d="M100 40L160 85V160H40V85L100 40Z"
              fill="white"
              stroke="#1C3D5A"
              stroke-width="3" />
        <rect x="85"
              y="120"
              width="30"
              height="40"
              fill="#1C3D5A" />
        <circle cx="90"
                cy="140"
                r="2"
                fill="white" />
        <rect x="60"
              y="100"
              width="20"
              height="20"
              fill="#1C3D5A" />
        <rect x="120"
              y="100"
              width="20"
              height="20"
              fill="#1C3D5A" />
      </svg>
    </div>

    <div class="features">
      <div class="feature-card">
        <svg class="feature-icon"
             viewBox="0 0 24 24">
          <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"
                fill="none"
                stroke="#FF385C"
                stroke-width="2" />
        </svg>
        <h3>Save Thousands</h3>
        <p>Our tech-first approach means lower fees and more savings for you.</p>
      </div>

      <div class="feature-card">
        <svg class="feature-icon"
             viewBox="0 0 24 24">
          <path d="M12 22s-8-6-8-12c0-4.4 3.6-8 8-8s8 3.6 8 8c0 6-8 12-8 12z"
                fill="none"
                stroke="#FF385C"
                stroke-width="2" />
          <circle cx="12"
                  cy="10"
                  r="3"
                  fill="none"
                  stroke="#FF385C"
                  stroke-width="2" />
        </svg>
        <h3>Local Experts</h3>
        <p>Work with experienced agents who know your market inside and out.</p>
      </div>

      <div class="feature-card">
        <svg class="feature-icon"
             viewBox="0 0 24 24">
          <path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2z"
                fill="none"
                stroke="#FF385C"
                stroke-width="2" />
          <path d="M2 8h20M7 4v4m10-4v4"
                stroke="#FF385C"
                stroke-width="2" />
        </svg>
        <h3>Simple Process</h3>
        <p>Buy or sell your home with our streamlined, tech-powered platform.</p>
      </div>
    </div>
  </div>

  <script>
    // Add smooth scrolling to navigation links
    document.querySelectorAll('a[href^="https://"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const href = this.getAttribute('href');
        window.location.href = href;
      });
    });

    // Add hover effects to feature cards
    document.querySelectorAll('.feature-card').forEach(card => {
      card.addEventListener('mouseenter', () => {
        card.style.transform = 'translateY(-10px)';
        card.style.transition = 'transform 0.3s ease';
      });

      card.addEventListener('mouseleave', () => {
        card.style.transform = 'translateY(0)';
      });
    });
  </script>
</body>

</html>