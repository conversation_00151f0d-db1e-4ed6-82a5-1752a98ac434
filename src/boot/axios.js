import { boot } from 'quasar/wrappers'
import axios from 'axios'

// Be careful when using SSR for cross-request state pollution
// due to creating a Singleton instance here;
// If any client changes this (global) instance, it might be a
// good idea to move this instance creation inside of the
// "export default () => {}" function below (which runs individually
// for each client)
const htocAxiosApi = axios.create({ baseURL: 'https://api.example.com' })

export default boot(({ app }) => {
  if (typeof window !== 'undefined') {
    axios.interceptors.request.use((config) => {
      const code = localStorage.getItem('userAccessCode')
      if (code) {
        config.headers['X-User-Access-Code'] = code
      }
      return config
    })

    htocAxiosApi.interceptors.request.use((config) => {
      const code = localStorage.getItem('userAccessCode')
      if (code) {
        config.headers['X-User-Access-Code'] = code
      }
      return config
    })
  }
  // for use inside Vue files (Options API) through this.$axios and this.$htocAxiosApi

  app.config.globalProperties.$axios = axios
  // ^ ^ ^ this will allow you to use this.$axios (for Vue Options htocAxiosApi form)
  //       so you won't necessarily have to import axios in each vue file

  app.config.globalProperties.$htocAxiosApi = htocAxiosApi
  // ^ ^ ^ this will allow you to use this.$htocAxiosApi (for Vue Options API form)
  //       so you can easily perform requests against your app's API
})

export { htocAxiosApi }
