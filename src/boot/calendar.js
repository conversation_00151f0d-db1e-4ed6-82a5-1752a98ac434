import { boot } from 'quasar/wrappers'
import {
  QCalendar,
  QCalendarDay,
  QCalendarMonth,
  QCalendarAgenda,
  QCalendarScheduler,
  QCalendarResource
} from '@quasar/quasar-ui-qcalendar/dist/index.esm.js'
import '@quasar/quasar-ui-qcalendar/dist/index.css'

export default boot(({ app }) => {
  app.component('QCalendar', QCalendar)
  app.component('QCalendarDay', QCalendarDay)
  app.component('QCalendarMonth', QCalendarMonth)
  app.component('QCalendarAgenda', QCalendarAgenda)
  app.component('QCalendarScheduler', QCalendarScheduler)
  app.component('QCalendarResource', QCalendarResource)
})