import VueChartkick from '/node_modules/vue-chartkick/dist/vue-chartkick.esm.js'

// have to use the .esm.js file instead of the regular import
// otherwise you get the following error:
// Error [ERR_REQUIRE_ESM]: require() of ES Module

// I run 'npm install vue-chartkick chart.js' but below wasn't
// working until. I added the gstatic import below so 'chartkick/chart.js'
// import not needed and I probably didn't need to install chart.js either
// import 'chartkick/chart.js'

import { boot } from 'quasar/wrappers'

export default boot(async ({ app, router }) => {
  if (process.env.CLIENT) {
    // export default async ({ app, router, Vue }) => {
    // Create a Promise to load the Google Charts library
    await new Promise((resolve, reject) => {
      const script = document.createElement('script')
      script.src = 'https://www.gstatic.com/charts/loader.js'
      script.async = true
      script.defer = true
      script.onload = resolve
      script.onerror = reject
      document.head.appendChild(script)
    })

    // Once the script is loaded, you can initialize Google Charts
    google.charts
      .load('current', { packages: ['corechart'] })
      .then(() => {
        // Here you can set up any initial configurations or global settings for Google Charts
        console.log('Google Charts loaded successfully')
      })
      .catch((err) => {
        console.error('Failed to load Google Charts:', err)
      })
    app.use(VueChartkick)
  }
})
