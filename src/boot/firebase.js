// import { boot } from 'quasar/wrappers'

// // "async" is optional;
// // more info on params: https://v2.quasar.dev/quasar-cli/boot-files
// export default boot(async (/* { app, router, ... } */) => {
//   // something to do
// })

// Import the functions you need from the SDKs you need
import { initializeApp } from 'firebase/app'
// import { currentSbdUserProvider } from "src/compose/current-user-provider.js"

import {
  getAuth,
  // onAuthStateChanged
  // signInWithEmailAndPassword,
  // createUserWithEmailAndPassword
} from 'firebase/auth'
// import { getAnalytics } from "firebase/analytics"
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  // feb 2023 - process.env..... values were not showing here
  // so had to use import.meta.env instead
  apiKey: import.meta.env.VITE_FIRE_KEY,
  authDomain: import.meta.env.VITE_FIRE_DOMAIN,
  //  "h-to-c.firebaseapp.com",
  // projectId: "h-to-c",
  // storageBucket: "h-to-c.appspot.com",
  // messagingSenderId: import.meta.env.FIRE_MSG_ID,
  // appId: import.meta.env.FIRE_ID,
  // measurementId: "G-R6ZQKQ30PS"
}

// console.log(`VITE_FIRE_KEY is ${import.meta.env.VITE_FIRE_KEY}`)
// console.log(`VITE_FIRE_DOMAIN is ${import.meta.env.VITE_FIRE_DOMAIN}`)
// console.log(`myvar is ${import.meta.env.VITE_MY_VAR}`)
// Initialize Firebase
const app = initializeApp(firebaseConfig)
// const analytics = getAnalytics(app);
// app.getCurrentUser = () => {
//   return new Promise((resolve, reject) => {
//     const unsubscribe = firebase.auth().onAuthStateChanged(user => {
//       unsubscribe();
//       resolve(user);
//     }, reject);
//   })
// };

// export default app
// Firebase: Need to provide options, when not being deployed to hosting via source.
// So trying this:

export const fireAuth = getAuth(app)

// onAuthStateChanged(fireAuth, (user) => {
//   if (user) {
//     // User is signed in, see docs for a list of available properties
//     // https://firebase.google.com/docs/reference/js/firebase.User
//     const uid = user.uid;
//     // ...

//     // currentSbdUserProvider.initHomeHunting(user)
//   } else {
//     // currentSbdUserProvider.initHomeHunting()
//     // User is signed out
//     // ...
//   }
// })
// as per:
// https://stackoverflow.com/questions/74016589/how-can-i-provide-options-when-not-being-deployed-to-hosting-via-source

// import firebase from "firebase";

// const firebaseConfig = {
//   apiKey: "AIzaSyBJEocBE6bhd6OeM5iqOi36zzL-1hizHuU",
//   authDomain: "h-to-c.firebaseapp.com",
//   projectId: "h-to-c",
//   storageBucket: "h-to-c.appspot.com",
//   messagingSenderId: "************",
//   appId: "1:************:web:9e7f723af9c0cf0fd45779",
//   measurementId: "G-R6ZQKQ30PS",
// };
// firebase.initializeApp(firebaseConfig);

// export default firebase
