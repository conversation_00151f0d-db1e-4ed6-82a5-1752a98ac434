import { boot } from 'quasar/wrappers'
// // import VueGoogleMaps from '@fawmi/vue-google-maps'
// // https://github.com/NathanAP/vue-google-maps-community-fork
// import VueGoogleMaps from 'vue-google-maps-community-fork'
// // import { currentConfigData } from "src/utils/config-data"

// export default boot(({ app }) => {
//   // console.log(`GMAPS_API_KEY1 is ${import.meta.env.GMAPS_API_KEY}`)
//   // // Oct 2024: above returns undefined while below is successful
//   // console.log(`GMAPS_API_KEY2 is ${process.env.GMAPS_API_KEY}`)
//   app.use(VueGoogleMaps, {
//     load: {
//       // key: gak,
//       key: process.env.GMAPS_API_KEY,
//       libraries: 'places',
//     },
//   })
// })

// import { createApp } from 'vue'
// import App from 'src/App.vue'
import OpenLayersMap from 'vue3-openlayers'
// The style are only needed for some map controls.
// However, you can also style them by your own
import 'vue3-openlayers/styles.css'

// export default async ({ app }) => {
//   // This code runs in the boot file and can access the app instance
//   app.use(OpenLayersMap /*, options */)
// }
export default boot(({ app }) => {
  app.use(OpenLayersMap /*, options */)
})
