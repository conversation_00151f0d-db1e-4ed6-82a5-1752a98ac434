import { boot } from 'quasar/wrappers'
// import VueGoogleMaps from '@fawmi/vue-google-maps'
// https://github.com/NathanAP/vue-google-maps-community-fork
import VueGoogleMaps from 'vue-google-maps-community-fork'
// import { currentConfigData } from "src/utils/config-data"

export default boot(({ app }) => {
  // console.log(`GMAPS_API_KEY1 is ${import.meta.env.GMAPS_API_KEY}`)
  // // Oct 2024: above returns undefined while below is successful
  // console.log(`GMAPS_API_KEY2 is ${process.env.GMAPS_API_KEY}`)
  app.use(VueGoogleMaps, {
    load: {
      // key: gak,
      key: process.env.GMAPS_API_KEY,
      libraries: 'places',
    },
  })
})
