<template>
  <div>
    <div v-if="this.currentSbdUserProvider.state.currentFbUser">
      <q-card-section class="text-center q-pa-none q-mb-md">
        <h5 class="text-grey-6 q-mb-none">
          You are currently signed in as
          {{ currentSbdUserProvider.state.currentFbUser.email }}
        </h5>
        <q-card-actions v-if="unAuthorisedForTenant"
                        class="text-center flex-center">
          <!-- <div class="q-pt-md text-body1">
            You do not have admin access to this site though.
          </div> -->
          <!-- <div class="q-pt-sm text-body1"
               v-if="userTenantUrl">
            Your site is at:
            <a :href="userTenantUrl">
              {{ userTenantUrl }}</a>
          </div> -->
        </q-card-actions>
        <q-card-actions v-else
                        class="text-center flex-center">
          <div class="q-pt-md">

            <q-btn :to="{
              name: 'rAdminRootIndex', params: {
                userUuid: currentSbdUserProvider.state.currentFbUser.uid,
              },
            }"
                   color="primary">Go to your dashboard</q-btn>
          </div>
          <!-- <div class="q-pt-sm text-body1"
               v-if="userTenantUrl">
          </div> -->
        </q-card-actions>

        <q-card-actions class="text-center flex-center">
          <q-btn @click="startSignOut"
                 color="primary">Click Here To Sign Out</q-btn>
        </q-card-actions>
      </q-card-section>

      <q-card-actions v-if="showVerificationStatus"
                      class="text-center flex-center">
        <div v-if="!currentSbdUserProvider.state.currentFbUser.emailVerified"
             class="q-pa-sm q-ma-sm flex justify-center items-center full-width">
          Your account has not been verified.&nbsp;&nbsp;
          <a @click="startSendValidationEmail"
             href="">
            Please click here to resend the verification email.</a>
        </div>
        <div v-else
             class="q-pa-sm q-ma-sm flex justify-center items-center full-width">
          Your account has been verified.
        </div>
      </q-card-actions>
    </div>
    <div v-else>
      <q-circular-progress indeterminate
                           rounded
                           size="50px"
                           color="purple"
                           class="q-ma-md" />
    </div>
  </div>
</template>
<script>
// import { useQuasar, Cookies } from "quasar"
import useAuthStore from "src/compose/useAuthStore.js"
// import useSiteVisitor from "src/compose/useSiteVisitor.js"
// import useEditHelper from "src/compose/useEditHelper.js"
export default {
  inject: ["currentSbdUserProvider"],
  data() {
    return {
      loading: false,
    }
  },
  mounted: function () { },
  setup() {
    const { authStoreLogout, sendVerificationEmail } = useAuthStore()
    // const { makeEditCall } = useEditHelper()
    return {
      sendVerificationEmail,
      authStoreLogout,
    }
  },
  props: {
    currentSbdGuestInstance: {
      type: Object,
      default() {
        return {}
      },
    },
    showVerificationStatus: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    unAuthorisedForTenant() {
      let htocData = this.currentSbdUserProvider.state.htocData || {}
      return htocData.permissions !== "full"
    },
    // userTenantUrl() {
    //   let userSubdomain = this.currentSbdUserProvider.state.htocData?.agency_tenant?.subdomain;
    //   let userDomain = this.currentSbdUserProvider.state.htocData?.agency_tenant?.domain;
    //   if (userSubdomain && userDomain) {
    //     return `https://${userSubdomain}.${userDomain}`
    //   } else {
    //     return null
    //   }
    // },
    // weHaveAValidSignedInUser() {
    //   // had previously been using currentSbdUserProvider.state.currentBeUser.email
    //   return !!this.currentSbdUserProvider.state.currentFbUser.accessToken
    // },
  },
  created() { },
  methods: {
    // startRequestAccess(elEvent) {
    //   elEvent.preventDefault()
    //   let updateObject = {
    //     customerRequestForm: {},
    //     enquiryCat: "request_sbd_access",
    //     sgt: this.currentSbdUserProvider.state.currentBeUser.sgt,
    //     sbdGuestInstanceUuid: this.currentSbdGuestInstance.uuid,
    //   }
    //   let helperDetails = {
    //     reloadLocation: false,
    //     progressMessage: false, // "Thank you. We are processing the url provided ....",
    //     successMessage: false,
    //   }
    //   // this.makeEditCall(this.saveGuestComm, updateObject, helperDetails).then(
    //   //   (responseObject) => {
    //   //     // this.isPopupFormVisible = false
    //   //   }
    //   // )
    // },
    startSendValidationEmail(elEvent) {
      elEvent.preventDefault()
      this.sendVerificationEmail().then(
        (result) => {
          // Nov 2024 - when I tested result was undefined
          // location.reload()
        },
        (error) => { }
      )
      // }
    },
    startSignOut() {
      this.authStoreLogout().then(
        (result) => {
          // location.reload()
        },
        (error) => { }
      )
      // }
    },
  },
}
</script>
