<template>
  <div class="q-my-md editable-eval-cols">
    <h5 class="reg-edt-lst-title text-center"
        style="min-height: 80px; overflow: auto">
      <ReactivePopupEdit endPointName=""
                         @saveContent="savePurchaseEvaluationContent"
                         :fieldName="this.listingField('title').name"
                         editLabel="Listing Title"
                         defaultContent="Please enter a title for this listing"
                         :incomingContent="this.listingField('title').val"></ReactivePopupEdit>
    </h5>

    <div class="regular-lst-desc-edit editable-block"
         style="min-height: 10px; overflow: auto">
      <ReactivePopupEdit :isHtml="true"
                         endPointName=""
                         @saveContent="savePurchaseEvaluationContent"
                         :fieldName="this.listingField('description').name"
                         editLabel="Listing Description"
                         defaultContent="Please enter a description for this listing"
                         :incomingContent="this.listingField('description').val"></ReactivePopupEdit>
    </div>
    <!-- <div style="display: none"></div> -->
    <div>
      <div style="display: inline; margin-left: 8px"
           class="curr-edit-ctr">
        <span class="text-gray-900 text-weight-bold">
          <CurrencyPopupEdit @saveContent="savePurchaseEvaluationContent"
                             endPointName=""
                             prefixText="Currency: "
                             fieldName="listed_sale_price_currency"
                             defaultContent="USD"
                             :incomingContent="String(this.listingField('listed_sale_price_currency').val)">
          </CurrencyPopupEdit>
        </span>
      </div>
      <div style="display: inline; margin-left: 8px"
           class="ed-eval-cols-price-edit-ctr">
        <span class="text-gray-900 text-weight-bold">
          <ReactivePopupEditMoney @saveContent="savePurchaseEvaluationContent"
                                  endPointName=""
                                  prefixText="Price: "
                                  fieldName="listed_sale_price_cents"
                                  defaultContent="0"
                                  :currencyToUse="updatedCurrency ||
                                    String(this.listingField('listed_sale_price_currency').val)
                                    "
                                  :incomingContent="String(this.listingField('listed_sale_price_cents').val)">
          </ReactivePopupEditMoney>
        </span>
      </div>
    </div>
    <div>
      <div style="display: inline; margin-left: 8px"
           class="baths-edit-ctr">
        <span class="text-gray-900 text-weight-bold">
          <ReactivePopupEdit @saveContent="savePurchaseEvaluationContent"
                             endPointName=""
                             prefixText="Bathrooms: "
                             fieldName="count_bathrooms"
                             defaultContent="0"
                             :incomingContent="String(this.listingField('count_bathrooms').val || 0)">
          </ReactivePopupEdit>
        </span>
      </div>
    </div>
    <div>
      <div style="display: inline; margin-left: 8px"
           class="beds-edit-ctr">
        <span class="text-gray-900 text-weight-bold">
          <ReactivePopupEdit @saveContent="savePurchaseEvaluationContent"
                             endPointName=""
                             prefixText="Bedrooms: "
                             fieldName="count_bedrooms"
                             defaultContent="0"
                             :incomingContent="String(this.listingField('count_bedrooms').val || 0)">
          </ReactivePopupEdit>
        </span>
      </div>
    </div>
  </div>
</template>
<script>
import usePurchaseEvaluations from "src/compose/usePurchaseEvaluations.js"
import ReactivePopupEdit from "src/components/edit/ReactivePopupEdit.vue"
import ReactivePopupEditMoney from "src/components/edit/ReactivePopupEditMoney.vue"
import CurrencyPopupEdit from "src/components/edit/CurrencyPopupEdit.vue"
export default {
  components: {
    ReactivePopupEditMoney,
    CurrencyPopupEdit,
    ReactivePopupEdit,
  },
  methods: {
    listingField(fieldName) {
      // apr 2023
      // This is for fields like title that exist associated listing
      let tVal = ""
      if (this.listingItem) {
        tVal = this.listingItem.listing[fieldName]
        if (["count_bedrooms", "count_bathrooms"].includes(fieldName)) {
          tVal = this.listingItem.realty_asset[fieldName]
        }
      }
      //  this.listItemDetails[fieldName] // || this.listingItem.listing.title
      return {
        name: fieldName,
        val: tVal,
      }
    },
    getUpdateObject(fieldName, fieldContent, endPointName) {
      // let topOrBottom = "top"
      // if (this.listingPosition !== "left") {
      //   topOrBottom = "bottom"
      // }
      let updateObject = {
        //this.listingItem.listing.uuid is actually the evaluationUuid
        // TODO: prevent the workaround that causes this confusion
        evaluationUuid: this.listingItem.listing.uuid, // this.$route.params.evaluationUuid,
        // realtyAssetUuid: this.listingItem.realty_asset.uuid,
        // comparisonUuid: this.$route.params.comparisonUuid,
        // editToken: this.$route.params.editToken,
        fieldName: fieldName,
        fieldContent: fieldContent,
        endPointName: endPointName,
      }
      return updateObject
    },
    savePurchaseEvaluationContent(fieldName, fieldContent, endPointName) {
      if (["currency"].includes(fieldName)) {
        // This is just so the UI updates immediately
        this.updatedCurrency = fieldContent
      }
      endPointName = "set_evaluation_field" // endPointName || "set_comparison_field"
      let updateObject = this.getUpdateObject(fieldName, fieldContent, endPointName)
      this.setEvaluationField(updateObject)
        .then(async (response) => {
          // dismissProgressNote()
        })
        .catch((error) => {
          let errorMessage = "There has been an error"
          if (error.response && error.response.status === 403) {
            errorMessage = "You do not have permission to edit this"
          }
          this.$q.notify({
            color: "negative",
            position: "top",
            message: errorMessage,
            icon: "report_problem",
          })
        })
    },
  },
  setup(props) {
    const { setEvaluationField } = usePurchaseEvaluations()
    return {
      setEvaluationField,
    }
  },
  computed: {},
  data() {
    return {
      updatedCurrency: null,
    }
  },
  props: {
    listingItem: {
      type: Object,
      default: () => {
        listing: {
        }
      },
    },
  },
}
</script>
