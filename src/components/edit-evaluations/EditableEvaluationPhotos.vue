<template>
  <div class="q-my-md editable-evaluation-photos">
    <div class="text-center flex flex-center width-full">
      <div class="row"
           style="max-width: 500px">
        <div v-if="picsList.length > 1"
             class="width-full">
          <div class="q-link rounded-borders q-pa-xs cursor-pointer column justify-center bg-grey-3">
            <div class="row no-wrap items-center">
              <div class="col">
                <div class="row">
                  <q-icon v-ripple
                          clickable
                          name="info"
                          class="col-shrink q-pa-sm text-blue cursor-pointer q-hoverable"
                          style="font-size: 1.5rem" />
                  <div class="col"
                       style="margin-top: 10px">
                    Re-order the photos below by dragging and dropping them.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else
             class="q-py-lg q-my-none"
             style="width: 20px"></div>
        <div class="col-12"
             style="">
          <!-- <draggable @end="orderChanged"
                     class="list-group"
                     :component-data="{
                      tag: 'ul',
                      type: 'transition-group',
                      name: !drag ? 'flip-list' : null,
                    }"
                     v-model="picsList"
                     v-bind="dragOptions"
                     item-key="order">
            <template #item="{ element }">
              <li v-if="!element.details.flag_is_hidden"
                  style="list-style: none; height: 300px"
                  class="list-group-item q-pb-md q-pt-md">
                <ChangePhotoVisibilityBtn v-if="showPhotoVisibilityBtn"
                                          restoreOrRemove="remove"
                                          :setOnModelDirectly="true"
                                          :picUuid="element.details.uuid"></ChangePhotoVisibilityBtn>
                <q-img fit="cover"
                       height="100%"
                       :src="element.details.image_details.url"
                       class="edit-list-col-photo">
                  <div class="absolute-bottom text-subtitle2 flex flex-center caption-left">
                    <div v-if="enableTitleEdit"
                         class="width-full photo-txt-edit-ctr">
                      <ReactivePopupEdit @saveContent="savePhotoTitle"
                                         :fieldName="element.details.uuid"
                                         defaultContent="Type here to add something about this photo"
                                         :incomingContent="element.ev_pic_title"></ReactivePopupEdit>
                    </div>
                  </div>
                </q-img>
                <i :class="element.fixed ? 'fa fa-anchor' : 'glyphicon glyphicon-pushpin'"
                   @click="element.fixed = !element.fixed"
                   aria-hidden="true"></i>
              </li>
            </template>
</draggable> -->
          <div class="q-my-lg q-py-lg width-full">
            <q-btn class="width-full"
                   icon="add"
                   @click="startAddPhoto"
                   outline>
              Add Photo
            </q-btn>
            <q-dialog full-width
                      v-model="showUrlPromptModal">
              <q-card>
                <q-card-section>
                  <div class="text-h6">Url of photo to be added:</div>
                </q-card-section>
                <q-card-section class="q-pt-none">
                  <!-- <h4>Coming soon</h4> -->
                  <div>
                    <form @submit.prevent.stop="newPhotoFromUrl"
                          class="q-gutter-md">
                      <q-input style=""
                               bg-color="white"
                               ref="newPhotoUrlRef"
                               color="black"
                               autofocus
                               outlined
                               v-model="newPhotoUrl"
                               label=""
                               hint=""
                               lazy-rules
                               :rules="urlRules" />
                      <div>
                        <q-btn label="Ok"
                               type="submit"
                               color="green" />
                      </div>
                    </form>
                  </div>
                </q-card-section>
              </q-card>
            </q-dialog>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { ref } from "vue"
import useEditHelper from "src/compose/useEditHelper.js"
import usePurchaseEvaluations from "src/compose/usePurchaseEvaluations.js"
import ReactivePopupEdit from "src/components/edit/ReactivePopupEdit.vue"
import ChangePhotoVisibilityBtn from "src/components/edit/ChangePhotoVisibilityBtn.vue"
// import draggable from "vuedraggable"
export default {
  inject: ["currentSbdUserProvider"],
  components: {
    // ChangePhotoVisibilityBtn,
    // ReactivePopupEdit,
    // draggable,
  },
  methods: {
    startAddPhoto() {
      this.showUrlPromptModal = true
    },
    newPhotoFromUrl() {
      this.newPhotoUrlRef.validate()
      if (!this.newPhotoUrlRef.hasError) {
        let currentSvt = this.currentSbdUserProvider.state.currentSvt
        let evaluationUuid = this.evaluationDetails.uuid // this.$route.params.evaluationUuid,
        let updateObject = {
          evaluationUuid: evaluationUuid,
          // editToken: this.$route.params.editToken,
          newPhotoUrl: this.newPhotoUrl,
          svtForBe: currentSvt,
        }
        let helperDetails = {
          reloadLocation: true,
          progressMessage: `Adding ${this.newPhotoUrl}`, // "Thank you. We are processing the url provided ....",
          successMessage: false,
        }
        this.makeEditCall(this.setNewEvaluationPhoto, updateObject, helperDetails).then(
          (responseObject) => {
            // can check responseObject.success
            // and use responseObject.data if true
          }
        )
      }
    },
    orderChanged(sortEvent) {
      let picsOrder = {}
      this.picsList.forEach((picItem, index) => {
        picsOrder[picItem.details.uuid] = index
      })
      let currentSvt = this.currentSbdUserProvider.state.currentSvt
      let evaluationUuid = this.evaluationDetails.uuid // this.$route.params.evaluationUuid,
      let updateObject = {
        picsOrder: picsOrder,
        evaluationUuid: evaluationUuid, // this.$route.params.evaluationUuid,
        // editToken: this.$route.params.editToken,
        svtForBe: currentSvt,
      }
      let helperDetails = {
        reloadLocation: true,
        progressMessage: `Updating order of photos ...`, // "Thank you. We are processing the url provided ....",
        successMessage: false,
      }

      this.makeEditCall(this.setEvaluationPhotosOrder, updateObject, helperDetails).then(
        (responseObject) => {
          // can check responseObject.success
          // and use responseObject.data if true
        }
      )
    },
    savePhotoTitle(picUuid, fieldContent) {
      let currentSvt = this.currentSbdUserProvider.state.currentSvt
      let evaluationUuid = this.evaluationDetails.uuid // this.$route.params.evaluationUuid,
      let updateObject = {
        fieldContent: fieldContent,
        picUuid: picUuid,
        evaluationUuid: evaluationUuid,
        // editToken: this.$route.params.editToken,
        svtForBe: currentSvt,
      }
      let helperDetails = {
        reloadLocation: true,
        progressMessage: `Updating photo title ...`, // "Thank you. We are processing the url provided ....",
        successMessage: false,
      }
      this.makeEditCall(this.setEvaluationPhotoTitle, updateObject, helperDetails).then(
        (responseObject) => {
          // can check responseObject.success
          // and use responseObject.data if true
        }
      )
    },
  },
  setup(props) {
    const {
      setNewEvaluationPhoto,
      setEvaluationPhotoTitle,
      setEvaluationPhotosOrder,
    } = usePurchaseEvaluations()
    const { makeEditCall } = useEditHelper()
    function urlValidator(urlString) {
      let urlIsValid = false
      try {
        const urlObject = new URL(urlString)
        if (urlObject.host.length > 1) {
          urlIsValid = true
        }
      } catch (error) { }
      return urlIsValid
    }
    const newPhotoUrlRef = ref(null)
    return {
      makeEditCall,
      newPhotoUrlRef,
      setEvaluationPhotoTitle,
      setEvaluationPhotosOrder,
      setNewEvaluationPhoto,
      urlRules: [
        (val) => (val && val.length > 0) || "Please type a url here",
        (val) => (val && urlValidator(val)) || "Please enter a valid url",
        // (val) => (val && urlRegex.test(val)) || "Please enter a valid url",
      ],
    }
  },
  watch: {
    viewPics: {
      deep: true,
      immediate: true,
      handler: function (newVal) {
        if (newVal) {
          let pl = {}
          let picsOrder = []
          if (this.evaluationDetails.evaluation_pics_order) {
            picsOrder = this.evaluationDetails.evaluation_pics_order || []
          }
          if (Object.keys(picsOrder).length > 1) {
            pl = newVal.map((details, index) => {
              // let order = picsOrder[newVal[index].uuid]
              let order = picsOrder[details.uuid]
              let ev_pic_title =
                this.evaluationDetails.evaluation_pic_titles[details.uuid] ||
                "Type here to add a title for this photo"
              return { details: details, order: order, ev_pic_title: ev_pic_title }
            })
          } else {
            pl = newVal.map((details, index) => {
              let ev_pic_title =
                this.evaluationDetails.evaluation_pic_titles[details.uuid] ||
                "Type here to add a title for this photo"
              return { details: details, order: index + 1, ev_pic_title: ev_pic_title }
            })
          }
          this.picsList = pl.sort((a, b) => a.order - b.order)
        }
        //  else {
        //   this.localBoolean = this.defaultContent
        // }
      },
    },
  },
  computed: {
    dragOptions() {
      return {
        animation: 200,
        group: "description",
        disabled: false,
        ghostClass: "ghost",
      }
    },
    viewPics() {
      var picsColl = this.evaluationDetails.purchase_evaluation_pics || []
      // if (this.listingPosition === "right") {
      //   picsColl = this.evaluationDetails.bottom_listing_visible_pics
      // }
      // if (this.listingPosition === "left") {
      //   var picsColl = this.evaluationDetails.top_listing_visible_pics
      // }
      // if (this.listingPosition === "none") {
      //   var picsColl = this.evaluationDetails.mgmt_pics
      // }
      return picsColl
    },
    // hiddenPics() {
    //   if (this.listingPosition === "right") {
    //     var picsColl = this.evaluationDetails.bottom_listing_hidden_pics
    //   } else {
    //     var picsColl = this.evaluationDetails.top_listing_hidden_pics
    //   }
    //   return picsColl
    // },
    // picTitles() {
    //   return this.evaluationDetails.pic_comparison_titles || {}
    // },
  },
  data() {
    return {
      showUrlPromptModal: false,
      newPhotoUrl: "",
      picsList: [],
      // list: message.map((name, index) => {
      //   return { name, order: index + 1 }
      // }),
      drag: false,
    }
  },
  props: {
    enableTitleEdit: {
      type: Boolean,
      default: false,
    },
    showPhotoVisibilityBtn: {
      type: Boolean,
      default: true,
    },
    evaluationDetails: {
      type: Object,
      default: () => { },
    },
  },
}
</script>
