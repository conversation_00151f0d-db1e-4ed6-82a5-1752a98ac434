<template>
  <div>
    <q-btn v-if="restoreOrRemove === 'remove'"
           size="xs"
           class=""
           style="float: right; margin-bottom: -25px; z-index: 1"
           fab-mini
           @click="startVisibilityChange()"
           dense
           color="red"
           icon="delete">
      <!-- {{ cardIndex + 1 }} -->
    </q-btn>
    <q-btn v-else
           size="xs"
           class=""
           style="float: right; margin-bottom: -25px; z-index: 1"
           fab-mini
           @click="startVisibilityChange"
           dense
           color="green"
           icon="visibility">
      <!-- {{ cardIndex + 1 }} -->
    </q-btn>
    <!-- <q-dialog v-model="actionsModalVisible">
      <q-card>
        <q-card-section>
          <div class="text-h6 text-center">Actions</div>
        </q-card-section>
        <q-card-section class="q-pt-none">
          <q-btn
            label="View Property"
            color="primary"
            style="color: white"
            class="full-width q-mb-md"
            :to="currentListingRoute"
            icon="visibility"
            v-close-popup
          />
          <br />
          <q-btn
            label="Delete Property"
            style="background: red; color: white"
            @click="showDeleteConfirmation"
            v-close-popup
          />
          <BtnWithAcl
            btnClass=""
            color="red"
            label="Delete Property"
            v-close-popup
            icon="delete"
            @boardOwnerClickEvent="showDeleteConfirmation"
          ></BtnWithAcl>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat label="Cancel" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
    <q-dialog v-model="deleteConfirmationModalVisible">
      <q-card>
        <q-card-section>
          <div class="text-h6">Are you sure you want to delete this property?</div>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn
            flat
            label="Delete"
            color="danger"
            @click="startPropertyDelete"
            v-close-popup
          />
          <q-btn flat label="Cancel" color="primary" v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog> -->
  </div>
</template>
<script>
// import useHomeComparisonEdit from "src/compose/useHomeComparisonEdit.js"
// import useListItemEdit from "src/compose/useListItemEdit.js"
import usePhotoEdit from "src/compose/usePhotoEdit.js"
import useEditHelper from "src/compose/useEditHelper.js"
// import BtnWithAcl from "components/BtnWithAcl.vue"
export default {
  components: {
    // BtnWithAcl,
  },
  props: {
    useCuratedListItem: {
      type: Boolean,
      default: false,
    },
    setOnModelDirectly: {
      type: Boolean,
      default: false,
    },
    restoreOrRemove: {
      type: String,
      default: "remove",
    },
    picUuid: {
      type: String,
    },
  },
  data: () => ({
    // deleteConfirmationModalVisible: false,
    // actionsModalVisible: false,
  }),
  setup(props) {
    let { localPhotoVisibilitySetter } = {}
    // if (props.useCuratedListItem) {
    //   localPhotoVisibilitySetter = useListItemEdit().setPhotoVisibility
    // } else if (props.setOnModelDirectly) {
    //   localPhotoVisibilitySetter = usePhotoEdit().setPhotoVisibilityDirectlyOnModel
    // } else {
    //   localPhotoVisibilitySetter = useHomeComparisonEdit().setPhotoVisibility
    // }
    // Spt 2023 - used above to be able to support different models.
    // In future if I want to support different models I'll emit a callback
    if (props.setOnModelDirectly) {
      localPhotoVisibilitySetter = usePhotoEdit().setPhotoVisibilityDirectlyOnModel
    }

    const { makeEditCall } = useEditHelper()
    return {
      makeEditCall,
      localPhotoVisibilitySetter,
    }
  },
  methods: {
    startVisibilityChange(event) {
      if (event) {
        event.preventDefault()
      }
      let updateObject = {
        comparisonUuid: this.$route.params.comparisonUuid,
        editToken: this.$route.params.editToken,
        picUuid: this.picUuid,
        restoreOrRemove: this.restoreOrRemove,
      }
      if (this.$route.params.listItemUuid) {
        updateObject.listItemUuid = this.$route.params.listItemUuid
      }
      let helperDetails = {
        reloadLocation: true,
        progressMessage: false, // "Thank you. We are processing the url provided ....",
        successMessage: false,
      }
      this.makeEditCall(
        this.localPhotoVisibilitySetter,
        updateObject,
        helperDetails
      ).then((responseObject) => {
        // can check responseObject.success
        // and use responseObject.data if true
      })
    },
    showDeleteConfirmation(event) {
      if (event) {
        event.preventDefault()
      }
      this.deleteConfirmationModalVisible = true
    },
  },
  computed: {},
  mounted: function () { },
}
</script>
<style></style>
