<template>
  <div class="cursor-pointer editable-text" @click="triggerStartEdit">
    {{ prefixText }} {{ localContent }} {{ suffixText }}
    <q-dialog v-model="showCurrOptionsModal">
      <q-card style="width: 300px; max-width: 100vw">
        <q-bar>
          <div class="text-h6 text-center full-width">
            <q-icon name="currency_yen" />
            Select Currency
          </div>
          <q-space />
          <q-btn dense flat icon="close" v-close-popup>
            <q-tooltip class="bg-white text-primary">Close</q-tooltip>
          </q-btn>
        </q-bar>
        <q-card-section class="q-pt-none">
          <div class="q-pa-md">
            <q-option-group
              :options="currencyOptions"
              type="radio"
              @update:model-value="triggerSaveContent"
              v-model="currencyGroup"
            />
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      localContent: "",
      showCurrOptionsModal: false,
      currencyGroup: null,
      currencyOptions: [
        { label: "Euro", value: "EUR", color: "green" },
        { label: "US Dollar", value: "USD", color: "green" },
        { label: "UK Pound", value: "GBP", color: "green" },
        { value: "ARS", label: "Argentine Peso", color: "green" },
        { label: "Australian Dollar", value: "AUD", color: "green" },
        { label: "Brazilian Real", value: "BRL", color: "green" },
        { label: "Canadian Dollar", value: "CAD", color: "green" },
        { label: "Chinese Yuan", value: "CNY", color: "green" },
        { value: "GHS", label: "Ghanaian Cedi", color: "green" },
        { label: "Hong Kong Dollar", value: "HKD", color: "green" },
        { label: "Indian Rupee", value: "INR", color: "green" },
        { label: "Japanese Yen", value: "JPY", color: "green" },
        { label: "Mexican Peso", value: "MXN", color: "green" },
        { value: "NOK", label: "Norwegian Krone", color: "green" },
        { value: "RUB", label: "Russian Ruble", color: "green" },
        { label: "South African Rand", value: "ZAR", color: "green" },
        { label: "Swiss Franc", value: "CHF", color: "green" },
        { label: "Turkish Lira", value: "TRY", color: "green" },
        { label: "Ukrainian Hryvnia", value: "UAH", color: "green" },
      ],
    }
  },
  methods: {
    triggerStartEdit() {
      this.showCurrOptionsModal = true
      this.currencyGroup = this.localContent
    },
    triggerSaveContent(newContent) {
      this.$emit("saveContent", this.fieldName, newContent, this.endPointName)
      this.showCurrOptionsModal = false
      this.localContent = newContent
    },
  },
  watch: {
    incomingContent: {
      deep: true,
      immediate: true,
      handler: function (newVal) {
        if (newVal && !!!["null"].includes(newVal)) {
          this.localContent = newVal
        } else {
          this.localContent = this.defaultContent
        }
      },
    },
  },
  props: {
    incomingContent: {
      type: String,
      default: "",
    },
    defaultContent: {
      type: String,
      default: "Enter a currency",
    },
    fieldName: {
      type: String,
      default: "",
    },
    suffixText: {
      type: String,
      default: "",
    },
    prefixText: {
      type: String,
      default: "",
    },
    endPointName: {
      type: String,
    },
  },
}
</script>
