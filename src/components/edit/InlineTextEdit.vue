<template>
  <div>
    <ReactiveInput
      endPointName=""
      @triggerUpdateContent="triggerUpdateContent"
      @triggerSaveContent="triggerSaveContent"
      :fieldName="fieldName"
      editLabel=""
      defaultContent=""
      :incomingContent="incomingContent"
      :cancelTrigger="cancelTrigger"
      :isHtml="isHtml"
    >
    </ReactiveInput>
    <div>
      <div v-if="contentChanged" class="q-popup-edit__buttons row justify-center no-wrap">
        <q-btn
          flat
          class="q-btn q-btn-item non-selectable no-outline q-btn--flat q-btn--rectangle text-primary q-btn--actionable q-focusable q-hoverable q-btn--active"
          @click="cancelUpdate"
          padding="sm"
          size="md"
        >
          CANCEL
        </q-btn>
        <q-btn
          flat
          class="q-btn q-btn-item non-selectable no-outline q-btn--flat q-btn--rectangle text-primary q-btn--actionable q-focusable q-hoverable q-btn--active"
          @click="triggerSaveContent"
          padding="sm"
          size="md"
        >
          SAVE
        </q-btn>
        <!--  -->
        <!-- <button
          class="q-btn q-btn-item non-selectable no-outline q-btn--flat q-btn--rectangle text-primary q-btn--actionable q-focusable q-hoverable"
          tabindex="0"
          type="button"
        >
          <span class="q-focus-helper"></span
          ><span
            class="q-btn__content text-center col items-center q-anchor--skip justify-center row"
            ><span class="block">Set</span></span
          >
        </button> -->
      </div>
    </div>
    <!-- -->
  </div>
</template>
<script>
import ReactiveInput from "src/components/edit/ReactiveInput.vue"
export default {
  components: { ReactiveInput },
  data() {
    return {
      newContent: "",
      cancelTrigger: "",
    }
  },
  computed: {
    contentChanged() {
      return this.newContent !== this.incomingContent
    },
  },
  methods: {
    triggerUpdateContent(newContent) {
      this.newContent = newContent
    },
    triggerSaveContent(saveEvent) {
      this.$emit("saveContent", this.fieldName, this.newContent)
    },
    cancelUpdate() {
      this.cancelTrigger = Date.now().toString()
      // this.newContent = this.incomingContent
      // below was to retrigger incomingContent
      // watcher on reactiveInput component
      // this.incomingContent = ""
    },
  },
  watch: {
    incomingContent: {
      deep: true,
      immediate: true,
      handler: function (newVal) {
        if (newVal) {
          this.newContent = newVal
        } else {
          this.newContent = this.defaultContent
        }
      },
    },
  },
  props: {
    isHtml: {
      type: Boolean,
      default: false,
    },
    incomingContent: {
      type: String,
      default: "",
    },
    defaultContent: {
      type: String,
      default: "Type Something",
    },
    fieldName: {
      type: String,
      default: "",
    },
    editLabel: {
      type: String,
      default: "",
    },
  },
}
</script>
