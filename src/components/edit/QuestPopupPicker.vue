<template>
  <div class="cursor-pointer editable-text" @click="triggerStartPicking">
    <q-btn
      no-caps
      style="border-radius: 90px; background: rgb(10, 0, 131); color: white"
      class="rounded-borders"
      @click="triggerStartPicking"
      padding="sm"
      size="md"
    >
      <div class="row items-center no-wrap">
        <!-- <q-icon size="sm" left name="thumb_up" /> -->
        <div class="text-center">Change Quest</div>
      </div>
    </q-btn>
    <q-dialog v-model="showCurrOptionsModal">
      <q-card style="width: 300px; max-width: 100vw">
        <q-bar>
          <div class="text-h6 text-center full-width">
            <!-- <q-icon name="currency_yen" /> -->
            Select Quest
          </div>
          <q-space />
          <q-btn dense flat icon="close" v-close-popup>
            <q-tooltip class="bg-white text-primary">Close</q-tooltip>
          </q-btn>
        </q-bar>
        <q-card-section class="q-pt-none">
          <div v-if="questsToPickFrom.length > 0" class="q-pa-md">
            <q-option-group
              :options="questsToPickFrom"
              type="radio"
              @update:model-value="triggerSaveContent"
              v-model="newlySelectedQuest"
            >
              <template v-slot:label="opt">
                <div class="row items-center">
                  <span class="text-teal">{{ opt.quest_title_html || opt.uuid }}</span>
                  <q-icon :name="opt.icon" color="teal" size="1.5em" class="q-ml-sm" />
                </div>
              </template>
            </q-option-group>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>
  </div>
</template>
<script>
import useSubdomains from "src/compose/useSubdomains.js"
import useEditHelper from "src/compose/useEditHelper.js"
export default {
  components: {},
  setup() {
    const { getAllQuests } = useSubdomains()
    const { makeEditCall } = useEditHelper()
    return {
      getAllQuests,
      makeEditCall,
    }
  },
  mounted: function () {},
  data() {
    return {
      questsToPickFrom: [],
      localContent: "",
      showCurrOptionsModal: false,
      newlySelectedQuest: null,
      // currencyOptions: [
      // ],
    }
  },
  methods: {
    getQuestsForSelectDialog() {
      let updateObject = {}
      let helperDetails = {}
      this.makeEditCall(this.getAllQuests, updateObject, helperDetails).then(
        (responseObject) => {
          if (responseObject && responseObject.data) {
            this.questsToPickFrom = responseObject.data.quests
          }
        }
      )
    },
    triggerStartPicking() {
      this.showCurrOptionsModal = true
      this.getQuestsForSelectDialog()
      // this.newlySelectedQuest = this.localContent
    },
    triggerSaveContent(newQuestUuid, bb) {
      let newQuestObj = this.questsToPickFrom.find((obj) => obj.value == newQuestUuid)
      this.$emit("setNewQuest", newQuestObj)
      this.showCurrOptionsModal = false
      // this.localContent = newContent
    },
  },
  props: {
    // fieldName: {
    //   type: String,
    //   default: "",
    // },
  },
}
</script>
