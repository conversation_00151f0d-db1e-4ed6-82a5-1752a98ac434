<template>
  <div>
    <h6
      class="reactive-inp-ctr q-my-sm q-mr-lg text-left text-body1"
      style="min-height: 10px; overflow: auto"
    >
      <q-editor
        :toolbar="[
          ['bold', 'italic', 'strike', 'underline', 'subscript', 'superscript'],
          ['token', 'hr', 'link', 'custom_btn'],
          ['print', 'fullscreen'],
          ['undo', 'redo'],
          ['viewsource'],
        ]"
        v-if="isHtml"
        v-model="localContent"
        min-height="5rem"
      />
      <q-input
        v-else
        @blur="triggerCancel"
        v-model="localContent"
        @keyup.enter="enterPressed"
        debounce="100"
        label=""
      >
        <template v-slot:before>
          <div
            v-if="editLabel"
            class="text-black q-mt-sm q-pt-sm q-mr-lg text-body1 quest-req-items-lbl items-end flex full-height"
          >
            {{ editLabel }}
          </div>
        </template>
      </q-input>
    </h6>
  </div>
</template>
<script>
export default {
  components: {},
  computed: {
    // localContent: {
    //   // getter
    //   get() {
    //     return this.incomingContent
    //   },
    //   set(newContent) {
    //     this.$emit("triggerUpdateContent", newContent) // this.fieldName, newContent)
    //   },
    // },
  },
  data() {
    return {
      localContent: "",
    }
  },
  methods: {
    enterPressed(keyEvent) {
      this.$emit("triggerSaveContent", "") // this.fieldName, newContent)
    },
    triggerCancel(cancelEvt) {
      // this.localContent = this.incomingContent
      // cancelEvt.preventDefault()
    },
  },
  watch: {
    cancelTrigger: {
      deep: false,
      immediate: false,
      handler: function (newVal) {
        this.localContent = this.incomingContent
      },
    },
    localContent: {
      deep: false,
      immediate: false,
      handler: function (newVal) {
        this.$emit("triggerUpdateContent", newVal) // this.fieldName, newContent)
      },
    },
    incomingContent: {
      deep: true,
      immediate: true,
      handler: function (newVal) {
        if (newVal) {
          this.localContent = newVal
        } else {
          this.localContent = this.defaultContent
        }
      },
    },
  },
  props: {
    incomingContent: {
      type: String,
      default: "",
    },
    defaultContent: {
      type: String,
      default: "Type Something",
    },
    fieldName: {
      type: String,
      default: "",
    },
    cancelTrigger: {
      type: String,
      default: "",
    },
    editLabel: {
      type: String,
      default: "",
    },
    isHtml: {
      type: Boolean,
      default: false,
    },
  },
}
</script>
