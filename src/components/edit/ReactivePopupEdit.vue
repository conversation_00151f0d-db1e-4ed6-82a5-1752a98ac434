<template>
  <div class="cursor-pointer reactive-popup-edit">
    <div v-if="isHtml" style="min-height: 100px">
      <div v-html="localContent"></div>
    </div>
    <div v-else>
      <div class="editable-text" style="">
        {{ prefixText }}
        <span style=""> {{ localContent }} </span>{{ suffixText }}
      </div>
      <!-- <q-input v-model="localContent" dense autofocus counter /> -->
    </div>
    <q-popup-edit
      @save="triggerSaveContent"
      v-model="localContent"
      buttons
      auto-save
      v-slot="scope"
    >
      <div class="edit-color q-pb-sm">
        {{ editLabel || prefixText }}
      </div>
      <q-editor
        :toolbar="[
          ['bold', 'italic', 'strike', 'underline', 'subscript', 'superscript'],
          ['token', 'hr', 'link', 'custom_btn'],
          ['print', 'fullscreen'],
          ['undo', 'redo'],
          ['viewsource'],
        ]"
        v-if="isHtml"
        v-model="scope.value"
        min-height="5rem"
      />
      <q-input
        v-else
        v-model="scope.value"
        dense
        autofocus
        counter
        @keyup.enter="scope.set"
      />
    </q-popup-edit>
  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      localContent: "",
    }
  },
  methods: {
    triggerSaveContent(newContent) {
      this.$emit("saveContent", this.fieldName, newContent, this.endPointName)
    },
  },
  watch: {
    incomingContent: {
      deep: true,
      immediate: true,
      handler: function (newVal) {
        if (newVal) {
          this.localContent = newVal
        } else {
          this.localContent = this.defaultContent
        }
      },
    },
  },
  props: {
    incomingContent: {
      type: String,
      default: "",
    },
    defaultContent: {
      type: String,
      default: "Type Something",
    },
    fieldName: {
      type: String,
      default: "",
    },
    suffixText: {
      type: String,
      default: "",
    },
    prefixText: {
      type: String,
      default: "",
    },
    editLabel: {
      type: String,
      default: "",
    },
    endPointName: {
      type: String,
    },
    isHtml: {
      type: Boolean,
      default: false,
    },
  },
}
</script>
