<template>
  <div class="cursor-pointer editable-text-disabled ReactivePopupEditMoney">
    {{ prefixText }}
    <div style="display: inline-block" class="cursor-pointer editable-text-disabled">
      <ConvertableCurrencyDisplay
        :currencyConversionEnabled="false"
        :prefixText="prefixText"
        :priceInCents="localPriceInCents"
        :originalCurrency="checkedCurrencyToUse || 'GBP'"
      >
      </ConvertableCurrencyDisplay>
    </div>
    {{ suffixText }}
    <q-popup-edit
      @save="triggerSaveContent"
      v-model="localPrice"
      buttons
      auto-save
      v-slot="scope"
    >
      <div class="edit-color q-pb-sm">{{ editLabel }}</div>
      <MoneyValueField
        v-model="scope.value"
        :currencyToUse="checkedCurrencyToUse"
        dense
        autofocus
        @keyup.enter="scope.set"
      ></MoneyValueField>
      <!-- <q-input v-model="scope.value" dense autofocus counter @keyup.enter="scope.set" /> -->
    </q-popup-edit>
  </div>
</template>
<script>
import ConvertableCurrencyDisplay from "components/money/ConvertableCurrencyDisplay.vue"
// import ConvertableCurrencyDisplay from "components/widgets/ConvertableCurrencyDisplay.vue"
import MoneyValueField from "src/components/money/MoneyValueField.vue"
export default {
  components: { MoneyValueField, ConvertableCurrencyDisplay },
  data() {
    return {
      localPrice: "",
      localPriceInCents: null,
    }
  },
  computed: {
    checkedCurrencyToUse() {
      let ccu = this.currencyToUse
      if (ccu === "null") {
        return "USD"
      } else {
        return ccu
      }
    },
  },
  methods: {
    triggerSaveContent(newValue) {
      newValue = newValue * 100
      this.localPriceInCents = newValue
      this.$emit("saveContent", this.fieldName, newValue, this.endPointName)
    },
    // moneyValueChanged(newValue) {
    //   // let newValue = this.parse(event.currentTarget.value, this.currencyOptions)
    //   //  event.currentTarget.value
    //   // newValue = newValue * 100
    //   // this.$emit("saveContent", this.fieldName, newValue, this.endPointName)
    // },
  },
  watch: {
    incomingContent: {
      deep: true,
      immediate: true,
      handler: function (newVal) {
        if (newVal && !!!["null"].includes(newVal)) {
          this.localPriceInCents = parseFloat(newVal)
          this.localPrice = parseFloat(newVal) / 100
          // this.localPrice = newVal
        } else {
          this.localPrice = this.defaultContent
        }
      },
    },
  },
  props: {
    currencyToUse: {
      type: String,
      default: "GBP",
    },
    incomingContent: {
      type: String,
      default: "",
    },
    defaultContent: {
      type: String,
      default: "0",
    },
    fieldName: {
      type: String,
      default: "",
    },
    suffixText: {
      type: String,
      default: "",
    },
    prefixText: {
      type: String,
      default: "",
    },
    endPointName: {
      type: String,
    },
    editLabel: {
      type: String,
    },
  },
}
</script>
