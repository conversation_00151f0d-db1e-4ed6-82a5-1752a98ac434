<template>
  <div class="cursor-pointer">
    <q-toggle
      v-model="localBoolean"
      color="green"
      label=""
      unchecked-icon="lock"
      checked-icon="visibility"
      @update:model-value="fieldChangeHandler"
    />
  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {
      localBoolean: false,
    }
  },
  computed: {
    // localBoolean: {
    //   // getter
    //   get() {
    //     return this.incomingBoolean
    //   },
    //   set(newVal) {
    //     return newVal
    //     // this.editBoardModalIsVisible = true
    //   },
    // },
  },
  methods: {
    fieldChangeHandler(newBool) {
      this.$emit("saveContent", this.fieldName, newBool)
    },
  },
  watch: {
    incomingBoolean: {
      deep: true,
      immediate: true,
      handler: function (newVal) {
        if (newVal) {
          this.localBoolean = newVal
        }
        //  else {
        //   this.localBoolean = this.defaultContent
        // }
      },
    },
  },
  props: {
    incomingBoolean: {
      type: <PERSON><PERSON><PERSON>,
      default: false,
    },
    // defaultContent: {
    //   type: String,
    //   default: "Type Something",
    // },
    fieldName: {
      type: String,
      default: "",
    },
  },
}
</script>
