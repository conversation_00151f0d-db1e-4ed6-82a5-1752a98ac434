<template>
  <div class="q-mx-xs q-py-md purchase-evaluation-block">
    <div class="q-py-md row no-wrap items-center justify-around">
      <div v-if="priceInCents"
           class="text-h5 q-pr-lg col-md-6 pe-curr-disp">
        <ConvertableCurrencyDisplay :priceInCents="priceInCents"
                                    :currencyConversionEnabled="false"
                                    :originalCurrency="evaluationDetails.listed_sale_price_currency || 'EUR'">
        </ConvertableCurrencyDisplay>
      </div>
      <div class="flex-1 inline-flex items-center q-pr-sm q-mt-md">
        <q-icon size="1rem">
          <svg class="h-6 w-6 text-gray-600 fill-current mr-3"
               xmlns="http://www.w3.org/2000/svg"
               viewBox="0 0 24 24">
            <path
                  d="M0 16L3 5V1a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v4l3 11v5a1 1 0 0 1-1 1v2h-1v-2H2v2H1v-2a1 1 0 0 1-1-1v-5zM19 5h1V1H4v4h1V4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v1h2V4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v1zm0 1v2a1 1 0 0 1-1 1h-4a1 1 0 0 1-1-1V6h-2v2a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1V6H3.76L1.04 16h21.92L20.24 6H19zM1 17v4h22v-4H1zM6 4v4h4V4H6zm8 0v4h4V4h-4z">
            </path>
          </svg>
        </q-icon>
        <div style="display: inline; margin-left: 8px">
          <span class="text-gray-900 text-weight-bold">
            {{ evaluationDetails?.count_bedrooms }}
          </span>
          Bedrooms
        </div>
      </div>
      <div class="flex-1 inline-flex items-center q-pl-sm q-mt-md">
        <q-icon size="1rem">
          <svg class="h-6 w-6 text-gray-600 fill-current mr-3"
               xmlns="http://www.w3.org/2000/svg"
               viewBox="0 0 24 24">
            <path fill-rule="evenodd"
                  d="M17.03 21H7.97a4 4 0 0 1-1.3-.22l-1.22 2.44-.9-.44 1.22-2.44a4 4 0 0 1-1.38-1.55L.5 11h7.56a4 4 0 0 1 1.78.42l2.32 1.16a4 4 0 0 0 1.78.42h9.56l-2.9 5.79a4 4 0 0 1-1.37 1.55l1.22 2.44-.9.44-1.22-2.44a4 4 0 0 1-1.3.22zM21 11h2.5a.5.5 0 1 1 0 1h-9.06a4.5 4.5 0 0 1-2-.48l-2.32-1.15A3.5 3.5 0 0 0 8.56 10H.5a.5.5 0 0 1 0-1h8.06c.7 0 1.38.16 2 .48l2.32 1.15a3.5 3.5 0 0 0 1.56.37H20V2a1 1 0 0 0-1.74-.67c.64.97.53 2.29-.32 3.14l-.35.36-3.54-3.54.35-.35a2.5 2.5 0 0 1 3.15-.32A2 2 0 0 1 21 2v9zm-5.48-9.65l2 2a1.5 1.5 0 0 0-2-2zm-10.23 17A3 3 0 0 0 7.97 20h9.06a3 3 0 0 0 2.68-1.66L21.88 14h-7.94a5 5 0 0 1-2.23-.53L9.4 12.32A3 3 0 0 0 8.06 12H2.12l3.17 6.34z">
            </path>
          </svg>
        </q-icon>
        <div style="display: inline; margin-left: 8px">
          <span class="text-gray-900 text-weight-bold">
            {{ evaluationDetails?.count_bathrooms }}
          </span>
          Bathrooms
        </div>
      </div>
    </div>
    <q-separator />
    <div v-if="evaluationDetails?.import_url"
         class="row or-ls-url q-py-md">
      <div class="col-xs-12">
        <span>Original Listing: </span>
        <a target="_blank"
           :href="evaluationDetails.import_url">
          {{ evaluationDetails.import_url }}</a>
      </div>
    </div>
    <div v-if="showDescription && evaluationDetails?.description">
      <div class="col-xs-12 lst-subsect-txt-ctr">
        <h5 class="font-medium text-center title-font text-gray-900 q-py-sm q-my-xs sbs-desc-txt">
          Description
          <br />
        </h5>
        <hr class="custom-hr-listing" />
      </div>
      <!-- <div
        :class="[`text-gray text-body1 text-weight-light q-py-lg`, descCtrClass]"
      ></div> -->
      <div v-if="evaluationDetails.description">
        <div v-html="evaluationDetails.description"
             class="pe-desc-text-blk"></div>
      </div>
      <div v-else
           class="text-left q-pt-sm">
        <div>There is no description available for this listing.</div>
      </div>
    </div>
  </div>
</template>
<script>
import ConvertableCurrencyDisplay from "components/money/ConvertableCurrencyDisplay.vue"
// import ConvertableCurrencyDisplay from "components/widgets/ConvertableCurrencyDisplay.vue"
export default {
  // inject: ["configAndLocalData"],
  components: {
    ConvertableCurrencyDisplay,
  },
  setup(props) {
    // return {
    // }
  },
  data() {
    return {}
  },
  computed: {
    // descCtrClass() {
    //   return ""
    // },
    priceInCents() {
      return this.evaluationDetails?.listed_sale_price_cents // || 0
    },
  },
  methods: {},
  props: {
    evaluationDetails: {
      type: Object,
      default: () => { },
    },
    showDescription: {
      type: Boolean,
      default: true,
    },
  },
}
</script>
<style scoped>
.pe-desc-text-blk {
  max-height: 200px;
  overflow: scroll;
}
</style>
