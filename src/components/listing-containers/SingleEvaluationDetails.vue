<template>
  <div class="q-mx-xs q-py-md single-evaluation-details">
    <div class="lst-car-ctr">
      <ListingCarousel :carouselSlides="carouselSlides"> </ListingCarousel>
      <!-- <q-img fit="cover" :src="singleColImage" class="sing-lst-det-img">
      </q-img> -->
    </div>
    <div class="peb-ctr">
      <PurchaseEvaluationBlock :evaluationDetails="evaluationDetails"></PurchaseEvaluationBlock>
    </div>
    <div class="sed-map-ctr">
      <!-- <ExpandableContainer :expansionDetails="mapExpansionDetails">
        <PoiMap :activeEvaluationDetails="evaluationDetails"
                :listingSummaries="listingSummaries"></PoiMap>
      </ExpandableContainer> -->
    </div>

    <div class="row q-col-gutter-md sl-sum-thb-ctr"
         v-if="listingSummaries.length > 0">
      <div class="col-xs-12 lst-subsect-txt-ctr">
        <h5 class="font-medium text-center title-font text-gray-900 q-py-sm q-my-xs other-props-txt">
          Other Properties
          <br />
        </h5>
        <hr class="custom-hr-listing" />
      </div>
      <!-- <div class="col-xs-6 col-md-4 col-lg-3 col-xl-2"
           v-for="listingSummary in listingSummaries"
           :key="listingSummary.uuid">
        <div class="q-mb-md">
          <div class="q-pa-none">
            <ShareLinkThumb :listingSummary="listingSummary"> </ShareLinkThumb>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>
<script>
import PurchaseEvaluationBlock from "src/components/listing-blocks/PurchaseEvaluationBlock.vue"
import ListingCarousel from "src/components/pics/ListingCarousel.vue"
// import PoiMap from "src/components/maps/PoiMap.vue"
// import ExpandableContainer from "src/components/content/ExpandableContainer.vue"
// import ShareLinkThumb from "src/components/summary-widgets/ShareLinkThumb.vue"
export default {
  components: {
    // ExpandableContainer,
    // ShareLinkThumb,
    // PoiMap,
    PurchaseEvaluationBlock,
    ListingCarousel,
  },
  // setup(props) {},
  data() {
    return {
      mapExpansionDetails: {
        expTitle: "Location",
        expModel: false,
      },
    }
  },
  computed: {
    listingSummaries() {
      if (this.currQuest && this.currQuest.rawPfch) {
        let listingSummaries = []
        let questPeSharelinks = this.currQuest.rawPfch.slqpe || []
        questPeSharelinks.forEach((slItem) => {
          let mainPic = slItem.purchase_evaluation.sale_listing_pics[0]
          let infoWindowText =
            slItem.purchase_evaluation.title || slItem.purchase_evaluation.street_address
          let markerPosition = null
          let mainPicUrl = null
          if (mainPic) {
            mainPicUrl = mainPic.image_details.url
          }
          if (slItem.purchase_evaluation && slItem.purchase_evaluation.longitude) {
            // let slItem.purchase_evaluation = placesOfInterest[placeKey]
            markerPosition = {
              lat: parseFloat(slItem.purchase_evaluation.latitude),
              lng: parseFloat(slItem.purchase_evaluation.longitude),
            }
          }
          let shareRoute = {
            name: "rFeedbackForEvalGeneric",
            params: {
              evaluationShareLinkUuid: slItem.uuid,
            },
          }
          let shareRouteUrl = ""
          if (location) {
            shareRouteUrl = `${location.origin}${this.$router.resolve(shareRoute).href}`
          }
          if (this.evaluationDetails.uuid !== slItem.purchase_evaluation.uuid) {
            listingSummaries.push({
              shareRouteUrl: shareRouteUrl,
              uuid: slItem.purchase_evaluation.uuid,
              position: markerPosition,
              infoWindowText: infoWindowText,
              mainPicUrl: mainPicUrl,
              count_bedrooms: slItem.purchase_evaluation.count_bedrooms,
              count_bathrooms: slItem.purchase_evaluation.count_bathrooms,
              listed_sale_price_cents: slItem.purchase_evaluation.listed_sale_price_cents,
              listed_sale_price_currency:
                slItem.purchase_evaluation.listed_sale_price_currency,
            })
          }
        })
        return listingSummaries
      } else {
        return []
      }
    },
    carouselSlides() {
      let carouselSlides = []
      let picsColl = []
      let picsOrder = []
      if (this.evaluationDetails && this.evaluationDetails.sale_listing_pics) {
        picsColl = this.evaluationDetails.sale_listing_pics
        picsOrder = this.evaluationDetails.evaluation_pics_order || []
      }

      picsColl.forEach(function (picObject, index) {
        let imageUrl = picObject.image_details.url
        // if (imageUrl[0] === "/") {
        //   // imageUrl = `${dataApiBase}${picObject.image_details.url}`
        // }
        if (!!!picObject.flag_is_hidden) {
          // having picObject.sort_order fallback below can lead to errors as
          // 0 gets interpretted as false
          let sortOrder = picsOrder[picObject.uuid] // || picObject.sort_order
          carouselSlides.push({
            thumb: imageUrl,
            src: imageUrl,
            altText: picObject.photo_title,
            // sortOrder: picObject.sort_order,
            sortOrder: sortOrder,
          })
        }
      })
      return carouselSlides.sort((a, b) => a.sortOrder - b.sortOrder)
    },
  },
  methods: {},
  props: {
    evaluationDetails: {
      type: Object,
      default: () => { },
    },
    currQuest: {
      type: Object,
      default: () => { },
    },
  },
}
</script>
<style>
.dtp-summ-card {
  border-top: 3px solid #9acd32;
}
</style>
