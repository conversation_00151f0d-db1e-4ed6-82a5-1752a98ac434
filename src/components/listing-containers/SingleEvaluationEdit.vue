<template>
  <div class="q-ma-xs single-bex-listing-edit"
       style="height: 100vh">
    <q-no-ssr>
      <div class="full-height"
           v-if="evaluationDetails">
        <!-- <h3 class="text-center text-h5 q-pt-md q-ma-sm">
          {{ evaluationDetails.title }}
        </h3> -->

        <div class="column">
          <div>
            <q-card class="q-pa-md">
              <EditableEvaluationCols :listingItem="bexListingWorkaround"></EditableEvaluationCols>
            </q-card>
          </div>
          <div>
            <EditableEvaluationPhotos :enableTitleEdit="true"
                                      :showPhotoVisibilityBtn="true"
                                      :evaluationDetails="evaluationDetails"></EditableEvaluationPhotos>
          </div>
        </div>
      </div>
    </q-no-ssr>
  </div>
</template>
<script>
import useEditHelper from "src/compose/useEditHelper.js"
// import usePurchaseEvaluation from "src/compose/usePurchaseEvaluation.js"
import EditableEvaluationCols from "src/components/edit-evaluations/EditableEvaluationCols.vue"
import EditableEvaluationPhotos from "src/components/edit-evaluations/EditableEvaluationPhotos.vue"
export default {
  // inject: ["currentUserProvider"],
  components: {
    EditableEvaluationPhotos,
    EditableEvaluationCols,
  },
  setup(props) {
    // const { getSinglePurchaseEvaluation } = usePurchaseEvaluation()
    // const { makeEditCall } = useEditHelper()
    // return {
    //   getSinglePurchaseEvaluation,
    //   makeEditCall,
    // }
  },
  data() {
    return {}
  },
  computed: {
    bexListingWorkaround() {
      let bexListingWorkaround = {
        listing: this.evaluationDetails,
        realty_asset: this.evaluationDetails,
      }
      return bexListingWorkaround
    },
  },
  methods: {},
  watch: {},
  props: {
    evaluationDetails: {
      type: Object,
      default: () => { },
    },
  },
}
</script>
