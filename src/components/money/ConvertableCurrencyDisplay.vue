<template>
  <div class="c-curr-disp" style="">
    <ConvertableCurrencyDisplayInner
      v-if="currencyConversionReallyEnabled"
      :priceInCents="priceInCents"
      :originalCurrency="originalCurrency"
      :currencyRates="currencyRates"
    ></ConvertableCurrencyDisplayInner>
    <div v-else>
      {{ originalPrice }}
    </div>
    <!-- <div class="cursor-pointer" v-if="priceIsValid">
      {{ prefixText }} {{ convertedPrice }}
      <br v-if="$q.platform.is.mobile" />
      <span v-if="showOriginalPrice"> ({{ originalPrice }}) </span>
    </div>
    <div v-else>Unknown</div> -->
    <!-- <ChangeCurrencyDialog
      @endChangeCurr="endChangeCurr"
      :showCurrOptionsModal="showCurrOptionsModal"
    ></ChangeCurrencyDialog> -->
  </div>
</template>
<script>
// import ChangeCurrencyDialog from "src/h2c/components/nav/ChangeCurrencyDialog.vue"
import ConvertableCurrencyDisplayInner from "components/money/ConvertableCurrencyDisplayInner.vue"

export default {
  // inject: ["currentSbdUserProvider"],
  components: {
    // ChangeCurrencyDialog,
    ConvertableCurrencyDisplayInner,
  },
  data() {
    return {}
  },
  props: {
    currencyConversionEnabled: {
      type: Boolean,
      default: true,
    },
    showCurrOptionsModal: {
      type: Boolean,
      default: false,
    },
    prefixText: {
      type: String,
      default: "",
    },
    interestedInOriginalPrice: {
      type: Boolean,
      default: true,
    },
    priceInCents: {
      type: Number,
      default: null,
    },
    originalCurrency: {
      type: String,
      default: "USD",
    },
  },
  computed: {
    currencyRates() {
      // spt 2023 - will worry about this later
      let currencyRates = [] // this.currentSbdUserProvider.state.currencyRates
      if (Object.keys(currencyRates).length < 1 || currencyRates.rates.length < 1) {
        return null
      } else {
        return currencyRates
      }
    },
    currencyConversionReallyEnabled() {
      if (this.currencyRates === null) {
        return false
      } else {
        return this.currencyConversionEnabled
      }
      // return this.currencyConversionEnabled
    },
    priceIsValid() {
      if (this.priceInCents === 0) {
        return false
      } else {
        return true
      }
    },
    checkedOriginalCurrency() {
      return this.originalCurrency || "USD" // this.currentSbdUserProvider.getCurrencyToUse()
    },
    currencyToConvertTo() {
      return "EUR"
      // spt 2023 - will worry about this later
      // return this.currentSbdUserProvider.getCurrencyToUse()
    },
    showOriginalPrice() {
      return (
        this.interestedInOriginalPrice &&
        this.checkedOriginalCurrency !== this.currencyToConvertTo
      )
    },
    originalPrice() {
      const originalPrice = this.priceInCents / 100
      let formatedOriginalPrice = originalPrice
      try {
        formatedOriginalPrice = new Intl.NumberFormat("en", {
          style: "currency",
          currency: this.checkedOriginalCurrency,
          maximumFractionDigits: 0,
        }).format(originalPrice)
      } catch (err) {
        // Will result in Invalid currency code err
        // if invalid code returned from server
        console.log(err.message)
      }
      return formatedOriginalPrice
    },
  },
  methods: {
    endChangeCurr() {
      this.$emit("endChangeCurr")
    },
  },
}
</script>
