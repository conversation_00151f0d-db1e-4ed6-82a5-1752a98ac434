<template>
  <div class="cursor-pointer" v-if="priceIsValid">
    {{ prefixText }} {{ convertedPrice }}
    <br v-if="$q.platform.is.mobile" />
    <span v-if="showOriginalPrice"> ({{ originalPrice }}) </span>
  </div>
</template>
<script>
export default {
  inject: ["currentSbdUserProvider"],
  data() {
    return {
      // showCurrOptionsModal: false,
    }
  },
  props: {
    currencyRates: {
      type: Object,
      default: () => {
        return {}
      },
    },
    showCurrOptionsModal: {
      type: Boolean,
      default: false,
    },
    prefixText: {
      type: String,
      default: "",
    },
    interestedInOriginalPrice: {
      type: Boolean,
      default: true,
    },
    priceInCents: {
      type: Number,
      default: null,
    },
    originalCurrency: {
      type: String,
      default: "USD",
    },
  },
  computed: {
    priceIsValid() {
      if (this.priceInCents === 0) {
        return false
      } else {
        return true
      }
    },
    checkedOriginalCurrency() {
      return this.originalCurrency || "USD" // this.currentSbdUserProvider.getCurrencyToUse()
    },
    currencyToConvertTo() {
      // When I implement currency conversions I will set below to the current
      // user's preference
      // return this.checkedOriginalCurrency
      return this.currentSbdUserProvider.getCurrencyToUse()
    },
    showOriginalPrice() {
      return (
        this.interestedInOriginalPrice &&
        this.checkedOriginalCurrency !== this.currencyToConvertTo
      )
    },
    originalPrice() {
      const originalPrice = this.priceInCents / 100
      let formatedOriginalPrice = originalPrice
      try {
        formatedOriginalPrice = new Intl.NumberFormat("en", {
          style: "currency",
          currency: this.checkedOriginalCurrency,
          maximumFractionDigits: 0,
        }).format(originalPrice)
      } catch (err) {
        // Will result in Invalid currency code err
        // if invalid code returned from server
        console.log(err.message)
      }
      return formatedOriginalPrice
    },
    convertedPrice() {
      let convertedPrice = this.priceInCents / 100

      if (this.checkedOriginalCurrency !== this.currencyToConvertTo) {
        const currencyRates = this.currentSbdUserProvider.state.currencyRates
        // const currencyRates = this.currencyRates
        if (Object.keys(currencyRates).length < 1 || currencyRates.rates.length < 1) {
          // There is an error in the currencyRates object
          // so we will just return the original price
          return convertedPrice
        }
        let priceInUsd = convertedPrice
        if (this.originalCurrency !== "USD") {
          const toUsdRateName = `${this.originalCurrency}`
          const toUsdRate = currencyRates.rates[toUsdRateName] // currencyRates[toUsdRateName]
          priceInUsd = convertedPrice / parseFloat(toUsdRate)
        }
        if (this.currencyToConvertTo === "USD") {
          convertedPrice = priceInUsd
        } else {
          const targetRateName = `${this.currencyToConvertTo}`
          const targetRate = currencyRates.rates[targetRateName]
          convertedPrice = priceInUsd * parseFloat(targetRate)
        }
      }
      let formatedConvertedPrice = convertedPrice
      // if (isNaN(convertedPrice)) {
      // }
      try {
        formatedConvertedPrice = new Intl.NumberFormat("en", {
          style: "currency",
          currency: this.currencyToConvertTo,
          maximumFractionDigits: 0,
        }).format(convertedPrice)
      } catch (err) {
        // Will result in Invalid currency code err
        // if invalid code returned from server
        console.log(err.message)
      }
      return formatedConvertedPrice
    },
  },
  methods: {
    // endChangeCurr() {
    //   this.$emit("endChangeCurr")
    // },
  },
}
</script>
