<template>
  <div>
    <q-input
      :hide-bottom-space="true"
      class="regular-textfield-input"
      dense
      autofocus
      v-model="formattedValue"
      :label="prefixText"
      hint=""
      lazy-rules
      ref="inputRef"
    />
  </div>
</template>
<script>
import { useCurrencyInput } from "vue-currency-input"
import { computed, watch } from "vue"
// import { CurrencyDirective, setValue, getValue } from "vue-currency-input"
export default {
  // setup(props) {
  //   // currencyOptions.currency = props.currencyToUse
  //   // const { inputRef } = useCurrencyInput(    props.options)
  //   const {
  //     inputRef,
  //     formattedValue,
  //     numberValue,
  //     setOptions,
  //     setValue,
  //   } = useCurrencyInput(currencyOptions)

  //   // watch(
  //   //   () => props.modelValue, // Vue 2: props.value
  //   //   (value) => {
  //   //     setValue(value)
  //   //   }
  //   // )
  //   // watch(
  //   //   () => props.options,
  //   //   (options) => {
  //   //     setOptions(options)
  //   //   }
  //   // )
  //   return { inputRef, formattedValue, numberValue, currencyOptions }
  // },
  setup(props) {
    let currencyOptions = {
      currency: props.currencyToUse,
      currencyDisplay: "symbol",
      hideCurrencySymbolOnFocus: false,
      hideGroupingSeparatorOnFocus: false,
      hideNegligibleDecimalDigitsOnFocus: true,
      autoDecimalDigits: false,
      autoSign: false,
      useGrouping: true,
      accountingSign: false,
    }
    const { inputRef, formattedValue, numberValue, setValue } = useCurrencyInput(
      currencyOptions
    )

    const errorMessage = computed(() =>
      numberValue.value <= 100 ? "Value must be greater than 100" : undefined
    )

    return { inputRef, formattedValue, errorMessage }
  },
  props: {
    modelValue: {
      type: Number,
      default: null,
    },
    currencyToUse: {
      type: String,
      default: "GBP",
    },
    // fieldName: {
    //   type: String,
    //   default: "",
    // },
    suffixText: {
      type: String,
      default: "",
    },
    prefixText: {
      type: String,
      default: "",
    },
  },
  // directives: {
  //   currency: CurrencyDirective,
  // },
  data() {
    return {
      localFieldValue: "",
      originalValue: "",
    }
  },
  watch: {},
  computed: {},
  methods: {},
}
</script>
