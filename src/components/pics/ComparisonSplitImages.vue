// formerly EvaluationComparisonImages
<template>
  <div class="overflow-hidden" v-if="rightSideDetails">
    <q-resize-observer @resize="onResize" :debounce="0" />

    <q-splitter
      id="photos"
      v-model="splitterModel"
      @update:model-value="splitChangeHandler"
      :limits="[0, 100]"
      :style="splitterStyle"
      before-class="overflow-hidden"
      after-class="overflow-hidden"
    >
      <template v-slot:before>
        <!-- <div class="text-h4 q-mb-md">beforrrreee</div> -->
        <q-img
          @click="imagesSeparatorClicked"
          fit="cover"
          height="100%"
          :width="width.toString()"
          :src="leftEvalImageUrl"
          class="left-img eval-comp-left-img"
        >
          <!-- <ConvertableCurrencyRibbon
            v-if="showCurrRibbon"
            :isLeft="true"
            :priceInCents="leftSideDetails.leftColPriceInCents"
            :originalCurrency="leftColCurrency"
          >
          </ConvertableCurrencyRibbon> -->
          <div
            v-if="leftSideDetails.countBedrooms"
            style="padding: 3px"
            class="left-col-teaser q-py-none absolute-bottom"
          >
            <div style="cursor: pointer" role="link">
              <div class="q-my-sm text-body1 text-center text-white">
                <BedBathIconsBlock
                  :countBedrooms="leftSideDetails.countBedrooms.toString()"
                  :countBathrooms="leftSideDetails.countBathrooms.toString()"
                ></BedBathIconsBlock>
              </div>
            </div>
          </div>
        </q-img>
        <!-- <img :src="leftEvalImageUrl" :width="width" class="absolute-top-left" /> -->
      </template>

      <template v-slot:separator>
        <div>
          <q-avatar
            @click="imagesSeparatorClicked"
            text-color="white"
            size="60px"
            class="rotate-90 ev-comp-img-avt h2caccent"
            icon="unfold_more"
          />
        </div>
        <!-- <q-avatar
          color="primary"
          text-color="black"
          size="40px"
          icon="unfold_more_vertical"
        /> -->
      </template>
      <template v-slot:after>
        <q-img
          @click="imagesSeparatorClicked"
          fit="cover"
          height="100%"
          :src="rightEvalImageUrl"
          :width="width.toString()"
          class="right-img eval-comp-right-img"
        >
          <!-- <ConvertableCurrencyRibbon
            v-if="showCurrRibbon"
            :priceInCents="rightSideDetails.rightColPriceInCents"
            :originalCurrency="rightColCurrency"
          >
          </ConvertableCurrencyRibbon> -->
          <div
            v-if="rightSideDetails.countBedrooms"
            style="padding: 3px"
            class="right-col-teaser q-py-none absolute-bottom"
          >
            <div style="cursor: pointer" role="link">
              <div class="q-my-sm text-body1 text-center text-white">
                <BedBathIconsBlock
                  :countBedrooms="rightSideDetails.countBedrooms.toString()"
                  :countBathrooms="rightSideDetails.countBathrooms.toString()"
                ></BedBathIconsBlock>
              </div>
            </div>
          </div>
        </q-img>
        <!-- <img :src="rightEvalImageUrl"  /> -->
      </template>
    </q-splitter>
  </div>
</template>

<script>
import { ref, computed } from "vue"
// import ConvertableCurrencyRibbon from "components/widgets/ConvertableCurrencyRibbon.vue"
import BedBathIconsBlock from "src/components/listing-blocks/BedBathIconsBlock.vue"
//  "src/shared/components/user-listings/blocks/BedBathIconsBlock.vue"
export default {
  components: {
    // ConvertableCurrencyRibbon,
    BedBathIconsBlock,
  },
  data() {
    return {
      clicksCount: 0,
    }
  },
  props: {
    routeShowSideBySide: {
      type: Object,
      default: () => {},
    },
    rightSideDetails: {
      type: Object,
      default: () => {
        countBathrooms: "oio"
      },
    },
    leftSideDetails: {
      type: Object,
      default: () => {
        countBathrooms: "oio"
      },
    },
    leftColPriceInCents: {
      type: Number,
    },
    // rightColPriceInCents: {
    //   type: Number,
    // },
    leftColCurrency: {
      type: String,
    },
    rightColCurrency: {
      type: String,
    },
    listItemUuid: {
      type: String,
    },
    compUuid: {
      type: String,
    },
    leftColTeaser: {
      type: String,
    },
    rightColTeaser: {
      type: String,
    },
    rightEvalImageUrl: {
      type: String,
    },
    leftEvalImageUrl: {
      type: String,
      // default: () => {
      //   title: ""
      // },
    },
    comparisonsHeight: {
      type: Number,
      default: 300,
    },
  },
  methods: {
    imagesSeparatorClicked(event) {
      if (this.splitterModel < 45) {
        //   this.splitterModel = 50
        // } else if (this.splitterModel === 50) {
        this.splitterModel = 80
      } else {
        this.splitterModel = 20
      }
      this.clicksCount += 1
      if (this.clicksCount > 3) {
        let routeDetails = this.routeShowSideBySide
        if (this.$route.name !== "rPublicSideBySideCompare") {
          this.$router.push(routeDetails)
        }
      }
    },
    // leftTeaserClicked() {
    //   this.splitterModel = 80
    // },
    // rightTeaserClicked() {
    //   this.splitterModel = 20
    // },
    splitChangeHandler(newSplit) {
      // if (newSplit < 45) {
      //   this.rightImgCaptionStyle = "display:none;"
      //   this.leftImgCaptionStyle = "display:flex;"
      // }
      // if (newSplit > 55) {
      //   this.rightImgCaptionStyle = "display:flex;"
      //   this.leftImgCaptionStyle = "display:none;"
      // }
    },
    routeShowSide(propertySide) {
      let routeShowSide = {
        name: "rPublicSideBySideCompareWithSide",
        params: {
          propertySide: propertySide,
          comparisonUuid: this.compUuid,
        },
      }
      if (this.listItemUuid) {
        routeShowSide.params.listItemUuid = this.listItemUuid
      }
      return routeShowSide
    },
  },
  computed: {
    // leftSideDetails() {
    //   return {
    //     countBedrooms: "2",
    //   }
    // },
    showCurrRibbon() {
      return true
      // if (this.splitterModel !== 50) {
      //   return true
      // } else {
      //   return false
      // }
    },
    // showLeftTeaser() {
    //   return true
    //   // if (this.splitterModel < 51) {
    //   //   return false
    //   // } else {
    //   //   return this.leftColTeaser
    //   // }
    // },
    // showRightTeaser() {
    //   if (this.splitterModel > 49) {
    //     return false
    //   } else {
    //     return this.rightColTeaser
    //   }
    // },
    splitterStyle() {
      let splitterStyle = {
        height: Math.min(this.comparisonsHeight, 0.96 * this.width) + "px",
        width: this.width + "px",
      }
      return splitterStyle
    },
  },
  setup() {
    const width = ref(400)

    return {
      width,
      splitterModel: ref(50), // start at 50%

      // splitterStyle: computed(() => ({
      //   height: Math.min(600, 0.66 * width.value) + "px",
      //   width: width.value + "px",
      // })),

      // we are using QResizeObserver to keep
      // this example mobile-friendly
      onResize(info) {
        width.value = info.width
      },
    }
  },
}
</script>
