<template>
  <div class="img-picker-sect q-pa-md">
    <div>
      <!-- <h3>Image Picker</h3> -->
      <div class="selectable-images-container">
        <div class="sic-list row q-col-gutter-md"
             bordered
             padding>
          <div class="col-6 col-md-4"
               v-for="(imageItem, index) in imageOptions"
               :key="index">
            <q-card class="img-picker-item-card">
              <q-card-section class="q-pa-none">
                <q-item-section class="column items-center"
                                avatar>
                  <q-radio class="col"
                           v-model="selectedImageOption"
                           :val="imageItem"
                           color="teal" />
                </q-item-section>
                <q-item-section @click="imageClicked(imageItem)"
                                horizontal>
                  <q-img class="cursor-pointer img-to-pick"
                         :ratio="16 / 9"
                         :src="imageItem.label" />
                </q-item-section>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent, ref } from "vue"
// import SppSubmitter from "src/components/editor-forms-parts//SppSubmitter.vue"
export default defineComponent({
  name: "ImagePickerPart",
  // inject: ["listingsStore"],
  components: {
    // SppSubmitter,
  },
  props: {
    targetDetails: {
      type: Object,
      default: () => { },
    },
    imagesToPickFrom: {
      type: Object,
      default: () => { },
    },
  },
  computed: {
    currentFieldDetails() {
      var currentFieldDetails = {
        // tooltipTextTKey: "",
        // autofocus: false,
        fieldName: "updated_pic_urls",
        // fieldType: "simpleInput",
        // inputType: "text",
        // constraints: {
        //   inputValue: {},
        // },
      }
      return currentFieldDetails
    },
    selectedImageOption: {
      get: function () {
        return ""
        // if (this.selectedImageProxy === "") {
        //   return this.currentPropForEditing.pic_urls.primary.default_url || ""
        // } else {
        //   return this.selectedImageProxy
        // }
      },
      set: function (selectedImageDetails) {
        this.$emit("newImageSelected", selectedImageDetails, this.targetDetails)
        // let urlDetails = {
        //   default_url: newValue,
        // }
        // let newUpdateVal = {
        //   url_details: urlDetails,
        //   target: this.imageTarget,
        // }
        // this.currentFieldDetails.newValue = newUpdateVal
        // this.lastChangedField.fieldDetails = this.currentFieldDetails
        // this.lastChangedField.lastUpdateStamp = Date.now()
        // this.cancelPendingChanges = false
        // this.selectedImageProxy = newValue
        // // I cannot directly change incoming prop below which is why I'm
        // // using this.selectedImageProxy = above
        // // this.currentPropForEditing.pic_urls.primary.default_url = newValue
        //
      },
    },
    imageOptions() {
      let options = []
      if (this.imagesToPickFrom) {
        this.imagesToPickFrom.forEach(function (sppPhoto) {
          options.push({
            label: sppPhoto.image_details.url,
            value: sppPhoto.image_details.url,
            image_url: sppPhoto.image_details.url,
            image_uuid: sppPhoto.uuid,
          })
        })
      }
      return options
    },
  },
  methods: {
    imageClicked(selectedImageDetails) {
      this.$emit("newImageSelected", selectedImageDetails, this.targetDetails)
    },
    // changesCanceled() {
    //   this.selectedImageProxy = this.currentPropForEditing.pic_urls.primary.default_url
    //   this.cancelPendingChanges = true
    // },
  },
  data() {
    return {
      selectedImageProxy: "",
      // cancelPendingChanges: false,
      // lastChangedField: {
      //   fieldDetails: {},
      //   lastUpdateStamp: "",
      // },
    }
  },
})
</script>
