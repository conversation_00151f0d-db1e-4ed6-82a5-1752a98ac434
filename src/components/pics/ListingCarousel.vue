<template>
  <div class="responsive-listing-carousel">
    <q-responsive class="col"
                  :ratio="16 / 9"
                  style="max-width: 100%">
      <q-carousel v-if="carouselSlides.length > 0"
                  swipeable
                  :thumbnails="showThumbnails"
                  animated
                  v-model="slideModel"
                  :autoplay="autoplay"
                  ref="carousel"
                  infinite
                  arrows
                  control-type="regular"
                  :control-color="controlColor"
                  control-text-color="white"
                  class="bg-black text-white rounded-borders main-carousel"
                  v-model:fullscreen="fullscreen">
        <q-carousel-slide v-for="image in carouselSlides"
                          :name="image.src"
                          :key="image.src"
                          :img-src="image.src"
                          class="main-carousel-slide">
          <div class="q-mt-md text-center main-carousel-alt-text">
            {{ image.altText }}
          </div>
          <q-scroll-area class="fit main-carousel-scroll-area"> </q-scroll-area>
        </q-carousel-slide>
        <template v-if="showFullScreenOption"
                  v-slot:control>
          <q-carousel-control position="bottom-right"
                              :offset="[18, 18]">
            <q-btn push
                   round
                   dense
                   color="white"
                   text-color="primary"
                   :icon="fullscreen ? 'fullscreen_exit' : 'fullscreen'"
                   @click="fullscreen = !fullscreen" />
          </q-carousel-control>
          <!-- <q-carousel-control
            position="top-right"
            :offset="[18, 18]"
            class="text-white rounded-borders"
            style="background: rgba(0, 0, 0, 0.3); padding: 4px 8px"
          >
            <q-toggle
              dense
              dark
              color="black"
              v-model="autoplay"
              label="Auto Play"
            />
          </q-carousel-control> -->

          <!-- <q-carousel-control
            position="bottom-right"
            :offset="[18, 18]"
            class="q-gutter-xs"
          >
            <q-btn
              push
              round
              dense
              color="black"
              text-color="black"
              icon="arrow_left"
              @click="$refs.carousel.previous()"
            />
            <q-btn
              push
              round
              dense
              color="black"
              text-color="black"
              icon="arrow_right"
              @click="$refs.carousel.next()"
            />
          </q-carousel-control> -->
        </template>
      </q-carousel>
      <div v-else
           style="background: darkgray"
           class="missing-crsl-images"></div>
    </q-responsive>
  </div>
</template>
<script>
import { ref, toRef, watch } from "vue"
// import { sharedConfig } from "boot/shared-config"
// import { toRef, watch } from "vue"
export default {
  setup(props) {
    return {
      // showThumbnailsRef,
      slideModel: ref(1),
      autoplay: ref(false),
      fullscreen: ref(false),
    }
  },
  data() {
    return {}
  },
  props: {
    showThumbnails: {
      type: Boolean,
      default: true,
    },
    carouselSlides: {
      type: Array,
      default: () => [],
    },
    showFullScreenOption: {
      type: Boolean,
      default: true,
    },
    controlColor: {
      type: String,
      default: "accent",
    },
  },
  watch: {
    carouselSlides: {
      immediate: true,
      // deep: true,
      handler: function (newVal) {
        if (newVal[0]) {
          this.slideModel = newVal[0].src
        }
      },
    },
  },
  computed: {},
}
</script>
