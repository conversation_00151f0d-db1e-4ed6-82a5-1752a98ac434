<template>
  <div class="simple-listing-carousel">

    <q-carousel v-if="carouselSlides.length > 0"
                swipeable
                :thumbnails="reallyShowThumbnails"
                animated
                v-model="slideModel"
                :autoplay="autoplay"
                ref="carousel"
                infinite
                :arrows="showArrows"
                control-type="regular"
                :control-color="controlColor"
                control-text-color="white"
                class="bg-black text-white rounded-borders main-carousel"
                v-model:fullscreen="fullscreen">
      <q-carousel-slide v-for="image in carouselSlides"
                        :name="image.src || image.full_image_url"
                        :key="image.src || image.full_image_url"
                        :img-src="image.src || image.full_image_url"
                        class="
                        main-carousel-slide">
        <div class="q-mt-md text-center main-carousel-alt-text">
          {{ image.altText }}
        </div>
        <q-scroll-area class="fit main-carousel-scroll-area"> </q-scroll-area>
      </q-carousel-slide>
      <template v-if="showFullScreenOption"
                v-slot:control>
        <q-carousel-control position="bottom-right"
                            :offset="[18, 18]">
          <q-btn push
                 round
                 dense
                 color="white"
                 text-color="primary"
                 :icon="fullscreen ? 'fullscreen_exit' : 'fullscreen'"
                 @click="fullscreen = !fullscreen" />
        </q-carousel-control>
      </template>
    </q-carousel>
    <div v-else
         style="background: darkgray"
         class="missing-crsl-images"></div>
  </div>
</template>
<script>
import { ref, onMounted, onBeforeUnmount } from 'vue'
// import { useQuasar } from "quasar"
export default {
  setup(props) {

    let fullscreen = ref(false);

    // const $q = useQuasar();

    // // Quasar’s onKeydown helper
    // grok hallucinated below as a better solution
    //  // $q.platform.onKeydown = (event) => {
    //   if (event.key === 'Escape' && fullscreen.value) {
    //     fullscreen.value = false;
    //   }
    // };


    const handleKeyDown = (event) => {
      if (event.key === 'Escape' && fullscreen.value) {
        fullscreen.value = false;
      }
    };

    onMounted(() => {
      document.addEventListener('keydown', handleKeyDown);
    });

    onBeforeUnmount(() => {
      document.removeEventListener('keydown', handleKeyDown);
    });

    // rest of your setup code
    return {
      slideModel: ref(1),
      autoplay: ref(false),
      fullscreen: fullscreen,
    }
  },
  data() {
    return {}
  },
  props: {
    showThumbnails: {
      type: Boolean,
      default: true,
    },
    carouselSlides: {
      type: Array,
      default: () => [],
    },
    showFullScreenOption: {
      type: Boolean,
      default: true,
    },
    controlColor: {
      type: String,
      default: "accent",
    },
  },
  watch: {
    carouselSlides: {
      immediate: true,
      // deep: true,
      handler: function (newVal) {
        // if (newVal.length > 1) {
        // }
        if (newVal[0]) {
          this.slideModel = newVal[0].src || newVal[0].full_image_url
        }
      },
    },
  },
  computed: {
    showArrows() {
      return this.carouselSlides.length > 1
    },
    reallyShowThumbnails() {
      return this.showThumbnails && this.carouselSlides.length > 1
    },
  },
}
</script>
