<template>
  <div class="q-my-md">
    <div v-if="showListingCarousel">
      <div></div>
      <!-- <ReactiveCarousel
        @fullScreenClosed="fullScreenClosed"
        :currentCarouselSlides="viewPics"
        :initialUrl="initialCarouselUrl"
      ></ReactiveCarousel> -->
    </div>
    <div v-for="viewPic in viewPics"
         :key="viewPic.image_details.url">
      <div class="q-pb-md"
           :style="viewPicStyle">
        <q-img @click="listingImgClicked(viewPic.image_details.url)"
               fit="cover"
               height="100%"
               :src="viewPic.image_details.url"
               class="view-list-col-photo">
          <div v-if="picTitles[viewPic.uuid]"
               class="absolute-bottom text-subtitle2 flex flex-center caption-left">
            {{ picTitles[viewPic.uuid] }}
          </div>
        </q-img>
      </div>
    </div>
  </div>
</template>
<script>
// import ReactiveCarousel from "src/h2c/components/ReactiveCarousel.vue"
import { onKeyStroke } from "@vueuse/core"
import { ref } from "vue"
export default {
  components: {
    // ReactiveCarousel,
  },
  methods: {
    listingImgClicked(clickedImgUrl) {
      this.initialCarouselUrl = clickedImgUrl
      this.showListingCarousel = true // !this.showListingCarousel
    },
    fullScreenClosed() {
      this.showListingCarousel = false
    },
  },
  data() {
    return {
      initialCarouselUrl: null,
    }
  },
  setup(props) {
    let showListingCarousel = ref(false)
    // onStartTyping(() => {
    //   if (!showListingCarousel.value.active) showListingCarousel.value.focus()
    // })
    onKeyStroke(["Escape", "ArrowDown", "ArrowUp", "q", "w"], (KeyboardEvent) => {
      // console.log(showListingCarousel)
      showListingCarousel.value = false
    })

    return {
      showListingCarousel,
    }
  },
  watch: {},
  computed: {
    viewPicStyle() {
      if (this.showingSideBySide) {
        if (this.$q.platform.is.mobile) {
          return "height: 200px"
        } else {
          return "height: 400px"
        }
        // return "height: 500px"
      } else {
        return "height: 100%"
      }
    },
    picTitles() {
      return this.comparisonDetails.pic_comparison_titles || {}
    },
  },
  props: {
    showingSideBySide: {
      type: Boolean,
    },
    viewPics: {
      type: Array,
      default: () => [],
    },
    comparisonDetails: {
      type: Object,
      default: () => { },
    },
  },
}
</script>
