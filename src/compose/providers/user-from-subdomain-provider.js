// https://dev.to/nonso/shared-state-management-with-vue-composition-api-2938

// import { pwbFlexConfig } from 'src/compose/appConfig.js'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
import { reactive, computed, readonly } from 'vue'
import { Cookies } from 'quasar'
import axios from 'axios'
// import authHeader from "src/compose/auth-header"
import { fireAuth } from 'src/boot/firebase'
// import { bootSbdSvt } from "src/boot/subdomain-svt"
import useDashLocalData from 'src/compose/useDashLocalData.js'

import {
  onAuthStateChanged,
  // createUserWithEmailAndPassword
} from 'firebase/auth'

const state = reactive({
  // authComplete: false,
  htocData: {},
  // currentSvt: null,
  currentFbUser: {},
  currentBeUser: {},
  // afterSignInRoute: {
  //   name: 'rSbdDashRoot',
  //   params: {},
  // },
  // signInRoute: { name: 'rH2cLoginPage' },
})

onAuthStateChanged(fireAuth, (user) => {
  let locDetails = {}
  if (process.env.CLIENT) {
    // locPath = location.pathname
    locDetails.path = location.pathname
    locDetails.host = location.hostname
  }
  console.log('Calling onAuthStateChanged')
  if (user) {
    // User is signed in, see docs for a list of available properties
    // https://firebase.google.com/docs/reference/js/firebase.User
    // const uid = user.uid;
    // authUpdateOn(user, locDetails)
  } else {
    authUpdateOff(locDetails)
    // User is signed out
    // ...
  }
})

function authUpdateOff(locDetails) {
  const { saveLocalDashSvt, getLocalDashSvt, getLocalSbdKlave } =
    useDashLocalData()

  let svt = getLocalDashSvt()
  let sbdKlave = getLocalSbdKlave()
  state.currentSvt = svt
  if (!pwbFlexConfig.dataApiBase) {
    console.log('pwbFlexConfig.dataApiBase not set')
    return
  }
  let apiUrl = `${pwbFlexConfig.dataApiBase}/api_admin/${pwbFlexConfig.packageCode}/v1/frb_auth_off`
  let fbaseIdToken = ''
  return axios
    .post(
      apiUrl,
      {
        svt: svt,
        sbd_klave: sbdKlave,
        fid_tok: fbaseIdToken,
        // site_tlc: pwbFlexConfig.siteName,
        loc_path: locDetails.path,
        loc_host: locDetails.host,
      },
      {
        // headers: authHeader()
      }
    )
    .then((response) => {
      updateStateFromResponse(response, saveLocalDashSvt)
    })
    .catch((error) => {
      console.log('Error at authUpdateOff')
      console.log(error)
    })
}

// Nov 2024 - below might not be necessary.
// Will later look into getting a grant_token from the
// response and using it from axios like so:
//         # header: { 'Authorization': 'Bearer <token>' }
function updateStateFromResponse(response, saveLocalDashSvt) {
  // console.log(this.$flexiConfig)
  // would have been nice to have access to this.$flexiConfig
  // here so I didn't have to inject user provider everywhere
  let currentBeUser = response.data.csvr
  if (currentBeUser) {
    Cookies.set('h2c_gt', currentBeUser.grantToken, {
      path: '/',
    })
    state.currentBeUser = currentBeUser
  }

  // if (response.data.subdomain_guest) {
  //   state.sbdGuestInstance = response.data.subdomain_guest
  // }

  // state.subdomainDetails = response.data.subdomain_space
  // state.currentShareLinks = response.data.share_links

  // if (currentBeUser && currentBeUser.svt) {
  //   if (currentBeUser.svt !== state.currentSvt) {
  //     // should probably check the currentBeUser.forceOver param too
  //     let forceSet = true
  //     // console.log(`saveLocalDashSvt is ${saveLocalDashSvt}`)
  //     saveLocalDashSvt(currentBeUser.svt, forceSet)
  //     state.currentSvt = currentBeUser.svt
  //     // window.location.href = "/"
  //   }
  // }
  // state.authComplete = true
}

// Nov 2024 - I had been reacting to all the change above
// But now I am proactively getting current user based on:
//     // https://github.com/gautemo/Vue-guard-routes-with-Firebase-Authentication?tab=readme-ov-file
// const app = initializeApp(firebaseConfig)
// const fireAuth = getAuth(app)

function getCurrentUser() {
  return new Promise((resolve, reject) => {
    const unsubscribe = onAuthStateChanged(
      fireAuth,
      async (fbAuthenticatedUser) => {
        unsubscribe()

        if (fbAuthenticatedUser) {
          try {
            let apiUrl = `${pwbFlexConfig.dataApiBase}/api_admin/${pwbFlexConfig.packageCode}/v1/htoc_auth_on`
            let fbaseIdToken = fbAuthenticatedUser.accessToken
            state.currentFbUser = fbAuthenticatedUser
            // Make the subsequent axios request here
            const response = await axios.post(
              apiUrl,
              {
                // svt: svt,
                fid_tok: fbaseIdToken,
                loc_path: location.path,
                loc_host: location.host,
                // site_tlc: pwbFlexConfig.siteName,
              },
              {
                // headers: authHeader()
              }
            )
            state.htocData = response.data.htoc_data
            // 4 Dec 2024 - can't remember why I can't just use htoc_data or user_data
            // everywhere - will have to revisit
            // Resolve with both fbAuthenticatedUser and response data
            resolve({ fbAuthenticatedUser, userData: response.data.user_data })
          } catch (error) {
            // If the axios request fails, reject with the error
            reject(error)
          }
        } else {
          // Resolve with null if no user is found
          resolve(null)
        }
      },
      reject
    )
  })
}

export const currentSbdUserProvider = readonly({
  state,
  getCurrentUser,
})
