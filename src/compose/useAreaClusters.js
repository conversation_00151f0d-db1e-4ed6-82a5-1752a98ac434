import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export default function () {
  function getPostcodeAreas() {
    // This is currently only used by
    // /src/concerns/gmaps/pages/PostCodeMapSecond.vue
    // Will be removed soom
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/postcode_areas`
    return axios.get(apiUrl, {}, {})
  }

  function getPostcodeCluster(clusterUuid) {
    // let clusterUuiddd = 'cf02d8cf-07a4-44aa-8c26-a21f0cd3ba09'
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/postcode_clusters/show/${clusterUuid}`
    return axios.get(apiUrl, {}, {})
  }

  function getPostcodeClusters(outcodeId) {
    outcodeId ||= 'cv11'
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/postcode_clusters/list/${outcodeId}`
    return axios.get(apiUrl, {}, {})
  }

  return {
    getPostcodeClusters,
    getPostcodeCluster,
    getPostcodeAreas,
  }
}
