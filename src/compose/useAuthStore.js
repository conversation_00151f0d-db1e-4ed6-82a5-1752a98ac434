import { fireAuth } from 'src/boot/firebase'
import axios from 'axios'
import { Cookies } from 'quasar'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
// import useDashLocalData from 'src/compose/useDashLocalData.js'

import {
  getAuth,
  signOut,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  sendEmailVerification,
} from 'firebase/auth'

export default function () {
  let fireBaseTokenCookieKey = pwbFlexConfig.fireBaseTokenCookieKey || 'fid_tok'

  async function sendVerificationEmail() {
    return sendEmailVerification(fireAuth.currentUser)
      .then((userCredential) => {
        // Nov 2024 - when I tested userCredential was undefined
      })
      .catch((error) => {
        // An error happened.
      })
  }

  function authStoreLogout() {
    return signOut(fireAuth)
      .then((userCredential) => {
        location.reload()
        // Might add a method to useDashLocalData called clearFbtoken
        // that does below
        Cookies.set(fireBaseTokenCookieKey, '', {
          path: '/',
        })
        // Spt 2023 - perhaps I should redirect somewhere
        // userCredential here is undefined
      })
      .catch((error) => {
        // An error happened.
      })
  }

  function authStoreLogin(userParams) {
    return signInWithEmailAndPassword(
      fireAuth,
      userParams.email,
      userParams.password
    )
      .then((userCredential) => {
        let authResponseSummary = {
          success: true,
        }
        // console.log(userCredential.user.email)
        // this.$q.notify({ message: 'Sign In Success.' })
        // this.$router.push('/home')
        userCredential.user.getIdToken().then(function (idToken) {
          // <------ Check this line
          Cookies.set(fireBaseTokenCookieKey, idToken, {
            path: '/',
          })
          // console.log(idToken); // It shows the Firebase token now
        })
        // if (userCredential.user.accessToken) {
        //   let h2cUser = {
        //     fireBaseAccessToken: userCredential.user.accessToken,
        //     uuid: userCredential.user.uuid,
        //     email: userCredential.user.email
        //   }
        //   authResponseSummary.h2cUser = h2cUser
        // }
        // Will no longer do above.
        // When I pass fireBaseAccessToken to server I’ll be able
        // to use that to retrieve correct user..

        return authResponseSummary
      })
      .catch((error) => {
        let authResponseSummary = {
          success: false,
          error: error,
        }
        // this.$q.notify({ message: 'Sign In Error.' })
        return authResponseSummary
      })
  }

  async function authStoreRegister(userParams) {
    try {
      // const { getLocalDashSvt } = useDashLocalData()
      // let svt = getLocalDashSvt()

      const userCredential = await createUserWithEmailAndPassword(
        fireAuth,
        userParams.email,
        userParams.password
      )
      let authResponseSummary = {
        success: true,
      }
      // console.log(userCredential.user.email)
      // this.$q.notify({ message: 'Sign In Success.' })
      // this.$router.push('/home')
      userCredential.user.getIdToken().then(function (idToken) {
        Cookies.set(fireBaseTokenCookieKey, idToken, {
          path: '/',
        })
        // console.log(idToken) // It shows the Firebase token now
        let apiUrl = `${pwbFlexConfig.dataApiBase}/api_admin/v1/htoc_auth_reg`
        // let fbaseIdToken = ""
        return axios
          .post(
            apiUrl,
            {
              fid_tok: idToken,
              // site_tlc: pwbFlexConfig.siteName,
              loc_path: location.path,
              loc_host: location.host,
            },
            {
              // headers: authHeader()
            }
          )
          .then((response) => {
            updateStateFromResponse(response, saveLocalDashSvt)
          })
          .catch((error) => {
            console.log('Error at authUpdateOff')
            console.log(error)
          })
      })
      return authResponseSummary
    } catch (error) {
      let authResponseSummary_1 = {
        success: false,
        error: error,
      }
      // console.log(error)
      return authResponseSummary_1
    }
  }

  return {
    sendVerificationEmail,
    authStoreLogout,
    authStoreLogin,
    authStoreRegister,
  }
}
