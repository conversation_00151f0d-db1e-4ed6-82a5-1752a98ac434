import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export default function () {
  // function getChartData() {
  //   const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/charts_data/sold_data_scatter_charts`
  //   return axios.get(apiUrl)
  // }
  function getChartData(chartDataName = 'sold_data_scatter_charts') {
    const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/charts_data/${chartDataName}`
    return axios.get(apiUrl)
  }
  function getExampleChartData(chartDataName = 'time_series') {
    const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/charts_data_examples/${chartDataName}`
    return axios.get(apiUrl)
  }

  return {
    getChartData,
    getExampleChartData,
  }
}
