// June 2023 - will use this over useLocalData as I now want more security..
// Aug 2023 - would have been good if I'd explained "more security" above
// Main thing here is that I am using siteConfig to determine the cookieKey
// so I guess it is better than useLocalData in that sense
import { LocalStorage, SessionStorage } from 'quasar'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export default function () {
  // let svtCookieKey = "dash-svt"
  let svtCookieKey = pwbFlexConfig.svtCookieKey || 'fallback-svt'
  function saveLocalDashSvt(siteVisitorToken, forceSet = false) {
    if (forceSet) {
      LocalStorage.set(svtCookieKey, siteVisitorToken)
      // return
    } else {
      // only set if not already set
      let existingSvt = LocalStorage.getItem(svtCookieKey)
      if (!!!existingSvt || existingSvt === 'undefined') {
        LocalStorage.set(svtCookieKey, siteVisitorToken)
      }
    }
    return ''
  }

  function getLocalDashSvt() {
    return LocalStorage.getItem(svtCookieKey)
  }

  let visitorNickNameKey = pwbFlexConfig.visitorNickNameKey || 'nickname-for-sv'
  function saveLocalNickName(nickName, forceSet = false) {
    if (forceSet) {
      LocalStorage.set(visitorNickNameKey, nickName)
      // return
    } else {
      // only set if not already set
      let existingSvt = LocalStorage.getItem(visitorNickNameKey)
      if (!!!existingSvt || existingSvt === 'undefined') {
        LocalStorage.set(visitorNickNameKey, nickName)
      }
    }
    return ''
  }

  function getLocalNickName() {
    return LocalStorage.getItem(visitorNickNameKey)
  }

  function saveLocalSbdKlave(sbdKlave, forceSet = false) {
    if (forceSet) {
      LocalStorage.set('sbd-klave', sbdKlave)
      // return
    } else {
      // only set if not already set
      let existingSbdKlave = LocalStorage.getItem('sbd-klave')
      if (!!!existingSbdKlave || existingSbdKlave === 'undefined') {
        LocalStorage.set('sbd-klave', sbdKlave)
      }
    }
    return ''
  }
  // Spt 2023 - above and below are related to main_passklave field
  // on subdomain model which is used to lock an entire subdomain
  // for privacy
  function getLocalSbdKlave() {
    return LocalStorage.getItem('sbd-klave')
  }

  return {
    getLocalSbdKlave,
    saveLocalSbdKlave,
    getLocalNickName,
    saveLocalNickName,
    getLocalDashSvt,
    saveLocalDashSvt,
  }
}
