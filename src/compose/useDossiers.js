// import axios from 'axios'
import { htocAxiosApi } from 'boot/axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export default function () {
  function getDossierComparison(retrievalObject) {
    // let apiUrl = `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/show/${retrievalObject.dossierUuid}`
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossier_assets_comparisons/show/${retrievalObject.assetsComparisonUuid}`
    // 19637b1c-928f-4480-a0f4-893f7bd58b9e`
    return htocAxiosApi.get(apiUrl)
  }

  function getDossier(retrievalObject) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/show/${retrievalObject.dossierUuid}`
    return htocAxiosApi.get(apiUrl)
  }

  // below 2 are used for the superwiser route - should add auth
  function getAllDossiers() {
    // TODO - add auth
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/dossiers/list/superwiser`
    return htocAxiosApi.get(apiUrl)
  }

  function getDossierAsset(retrievalObject) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossier_assets/show/${retrievalObject.dossierAssetId}`
    return htocAxiosApi.get(apiUrl)
  }

  return {
    getDossierAsset,
    getDossierComparison,
    getDossier,
    getAllDossiers,
  }
}
