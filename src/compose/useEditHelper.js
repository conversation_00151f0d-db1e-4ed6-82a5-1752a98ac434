// import { useStore } from "vuex"

export default function () {
  function urlValidator(urlString) {
    let urlIsValid = false
    try {
      const urlObject = new URL(urlString)
      if (urlObject.host.length > 1) {
        urlIsValid = true
      }
    } catch (error) {}
    return urlIsValid
  }

  function makeEditCall(funcToCall, updateObject, helperDetails) {
    let dismissProgressNote = null
    if (helperDetails.progressMessage) {
      dismissProgressNote = this.$q.notify({
        color: 'positive',
        position: 'bottom',
        spinner: true,
        message: helperDetails.progressMessage,
        // TODO Apr 2023: make timeout configurable
        timeout: 0,
        //  "Thank you. We are adding the photo provided ....",
      })
    }

    return funcToCall(updateObject)
      .then(async (response) => {
        if (dismissProgressNote) {
          dismissProgressNote()
        }
        if (helperDetails.reloadLocation) {
          location.reload()
        } else {
          if (helperDetails.successMessage) {
            this.$q.notify({
              color: 'positive',
              position: 'bottom',
              spinner: false,
              message: helperDetails.successMessage,
            })
          }
          return {
            success: true,
            data: response.data,
          }
        }
      })
      .catch((error) => {
        if (dismissProgressNote) {
          dismissProgressNote()
        }
        let errorMessage = 'Sorry, '
        if (error.response && error.response.data.error) {
          errorMessage += error.response.data.error
        } else {
          errorMessage += 'there has been an error from ueh'
        }

        this.$honeybadger.setContext({
          user_id: 'none',
          user_email: '<EMAIL>',
        })

        this.$honeybadger.notify(errorMessage)
        this.$honeybadger.notify(error)

        // this.$q.notify({
        //   color: "negative",
        //   position: "top",
        //   message: errorMessage,
        //   icon: "report_problem",
        // })
        return {
          success: false,
          message: errorMessage,
        }
      })
    // return ""
  }

  return {
    urlValidator,
    makeEditCall,
  }
}
