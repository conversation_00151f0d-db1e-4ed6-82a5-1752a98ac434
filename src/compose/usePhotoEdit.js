// import { useStore } from "vuex"
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export default function () {
  function setPhotoVisibilityDirectlyOnModel(updateObject) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/realty_asset_photos/${updateObject.picUuid}/set_photo_visibility`
    return axios.put(apiUrl, {
      edit_token: updateObject.editToken,
      restore_or_remove: updateObject.restoreOrRemove,
    })
  }

  function discardPhoto(updateObject) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/realty_asset_photos/${updateObject.picUuid}/discard_photo`
    return axios.put(apiUrl, {
      // edit_token: updateObject.editToken,
    })
  }

  function updatePhotoDetails(updateObject) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_mgmt/v4/realty_asset_photos/${updateObject.picUuid}/update_details`
    return axios.put(apiUrl, {
      photo_title: updateObject.photo_title,
      photo_description: updateObject.photo_description,
      // edit_token: updateObject.editToken, // Uncomment if authentication is needed
    })
  }

  return {
    discardPhoto,
    setPhotoVisibilityDirectlyOnModel,
    updatePhotoDetails,
  }
}
