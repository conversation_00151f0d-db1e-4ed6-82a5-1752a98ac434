import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export default function () {
  // let dataApiBase = 'http://localhost:3030'
  function getPurchaseEvaluations() {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api/v1/purchase_evaluations/list`
    return axios.get(apiUrl, {}, {})
  }
  function getSinglePurchaseEvaluationForDash(retrievalObject) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api/v1/purchase_evaluations/show/333`
    return axios.get(apiUrl, {})
    // let apiUrl = `${pwbFlexConfig.dataApiBase}/api_h2c/v1/purchase_evaluations/get_for_dash/${retrievalObject.evaluationUuid}`
    // return axios.put(apiUrl, {})
  }

  return {
    getPurchaseEvaluations,
    getSinglePurchaseEvaluationForDash,
  }
}
