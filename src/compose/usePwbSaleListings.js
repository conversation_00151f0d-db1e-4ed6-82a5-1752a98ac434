import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export default function () {
  // let dataApiBase = 'http://localhost:3030'
  function getPwbSaleListingsList() {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api/v1/sale_listings/list`
    return axios.get(apiUrl, {}, {})
  }
  function getPwbSaleListing(retrievalObject) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api/v1/sale_listings/show/${retrievalObject.listingUuid}`
    return axios.get(apiUrl, {})
    // return axios.put(apiUrl, {})
  }

  return {
    getPwbSaleListingsList,
    getPwbSaleListing,
  }
}
