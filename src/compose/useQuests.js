// import { useStore } from "vuex"
import axios from 'axios'

// import { appConfig } from 'src/compose/appConfig.js'

export default function () {
  let appConfig = {
    dataApiBase: 'http://localhost:3000',
  }
  function getAiQuestOverview(retrievalObject) {
    let apiUrl = `${appConfig.dataApiBase}/api_h2c/v1/quests/quest_overview`
    apiUrl += `/${retrievalObject.userQuestUuid}`
    return axios.get(apiUrl, {
      // user_quest_uuid: retrievalObject.userQuestUuid,
    })
  }

  function getPurchaseEvaluationsForQuest(retrievalObject) {
    let apiUrl = `${appConfig.dataApiBase}/api_h2c/v1/purchase_evaluations/list_for_subdomain_quest_dashboard/${retrievalObject.questUuid}`
    return axios.get(apiUrl, {})
  }

  function setQuestPermissionsForVisitor(updateObject) {
    let apiUrl = `${appConfig.dataApiBase}/api_h2c/v1/quests/set_permissions_for_svt/${updateObject.targetQuestUuid}`
    return axios.put(apiUrl, {
      target_svt: updateObject.targetSvt,
      // TODO: make use of permission level
      permission_level: 'full',
    })
  }

  function setQuestField(updateObject) {
    let apiUrl = `${appConfig.dataApiBase}/api_h2c/v1/quests/update/${updateObject.targetQuestUuid}`
    return axios.put(
      apiUrl,
      {
        field_name: updateObject.fieldName,
        field_value: updateObject.fieldContent,
      }
      // { headers: authHeader() }
    )
  }

  function initQuestTriad(updateObject) {
    let apiUrl = `${appConfig.dataApiBase}/api_h2c/v1/quests/init_triad/${updateObject.userQuestUuid}`
    return axios.put(apiUrl, {})
  }

  function newSearchForQuest(updateObject) {
    let userQuestUuid = updateObject.userQuestUuid
    //  'a2b6d10d-f771-4284-85b5-8f58eae09263'
    //  updateObject.userQuestUuid
    // http://localhost:3000/api_h2c/v1/quests/add_search_to_quest/a2b6d10d-f771-4284-85b5-8f58eae09263
    let apiUrl = `${appConfig.dataApiBase}/api_h2c/v1/quests/add_search_to_quest/${userQuestUuid}`
    return axios.put(apiUrl, updateObject)
  }

  return {
    newSearchForQuest,
    initQuestTriad,
    getAiQuestOverview,
    setQuestField,
    setQuestPermissionsForVisitor,
    // getQuestForPublicSubdomain,
    // getQuestForPublic,
    getPurchaseEvaluationsForQuest,
  }
}
