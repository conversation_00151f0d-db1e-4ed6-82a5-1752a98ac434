import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export default function () {
  // I'm dumping a load of stuff here - will need to refactor later
  // let dataApiBase = 'http://localhost:3030'
  function getReferencePropertysList() {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api/v1/reference_properties/list`
    return axios.get(apiUrl, {}, {})
  }
  function getReferenceProperty(retrievalObject) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api/v1/reference_properties/show/${retrievalObject.refPropUuid}`
    return axios.get(apiUrl, {})
  }

  return {
    getReferencePropertysList,
    getReferenceProperty,
  }
}
