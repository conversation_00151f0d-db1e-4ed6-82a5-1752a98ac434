import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export default function () {
  function getScoot(subdomainName) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/scoots/show/${subdomainName}`
    return axios.get(apiUrl, {})
  }

  function checkScoot(subdomainName, accessCode) {
    let apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/scoots/check/${subdomainName}/${accessCode}`
    return axios.post(apiUrl, {})
  }

  return {
    getScoot,
    checkScoot,
  }
}
