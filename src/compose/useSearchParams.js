// import { useStore } from "vuex"
import axios from 'axios'
// import { appConfig } from 'src/compose/appConfig.js'

export default function () {
  let appConfig = {
    dataApiBase: 'http://localhost:3000',
  }

  function getSearchQueryFields(updateObject) {
    let apiUrl = `${appConfig.dataApiBase}/api_h2c/v1/search_queries/query_fields_overview`
    return axios.get(
      apiUrl,
      {}
      // { headers: authHeader() }
    )
  }

  function getSearchQueryWithFields(updateObject) {
    let apiUrl = `${appConfig.dataApiBase}/api_h2c/v1/search_queries/search_query_overview/${updateObject.searchQueryUuid}`
    return axios.get(apiUrl, {})
  }

  function getSearchQueriesForQuest(retrievalObject) {
    let apiUrl = `${appConfig.dataApiBase}/api_h2c/v1/search_queries/queries_for_quest/${retrievalObject.questUuid}`
    return axios.get(apiUrl, {})
  }

  function getCannedQueries() {
    let apiUrl = `${appConfig.dataApiBase}/api_h2c/v1/search_queries/canned_queries`
    return axios.get(apiUrl, {})
  }

  return {
    getCannedQueries,
    getSearchQueriesForQuest,
    getSearchQueryWithFields,
    getSearchQueryFields,
  }
}
