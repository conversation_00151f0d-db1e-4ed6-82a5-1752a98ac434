<template>
  <q-layout view="lHh Lpr lFf">
    <!-- Header -->
    <q-header elevated
              class="bg-white text-grey-8">
      <q-toolbar>
        <!-- Mobile Menu Toggle -->
        <q-btn flat
               dense
               round
               icon="menu"
               aria-label="Menu"
               @click="toggleLeftDrawer"
               class="lg:hidden" />

        <!-- Logo -->
        <q-toolbar-title>
          <router-link to="/home">
            <img src="https://via.placeholder.com/150x50?text=Doorsteps"
                 alt="Doorsteps Logo"
                 class="h-8" />
          </router-link>
        </q-toolbar-title>

        <!-- Main Navigation (Desktop) -->
        <div class="hidden lg:flex items-center space-x-4">
          <q-btn flat
                 to="/home"
                 label="Homebase"
                 icon="home"
                 class="flex flex-col items-center">
            <span class="text-xs">Homebase</span>
          </q-btn>
          <q-btn flat
                 to="/listings"
                 label="Listings"
                 icon="list"
                 class="flex flex-col items-center">
            <span class="text-xs">Listings</span>
          </q-btn>
          <q-btn flat
                 to="/profile"
                 label="Profile"
                 icon="person"
                 class="flex flex-col items-center">
            <span class="text-xs">Profile</span>
          </q-btn>
          <q-btn flat
                 to="/map"
                 label="Map"
                 icon="map"
                 class="flex flex-col items-center">
            <span class="text-xs">Map</span>
          </q-btn>
        </div>

        <!-- User Settings -->
        <q-btn-dropdown flat
                        class="ml-auto">
          <template v-slot:label>
            <div class="flex items-center">
              <q-avatar size="sm">
                <img src="https://via.placeholder.com/30?text=Avatar"
                     alt="User Avatar" />
              </q-avatar>
              <span class="ml-2">Hi Ed</span>
            </div>
          </template>
          <q-list>
            <q-item clickable
                    v-close-popup
                    to="/settings">
              <q-item-section>Settings</q-item-section>
            </q-item>
            <q-item clickable
                    v-close-popup
                    to="/log-out">
              <q-item-section>Sign Out</q-item-section>
            </q-item>
          </q-list>
        </q-btn-dropdown>
      </q-toolbar>
    </q-header>

    <!-- Left Drawer for Mobile Journey Navigation -->
    <q-drawer v-model="leftDrawerOpen"
              show-if-above
              bordered
              class="bg-grey-1"
              :width="300">
      <q-scroll-area class="fit">
        <q-list>
          <q-expansion-item v-for="phase in journeyPhases"
                            :key="phase.id"
                            :label="phase.title"
                            :caption="`Phase ${phase.id}`"
                            icon="map"
                            group="journey"
                            default-opened>
            <q-list dense>
              <q-item v-for="step in phase.steps"
                      :key="step.slug"
                      clickable
                      :to="`/journey/${step.slug}`"
                      :class="{ 'text-grey-5': step.completed }">
                <q-item-section>
                  <q-item-label>{{ step.title }}</q-item-label>
                  <q-item-label caption>{{ step.number }}</q-item-label>
                </q-item-section>
                <q-item-section side>
                  <q-icon :name="step.completed ? 'check_circle' : 'radio_button_unchecked'"
                          :color="step.completed ? 'positive' : 'grey'" />
                </q-item-section>
              </q-item>
            </q-list>
          </q-expansion-item>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <!-- Main Content -->
    <q-page-container>
      <q-page padding>
        <BuyersStepsSectionA />

        <!-- Footer -->
        <footer class="nav-footer q-mt-xl q-py-md once-bggrey2 text-center">
          <div class="nav-footer-social q-mb-md">
            <q-btn flat
                   round
                   icon="fab fa-facebook"
                   href="http://www.facebook.com/doorstepsapp"
                   target="_blank"
                   class="q-mx-sm" />
            <q-btn flat
                   round
                   icon="fab fa-twitter"
                   href="http://twitter.com/doorsteps"
                   target="_blank"
                   class="q-mx-sm" />
            <q-btn flat
                   round
                   icon="fab fa-pinterest"
                   href="http://pinterest.com/doorsteps"
                   target="_blank"
                   class="q-mx-sm" />
            <q-btn flat
                   round
                   icon="fab fa-vimeo"
                   href="http://vimeo.com/doorsteps"
                   target="_blank"
                   class="q-mx-sm" />
          </div>
          <q-list class="flex justify-center flex-wrap q-mb-md">
            <q-item to="/about_us">
              <q-item-section>About</q-item-section>
            </q-item>
            <q-item to="/faq">
              <q-item-section>FAQ</q-item-section>
            </q-item>
            <q-item to="/contact">
              <q-item-section>Contact</q-item-section>
            </q-item>
            <q-item to="/terms">
              <q-item-section>Terms</q-item-section>
            </q-item>
            <q-item to="/privacy">
              <q-item-section>Privacy</q-item-section>
            </q-item>
          </q-list>
          <div class="nav-footer-copyright text-grey-6">
            © 2019 Move, Inc. All rights reserved.
          </div>
        </footer>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script>
import BuyersStepsSectionA from "src/concerns/buyers-steps/pages/BuyersStepsSectionA.vue";

export default {
  name: 'BuyerProfile',
  components: {
    BuyersStepsSectionA

  },
  data() {
    return {
      leftDrawerOpen: false,
      journeyPhases: [
        {
          id: 1,
          title: 'Visualize Home Ownership',
          steps: [
            { number: '1.0', title: 'Overview', slug: 'phase-1-intro', completed: false },
            { number: '1.1', title: 'Your Life Today', slug: 'your-life-today', completed: false },
            { number: '1.2', title: 'Your Finances Today', slug: 'your-finances-today', completed: false },
            { number: '1.3', title: 'Hopes For Tomorrow', slug: 'hopes-for-tomorrow', completed: false },
            { number: '1.4', title: 'Where You Imagine Moving', slug: 'moving-where', completed: false },
            { number: '1.5', title: 'Neighborhood Qualities You Love', slug: 'neighborhood-qualities-you-love', completed: false },
            { number: '1.6', title: 'Start Collecting Listings', slug: 'start-collecting-listings', completed: false },
            { number: '1.7', title: 'Your Timing', slug: 'moving-when', completed: false },
          ],
        },
        {
          id: 2,
          title: 'Assess Your Buying Power',
          steps: [
            { number: '2.0', title: 'Overview', slug: 'phase-2-intro', completed: false },
            { number: '2.1', title: 'What You Can Afford', slug: 'what-can-you-afford', completed: false },
            { number: '2.2', title: 'Get Your Credit Score', slug: 'credit-score', completed: false },
            { number: '2.3', title: 'Best Ways To Connect', slug: 'best-ways-to-connect', completed: false },
            { number: '2.4', title: 'Mortgages 101', slug: 'mortgage-basics', completed: false },
            { number: '2.5', title: 'Types Of Mortgages', slug: 'types-of-mortgages', completed: false },
            { number: '2.6', title: 'Calculating Down Payment', slug: 'calculating-down-payment', completed: false },
            { number: '2.7', title: 'Lenders 101', slug: 'lenders-101', completed: false },
            { number: '2.8', title: 'Compare Potential Lenders', slug: 'compare-lenders', completed: false },
            { number: '2.9', title: 'Pre-Approval 101', slug: 'pre-approval-101', completed: false },
            { number: '2.10', title: 'Get Pre-Approved', slug: 'get-pre-approved', completed: false },
          ],
        },
        {
          id: 3,
          title: 'House Hunt in Earnest',
          steps: [
            { number: '3.0', title: 'Overview', slug: 'phase-3-intro', completed: false },
            { number: '3.1', title: 'My Future Neighborhood', slug: 'my-future-neighborhood', completed: false },
            { number: '3.2', title: 'My Future Home', slug: 'my-future-home', completed: false },
            { number: '3.3', title: 'Narrow Down Your Listings', slug: 'narrow-down-your-listings', completed: false },
            { number: '3.4', title: 'Find a Great Agent', slug: 'find-a-great-agent', completed: false },
            { number: '3.5', title: 'Share Touring Availability', slug: 'share-availability', completed: false },
            { number: '3.6', title: 'Tour Houses Like A Pro', slug: 'tour-homes', completed: false },
            { number: '3.7', title: 'Decide on a House', slug: 'decide-on-a-house', completed: false },
          ],
        },
        {
          id: 4,
          title: 'Make an Offer & Negotiate',
          steps: [
            { number: '4.0', title: 'Overview', slug: 'phase-4-intro', completed: false },
            { number: '4.1', title: 'Closings 101', slug: 'closings-101', completed: false },
            { number: '4.2', title: 'Find An Attorney / Escrow Agent', slug: 'unknown-escrow', completed: false },
            { number: '4.3', title: 'Making an Offer 101', slug: 'making-an-offer-101', completed: false },
            { number: '4.4', title: 'Complete Purchase Offer', slug: 'complete-purchase-offer', completed: false },
            { number: '4.5', title: 'Write A Personal Note', slug: 'write-a-personal-note', completed: false },
            { number: '4.6', title: 'Submit Purchase Offer', slug: 'submit-purchase-offer', completed: false },
            { number: '4.7', title: 'Review Good Faith Estimate', slug: 'contingency-good-faith-estimate', completed: false },
            { number: '4.8', title: 'Get A Home Inspection', slug: 'contingency-get-home-inspection', completed: false },
            { number: '4.9', title: 'Confirm Home Appraisal', slug: 'contingency-confirm-home-appraisal', completed: false },
            { number: '4.10', title: 'Clear Title & Order Insurance', slug: 'contingency-clear-title-order-insurance', completed: false },
            { number: '4.11', title: 'Arrange Final Walk-Thru', slug: 'contingency-arrange-final-walk-thru', completed: false },
          ],
        },
        {
          id: 5,
          title: 'Prepare for Closing',
          steps: [
            { number: '5.0', title: 'Overview', slug: 'phase-5-intro', completed: false },
            { number: '5.1', title: 'Closing & Move-In Dates', slug: 'confirm-closing-and-move-in-date', completed: false },
            { number: '5.2', title: "Homeowners' Insurance 101", slug: 'homeowners-insurance-101', completed: false },
            { number: '5.3', title: "Order Homeowners' Insurance", slug: 'order-homeowners-insurance', completed: false },
            { number: '5.4', title: 'Prepare To Move', slug: 'prepare-to-move', completed: false },
            { number: '5.5', title: 'Change Address & Utilities', slug: 'change-address-and-utilities', completed: false },
            { number: '5.6', title: 'Request HUD-1 from Lender', slug: 'request-hud-1', completed: false },
            { number: '5.7', title: 'Arrange To Pay Closing Costs', slug: 'arrange-closing-costs', completed: false },
            { number: '5.8', title: 'Gather Closing Docs', slug: 'gather-closing-docs', completed: false },
            { number: '5.9', title: 'Perform Final Walk-Thru', slug: 'perform-final-walk-thru', completed: false },
          ],
        },
        {
          id: 6,
          title: 'Close & Move In',
          steps: [
            { number: '6.0', title: 'Overview', slug: 'phase-6-intro', completed: false },
            { number: '6.1', title: 'Confirm Closing Details', slug: 'confirm-closing-details', completed: false },
            { number: '6.2', title: 'Review HUD-1', slug: 'review-hud-1', completed: false },
            { number: '6.3', title: 'Compare GFE to HUD-1', slug: 'compare-gfe-to-hud-1', completed: false },
            { number: '6.4', title: 'Attend Closing', slug: 'attend-closing', completed: false },
            { number: '6.5', title: 'Welcome To Your Home', slug: 'welcome-to-your-home', completed: false },
          ],
        },
      ],
    };
  },
  methods: {
    toggleLeftDrawer() {
      this.leftDrawerOpen = !this.leftDrawerOpen;
    },
  },
};
</script>

<style scoped lang="scss">
@import 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css';

.nav-footer {
  @apply border-t border-grey-4;
}

.nav-footer-social {
  @apply flex justify-center space-x-4 mb-4;
}

.nav-footer-copyright {
  @apply text-sm text-grey-6;
}
</style>