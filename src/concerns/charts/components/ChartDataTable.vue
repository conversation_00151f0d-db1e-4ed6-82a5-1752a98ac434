<template>
  <div v-if="dataForTable">
    <q-table title="Price by Floor Area"
             :rows="dataForTable"
             :rows-per-page-options="[20]"
             :columns="columns"
             :row-class="rowClass"
             :class="[`text-gray text-body1`, rowClass]"
             row-key="SoldTransactionId">
      <template v-slot:body="props">
        <q-tr :props="props"
              @click="rowClicked(props.row)"
              :class="{ 'bg-blue-1': props.row === selectedRow }">
          <q-td v-for="col in props.cols"
                :key="col.name"
                :props="props">
            {{ col.value }}
          </q-td>
        </q-tr>
      </template>
    </q-table>

    <!-- <q-dialog v-model="showModal">
      <q-card style="width: 400px">
        <q-card-section class="row items-center no-wrap">
          <div class="text-h6">Details</div>
          <q-space />
          <q-btn icon="close"
                 flat
                 round
                 dense
                 @click="showModal = false" />
        </q-card-section>

        <q-card-section class="q-pa-sm">
          <div v-for="(value, key) in selectedRow"
               :key="key">
            <strong>{{ key }}:</strong> {{ value }}
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat
                 label="OK"
                 color="primary"
                 @click="showModal = false" />
        </q-card-actions>
      </q-card>
    </q-dialog> -->
  </div>
</template>

<script>
export default {
  computed: {
    dataForTable() {
      if (this.chartTableSeries.length > 0) {
        return this.chartTableSeries[0].data;
      } else {
        return null;
      }
    },
  },
  data() {
    return {
      // series: [
      //   {
      //     name: "Price By Floor Area",
      //     data: [
      //       {
      //         x: 111,
      //         y: 365000,
      //         dataItemLabel: "9, CHARLECOTE WALK",
      //         fillColor: "green",
      //         totalFloorArea: "111.13",
      //         soldDate: "August 2024",
      //         postalCode: "CV11 4YE",
      //         soldPrice: "£365,000.00",
      //         SoldTransactionEpcId: 3,
      //         SoldTransactionId: 137,
      //       },
      //       {
      //         x: 93,
      //         y: 240000,
      //         dataItemLabel: "45, EXBURY WAY",
      //         fillColor: "green",
      //         totalFloorArea: "93.0",
      //         soldDate: "June 2024",
      //         postalCode: "CV11 4RY",
      //         soldPrice: "£240,000.00",
      //         SoldTransactionEpcId: 5,
      //         SoldTransactionId: 6,
      //       },
      //       {
      //         x: 155,
      //         y: 195000,
      //         dataItemLabel: "14, GLEBE ROAD",
      //         fillColor: "green",
      //         totalFloorArea: "155.66",
      //         soldDate: "March 2024",
      //         postalCode: "CV11 4BJ",
      //         soldPrice: "£195,000.00",
      //         SoldTransactionEpcId: 8,
      //         SoldTransactionId: 340,
      //       },
      //     ],
      //   },
      // ],
      columns: [
        { name: 'dataItemLabel', label: 'Address', field: 'dataItemLabel', align: 'left' },
        { name: 'soldPrice', label: 'Sold Price', field: 'soldPrice', align: 'left' },
        { name: 'soldDate', label: 'Sold Date', field: 'soldDate', align: 'left' },
        { name: 'totalFloorArea', label: 'Total Floor Area', field: 'totalFloorArea', align: 'left' },
        { name: 'postalCode', label: 'Postal Code', field: 'postalCode', align: 'left' },
      ],
      selectedRow: null,
      clickedRowKey: null, // Add this to track the clicked row
    };
  },
  props: {
    chartTableSeries: {
      type: Array,
      default: () => [],
    },
    incomingTransactionId: {
      type: String,
      default: null,
    },
  },
  watch: {
    incomingTransactionId: {
      immediate: false,
      handler(newVal) {
        if (newVal && this.dataForTable) {
          this.clickedRowKey = parseInt(newVal); // Parse to integer if needed
          this.selectedRow = this.dataForTable.find(row => row.SoldTransactionId === this.clickedRowKey) || null;
        }
      },
    },
    dataForTable: {
      immediate: true, // Watch on initial load as well
      handler(newVal) {
        const transactionId = this.$route.query.actionId;
        if (transactionId && newVal) {
          this.clickedRowKey = parseInt(transactionId); // Parse to integer if needed
          this.selectedRow = newVal.find(row => row.SoldTransactionId === this.clickedRowKey) || null;
        }
      },
    },
  },
  methods: {
    rowClicked(row) {
      this.selectedRow = row;
      this.$emit('setDataFocus', row);
      this.clickedRowKey = row.SoldTransactionId; // Store the clicked row's key
      const queryString = new URLSearchParams({ actionId: row.SoldTransactionId }).toString();
      window.history.pushState(null, '', `?${queryString}`);
      // window.dispatchEvent(new Event('popstate'))
      // this.$router.push({
      //   query: { transactionId: row.SoldTransactionId },
      // });
    },
    rowClass(row) {
      if (row.SoldTransactionId === this.clickedRowKey) {
        return 'bg-yellow'; // Apply a light blue background class
      }
      return 'bg-green'; // No class for other rows
    },
  },
};
</script>