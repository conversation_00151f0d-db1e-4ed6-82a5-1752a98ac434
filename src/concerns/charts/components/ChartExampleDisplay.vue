<template>
  <div>
    <div v-if="chartDataReady">
      <apexchart @dataPointSelection="openChartModal"
                 height="450"
                 width="850"
                 :options="chartOptions"
                 :series="chartSeries"></apexchart>
    </div>
    <div v-else
         class="full-width flex justify-center items-center"
         style="height: 80vh">
      <q-circular-progress indeterminate
                           rounded
                           size="50px"
                           color="primary"
                           class="q-ma-md" />
      loading...
    </div>
  </div>
</template>

<script>
import useCharts from "src/compose/useCharts.js"

export default {
  props: {
    chartType: {
      type: String,
      default: 'time_series'
    },
    chartName: {
      type: String,
      default: 'scatter_plot'
    },
    // openChartModal: Function
  },
  methods: {
    openChartModal(event, chartContext, config) {
      let selectedPoint = this.chartSeries[0].data[config.dataPointIndex];
      this.$emit("openChartModal", selectedPoint)
    }
  },
  data() {
    return {
      chartDataReady: false,
      chartOptions: {},
      chartSeries: []
    }
  },
  mounted() {
    const { getExampleChartData } = useCharts()
    getExampleChartData(this.chartName)
      .then((response) => {
        this.chartOptions = response.data?.chart_setup?.options || {}
        this.chartSeries = response.data?.chart_setup?.series || []
        this.chartDataReady = true
      })
      .catch((error) => {
        console.error("Error fetching chart data:", error)
      })
  }
}
</script>
