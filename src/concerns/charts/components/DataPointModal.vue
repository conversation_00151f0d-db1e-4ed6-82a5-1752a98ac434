<template>
  <q-dialog v-model="localModalOpen">
    <q-card style="width: 300px">
      <q-card-section>
        <div class="text-h6">Data Point Info</div>
      </q-card-section>

      <q-card-section>
        <p>Label: {{ selectedPoint.label }}</p>
        <p>X: {{ selectedPoint.x }}</p>
        <p>Y: {{ selectedPoint.y }}</p>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat
               label="Close"
               color="primary"
               v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script>
export default {
  props: {
    modalOpen: Boolean,
    selectedPoint: Object
  },
  data() {
    return {
      localModalOpen: this.modalOpen
    };
  },
  watch: {
    modalOpen(newVal) {
      this.localModalOpen = newVal;
    },
    localModalOpen(newVal) {
      this.$emit('modalStateChanged', newVal);
    }
  }
}
</script>
