<template>
  <q-dialog v-model="localModalOpen">
    <q-card style="width: 300px">
      <q-card-section>
        <div class="text-h6">Property Info</div>
      </q-card-section>

      <q-card-section v-if="selectedPoint.predictedPriceA">
        <p>{{ selectedPoint.dataItemLabel }}</p>
        <p>Predicted price A: {{ selectedPoint.predictedPriceA }}</p>
        <p>{{ selectedPoint.postalCode }}</p>
        <p>Sold date: {{ selectedPoint.soldDate }}</p>
        <p>Floor area: {{ selectedPoint.totalFloorArea }}</p>
        <p>Sold price: {{ selectedPoint.soldPrice }}</p>
        <a :href="soldTransactionUrl">
          {{ soldTransactionUrl }}</a>
      </q-card-section>
      <q-card-section v-else>
        <p>{{ selectedPoint.dataItemLabel }}</p>
        <p>{{ selectedPoint.postalCode }}</p>
        <p>Sold date: {{ selectedPoint.soldDate }}</p>
        <p>Floor area: {{ selectedPoint.totalFloorArea }}</p>
        <p>Sold price: {{ selectedPoint.soldPrice }}</p>
        <a :href="soldTransactionUrl">
          {{ soldTransactionUrl }}</a>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat
               label="Close"
               color="primary"
               v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script>
export default {
  props: {
    modalOpen: Boolean,
    selectedPoint: Object
  },
  data() {
    return {
      localModalOpen: this.modalOpen
    };
  },
  computed: {
    soldTransactionUrl() {
      return `http://localhost:3333/superwiser/sold_transactions/${this.selectedPoint.SoldTransactionId}`;
    }
  },
  watch: {
    modalOpen(newVal) {
      this.localModalOpen = newVal;
    },
    localModalOpen(newVal) {
      this.$emit('modalStateChanged', newVal);
    }
  }
}
</script>
