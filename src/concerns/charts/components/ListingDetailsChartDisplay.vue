<template>
  <div>
    <div v-if="chartDataReady">
      <apexchart @dataPointSelection="openChartModal"
                 height="450"
                 width="850"
                 ref="soldHomesApexChart"
                 :options="chartOptions"
                 :series="chartSeries"></apexchart>
    </div>
    <div v-else
         class="full-width flex justify-center items-center"
         style="height: 80vh">
      <q-circular-progress indeterminate
                           rounded
                           size="50px"
                           color="primary"
                           class="q-ma-md" />
      loading...
    </div>
    <div>
      <!-- <ChartDataTable :chartTableSeries="chartSeries"
                      :incomingTransactionId="outgoingTransactionId"
                      @setDataFocus="setDataFocus" /> -->
    </div>
  </div>
</template>

<script>
// // import ChartDataTable from "src/concerns/charts/components/ChartDataTable.vue"
import useCharts from "src/compose/useCharts.js"
export default {
  components: {
    // ChartDataTable
  },
  props: {
    chartType: {
      type: String,
      default: 'time_series'
    },
    chartName: {
      type: String,
      default: 'scatter_plot'
    },
    // openChartModal: Function
  },
  methods: {
    updateAnnotations(selectedPoint) {
      let annotationPoint = null
      if (selectedPoint) {
        annotationPoint = selectedPoint
      } else {
        const urlParams = new URLSearchParams(window.location.search);
        const transactionId = urlParams.get('actionId');
        annotationPoint = this.chartSeries[0].data.find(
          point => point.SoldTransactionId === parseInt(transactionId))
      }
      if (annotationPoint) {
        // below passes annotationPoint to parent page
        this.$emit("passAnnotationPoint", annotationPoint)
        let newPointAnnotation = {
          x: annotationPoint.x,
          y: annotationPoint.y,
          marker: {
            size: 0,
            fillColor: '#FF4560',
            strokeColor: '#FF4560',
            radius: 2
          },
          label: {
            borderColor: 'black',
            offsetY: 0,
            style: {
              color: '#fff',
              background: 'green'
            },
            text: annotationPoint.dataItemLabel
          }
        }


        if (this.$refs.soldHomesApexChart) {
          this.$refs.soldHomesApexChart.clearAnnotations()
          // this.$refs.soldHomesApexChart.updateOptions(
          //   this.chartOptions)
          this.$refs.soldHomesApexChart.addPointAnnotation(newPointAnnotation)
        } else {
          this.chartOptions.annotations = {
            points: [newPointAnnotation]
          }
        }
      }
      // }
    },
    setDataFocus(selectedPoint) {
      this.outgoingTransactionId = selectedPoint.SoldTransactionId.toString()
      this.updateAnnotations(selectedPoint)
    },
    openChartModal(event, chartContext, config) {
      let selectedPoint = this.chartSeries[0].data[config.dataPointIndex];
      // below tells child datatable to highlight the row
      this.outgoingTransactionId = selectedPoint.SoldTransactionId.toString()
      this.updateAnnotations(selectedPoint)
    }
  },
  data() {
    return {
      outgoingTransactionId: null,
      chartDataReady: false,
      chartOptions: {},
      chartSeries: [],
      defaultChartTooltip: {
        intersect: true,
        shared: false,
        custom: function ({ series, seriesIndex, dataPointIndex, w }) {
          const data = w.config.series[seriesIndex].data[dataPointIndex]
          const queryString = new URLSearchParams({ actionId: data.SoldTransactionId }).toString();
          window.history.pushState(null, '', `?${queryString}`);

          // let xaxisTitle = w.config.xaxis.title?.text || 'X'
          // let yaxisTitle = w.config.yaxis[0]?.title?.text || 'Y'
          let priceDiffContent = "", predictedPriceContent = ""
          if (data.predictedPriceDifference) {
            priceDiffContent = `<div>Difference: ${data.predictedPriceDifference}</div>`
          }
          if (data.predictedPriceA) {
            predictedPriceContent = `<div>Predicted Price: ${data.predictedPriceA}</div>`
          }
          return `<div class="custom-tooltip q-pa-md bg-primary text-white">
                    <span>${data.dataItemLabel}</span><br/>
                            <p>${data.postalCode}</p>
                  <div>Sold price: ${data.soldPrice}</div>
                  <div>Sold date: ${data.soldDate}</div>
                  <div>Floor area: ${data.totalFloorArea}</div>
                  <br/>
                    ${predictedPriceContent}
                    ${priceDiffContent}
                  </div>`;
        }
      }
    }
  },

  computed: {
    queryString() {
      return window.location.search;
    }
  },
  watch: {
    queryString() {
      this.updateAnnotations();
    },
    // '$route.query.actionId'(newActionId) {
    //   console.log("Postal code changed to:", newActionId);
    // },
  },
  // beforeUnmount() {
  //   window.removeEventListener("popstate", this.updateAnnotations);
  // },
  mounted() {
    // Listen for URL changes
    // window.addEventListener("popstate", this.updateAnnotations);
    const { getChartData } = useCharts()
    getChartData(this.chartName)
      .then((response) => {
        this.chartOptions = response.data?.chart_setup?.options || {}
        this.chartOptions['tooltip'] = this.defaultChartTooltip
        this.chartSeries = response.data?.chart_setup?.series || []

        this.$emit("passChartSeries", this.chartSeries)

        this.chartDataReady = true
        this.updateAnnotations()
      })
      .catch((error) => {
        console.error("Error fetching chart data:", error)
      })
  }
}
</script>
