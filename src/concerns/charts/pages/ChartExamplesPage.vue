<template>
  <q-page class="q-pa-md">
    <q-no-ssr>
      <q-card flat
              bordered
              class="q-mb-md">
        <q-card-section>
          <div class="text-h6">UK Property Price Insights</div>
        </q-card-section>
        <q-separator />
        <q-card-section>
          <div class="row q-col-gutter-md">
            <!-- Scatter Plot for Price vs. Size -->
            <div class="col-12 col-md-6">
              <apexchart type="scatter"
                         height="350"
                         :options="priceVsSizeOptions"
                         :series="priceVsSizeSeries"></apexchart>
            </div>

            <!-- Bar Chart for Property Types -->
            <div class="col-12 col-md-6">
              <apexchart type="bar"
                         height="350"
                         :options="propertyTypeOptions"
                         :series="propertyTypeSeries"></apexchart>
            </div>

            <!-- Pie Chart for Tenure Distribution -->
            <div class="col-12 col-md-6">
              <apexchart type="pie"
                         height="350"
                         :options="tenureOptions"
                         :series="tenureSeries"></apexchart>
            </div>

            <!-- Box Plot for Price Prediction Accuracy -->
            <div class="col-12 col-md-6">
              <apexchart type="boxPlot"
                         height="350"
                         :options="predictionAccuracyOptions"
                         :series="predictionAccuracySeries"></apexchart>
            </div>
          </div>
        </q-card-section>
      </q-card>
    </q-no-ssr>
  </q-page>
</template>

<script>
import { defineComponent, ref } from 'vue'

export default defineComponent({
  name: 'UKPropertyPrices',
  setup() {
    const data = ref({
      "sold_transaction_prices": [

        {
          "id": 3,
          "formatted_sold_price": "£365,000.00",
          "st_predicted_price_a": "£320,233.21",
          "st_predicted_price_off_by_a": -44766,
          "avm_features": {
            "st_tenure": "Freehold",
            "st_property_type": "Detached",
            "st_postal_code": "CV11 4YE",
            "st_age_of_property": "Established",
            "total_floor_area": "111.13",
            "number_heated_rooms": 6,
            "number_habitable_rooms": 6,
            "construction_age_band": "age_1996_2002",
            "current_energy_rating": "D",
            "month": "Aug",
            "sold_date": "2024-08-09",
            "sold_price_cents": 36500000
          },
          "total_floor_area": "111.13",
          "number_heated_rooms": 6,
          "number_habitable_rooms": 6,
          "construction_age_band": "age_1996_2002",
          "current_energy_rating": "D",
          "potential_energy_rating": "C",
          "current_energy_efficiency": 64,
          "potential_energy_efficiency": 72,
          "tenure": "owner-occupied"
        },
        {
          "id": 5,
          "formatted_sold_price": "£240,000.00",
          "st_predicted_price_a": "£301,915.02",
          "st_predicted_price_off_by_a": 61915,
          "avm_features": {
            "st_tenure": "Freehold",
            "st_property_type": "Detached",
            "st_postal_code": "CV11 4RY",
            "st_age_of_property": "Established",
            "total_floor_area": "93.0",
            "number_heated_rooms": 5,
            "number_habitable_rooms": 5,
            "construction_age_band": "age_1991_1995",
            "current_energy_rating": "E",
            "month": "Jun",
            "sold_date": "2024-06-10",
            "sold_price_cents": 24000000
          },
          "total_floor_area": "93.0",
          "number_heated_rooms": 5,
          "number_habitable_rooms": 5,
          "construction_age_band": "age_1991_1995",
          "current_energy_rating": "E",
          "potential_energy_rating": "B",
          "current_energy_efficiency": 53,
          "potential_energy_efficiency": 82,
          "tenure": "owner-occupied"
        },
        {
          "id": 8,
          "formatted_sold_price": "£195,000.00",
          "st_predicted_price_a": "£302,531.10",
          "st_predicted_price_off_by_a": 107531,
          "avm_features": {
            "st_tenure": "Freehold",
            "st_property_type": "Semi-Detached",
            "st_postal_code": "CV11 4BJ",
            "st_age_of_property": "Established",
            "total_floor_area": "155.66",
            "number_heated_rooms": 7,
            "number_habitable_rooms": 7,
            "construction_age_band": "age_1900_1929",
            "current_energy_rating": "E",
            "month": "Mar",
            "sold_date": "2024-03-14",
            "sold_price_cents": 19500000
          },
          "total_floor_area": "155.66",
          "number_heated_rooms": 7,
          "number_habitable_rooms": 7,
          "construction_age_band": "age_1900_1929",
          "current_energy_rating": "E",
          "potential_energy_rating": "D",
          "current_energy_efficiency": 54,
          "potential_energy_efficiency": 63,
          "tenure": "rental (private)"
        },
        {
          "id": 10,
          "formatted_sold_price": "£350,000.00",
          "st_predicted_price_a": "£366,953.55",
          "st_predicted_price_off_by_a": 16953,
          "avm_features": {
            "st_tenure": "Freehold",
            "st_property_type": "Detached",
            "st_postal_code": "CV11 6NN",
            "st_age_of_property": "Established",
            "total_floor_area": "124.0",
            "number_heated_rooms": 5,
            "number_habitable_rooms": 5,
            "construction_age_band": "age_1967_1975",
            "current_energy_rating": "D",
            "month": "Aug",
            "sold_date": "2024-08-30",
            "sold_price_cents": 35000000
          },
          "total_floor_area": "124.0",
          "number_heated_rooms": 5,
          "number_habitable_rooms": 5,
          "construction_age_band": "age_1967_1975",
          "current_energy_rating": "D",
          "potential_energy_rating": "C",
          "current_energy_efficiency": 58,
          "potential_energy_efficiency": 79,
          "tenure": "owner-occupied"
        },
      ]
    })

    // Scatter Plot: Price vs. Size
    const priceVsSizeSeries = ref([{
      name: "Properties",
      data: data.value.sold_transaction_prices.map(item => ({
        x: parseFloat(item.total_floor_area),
        y: parseInt(item.formatted_sold_price.replace(/[^\d.-]/g, '')),
        label: item.avm_features.st_property_type
      }))
    }])
    const priceVsSizeOptions = ref({
      chart: { type: 'scatter', height: 350 },
      xaxis: { title: { text: 'Total Floor Area (sqm)' } },
      yaxis: { title: { text: 'Sold Price (GBP)' } },
      title: { text: 'Sold Price vs. Size of Properties' }
    })

    // Bar Chart: Property Type Distribution
    const propertyTypeCounts = data.value.sold_transaction_prices.reduce((acc, item) => {
      acc[item.avm_features.st_property_type] = (acc[item.avm_features.st_property_type] || 0) + 1
      return acc
    }, {})
    const propertyTypeSeries = ref([{
      name: 'Number of Properties',
      data: Object.values(propertyTypeCounts)
    }])
    const propertyTypeOptions = ref({
      chart: { type: 'bar', height: 350 },
      plotOptions: { bar: { horizontal: true } },
      xaxis: { categories: Object.keys(propertyTypeCounts) },
      title: { text: 'Distribution of Property Types Sold' }
    })

    // Pie Chart: Tenure Distribution
    const tenureCounts = data.value.sold_transaction_prices.reduce((acc, item) => {
      acc[item.tenure] = (acc[item.tenure] || 0) + 1
      return acc
    }, {})
    const tenureSeries = ref(Object.values(tenureCounts))
    const tenureOptions = ref({
      chart: { type: 'pie' },
      labels: Object.keys(tenureCounts),
      title: { text: 'Distribution of Property Tenure' }
    })

    // Box Plot: Prediction Accuracy
    const priceDifferences = data.value.sold_transaction_prices.map(item =>
      Math.abs(parseInt(item.formatted_sold_price.replace(/[^\d.-]/g, '')) - parseFloat(item.st_predicted_price_a))
    )
    const predictionAccuracySeries = ref([{
      type: 'boxPlot',
      data: [{
        x: 'Price Prediction Difference',
        y: [
          Math.min(...priceDifferences),
          ...calculateQuartiles(priceDifferences),  // Assuming you have a function to calculate quartiles
          Math.max(...priceDifferences)
        ]
      }]
    }])
    const predictionAccuracyOptions = ref({
      chart: { type: 'boxPlot', height: 350 },
      title: { text: 'Accuracy of Price Predictions', align: 'left' },
      plotOptions: {
        boxPlot: {
          colors: {
            upper: '#5C4742',
            lower: '#A5978B'
          }
        }
      },
      xaxis: { type: 'category' },
      yaxis: { title: { text: 'Difference (GBP)' } }
    })

    // Helper function for quartiles - this is a placeholder, implement properly based on your needs
    function calculateQuartiles(arr) {
      arr.sort((a, b) => a - b);
      const mid = Math.floor(arr.length / 2);
      return [
        arr[Math.floor(arr.length / 4)], // Q1
        arr[mid],                         // Median
        arr[Math.floor(3 * arr.length / 4)] // Q3
      ];
    }

    return {
      priceVsSizeSeries,
      priceVsSizeOptions,
      propertyTypeSeries,
      propertyTypeOptions,
      tenureSeries,
      tenureOptions,
      predictionAccuracySeries,
      predictionAccuracyOptions
    }
  }
})
</script>

<style scoped>
/* Add any specific styles here */
</style>