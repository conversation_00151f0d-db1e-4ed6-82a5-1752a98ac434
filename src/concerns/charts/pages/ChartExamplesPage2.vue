<template>
  <q-page class="flex flex-center">
    <q-no-ssr>
      <div>
        <ChartExampleDisplay chartType="line"
                             chartName="time_series"
                             @openChartModal="openChartModal" />
        <ChartExampleDisplay chartType="scatter"
                             chartName="time_series"
                             @openChartModal="openChartModal" />

        <ChartExampleDisplay chartType="line"
                             chartName="scatter_plot"
                             @openChartModal="openChartModal" />
        <ChartExampleDisplay chartType="scatter"
                             chartName="scatter_plot"
                             @openChartModal="openChartModal" />


        <ChartExampleDisplay chartName="heatmap"
                             @openChartModal="openChartModal" />

        <ChartExampleDisplay chartName="bar_chart"
                             @openChartModal="openChartModal" />


        <ChartExampleDisplay chartName="box_plot"
                             @openChartModal="openChartModal" />
        <!--
        <ChartExampleDisplay chartName="radar_chart"
                      @openChartModal="openChartModal" /> -->
      </div>
      <!-- <div v-else
           class="full-width flex justify-center items-center"
           style="height: 80vh">
        <q-circular-progress indeterminate
                             rounded
                             size="50px"
                             color="primary"
                             class="q-ma-md" />
        loading...
      </div> -->

      <DataPointModal @modalStateChanged="modalStateChanged"
                      :modalOpen="modalOpen"
                      :selectedPoint="selectedPoint" />
    </q-no-ssr>
  </q-page>
</template>

<script>
// import useCharts from "src/compose/useCharts.js"
import ChartExampleDisplay from "src/concerns/charts/components/ChartExampleDisplay.vue"
import DataPointModal from "src/concerns/charts/components/DataPointModal.vue"

export default {
  components: {
    ChartExampleDisplay,
    DataPointModal
  },
  name: 'ChartExamples',
  // setup(props) {
  //   const { getExampleChartData } = useCharts()
  //   return {
  //     getExampleChartData,
  //   }
  // },
  mounted() {
    // this.getExampleChartData('scatter_plot')
    //   .then((response) => {
    //     this.lineChartOptions = response.data?.chart_setup?.options || {}
    //     this.lineChartSeries = response.data?.chart_setup?.series || {}
    //     this.scatterChartOptions = response.data?.chart_setup?.options || {}
    //     this.scatterChartSeries = response.data?.chart_setup?.series || {}
    //     this.chartsLibReady = true
    //   })
    //   .catch((error) => { })
  },
  methods: {
    modalStateChanged(newVal) {
      this.modalOpen = newVal;
    },
    openChartModal(selectedPoint) {
      this.selectedPoint = selectedPoint
      this.modalOpen = true;
    }
  },
  data() {
    return {
      selectedPoint: {},
      modalOpen: false,
      scatterChartSeries: [{}],
      scatterChartOptions: {},
      lineChartSeries: [{}],
      lineChartOptions: {
        chart: {
          height: 550,
          type: 'line',
          zoom: {
            enabled: false
          },
          events: {
            dataPointSelection: (event, chartContext, config) => {
              // This is handled by @dataPointSelection in the template
            }
          }
        },
        // dataLabels: {
        //   enabled: false
        // },
        // stroke: {
        //   curve: 'straight'
        // },
        // title: {
        //   text: 'ApexCharts Line Example',
        //   align: 'left'
        // },
        grid: {
          row: {
            colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
            opacity: 0.5
          },
        },
        // xaxis: {
        //   // categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
        // }
        markers: {
          size: 6 // Ensure markers are visible; adjust size as needed
        },
        tooltip: {
          intersect: true,
          shared: false,
          // custom: function ({ series, seriesIndex, dataPointIndex, w }) {
          //   const data = w.config.series[seriesIndex].data[dataPointIndex];
          //   return `<div class="custom-tooltip">
          //             <span>${data.label}</span><br/>
          //             <span>X: ${data.x}</span><br/>
          //             <span>Y: ${data.y}</span>
          //           </div>`;
          // }
        }
      },
      chartsLibReady: false,

    }
  }
}
</script>
