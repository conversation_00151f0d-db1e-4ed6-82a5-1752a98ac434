<template>
  <q-page class="flex flex-center">
    <q-no-ssr>
      <div>
        <ChartDisplay chartName="sale_v_predicted_price"
                      @openChartModal="openChartModal" />

      </div>
      <DataPointModalForSoldTransaction @modalStateChanged="modalStateChanged"
                                        :modalOpen="modalOpen"
                                        :selectedPoint="selectedPoint" />
    </q-no-ssr>
  </q-page>
</template>

<script>
// import useCharts from "src/compose/useCharts.js"
import ChartDisplay from "src/concerns/charts/components/ChartDisplay.vue"
import DataPointModalForSoldTransaction from "src/concerns/charts/components/DataPointModalForSoldTransaction.vue"

export default {
  components: {
    ChartDisplay,
    DataPointModalForSoldTransaction
  },
  name: 'ChartExamples',
  // setup(props) {
  //   const { getExampleChartData } = useCharts()
  //   return {
  //     getExampleChartData,
  //   }
  // },
  mounted() { },
  methods: {
    modalStateChanged(newVal) {
      this.modalOpen = newVal;
    },
    openChartModal(selectedPoint) {
      this.selectedPoint = selectedPoint
      this.modalOpen = true;
    }
  },
  data() {
    return {
      selectedPoint: {},
      modalOpen: false,
    }
  }
}
</script>
