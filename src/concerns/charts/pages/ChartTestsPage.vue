<template>
  <q-page class="flex flex-center">
    <q-no-ssr>
      <div v-if="chartsLibReady">
        <div>
          <apexchart type="line"
                     @dataPointSelection="openModal"
                     height="450"
                     width="850"
                     :options="chartOptions"
                     :series="series"></apexchart>
        </div>
        <div>
          <apexchart type="scatter"
                     @dataPointSelection="openModal"
                     height="450"
                     width="850"
                     :options="chartOptions"
                     :series="series"></apexchart>
        </div>
      </div>
      <div v-else
           class="full-width flex justify-center items-center"
           style="height: 80vh">
        <q-circular-progress indeterminate
                             rounded
                             size="50px"
                             color="primary"
                             class="q-ma-md" />
        loading...
      </div>

      <div>
        <apexchart type="line"
                   height="350"
                   :options="chartOptions"
                   :series="series"
                   @dataPointSelection="openModal">
        </apexchart>

        <q-dialog v-model="modalOpen">
          <q-card style="width: 300px">
            <q-card-section>
              <div class="text-h6">Data Point Info</div>
            </q-card-section>

            <q-card-section>
              <p>Label: {{ selectedPoint.label }}</p>
              <p>X: {{ selectedPoint.x }}</p>
              <p>Y: {{ selectedPoint.y }}</p>
              <!-- Add more info here if available -->
            </q-card-section>

            <q-card-actions align="right">
              <q-btn flat
                     label="Close"
                     color="primary"
                     v-close-popup />
            </q-card-actions>
          </q-card>
        </q-dialog>
      </div>
    </q-no-ssr>
  </q-page>
</template>

<script>
import useCharts from "src/compose/useCharts.js"
export default {
  name: 'ChartExample',
  setup(props) {
    const { getChartData } = useCharts()
    return {
      getChartData,
    }
  },
  mounted() {
    this.getChartData()
      .then((response) => {
        let chartData = response.data?.chart_data || []
        this.series = [{
          name: 'Series Name',
          data: chartData.map(item => ({
            x: parseFloat(item[0]),
            y: item[1],
            label: item[2]
          }))
        }];
        // this.series[0].data = response.data?.chart_data || []
        this.chartsLibReady = true
      })
      .catch((error) => { })
  },
  methods: {
    openModal(event, chartContext, config) {
      // Assuming config.dataPointIndex gives you the index of the clicked point
      this.selectedPoint = this.series[0].data[config.dataPointIndex];
      this.modalOpen = true;
    }
  },
  data() {
    return {
      modalOpen: false,
      series: [{
        name: "Series 1",
        data: [31, 40, 28, 51, 42, 109, 100]
      }],
      chartOptions: {
        chart: {
          height: 550,
          type: 'line',
          zoom: {
            enabled: false
          },
          events: {
            dataPointSelection: (event, chartContext, config) => {
              // This is handled by @dataPointSelection in the template
            }
          }
        },
        // dataLabels: {
        //   enabled: false
        // },
        // stroke: {
        //   curve: 'straight'
        // },
        // title: {
        //   text: 'ApexCharts Line Example',
        //   align: 'left'
        // },
        grid: {
          row: {
            colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
            opacity: 0.5
          },
        },
        // xaxis: {
        //   // categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
        // }
        markers: {
          size: 6 // Ensure markers are visible; adjust size as needed
        },
        tooltip: {
          intersect: true,
          shared: false,
          // custom: function ({ series, seriesIndex, dataPointIndex, w }) {
          //   const data = w.config.series[seriesIndex].data[dataPointIndex];
          //   return `<div class="custom-tooltip">
          //             <span>${data.label}</span><br/>
          //             <span>X: ${data.x}</span><br/>
          //             <span>Y: ${data.y}</span>
          //           </div>`;
          // }
        }
      },
      chartsLibReady: false,

      chartData: [
        ["2023-01-01", 3],
        ["2023-01-02", 4],
        ["2023-01-03", 3.5]
      ]

    }
  }
}
</script>