<template>
  <q-page class="flex flex-center SoldHomesCharts">
    <q-no-ssr>
      <div>
        <h2>Price data</h2>
        <DataPointDetailsForSoldTransaction @modalStateChanged="modalStateChanged"
                                            :modalOpen="modalOpen"
                                            :selectedPoint="selectedPoint" />

      </div>
      <div>
        <div class="q-mb-md">
          <q-select v-model="selectedChartName"
                    :options="chartOptions"
                    label="Select Chart"
                    @update:model-value="updateChartName"
                    dense
                    outlined
                    class="q-mx-sm" />
        </div>
      </div>
      <div>
        <ChartDisplay :chartName="targetChartName"
                      @passChartSeries="passChartSeries"
                      @passAnnotationPoint="passAnnotationPoint"
                      @openChartModal="openChartModal" />

      </div>
      <div class="q-my-lg shc-map-ctr"
           v-if="selectedPoint.SoldTransactionId">
        <BaseGoogleMap :selectedPoint="selectedPoint"
                       @passMapPoint="passMapPoint"
                       :soldDataPoints="soldDataPoints" />
      </div>
      <div class="q-my-lg q-py-lg"></div>
    </q-no-ssr>
  </q-page>
</template>

<script>
import DataPointDetailsForSoldTransaction from "src/concerns/charts/components/DataPointDetailsForSoldTransaction.vue"
import ChartDisplay from "src/concerns/charts/components/ChartDisplay.vue"
import BaseGoogleMap from "src/concerns/maps/components/BaseGoogleMap.vue";
export default {
  components: {
    BaseGoogleMap,
    ChartDisplay,
    DataPointDetailsForSoldTransaction,
  },
  computed: {
    targetChartName() {
      return this.$route.params.targetChartName || 'sale_v_predicted_price'
    },
  },
  // watch: {
  //   queryString: {
  //     immediate: true,
  //     handler(newVal) {
  //       window.dispatchEvent(new Event('popstate'))
  //       if (newVal && this.dataForTable) {
  //         this.clickedRowKey = parseInt(newVal); // Parse to integer if needed
  //         this.selectedRow = this.dataForTable.find(row => row.SoldTransactionId === this.clickedRowKey) || null;
  //         // this.showModal = true;
  //       }
  //     },
  //   },
  // },
  name: 'SoldHomesCharts',
  mounted() {
    if (this.$route.params.targetChartName) {
      this.selectedChartName = this.chartOptions.find(
        option => option.value === this.$route.params.targetChartName
      )
      //  this.$route.params.targetChartName
    }
  },
  methods: {
    modalStateChanged(newVal) {
      this.modalOpen = newVal;
    },
    openChartModal(selectedPoint) {
      this.selectedPoint = selectedPoint
      this.modalOpen = true;
      const queryString = new URLSearchParams({ actionId: selectedPoint.SoldTransactionId }).toString();
      window.history.pushState(null, '', `?${queryString}`);
      // window.dispatchEvent(new Event('popstate'))
    },
    passChartSeries(chartSeries) {
      this.soldDataPoints = chartSeries[0].data
      // transactionLatitude = chartSeries[0].data[0].Latitude
    },
    passMapPoint(clickedOnMapPoint) {
      this.selectedPoint = this.soldDataPoints.find(row => row.SoldTransactionId === clickedOnMapPoint.SoldTransactionId) || null;
      const queryString = new URLSearchParams({ actionId: this.selectedPoint.SoldTransactionId }).toString();
      window.history.pushState(null, '', `?${queryString}`)
      window.dispatchEvent(new Event('popstate'))
    },
    passAnnotationPoint(selectedPoint) {
      this.selectedPoint = selectedPoint
      // const queryString = new URLSearchParams({ actionId: selectedPoint.SoldTransactionId }).toString();
      // window.history.pushState(null, '', `?${queryString}`);
    },
    updateChartName(selectedOption) {
      // Here you might want to update the route if you need the change to persist in the URL
      this.$router.push({
        name: 'rSoldHomesCharts', params: { targetChartName: selectedOption.value }
      });
    }
  },
  data() {
    return {
      selectedPoint: {},
      soldDataPoints: [],
      modalOpen: false,
      selectedChartName: 'price_by_floor_area', // Default chart name
      chartOptions: [
        { label: 'Sale vs Predicted Price', value: 'sale_v_predicted_price' },
        { label: 'Price by floor area', value: 'price_by_floor_area' },
        // Add more chart options here
      ]
    }
  }
}
</script>
