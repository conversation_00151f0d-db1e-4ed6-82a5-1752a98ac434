<template>
  <div class="ai-chat-container q-pa-md bg-grey-1 rounded-borders">
    <div class="text-center">
      <h3 class="text-h5 q-my-md text-primary h2c-underline"><PERSON> Chat Assistant</h3>
      <p class="text-subtitle1 text-grey-8 q-mb-lg">
        Unlock personalized AI assistance by creating your own dossier
      </p>
    </div>

    <div class="q-mt-md text-center">
      <q-icon name="lock"
              size="3rem"
              color="grey-6"
              class="q-mb-sm" />
      <p class="text-body1 text-grey-9 q-mb-md">
        You are currently viewing this demo as a guest. Create and own your dossier to enable AI chat features.
      </p>
      <q-btn color="primary"
             label="Create Your Own Dossier"
             icon="add_circle"
             rounded
             target="_blank"
             class="q-px-lg"
             href="https://homestocompare.com" />
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue'
import { useQuasar } from 'quasar'
import { useRouter } from 'vue-router'

export default defineComponent({
  name: '<PERSON><PERSON><PERSON>',

  props: {
    saleListing: {
      type: Object,
      required: true,
    },
    realtyDossier: {
      type: Object,
      required: true,
    },
  },

  setup(props) {
    const $q = useQuasar()
    const $router = useRouter()

    // const navigateToDossierCreation = () => {
    //   $q.notify({
    //     type: 'positive',
    //     message: 'Redirecting to dossier creation...',
    //     position: 'top',
    //   })
    //   $router.push('/dossier/create')
    // }

    // return {
    //   navigateToDossierCreation,
    // }
  },
})
</script>

<style scoped>
.ai-chat-container {
  margin: 0 auto;
  transition: all 0.3s ease;
}

.h2c-underline {
  position: relative;
  display: inline-block;
}

.h2c-underline::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: currentColor;
  border-radius: 2px;
}

.q-btn {
  transition: transform 0.2s ease;
}

.q-btn:hover {
  transform: translateY(-2px);
}
</style>