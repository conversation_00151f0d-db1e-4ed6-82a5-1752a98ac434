<template>
  <div class="calendar-container q-pa-md">
    <div>
      <h3 class="text-h5 q-mb-lg q-mt-md text-center h2c-underline"
          style="">Property Calendar</h3>
    </div>
    <div class="row items-center justify-between q-mb-md">
      <div class="row q-gutter-sm">
        <q-btn flat
               icon="chevron_left"
               @click="onPrev" />
        <q-btn flat
               icon="today"
               @click="onToday" />
        <q-btn flat
               icon="chevron_right"
               @click="onNext" />
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading"
         class="text-center q-pa-lg">
      <q-spinner color="primary"
                 size="2em" />
      <div class="q-mt-sm">Loading calendar...</div>
    </div>

    <!-- Error State -->
    <div v-else-if="error"
         class="text-center q-pa-lg text-negative">
      {{ error }}
    </div>

    <!-- Main Content -->
    <template v-else>
      <div class="row justify-center">
        <div style="display: flex;  width: 100%">
          <q-calendar-month ref="calendar"
                            v-model="selectedDate"
                            animated
                            bordered
                            focusable
                            hoverable
                            no-active-date
                            :day-min-height="60"
                            :day-height="0"
                            locale="en-us"
                            :events="calendarEvents"
                            @change="onChange"
                            @moved="onMoved"
                            @click-date="onClickDate"
                            @click-day="onClickDay"
                            @click-workweek="onClickWorkweek"
                            @click-head-workweek="onClickHeadWorkweek"
                            @click-head-day="onClickHeadDay">
            <template #day="{ scope: { timestamp } }">
              <template v-for="event in eventsMap[timestamp.date]"
                        :key="event.id">
                <div :class="badgeClasses(event)"
                     class="row justify-start items-center no-wrap my-event"
                     @click.stop="navigateToTask(event.id)">
                  <q-icon v-if="event.icon"
                          :name="event.icon"
                          class="q-mr-xs"></q-icon>
                  <div class="title q-calendar__ellipsis">
                    {{ event.title }}
                    <q-tooltip>{{ event.details }}</q-tooltip>
                  </div>
                </div>
              </template>
            </template>
          </q-calendar-month>
        </div>
      </div>
    </template>

    <!-- Create Task Dialog -->
    <q-dialog v-model="showCreateTaskDialog">
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">Create New Task</div>
        </q-card-section>

        <q-card-section class="q-pt-none">
          <q-input v-model="newTaskText"
                   label="Task Description"
                   autofocus
                   @keyup.enter="createTask" />
          <div class="q-mt-md">
            <q-select v-model="taskAssociation"
                      :options="associationOptions"
                      label="Associate task with"
                      outlined
                      emit-value
                      map-options
                      class="q-mb-md" />
          </div>
          <div v-if="
            taskAssociation === 'comparison' &&
            dossierAssetsComparisons.length > 0
          ">
            <q-select v-model="selectedComparison"
                      :options="comparisonOptions"
                      label="Select comparison"
                      outlined
                      emit-value
                      map-options />
          </div>
        </q-card-section>

        <q-card-actions align="right">
          <q-btn flat
                 label="Cancel"
                 color="primary"
                 v-close-popup />
          <q-btn flat
                 label="Create Task"
                 color="primary"
                 @click="createTask"
                 :disable="!newTaskText ||
                  (taskAssociation === 'comparison' && !selectedComparison)
                  " />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue'
import { useQuasar } from 'quasar'
import { useRouter } from 'vue-router'
import { useDossierTasks } from '../composables/useDossierTasks'
import {
  addToDate,
  parseDate,
  parseTimestamp,
  today,
} from '@quasar/quasar-ui-qcalendar/dist/index.esm.js'

export default defineComponent({
  name: 'TaskCalendar',

  props: {
    saleListing: {
      type: Object,
      required: true,
    },
    realtyDossier: {
      type: Object,
      required: true,
    },
  },

  setup(props) {
    const $q = useQuasar()
    const $router = useRouter()
    const calendar = ref(null)
    const selectedDate = ref(today())
    const showCreateTaskDialog = ref(false)
    const newTaskText = ref('')
    const taskAssociation = ref('primary')
    const selectedComparison = ref(null)
    const { tasks, isLoading, error, formatDate, addTask } = useDossierTasks(
      props.realtyDossier,
      $q
    )

    // Get dossier assets comparisons
    const dossierAssetsComparisons = computed(() => {
      return props.realtyDossier?.dossier_assets_comparisons || []
    })

    // Create association options
    const associationOptions = computed(() => {
      const options = [{ label: 'Northstar Property', value: 'primary' }]
      if (dossierAssetsComparisons.value.length > 0) {
        options.push({ label: 'Property Comparison', value: 'comparison' })
      }
      return options
    })

    // Create comparison options
    const comparisonOptions = computed(() => {
      return dossierAssetsComparisons.value.map((comparison) => ({
        label:
          comparison.right_side_property?.street_address ||
          comparison.right_side_property?.title ||
          'Unnamed Property',
        value: comparison.uuid,
      }))
    })

    // Convert tasks to calendar events
    const calendarEvents = computed(() => {
      return tasks.value.map((task) => ({
        id: task.id,
        title: task.text,
        date: task.dueDate
          ? task.dueDate?.split('T')[0]
          : task.createdAt?.split('T')[0],
        details: `Status: ${task.completed ? 'Completed' : 'Pending'
          }\nCreated: ${formatDate(task.createdAt)}`,
        icon: task.completed ? 'check_circle' : 'pending',
        bgcolor: task.completed
          ? 'positive'
          : task.isPrimary
            ? 'primary'
            : 'secondary',
      }))
    })

    // Create events map for better event handling
    const eventsMap = computed(() => {
      const map = {}
      if (calendarEvents.value.length > 0) {
        calendarEvents.value.forEach((event) => {
          ; (map[event.date] = map[event.date] || []).push(event)
        })
      }
      return map
    })

    // Event badge styling
    const badgeClasses = (event) => {
      return {
        'text-white': true,
        [`bg-${event.bgcolor}`]: true,
        'q-calendar__ellipsis': true,
        'rounded-border': true,
      }
    }

    // Navigation methods
    const onToday = () => {
      if (calendar.value) {
        calendar.value.moveToToday()
      }
    }

    const onPrev = () => {
      if (calendar.value) {
        calendar.value.prev()
      }
    }

    const onNext = () => {
      if (calendar.value) {
        calendar.value.next()
      }
    }

    // Event handlers
    const onMoved = (data) => {
      console.info('onMoved', data)
    }

    const onChange = (data) => {
      console.info('onChange', data)
    }

    const onClickDate = (data) => {
      showCreateTaskDialog.value = true
    }

    const onClickDay = (data) => {
      showCreateTaskDialog.value = true
    }

    const onClickWorkweek = (data) => {
      console.info('onClickWorkweek', data)
    }

    const onClickHeadDay = (data) => {
      console.info('onClickHeadDay', data)
    }

    const onClickHeadWorkweek = (data) => {
      console.info('onClickHeadWorkweek', data)
    }

    // Navigate to task details
    const navigateToTask = (taskId) => {
      $router.push({ name: 'rTaskDetails', params: { taskId } })
    }

    // Create new task
    const createTask = () => {
      if (!newTaskText.value) return

      const isPrimary = taskAssociation.value === 'primary'
      const comparisonId = isPrimary ? null : selectedComparison.value
      const dueDate = selectedDate.value

      addTask(newTaskText.value, comparisonId, isPrimary, dueDate)

      // Reset form
      newTaskText.value = ''
      taskAssociation.value = 'primary'
      selectedComparison.value = null
      showCreateTaskDialog.value = false

      $q.notify({
        color: 'positive',
        message: 'Task created successfully',
        icon: 'check',
      })
    }

    return {
      calendar,
      selectedDate,
      calendarEvents,
      eventsMap,
      isLoading,
      error,
      badgeClasses,
      onToday,
      onPrev,
      onNext,
      onMoved,
      onChange,
      onClickDate,
      onClickDay,
      onClickWorkweek,
      onClickHeadDay,
      onClickHeadWorkweek,
      navigateToTask,
      showCreateTaskDialog,
      newTaskText,
      taskAssociation,
      selectedComparison,
      associationOptions,
      comparisonOptions,
      dossierAssetsComparisons,
      createTask,
    }
  },
})
</script>

<style scoped>
.calendar-container {
  max-width: 1200px;
  margin: 0 auto;
}

.my-event {
  position: relative;
  font-size: 12px;
  width: 100%;
  max-width: 100%;
  margin: 1px 0 0 0;
  padding: 0 2px;
  justify-content: start;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  cursor: pointer;
}

.title {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  max-width: 100%;
}

.text-white {
  color: white;
}

.rounded-border {
  border-radius: 2px;
}
</style>
