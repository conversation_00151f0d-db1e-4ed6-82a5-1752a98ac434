<template>
  <div class="create-task-container q-pa-md">
    <div class="text-h5 q-mb-md">Create New Task</div>

    <!-- Task Input Form -->
    <div class="row q-mb-md">
      <div class="col-12">
        <q-input v-model="newTask"
                 filled
                 label="Task description"
                 class="q-mb-sm"
                 :error="!!error"
                 :error-message="error" />
      </div>
    </div>

    <!-- Task Association Selection -->
    <div class="row q-mb-md">
      <div class="col-12">
        <q-select v-model="taskAssociation"
                  :options="associationOptions"
                  label="Associate task with"
                  outlined
                  emit-value
                  map-options />
      </div>
    </div>

    <!-- Comparison Selection -->
    <div class="row q-mb-md"
         v-if="
          dossierAssetsComparisons.length > 0 && taskAssociation === 'comparison'
        ">
      <div class="col-12">
        <q-select v-model="selectedComparison"
                  :options="comparisonOptions"
                  label="Select comparison"
                  outlined
                  emit-value
                  map-options />
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="row q-mt-lg">
      <div class="col-12">
        <div class="flex justify-between">
          <q-btn flat
                 color="primary"
                 label="Cancel"
                 @click="$router.push({ name: 'rTasks' })" />
          <q-btn color="primary"
                 label="Create Task"
                 @click="handleCreateTask"
                 :disable="!newTask ||
                  (taskAssociation === 'comparison' && !selectedComparison)
                  " />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue'
import { useQuasar } from 'quasar'
import { useRouter } from 'vue-router'
import { useDossierTasks } from '../composables/useDossierTasks'

export default defineComponent({
  name: 'CreateTask',

  props: {
    saleListing: {
      type: Object,
      required: true,
    },
    realtyDossier: {
      type: Object,
      required: true,
    },
  },

  setup(props) {
    const $q = useQuasar()
    const $router = useRouter()
    const newTask = ref('')
    const taskAssociation = ref('primary')
    const selectedComparison = ref(null)
    const { error, addTask } = useDossierTasks(props.realtyDossier, $q)

    // Get dossier assets comparisons
    const dossierAssetsComparisons = computed(() => {
      return props.realtyDossier?.dossier_assets_comparisons || []
    })

    // Create association options
    const associationOptions = computed(() => {
      const options = [{ label: 'Northstar Property', value: 'primary' }]
      if (dossierAssetsComparisons.value.length > 0) {
        options.push({ label: 'Property Comparison', value: 'comparison' })
      }
      return options
    })

    // Create comparison options
    const comparisonOptions = computed(() => {
      return dossierAssetsComparisons.value.map((comparison) => ({
        label:
          comparison.right_side_property?.street_address ||
          comparison.right_side_property?.title ||
          'Unnamed Property',
        value: comparison.uuid,
      }))
    })

    const handleCreateTask = () => {
      const isPrimary = taskAssociation.value === 'primary'
      const comparisonId = isPrimary ? null : selectedComparison.value
      addTask(newTask.value, comparisonId, isPrimary).then((newTask) => {
        $q.notify({
          color: 'positive',
          message: 'Task created successfully',
          icon: 'check',
        })

        // Reset form
        newTask.value = ''
        taskAssociation.value = 'primary'
        selectedComparison.value = null

        // Navigate to the new task
        $router.push({ name: 'rTaskDetails', params: { taskId: newTask.id } })
      })
    }

    return {
      newTask,
      taskAssociation,
      selectedComparison,
      error,
      associationOptions,
      comparisonOptions,
      dossierAssetsComparisons,
      handleCreateTask,
    }
  },
})
</script>

<style scoped>
.create-task-container {
  max-width: 600px;
  margin: 0 auto;
}
</style>
