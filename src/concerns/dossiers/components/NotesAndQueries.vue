<template>
  <div class="notes-queries-container q-pa-md">
    <!-- Loading State -->
    <div v-if="isLoading"
         class="text-center q-pa-lg">
      <q-spinner color="primary"
                 size="3em" />
      <div class="q-mt-md text-grey-7">Loading notes...</div>
    </div>

    <!-- Error State -->
    <div v-else-if="error"
         class="text-center q-pa-lg">
      <q-icon name="error_outline"
              color="negative"
              size="2em" />
      <div class="q-mt-sm text-negative">{{ error }}</div>
    </div>

    <!-- Main Content -->
    <div v-else-if="isChildRoute">
      <router-view :realtyDossier="realtyDossier"
                   :saleListing="saleListing" />
    </div>

    <template v-else>
      <div class="q-mb-lg">
        <h3 class="text-h5 q-mt-md text-center h2c-underline">
          Househunting Notes & Comments
        </h3>
      </div>

      <!-- Navigation Tabs -->
      <div class="navigation-section q-mb-lg">
        <q-tabs v-model="activeTab"
                class="text-grey-8 navigation-tabs"
                active-color="primary"
                indicator-color="primary"
                align="left"
                inline-label
                dense>
          <q-route-tab name="all"
                       label="All Notes"
                       :to="{ name: 'rNotesAndQueries' }"
                       exact
                       class="text-weight-medium" />
          <q-route-tab name="primary"
                       label="Primary Property"
                       :to="{ name: 'rNotesAndQueriesPrimary' }"
                       class="text-weight-medium" />
          <q-route-tab v-for="comparison in dossierAssetsComparisons"
                       :key="comparison.uuid"
                       :name="`comparison-${comparison.uuid}`"
                       :label="getComparisonTabLabel(comparison)"
                       :to="{ name: 'rNotesAndQueriesComparison', params: { comparisonUuid: comparison.uuid } }"
                       class="text-weight-medium comparison-tab" />
        </q-tabs>
      </div>

      <div class="controls-section q-mb-xl">
        <div class="row items-center justify-between q-mb-md">
          <!-- Create Note Button with Dropdown -->
          <div class="create-note-section">
            <!-- <q-btn v-if="showGenericCreateNoteBtn"
                   color="primary"
                   icon="add_comment"
                   label="Create New Note"
                   rounded
                   unelevated
                   @click="navigateToCreateNote">
            </q-btn> -->

            <q-btn v-if="showGenericCreateNoteBtn"
                   color="accent"
                   icon="add"
                   label="Create New Note"
                   size="sm"
                   outline
                   rounded
                   class="q-ml-sm"
                   @click="navigateToCreateNote" />
            <!-- Quick create button for current filter -->
            <q-btn v-if="currentFilterType !== 'all'"
                   color="accent"
                   icon="add"
                   :label="`Add ${currentFilterType === 'primary' ? 'Primary' : 'Comparison'} Note`"
                   size="sm"
                   outline
                   rounded
                   class="q-ml-sm"
                   @click="navigateToFilteredCreate" />
          </div>

          <!-- Filter Summary -->
          <div class="filter-summary">
            <q-chip v-if="currentFilterType !== 'all'"
                    :color="currentFilterType === 'primary' ? 'primary' : 'secondary'"
                    text-color="white"
                    icon="filter_list"
                    removable
                    @remove="clearFilter">
              {{ currentFilterLabel }}
            </q-chip>
            <q-chip v-if="filteredNotesAndQueries.length > 0"
                    color="accent"
                    text-color="white"
                    icon="speaker_notes">
              {{ filteredNotesAndQueries.length }} note{{ filteredNotesAndQueries.length !== 1 ? 's' : '' }}
            </q-chip>
          </div>
        </div>
      </div>

      <!-- Notes List - Conversational Style -->
      <div v-if="filteredNotesAndQueries.length > 0"
           class="notes-list">
        <div v-for="item in filteredNotesAndQueries"
             :key="item.id"
             class="note-item q-mb-lg">
          <q-chat-message :name="item.user ? (item.jotCreatorName || item.user.name || 'System') : 'System'"
                          :text="[item.text]"
                          :stamp="formatRelativeTime(item.createdAt)"
                          :sent="isCurrentUser(item.user)"
                          :bg-color="isCurrentUser(item.user) ? 'primary' : 'grey-2'"
                          :text-color="isCurrentUser(item.user) ? 'white' : 'black'"
                          class="note-chat-message">
            <template v-slot:default>
              <!-- Display text -->
              <div class="q-pb-sm message-text"
                   v-html="formatNoteText(item.text)"></div>

              <!-- Display Image if available -->
              <div v-if="item.image && item.image.image_details && item.image.image_details.url"
                   class="q-mt-sm image-attachment">
                <q-img :src="item.image.image_details.url"
                       class="rounded-borders cursor-pointer"
                       style="min-width: 300px; max-height: 650px;"
                       fit="cover"
                       @click="showImageFullScreen(item.image.image_details.url)">
                  <q-tooltip>Click to enlarge</q-tooltip>
                </q-img>
              </div>

              <!-- Metadata (e.g., related to) -->
              <div class="q-mt-xs text-caption metadata-info"
                   :class="isCurrentUser(item.user) ? 'text-blue-grey-1' : 'text-grey-7'">
                <!-- <span> <q-icon name="push_pin"
                          size="xs"
                          class="q-mr-xs" /> Related to: Northstar Property
                </span> -->
                <span v-if="item.isPrimaryListingJot">
                  <q-icon name="push_pin"
                          size="xs"
                          class="q-mr-xs" /> Related to: Northstar Property
                </span>
                <span v-else-if="item.assetsComparisonUuid">
                  <q-icon name="compare_arrows"
                          size="xs"
                          class="q-mr-xs" /> Related to: {{ getComparisonName(item) }}
                </span>
              </div>
            </template>

            <!-- Actions like Delete -->
            <!-- Positioned outside the text slot for better layout with q-chat-message -->
          </q-chat-message>
          <div class="actions-row row justify-end q-pr-sm q-mt-xs">
            <q-btn flat
                   round
                   dense
                   color="grey-7"
                   icon="edit"
                   size="sm"
                   @click.stop="$router.push({ name: 'rNoteOrQueryDetails', params: { itemId: item.id } })"
                   class="q-mr-xs">
              <q-tooltip>Edit Note</q-tooltip>
            </q-btn>
            <q-btn flat
                   round
                   dense
                   color="negative"
                   icon="delete_outline"
                   size="sm"
                   @click.stop="() => confirmDeleteItem(item.id)">
              <q-tooltip>Delete Note</q-tooltip>
            </q-btn>
          </div>
          <q-separator spaced="md"
                       v-if="filteredNotesAndQueries.length > 1 && item.id !== filteredNotesAndQueries[filteredNotesAndQueries.length - 1].id" />
        </div>
      </div>

      <!-- Empty State -->
      <div v-else
           class="text-center q-pa-xl text-grey-7">
        <q-icon name="speaker_notes_off"
                size="3em"
                class="q-mb-sm" />
        <div>No notes or queries found for the current filter.</div>
        <div>Create a new note or query to get started!</div>
      </div>
    </template>

    <!-- Full Screen Image Dialog -->
    <q-dialog v-model="imageFullScreenVisible"
              full-width
              full-height>
      <q-card class="column items-center justify-center bg-black-transparent">
        <q-img :src="currentFullScreenImage"
               style="max-width: 90vw; max-height: 90vh;"
               fit="contain" />
        <q-btn icon="close"
               flat
               round
               dense
               @click="imageFullScreenVisible = false"
               class="absolute-top-right q-ma-sm"
               color="white"
               style="background-color: rgba(0,0,0,0.3)" />
      </q-card>
    </q-dialog>

  </div>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'
import { useRoute, useRouter } from 'vue-router'
import { formatDistanceToNowStrict } from 'date-fns' // For relative time
import { useDossierNotesAndQueries } from 'src/concerns/dossiers/composables/useDossierNotesAndQueries' // Verify path

export default defineComponent({
  name: 'PropertyNotesAndQueries',
  props: {
    saleListing: { type: Object, required: true },
    realtyDossier: { type: Object, required: true },
    currentUser: { type: Object, default: () => ({ id: 'current_user_placeholder_id', name: 'Me' }) },
    comparisonUuid: { type: String, required: false } // For route-based filtering
  },
  setup(props) {
    const $q = useQuasar()
    const $route = useRoute()
    const $router = useRouter()

    const filterType = ref('all')
    const selectedComparison = ref(null)
    const imageFullScreenVisible = ref(false)
    const currentFullScreenImage = ref('')
    const activeTab = ref('all')

    // Assuming `notesAndQueries` from the composable includes user details like `item.user.id`
    // and `item.user.name` or `item.jotCreatorName`, `item.user.avatar_url`
    const {
      notesAndQueries,
      isLoading,
      error,
      deleteNoteOrQuery,
      // formatDate, // We'll use formatRelativeTime or a more detailed local one
    } = useDossierNotesAndQueries(props.realtyDossier, $q)

    const dossierAssetsComparisons = computed(() => {
      return props.realtyDossier?.dossier_assets_comparisons || []
    })

    // Route-based filtering
    const currentFilterType = computed(() => {
      const routeMeta = $route.meta
      if (routeMeta?.filterType === 'primary') return 'primary'
      if (routeMeta?.filterType === 'comparison') return 'comparison'
      return 'all'
    })

    const currentComparisonUuid = computed(() => {
      return props.comparisonUuid || $route.params.comparisonUuid
    })

    const currentFilterLabel = computed(() => {
      if (currentFilterType.value === 'primary') {
        return 'Primary Property Notes'
      }
      if (currentFilterType.value === 'comparison' && currentComparisonUuid.value) {
        return `${getComparisonName({ assetsComparisonUuid: currentComparisonUuid.value })} Notes`
      }
      return 'All Notes'
    })

    const isChildRoute = computed(() => {
      return ['rCreateNoteOrQuery',
        'rCreateNoteOrQueryPrimary',
        'rCreateNoteOrQueryComparison'].includes($route.name)
      // return $route.name === 'rCreateNoteOrQuery'
    })

    const showGenericCreateNoteBtn = computed(() => {
      // return ['rCreateNoteOrQuery',
      //   'rCreateNoteOrQueryPrimary',
      //   'rCreateNoteOrQueryComparison'].includes($route.name)
      return $route.name === 'rNotesAndQueries'
    })

    // Update active tab based on route
    watch(() => $route.name, (newRouteName) => {
      if (newRouteName === 'rNotesAndQueries') {
        activeTab.value = 'all'
      } else if (newRouteName === 'rNotesAndQueriesPrimary') {
        activeTab.value = 'primary'
      } else if (newRouteName === 'rNotesAndQueriesComparison') {
        activeTab.value = `comparison-${currentComparisonUuid.value}`
      }
    }, { immediate: true })

    const filterOptions = computed(() => {
      const options = [
        { label: 'All Notes', value: 'all' },
        { label: 'Northstar Property Notes', value: 'primary' },
      ]
      if (dossierAssetsComparisons.value.length > 0) {
        options.push({ label: 'Comparison Notes', value: 'comparison' })
      }
      return options
    })

    const comparisonOptions = computed(() => {
      return dossierAssetsComparisons.value.map((comparison) => ({
        label:
          comparison.right_side_property?.street_address ||
          comparison.right_side_property?.title ||
          '...Unnamed Property',
        value: comparison.uuid,
      }))
    })

    // --- Enhanced jotsWithImages to include user info ---
    // You'll need to ensure your `notesAndQueries` (jots) from the backend
    // or your composable can provide user information (e.g., `created_by_user_details`).
    const jotsWithImagesAndUsers = computed(() => {
      // const imgs = props.realtyDossier?.dossier_sale_listing?.sale_listing_pics || []
      return notesAndQueries.value.map((note) => {
        // const img = imgs.find((img) => img.uuid === note.primPhotoUuid)
        return {
          ...note,
          image: note.jotPhoto, // This structure is { uuid, image_details: { url }}
          user: note.jot_creator_name || note.jotCreatorName || { name: '..Unknown User', id: 'unknown' }, // Adjust based on your API
          isPrimaryListingJot: note.isPrimaryListingJot || false,
          assetsComparisonUuid: note.assetsComparisonUuid,
          comparisonId: note.comparisonId || null,
        }
      }).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)) // Sort newest first for conversational
    })

    const filteredNotesAndQueries = computed(() => {
      let notes = jotsWithImagesAndUsers.value;

      // Use route-based filtering instead of local filter state
      if (currentFilterType.value === 'primary') {
        return notes.filter((item) => item.isPrimaryListingJot)
      }
      if (currentFilterType.value === 'comparison') {
        if (currentComparisonUuid.value) {
          return notes.filter(
            (item) => item.assetsComparisonUuid === currentComparisonUuid.value
          )
        }
        return notes.filter((item) => item.assetsComparisonUuid)
      }
      return notes // 'all' or default
    })

    const getComparisonName = (jotItem) => { /* ... same ... */
      if (jotItem.isPrimaryListingJot) {
        return "Northstar Property"
      }
      let comparisonUuid = jotItem.assetsComparisonUuid
      const comparison = dossierAssetsComparisons.value.find(
        (c) => c.uuid === comparisonUuid
      )
      return (
        comparison?.right_side_property?.street_address ||
        comparison?.right_side_property?.title ||
        'Unnamed Property'
      )
    }

    const confirmDeleteItem = (itemId) => {
      $q.dialog({
        title: 'Confirm Delete',
        message: 'Are you sure you want to delete this note? This action cannot be undone.',
        cancel: true,
        persistent: true,
        ok: { label: 'Delete', color: 'negative', unelevated: true },
        cancel: { label: 'Cancel', flat: true }
      }).onOk(async () => {
        await deleteNoteOrQuery(itemId)
        $q.notify({ type: 'positive', message: 'Note deleted successfully.' })
      })
    }

    const formatRelativeTime = (dateString) => {
      if (!dateString) return ''
      try {
        return formatDistanceToNowStrict(new Date(dateString), { addSuffix: true })
      } catch (e) {
        return dateString
      }
    }

    // const getInitialsAvatar = (name) => {
    //   if (!name) return '?'
    //   const initials = name
    //     .split(' ')
    //     .map((n) => n[0])
    //     .join('')
    //     .substring(0, 2)
    //     .toUpperCase();
    //   // Create a data URL for a simple avatar with initials
    //   const canvas = document.createElement('canvas');
    //   const size = 64; // Avatar image size
    //   canvas.width = size;
    //   canvas.height = size;
    //   const context = canvas.getContext('2d');
    //   // Simple color hashing for background
    //   let hash = 0;
    //   for (let i = 0; i < name.length; i++) {
    //     hash = name.charCodeAt(i) + ((hash << 5) - hash);
    //   }
    //   const c = (hash & 0x00FFFFFF).toString(16).toUpperCase();
    //   const bgColor = "#" + "00000".substring(0, 6 - c.length) + c;

    //   context.fillStyle = bgColor;
    //   context.fillRect(0, 0, size, size);
    //   context.fillStyle = '#FFF'; // Text color
    //   context.font = `${size / 2.5}px Arial`;
    //   context.textAlign = 'center';
    //   context.textBaseline = 'middle';
    //   context.fillText(initials, size / 2, size / 2);
    //   return canvas.toDataURL();
    // }

    const isCurrentUser = (user) => {
      // Implement your logic to check if the note's user is the currently logged-in user
      // This is important for the `sent` prop of q-chat-message
      return user && user.id === props.currentUser.id;
    }

    const showImageFullScreen = (imageUrl) => {
      currentFullScreenImage.value = imageUrl
      imageFullScreenVisible.value = true
    }

    const formatNoteText = (text) => {
      // Simple link detection (naive)
      if (!text) return '';
      const urlRegex = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
      return text.replace(/\n/g, '<br />').replace(urlRegex, function (url) {
        return `<a href="${url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">${url}</a>`;
      });
    }

    // Helper method for tab labels
    const getComparisonTabLabel = (comparison) => {
      const address = comparison.right_side_property?.street_address ||
        comparison.right_side_property?.title ||
        'Unnamed Property'
      // Truncate long addresses for tab display
      return address.length > 20 ? address.substring(0, 20) + '...' : address
    }

    // Clear filter method
    const clearFilter = () => {
      $router.push({ name: 'rNotesAndQueries' })
    }

    // Navigation methods
    const navigateToCreateNote = () => {
      $router.push({ name: 'rCreateNoteOrQuery' })
    }

    const navigateToFilteredCreate = () => {
      if (currentFilterType.value === 'primary') {
        $router.push({ name: 'rCreateNoteOrQueryPrimary' })
      } else if (currentFilterType.value === 'comparison' && currentComparisonUuid.value) {
        $router.push({
          name: 'rCreateNoteOrQueryComparison',
          params: { comparisonUuid: currentComparisonUuid.value }
        })
      } else {
        $router.push({ name: 'rCreateNoteOrQuery' })
      }
    }

    return {
      // Route-based filtering
      isChildRoute,
      showGenericCreateNoteBtn,
      currentFilterType,
      currentFilterLabel,
      currentComparisonUuid,
      activeTab,

      // Data
      isLoading,
      error,
      filterType,
      selectedComparison,
      filterOptions,
      comparisonOptions,
      filteredNotesAndQueries,
      dossierAssetsComparisons,

      // Methods
      confirmDeleteItem,
      formatRelativeTime,
      getComparisonName,
      getComparisonTabLabel,
      clearFilter,
      navigateToCreateNote,
      navigateToFilteredCreate,
      isCurrentUser,
      imageFullScreenVisible,
      currentFullScreenImage,
      showImageFullScreen,
      formatNoteText,
    }
  },
})
</script>

<style scoped>
.notes-queries-container {
  max-width: 900px;
  /* Slightly wider for chat messages */
  margin: 0 auto;
  background-color: #f9f9f9;
  /* Lighter page background */
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}


.filter-card {
  background-color: #fff;
  border-radius: 8px;
}

.notes-list {
  padding: 0 8px;
  /* Add some padding if chat messages go full width */
}

/* Navigation Tabs */
.navigation-tabs {
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 1rem;
}

.navigation-tabs .q-tab {
  font-size: 0.9rem;
  padding: 8px 16px;
  min-height: 40px;
}

.comparison-tab {
  max-width: 200px;
}

.filter-summary {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.create-note-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Note items are handled by q-chat-message component */

.note-chat-message {
  width: 100%;
  /* Make chat message take full width for alignment */
  font-size: 0.95rem;
}

.note-chat-message .q-message-text--received .q-message-text-content {
  color: #222 !important;
  /* Darker text for received messages */
}


.note-chat-message :deep(.q-message-name) {
  font-weight: 600;
  margin-bottom: 2px;
  color: #555;
  /* Slightly subdued name color */
}

.note-chat-message :deep(.q-message-stamp) {
  font-size: 0.7rem;
  opacity: 0.8;
}

.image-attachment .q-img {
  border: 1px solid #eee;
  border-radius: 8px;
  /* Softer rounded corners for images */
  transition: transform 0.2s ease-out;
}

.image-attachment .q-img:hover {
  transform: scale(1.03);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.metadata-info {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  opacity: 0.9;
}

.actions-row {
  opacity: 0;
  /* Hidden by default */
  transition: opacity 0.2s ease-in-out;
  height: 28px;
  /* Reserve space to prevent layout jump */
}

.note-item:hover .actions-row {
  opacity: 1;
  /* Show on hover of the note-item container */
}

/* Ensure clickable items in list have pointer */
.q-list .q-item--clickable {
  cursor: pointer;
}

.bg-black-transparent {
  background-color: rgba(0, 0, 0, 0.85) !important;
}

.message-text {
  line-height: 1.5;
  white-space: pre-wrap;
  /* Respect newlines from input */
}

.message-text a {
  word-break: break-all;
  /* Ensure long URLs don't break layout */
}
</style>