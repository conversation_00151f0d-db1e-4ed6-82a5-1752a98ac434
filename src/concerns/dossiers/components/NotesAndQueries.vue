<template>
  <div class="notes-queries-container q-pa-md">
    <!-- Loading State -->
    <div v-if="isLoading"
         class="text-center q-pa-lg">
      <q-spinner color="primary"
                 size="3em" />
      <div class="q-mt-md text-grey-7">Loading notes...</div>
    </div>

    <!-- Error State -->
    <div v-else-if="error"
         class="text-center q-pa-lg">
      <q-icon name="error_outline"
              color="negative"
              size="2em" />
      <div class="q-mt-sm text-negative">{{ error }}</div>
    </div>

    <!-- Main Content -->
    <div v-else-if="$route.name !== 'rNotesAndQueries'">
      <router-view :realtyDossier="realtyDossier"
                   :saleListing="saleListing" />
    </div>

    <template v-else>
      <div class="q-mb-lg">
        <h3 class="text-h5 q-mt-md text-center  h2c-underline">
          Househunting Notes & Comments
        </h3>
      </div>

      <div class="controls-section q-mb-xl">
        <div class="row items-center justify-between q-mb-md">
          <q-btn color="primary"
                 icon="add_comment"
                 label="Create New Note"
                 @click="$router.push({ name: 'rCreateNoteOrQuery' })"
                 rounded
                 unelevated />
        </div>
        <!-- Filter Controls -->
        <q-card flat
                bordered
                class="q-pa-md filter-card">
          <div class="text-subtitle2 q-mb-sm text-grey-8">Filter Options</div>
          <div class="row q-col-gutter-md">
            <div class="col-12 col-sm-6">
              <q-select v-model="filterType"
                        :options="filterOptions"
                        label="Filter by Type"
                        outlined
                        dense
                        emit-value
                        map-options />
            </div>
            <div class="col-12 col-sm-6"
                 v-if="filterType === 'comparison' && dossierAssetsComparisons.length > 0">
              <q-select v-model="selectedComparison"
                        :options="comparisonOptions"
                        label="Select Comparison Property"
                        outlined
                        dense
                        emit-value
                        map-options
                        clearable />
            </div>
          </div>
        </q-card>
      </div>

      <!-- Notes List - Conversational Style -->
      <div v-if="filteredNotesAndQueries.length > 0"
           class="notes-list">
        <div v-for="item in filteredNotesAndQueries"
             :key="item.id"
             class="note-item q-mb-lg">
          <q-chat-message :name="item.user ? (item.jotCreatorName || item.user.name || 'System') : 'System'"
                          :text="[item.text]"
                          :stamp="formatRelativeTime(item.createdAt)"
                          :sent="isCurrentUser(item.user)"
                          :bg-color="isCurrentUser(item.user) ? 'primary' : 'grey-2'"
                          :text-color="isCurrentUser(item.user) ? 'white' : 'black'"
                          class="note-chat-message">
            <template v-slot:default>
              <!-- Display text -->
              <div class="q-pb-sm message-text"
                   v-html="formatNoteText(item.text)"></div>

              <!-- Display Image if available -->
              <div v-if="item.image && item.image.image_details && item.image.image_details.url"
                   class="q-mt-sm image-attachment">
                <q-img :src="item.image.image_details.url"
                       class="rounded-borders cursor-pointer"
                       style="min-width: 300px; max-height: 650px;"
                       fit="cover"
                       @click="showImageFullScreen(item.image.image_details.url)">
                  <q-tooltip>Click to enlarge</q-tooltip>
                </q-img>
              </div>

              <!-- Metadata (e.g., related to) -->
              <div class="q-mt-xs text-caption metadata-info"
                   :class="isCurrentUser(item.user) ? 'text-blue-grey-1' : 'text-grey-7'">
                <!-- <span> <q-icon name="push_pin"
                          size="xs"
                          class="q-mr-xs" /> Related to: Northstar Property
                </span> -->
                <span v-if="item.isPrimaryListingJot">
                  <q-icon name="push_pin"
                          size="xs"
                          class="q-mr-xs" /> Related to: Northstar Property
                </span>
                <span v-else-if="item.assetsComparisonUuid">
                  <q-icon name="compare_arrows"
                          size="xs"
                          class="q-mr-xs" /> Related to: {{ getComparisonName(item) }}
                </span>
              </div>
            </template>

            <!-- Actions like Delete -->
            <!-- Positioned outside the text slot for better layout with q-chat-message -->
          </q-chat-message>
          <div class="actions-row row justify-end q-pr-sm q-mt-xs">
            <q-btn flat
                   round
                   dense
                   color="grey-7"
                   icon="edit"
                   size="sm"
                   @click.stop="$router.push({ name: 'rNoteOrQueryDetails', params: { itemId: item.id } })"
                   class="q-mr-xs">
              <q-tooltip>Edit Note</q-tooltip>
            </q-btn>
            <q-btn flat
                   round
                   dense
                   color="negative"
                   icon="delete_outline"
                   size="sm"
                   @click.stop="() => confirmDeleteItem(item.id)">
              <q-tooltip>Delete Note</q-tooltip>
            </q-btn>
          </div>
          <q-separator spaced="md"
                       v-if="filteredNotesAndQueries.length > 1 && item.id !== filteredNotesAndQueries[filteredNotesAndQueries.length - 1].id" />
        </div>
      </div>

      <!-- Empty State -->
      <div v-else
           class="text-center q-pa-xl text-grey-7">
        <q-icon name="speaker_notes_off"
                size="3em"
                class="q-mb-sm" />
        <div>No notes or queries found for the current filter.</div>
        <div>Create a new note or query to get started!</div>
      </div>
    </template>

    <!-- Full Screen Image Dialog -->
    <q-dialog v-model="imageFullScreenVisible"
              full-width
              full-height>
      <q-card class="column items-center justify-center bg-black-transparent">
        <q-img :src="currentFullScreenImage"
               style="max-width: 90vw; max-height: 90vh;"
               fit="contain" />
        <q-btn icon="close"
               flat
               round
               dense
               @click="imageFullScreenVisible = false"
               class="absolute-top-right q-ma-sm"
               color="white"
               style="background-color: rgba(0,0,0,0.3)" />
      </q-card>
    </q-dialog>

  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue'
import { useQuasar } from 'quasar'
import { formatDistanceToNowStrict } from 'date-fns' // For relative time
import { useDossierNotesAndQueries } from 'src/concerns/dossiers/composables/useDossierNotesAndQueries' // Verify path

export default defineComponent({
  name: 'PropertyNotesAndQueries',
  props: {
    saleListing: { type: Object, required: true },
    realtyDossier: { type: Object, required: true },
    // Assuming you have a way to identify the current user, e.g., from a store or auth service
    currentUser: { type: Object, default: () => ({ id: 'current_user_placeholder_id', name: 'Me' }) } // Placeholder
  },
  setup(props) {
    const $q = useQuasar()
    const filterType = ref('all')
    const selectedComparison = ref(null)
    const imageFullScreenVisible = ref(false)
    const currentFullScreenImage = ref('')

    // Assuming `notesAndQueries` from the composable includes user details like `item.user.id`
    // and `item.user.name` or `item.jotCreatorName`, `item.user.avatar_url`
    const {
      notesAndQueries,
      isLoading,
      error,
      deleteNoteOrQuery,
      // formatDate, // We'll use formatRelativeTime or a more detailed local one
    } = useDossierNotesAndQueries(props.realtyDossier, $q)

    const dossierAssetsComparisons = computed(() => {
      return props.realtyDossier?.dossier_assets_comparisons || []
    })

    const filterOptions = computed(() => { /* ... same ... */
      const options = [
        { label: 'All Notes', value: 'all' },
        { label: 'Northstar Property Notes', value: 'primary' },
      ]
      if (dossierAssetsComparisons.value.length > 0) {
        options.push({ label: 'Comparison Notes', value: 'comparison' })
      }
      return options
    })
    const comparisonOptions = computed(() => { /* ... same ... */
      return dossierAssetsComparisons.value.map((comparison) => ({
        label:
          comparison.right_side_property?.street_address ||
          comparison.right_side_property?.title ||
          '...Unnamed Property',
        value: comparison.uuid,
      }))
    })

    // --- Enhanced jotsWithImages to include user info ---
    // You'll need to ensure your `notesAndQueries` (jots) from the backend
    // or your composable can provide user information (e.g., `created_by_user_details`).
    const jotsWithImagesAndUsers = computed(() => {
      // const imgs = props.realtyDossier?.dossier_sale_listing?.sale_listing_pics || []
      return notesAndQueries.value.map((note) => {
        // const img = imgs.find((img) => img.uuid === note.primPhotoUuid)
        return {
          ...note,
          image: note.jotPhoto, // This structure is { uuid, image_details: { url }}
          user: note.jot_creator_name || note.jotCreatorName || { name: '..Unknown User', id: 'unknown' }, // Adjust based on your API
          isPrimaryListingJot: note.isPrimaryListingJot || false,
          assetsComparisonUuid: note.assetsComparisonUuid,
          comparisonId: note.comparisonId || null,
        }
      }).sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)) // Sort newest first for conversational
    })

    const filteredNotesAndQueries = computed(() => {
      let notes = jotsWithImagesAndUsers.value;
      if (filterType.value === 'primary') {
        return notes.filter((item) => item.isPrimaryListingJot)
      }
      if (filterType.value === 'comparison') {
        if (!selectedComparison.value) {
          return notes.filter((item) => item.assetsComparisonUuid)
        }
        return notes.filter(
          (item) => item.assetsComparisonUuid === selectedComparison.value
        )
      }
      return notes // 'all' or default
    })

    const getComparisonName = (jotItem) => { /* ... same ... */
      if (jotItem.isPrimaryListingJot) {
        return "Northstar Property"
      }
      let comparisonUuid = jotItem.assetsComparisonUuid
      const comparison = dossierAssetsComparisons.value.find(
        (c) => c.uuid === comparisonUuid
      )
      return (
        comparison?.right_side_property?.street_address ||
        comparison?.right_side_property?.title ||
        'Unnamed Property'
      )
    }

    const confirmDeleteItem = (itemId) => {
      $q.dialog({
        title: 'Confirm Delete',
        message: 'Are you sure you want to delete this note? This action cannot be undone.',
        cancel: true,
        persistent: true,
        ok: { label: 'Delete', color: 'negative', unelevated: true },
        cancel: { label: 'Cancel', flat: true }
      }).onOk(async () => {
        await deleteNoteOrQuery(itemId)
        $q.notify({ type: 'positive', message: 'Note deleted successfully.' })
      })
    }

    const formatRelativeTime = (dateString) => {
      if (!dateString) return ''
      try {
        return formatDistanceToNowStrict(new Date(dateString), { addSuffix: true })
      } catch (e) {
        return dateString
      }
    }

    // const getInitialsAvatar = (name) => {
    //   if (!name) return '?'
    //   const initials = name
    //     .split(' ')
    //     .map((n) => n[0])
    //     .join('')
    //     .substring(0, 2)
    //     .toUpperCase();
    //   // Create a data URL for a simple avatar with initials
    //   const canvas = document.createElement('canvas');
    //   const size = 64; // Avatar image size
    //   canvas.width = size;
    //   canvas.height = size;
    //   const context = canvas.getContext('2d');
    //   // Simple color hashing for background
    //   let hash = 0;
    //   for (let i = 0; i < name.length; i++) {
    //     hash = name.charCodeAt(i) + ((hash << 5) - hash);
    //   }
    //   const c = (hash & 0x00FFFFFF).toString(16).toUpperCase();
    //   const bgColor = "#" + "00000".substring(0, 6 - c.length) + c;

    //   context.fillStyle = bgColor;
    //   context.fillRect(0, 0, size, size);
    //   context.fillStyle = '#FFF'; // Text color
    //   context.font = `${size / 2.5}px Arial`;
    //   context.textAlign = 'center';
    //   context.textBaseline = 'middle';
    //   context.fillText(initials, size / 2, size / 2);
    //   return canvas.toDataURL();
    // }

    const isCurrentUser = (user) => {
      // Implement your logic to check if the note's user is the currently logged-in user
      // This is important for the `sent` prop of q-chat-message
      return user && user.id === props.currentUser.id;
    }

    const showImageFullScreen = (imageUrl) => {
      currentFullScreenImage.value = imageUrl
      imageFullScreenVisible.value = true
    }

    const formatNoteText = (text) => {
      // Simple link detection (naive)
      if (!text) return '';
      const urlRegex = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
      return text.replace(/\n/g, '<br />').replace(urlRegex, function (url) {
        return `<a href="${url}" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">${url}</a>`;
      });
    }


    return {
      // notesAndQueries, // jotsWithImagesAndUsers is now the primary source
      isLoading,
      error,
      filterType,
      selectedComparison,
      filterOptions,
      comparisonOptions,
      filteredNotesAndQueries,
      dossierAssetsComparisons,
      confirmDeleteItem, // Renamed from handleDeleteItem
      formatRelativeTime,
      getComparisonName,
      // getInitialsAvatar,
      isCurrentUser,
      imageFullScreenVisible,
      currentFullScreenImage,
      showImageFullScreen,
      formatNoteText,
    }
  },
})
</script>

<style scoped>
.notes-queries-container {
  max-width: 900px;
  /* Slightly wider for chat messages */
  margin: 0 auto;
  background-color: #f9f9f9;
  /* Lighter page background */
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}


.filter-card {
  background-color: #fff;
  border-radius: 8px;
}

.notes-list {
  padding: 0 8px;
  /* Add some padding if chat messages go full width */
}

.note-item {
  /* No specific styles needed here now as q-chat-message handles its own block */
}

.note-chat-message {
  width: 100%;
  /* Make chat message take full width for alignment */
  font-size: 0.95rem;
}

.note-chat-message .q-message-text--received .q-message-text-content {
  color: #222 !important;
  /* Darker text for received messages */
}


.note-chat-message :deep(.q-message-name) {
  font-weight: 600;
  margin-bottom: 2px;
  color: #555;
  /* Slightly subdued name color */
}

.note-chat-message :deep(.q-message-stamp) {
  font-size: 0.7rem;
  opacity: 0.8;
}

.image-attachment .q-img {
  border: 1px solid #eee;
  border-radius: 8px;
  /* Softer rounded corners for images */
  transition: transform 0.2s ease-out;
}

.image-attachment .q-img:hover {
  transform: scale(1.03);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.metadata-info {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  opacity: 0.9;
}

.actions-row {
  opacity: 0;
  /* Hidden by default */
  transition: opacity 0.2s ease-in-out;
  height: 28px;
  /* Reserve space to prevent layout jump */
}

.note-item:hover .actions-row {
  opacity: 1;
  /* Show on hover of the note-item container */
}

/* Ensure clickable items in list have pointer */
.q-list .q-item--clickable {
  cursor: pointer;
}

.bg-black-transparent {
  background-color: rgba(0, 0, 0, 0.85) !important;
}

.message-text {
  line-height: 1.5;
  white-space: pre-wrap;
  /* Respect newlines from input */
}

.message-text a {
  word-break: break-all;
  /* Ensure long URLs don't break layout */
}
</style>