<template>
  <div class="dossier-notifications-container q-pa-md bg-grey-1 rounded-borders shadow-2">
    <div class="text-center">
      <h3 class="text-h5 q-my-md text-primary h2c-underline">Property Dossier Notifications</h3>
      <p class="text-subtitle1 text-grey-8 q-mb-lg">
        Price changes and other important events will show up here.
      </p>
    </div>

    <q-list bordered
            separator
            class="rounded-borders bg-white">
      <q-item v-for="(notification, index) in dummyNotifications"
              :key="index"
              clickable
              :class="{ 'bg-grey-2': !notification.read }"
              @click="handleNotificationClick(notification)">
        <q-item-section avatar>
          <q-icon :name="notification.icon"
                  :color="notification.type === 'positive' ? 'positive' : notification.type === 'warning' ? 'warning' : 'info'"
                  size="28px" />
        </q-item-section>

        <q-item-section>
          <q-item-label>{{ notification.message }}</q-item-label>
          <q-item-label caption
                        class="text-grey-7">
            {{ formatDate(notification.timestamp) }}
          </q-item-label>
        </q-item-section>

        <q-item-section side>
          <q-btn flat
                 round
                 icon="more_vert"
                 size="sm"
                 @click.stop="showNotificationMenu(notification, $event)" />
        </q-item-section>
      </q-item>

      <q-item v-if="!dummyNotifications.length"
              class="text-center text-grey-6">
        <q-item-section>
          <q-item-label>No notifications yet</q-item-label>
        </q-item-section>
      </q-item>
    </q-list>

    <!-- <div class="q-mt-md text-center">
      <q-btn color="primary"
             label="Create New Dossier"
             icon="add"
             @click="navigateToDossierCreation"
             class="q-px-lg" />
    </div> -->
  </div>
</template>

<script>
import { defineComponent, ref } from 'vue'
import { useQuasar } from 'quasar'
import { useRouter } from 'vue-router'
import { date } from 'quasar'

export default defineComponent({
  name: 'NotificationsPage',
  props: {
    saleListing: {
      type: Object,
      required: true,
    },
    realtyDossier: {
      type: Object,
      required: true,
    },
  },

  setup(props) {
    const $q = useQuasar()
    const $router = useRouter()

    // Dummy notifications for demonstration
    const dummyNotifications = ref([
      // {
      //   id: 1,
      //   message: 'New document added to property dossier',
      //   type: 'positive',
      //   icon: 'description',
      //   timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      //   read: false,
      //   action: '/dossier/documents',
      // },
      // {
      //   id: 2,
      //   message: 'Price update required for sale listing',
      //   type: 'warning',
      //   icon: 'warning',
      //   timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
      //   read: true,
      //   action: '/listing/update',
      // },
      // {
      //   id: 3,
      //   message: 'Property viewing scheduled for tomorrow',
      //   type: 'info',
      //   icon: 'event',
      //   timestamp: new Date(Date.now() - 48 * 60 * 60 * 1000),
      //   read: true,
      //   action: '/schedule',
      // },
    ])

    const formatDate = (timestamp) => {
      return date.formatDate(timestamp, 'MMM D, YYYY HH:mm')
    }

    const navigateToDossierCreation = () => {
      $q.notify({
        type: 'positive',
        message: 'Redirecting to dossier creation...',
        position: 'top',
        timeout: 2000,
      })
      $router.push('/dossier/create')
    }

    const handleNotificationClick = (notification) => {
      if (notification.action) {
        $q.notify({
          type: notification.type,
          message: `Navigating to ${notification.message}...`,
          position: 'top',
          timeout: 1500,
        })
        $router.push(notification.action)
        notification.read = true
      }
    }

    const showNotificationMenu = (notification, event) => {
      $q.notify({
        type: 'info',
        message: `Showing options for notification ${notification.id}`,
        position: 'top',
        timeout: 1500,
      })
      // Implement context menu logic here
    }

    return {
      dummyNotifications,
      formatDate,
      navigateToDossierCreation,
      handleNotificationClick,
      showNotificationMenu,
    }
  },
})
</script>

<style scoped>
.dossier-notifications-container {
  max-width: 800px;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.h2c-underline {
  position: relative;
  display: inline-block;
}

.h2c-underline::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: currentColor;
  border-radius: 2px;
}

.q-item {
  transition: background-color 0.3s ease;
}

.q-item:hover {
  background-color: #f5f5f5;
}

.q-btn {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.q-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}
</style>