<template>
  <div class="asset-part-section-inner q-py-none"
       :id="assetPart.asset_part_slug">
    <q-card flat
            class="q-mb-none">
      <!-- Header -->
      <!-- <q-card-section class="">
        <div class="text-h6">{{ assetPart.asset_part_title }}</div>
      </q-card-section> -->

      <!-- Main Details -->
      <q-card-section class="q-py-none">
        <div class="row q-col-gutter-md">
          <!-- Left Column: Key Details -->
          <div class="col-12 col-md-6 q-pt-none">
            <!-- <div class="row q-col-gutter-sm">
              <div class="col-6">
                <div class="text-subtitle2">
                  <q-icon name="category"
                          class="q-mr-sm" /> Type: {{ assetPart.asset_part_type }}
                </div>
              </div>
              <div class="col-6">
                <div class="text-subtitle2">
                  <q-icon name="square_foot"
                          class="q-mr-sm" /> Size:
                  {{ assetPart.dap_area_sq_meters || assetPart.dap_area_sq_feet ||
                    assetPart.asset_part_details.asset_part_size || 'N/A' }}
                  {{ assetPart.dap_area_sq_meters ? 'm²' : assetPart.dap_area_sq_feet ? 'sq ft' : '' }}
                </div>
              </div>
              <div class="col-6">
                <div class="text-subtitle2">
                  <q-icon name="palette"
                          class="q-mr-sm" /> Main Color: {{ assetPart.asset_part_main_color || 'N/A' }}
                </div>
              </div>
              <div class="col-6">
                <div class="text-subtitle2">
                  <q-icon name="build"
                          class="q-mr-sm" /> Condition: {{ assetPart.asset_part_condition || 'N/A' }}
                </div>
              </div>
              <div class="col-6">
                <div class="text-subtitle2">
                  <q-icon name="style"
                          class="q-mr-sm" /> Style: {{ assetPart.asset_part_style || 'N/A' }}
                </div>
              </div>
            </div> -->
            <div v-if="assetPart.asset_part_description"
                 class="text-subtitle2 q-mt-sm">
              <q-icon name="description"
                      class="q-mr-sm" />
              {{ assetPart.asset_part_description || 'No description available' }}
            </div>
          </div>

          <!-- Right Column: Features and Items -->
          <div class="col-12 col-md-6">
            <q-expansion-item v-if="assetPart?.asset_part_unique_features?.length > 0 || assetPart.asset_part_significant_items.length > 0"
                              icon="star"
                              label="Features & Items"
                              header-class="once-bggrey2"
                              expand-icon="expand_more"
                              default-opened>
              <q-tabs v-model="tab"
                      dense
                      class="bg-grey-1">
                <q-tab v-if="assetPart?.asset_part_unique_features?.length > 0"
                       name="features"
                       label="Unique Features" />
                <q-tab v-if="assetPart.asset_part_significant_items.length > 0"
                       name="items"
                       label="Significant Items" />
              </q-tabs>

              <q-tab-panels v-model="tab"
                            animated>
                <q-tab-panel v-if="assetPart?.asset_part_unique_features?.length > 0"
                             name="features">
                  <q-list dense>
                    <q-item v-for="(feature, index) in assetPart.asset_part_unique_features"
                            :key="index">
                      <q-item-section>
                        <q-item-label>{{ feature }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-tab-panel>
                <q-tab-panel v-if="assetPart.asset_part_significant_items.length > 0"
                             name="items">
                  <q-list dense>
                    <q-item v-for="(item, index) in assetPart.asset_part_significant_items"
                            :key="index">
                      <q-item-section>
                        <q-item-label>{{ item }}</q-item-label>
                      </q-item-section>
                    </q-item>
                  </q-list>
                </q-tab-panel>
              </q-tab-panels>
            </q-expansion-item>
          </div>
        </div>
      </q-card-section>

      <!-- Photos Section -->
      <q-card-section v-if="assetPart.asset_part_photos_json && assetPart.asset_part_photos_json.length > 0">
        <!-- <div class="text-subtitle2 q-mb-sm">
          <q-icon name="photo"
                  class="q-mr-sm" /> Photos ({{ assetPart.asset_part_photos_json.length }})
        </div> -->
        <div class="row q-col-gutter-sm photo-grid">
          <div v-for="(photo) in assetPart.asset_part_photos_json"
               :key="photo.uuid"
               class="col-12 col-md-6 photo-container">
            <q-img v-if="photo.image_details && photo.image_details.url"
                   :src="photo.image_details.url"
                   :alt="photo.photo_title"
                   fit="cover"
                   class="rounded-borders photo-image">
              <!-- <div class="absolute-bottom text-overlay">
                <div class="text-subtitle2 text-white">{{ photo.photo_title }}</div>
                <div class="text-caption text-white q-mb-xs">{{ photo.photo_description }}</div>
                <div v-if="photo.realty_asset_photo_tags.length > 0"
                     class="q-mb-xs">
                  <q-chip v-for="(tag, tagIndex) in photo.realty_asset_photo_tags"
                          :key="tagIndex"
                          dense
                          color="primary"
                          text-color="white"
                          class="q-ma-xs">
                    {{ tag }}
                  </q-chip>
                </div>
                <div v-if="photo.raw_ai_analysis && photo.raw_ai_analysis.general_photo_analysis"
                     class="text-caption text-white">
                </div>
              </div> -->
            </q-img>
          </div>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script>
export default {
  props: {
    assetPart: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      tab: 'features', // Default tab for features/items
    };
  },
};
</script>

<style scoped>
/* .asset-part-section {
  padding: 16px 0;
} */

.q-card {
  transition: box-shadow 0.3s;
}

.q-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
}

.photo-container {
  position: relative;
  /* height: 200px; */
  /* Fixed height for compactness */
}

.photo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.photo-image:hover {
  transform: scale(1.05);
}

.text-overlay {
  background: rgba(0, 0, 0, 0.6);
  /* Semi-transparent black background */
  padding: 8px;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  /* Improve readability */
  max-height: 100%;
  overflow-y: auto;
  /* Scroll if content overflows */
}
</style>