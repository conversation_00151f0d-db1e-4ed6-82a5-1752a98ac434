<template>
  <!--
  May 2025 - no longer in use
   The idea of this was to be able to toggle between focused and comparison mode
  - decideded it wasn't working well
   -->
  <div v-if="listingInRow"
       class="row">
    <!-- <div class="col-sm-12">
      <SingleListingInRow @toggle-view="showSideBySide = !showSideBySide"
                          :comparableUuid="comparableUuid"
                          :listingInRow="listingInRow"></SingleListingInRow>
    </div> -->
  </div>
</template>

<script>
import SingleListingInRow from "src/concerns/dossiers/components/row/SingleListingInRow.vue"
// import SideBySideListingsInRow from "src/concerns/dossiers/components/row/SideBySideListingsInRow.vue"
export default {
  // components: {
  //   SingleListingInRow,
  //   // SideBySideListingsInRow
  // },
  // name: 'SingleComparableRow',
  // props: {

  //   comparableInRow: {
  //     type: Object,
  //     required: false
  //   }
  // },
  // computed: {
  //   listingInRow() {
  //     return this.comparableInRow?.right_side_property
  //   },
  //   comparableUuid() {
  //     return this.comparableInRow?.uuid
  //   }
  // },
}
</script>
<style scoped></style>