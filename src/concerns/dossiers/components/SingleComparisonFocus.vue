// May 2025 - no longer in use
<template>
  <div class="q-py-md SingleComparisonFocus"
       v-if="comparison && comparison.full_comparisons">
    <div class="row items-center justify-center q-mb-md">
      <h3 class="text-h5 q-my-none">Rooms / Sections</h3>
      <!-- <q-btn flat
             round
             icon="expand_more"
             :class="{ 'rotate-180': allExpanded }"
             @click="toggleAll"
             class="q-ml-md overall-toggle-btn">
        <q-tooltip>{{ allExpanded ? 'Collapse All' : 'Expand All' }}</q-tooltip>
      </q-btn> -->
      <q-toggle v-model="allExpanded"
                color="primary"
                @update:model-value="toggleAll"
                :label="allExpanded ? 'Collapse All' : 'Expand All'"
                class="q-ml-md">
      </q-toggle>
    </div>

    <div v-for="(comp, key) in comparison.full_comparisons"
         :key="key"
         class="q-mb-xl">
      <AssetPartComparison :comparison-data="comp"
                           :title-key="key"
                           :initial-expanded="isExpanded[key]"
                           @toggle="updateExpanded(key, $event)" />
    </div>
  </div>
</template>

<script>
import { date } from 'quasar'
import AssetPartComparison from "src/concerns/dossiers/components/comparisons/AssetPartComparison.vue"
// import AssetPartComparison from './AssetPartComparison.vue' // Adjust path as needed

export default {
  name: 'SingleComparisonFocus',
  components: {
    AssetPartComparison
  },

  props: {
    comparisonData: {
      type: Object,
      required: true
    }
  },

  data() {
    return {
      comparison: this.comparisonData.comparison,
      isExpanded: {}
    }
  },

  computed: {
    allExpanded() {
      return Object.values(this.isExpanded).every(expanded => expanded);
    }
  },

  created() {
    // Initialize expansion states
    // Object.keys(this.comparison.full_comparisons).forEach(key => {
    //   this.isExpanded[key] = true;  // Default to expanded view
    // })
  },

  methods: {
    formatDate(dateStr) {
      return date.formatDate(new Date(dateStr), 'MMMM D, YYYY [at] h:mm A')
    },

    toggleAll() {
      const newState = !this.allExpanded;
      Object.keys(this.isExpanded).forEach(key => {
        this.isExpanded[key] = newState;
      });
    },

    updateExpanded(key, isExpanded) {
      this.isExpanded[key] = isExpanded;
    }
  }
}
</script>

<style scoped>
.overall-toggle-btn {
  transition: transform 0.3s ease;
}

.rotate-180 {
  transform: rotate(180deg);
}
</style>