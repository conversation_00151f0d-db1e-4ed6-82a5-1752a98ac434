<template>
  <q-page class="q-pa-md">
    <q-card class="comparison-card">
      <q-card-section>
        <div class="text-h4 text-center text-primary q-mb-md">{{ comparisonTitle }}</div>
        <div class="text-subtitle1 text-center text-grey-8 q-mb-lg">{{ comparisonDescription }}</div>
      </q-card-section>

      <!-- Property Overview -->
      <q-card-section>
        <q-tabs v-model="activeTab"
                dense
                class="text-primary"
                active-color="primary"
                indicator-color="primary"
                align="justify">
          <q-tab name="first"
                 label="3-Bedroom Property" />
          <q-tab name="second"
                 label="4-Bedroom Property" />
        </q-tabs>

        <q-tab-panels v-model="activeTab"
                      animated>
          <q-tab-panel name="first">
            <q-list>
              <q-item>
                <q-item-section>
                  <q-item-label class="text-h6">Strengths</q-item-label>
                  <q-item-label caption
                                v-for="strength in firstProperty.strengths"
                                :key="strength">{{ strength }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label class="text-h6">Weaknesses</q-item-label>
                  <q-item-label caption
                                v-for="weakness in firstProperty.weaknesses"
                                :key="weakness">{{ weakness }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label class="text-h6">Lifestyle Fit</q-item-label>
                  <q-item-label caption>{{ firstProperty.lifestyle_fit }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label class="text-h6">Unique Selling Points</q-item-label>
                  <q-item-label caption
                                v-for="usp in firstProperty.unique_selling_points"
                                :key="usp">{{ usp }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label class="text-h6">Estimated Renovation Costs</q-item-label>
                  <q-item-label caption>{{ firstProperty.estimated_renovation_costs }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-tab-panel>
          <q-tab-panel name="second">
            <q-list>
              <q-item>
                <q-item-section>
                  <q-item-label class="text-h6">Strengths</q-item-label>
                  <q-item-label caption
                                v-for="strength in secondProperty.strengths"
                                :key="strength">{{ strength }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label class="text-h6">Weaknesses</q-item-label>
                  <q-item-label caption
                                v-for="weakness in secondProperty.weaknesses"
                                :key="weakness">{{ weakness }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label class="text-h6">Lifestyle Fit</q-item-label>
                  <q-item-label caption>{{ secondProperty.lifestyle_fit }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label class="text-h6">Unique Selling Points</q-item-label>
                  <q-item-label caption
                                v-for="usp in secondProperty.unique_selling_points"
                                :key="usp">{{ usp }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item>
                <q-item-section>
                  <q-item-label class="text-h6">Estimated Renovation Costs</q-item-label>
                  <q-item-label caption>{{ secondProperty.estimated_renovation_costs }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-tab-panel>
        </q-tab-panels>
      </q-card-section>

      <!-- Comparison Details -->
      <q-card-section>
        <div class="text-h5 q-mb-md">Key Differentiators</div>
        <q-list dense>
          <q-item v-for="diff in keyDifferentiators"
                  :key="diff">
            <q-item-section>
              <q-item-label>{{ diff }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
        <div class="text-h5 q-my-md">Relative Strengths (4-Bedroom Property)</div>
        <q-list dense>
          <q-item v-for="strength in relativeStrengths"
                  :key="strength">
            <q-item-section>
              <q-item-label>{{ strength }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
        <div class="text-h5 q-my-md">Relative Weaknesses (4-Bedroom Property)</div>
        <q-list dense>
          <q-item v-for="weakness in relativeWeaknesses"
                  :key="weakness">
            <q-item-section>
              <q-item-label>{{ weakness }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>

      <!-- Investment Potential -->
      <q-card-section>
        <div class="text-h5 q-mb-md">Investment Potential</div>
        <q-splitter v-model="splitterModel"
                    style="height: 200px">
          <template v-slot:before>
            <div class="q-pa-md">
              <div class="text-h6">3-Bedroom Property</div>
              <p>{{ firstPropertyInvestment }}</p>
            </div>
          </template>
          <template v-slot:after>
            <div class="q-pa-md">
              <div class="text-h6">4-Bedroom Property</div>
              <p>{{ secondPropertyInvestment }}</p>
            </div>
          </template>
        </q-splitter>
      </q-card-section>

      <!-- Questions for Seller -->
      <q-card-section>
        <div class="text-h5 q-mb-md">Questions for Seller</div>
        <q-splitter v-model="splitterModel"
                    style="height: 300px">
          <template v-slot:before>
            <div class="q-pa-md">
              <div class="text-h6">3-Bedroom Property</div>
              <q-list dense>
                <q-item v-for="question in firstPropertyQuestions"
                        :key="question">
                  <q-item-section>
                    <q-item-label>{{ question }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </template>
          <template v-slot:after>
            <div class="q-pa-md">
              <div class="text-h6">4-Bedroom Property</div>
              <q-list dense>
                <q-item v-for="question in secondPropertyQuestions"
                        :key="question">
                  <q-item-section>
                    <q-item-label>{{ question }}</q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </template>
        </q-splitter>
      </q-card-section>

      <!-- Buyer Guidance -->
      <q-card-section>
        <div class="text-h5 q-mb-md">Buyer Guidance</div>
        <div class="text-h6">Recommendation</div>
        <p>{{ buyerRecommendation }}</p>
        <div class="text-h6 q-mt-md">Considerations</div>
        <q-list dense>
          <q-item v-for="consideration in buyerConsiderations"
                  :key="consideration">
            <q-item-section>
              <q-item-label>{{ consideration }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script>
export default {
  name: 'PropertyComparison',
  data() {
    return {
      activeTab: 'first',
      splitterModel: 50,
      comparisonTitle: 'Comparing Two Detached Family Homes',
      comparisonDescription: 'This comparison analyzes two detached properties: a 3-bedroom (main property) and a 4-bedroom (second property). The main property presents as a traditional family home requiring some cosmetic updates, featuring distinct living spaces and a good-sized rear garden. The second property offers more overall space with an additional bedroom, a dedicated office, and a conservatory, alongside a larger kitchen/utility area and a substantial garden. While the main property has two bathrooms (including a cloakroom), the second property only lists one main bathroom but compensates with extra reception and utility space. Both properties appear to be in established residential areas, offering different advantages depending on buyer priorities regarding space, layout, and readiness for immediate occupation.',
      keyDifferentiators: [
        'Number of bedrooms (3 vs 4)',
        'Presence of a conservatory and dedicated office in the second property',
        'Kitchen layout and modernity (two separate vs one large kitchen/utility)',
        'Number and style of bathrooms (2 vs 1 main + specific features)',
        'Overall living space configuration and flow'
      ],
      relativeStrengths: [
        'Additional fourth bedroom',
        'Dedicated home office space',
        'Added conservatory providing extra living/dining area',
        'More spacious and modern kitchen/utility area with island',
        'Larger main bathroom with bidet and glass block shower enclosure',
        'Potentially larger overall footprint',
        'Larger garden area with potential for greenhouse'
      ],
      relativeWeaknesses: [
        'Only one listed main bathroom for a 4-bedroom house (vs. main property\'s two bathrooms)',
        'Sloped ceilings in some bedrooms may limit furniture placement',
        'Tiled flooring in living areas may not suit all tastes',
        'Lack of a separate reception room similar to the main property',
        'Terracotta-style kitchen floor tiles may require updating',
        'Specific style choices (glass block shower) may not have universal appeal',
        'No clear hallway part listed in the descriptions'
      ],
      firstProperty: {
        strengths: [
          'Detached house with garage and driveway.',
          'Separate reception room with garden access.',
          'Traditional features like bay window and fireplace.',
          'Two bathrooms (main + downstairs cloakroom).',
          'Good-sized private rear garden with patio and shed.',
          'EPC information available, showing potential for improvement.'
        ],
        weaknesses: [
          'Kitchens and bathrooms appear dated and require updating.',
          'Decor (e.g., red accent wall, yellow kitchen) is specific.',
          'Carpet in Bedroom 3 shows wear.',
          'Separate kitchen layout might not suit all buyers.',
          'Concrete patio is basic.',
          'EPC rating \'D\' indicates average energy efficiency.'
        ],
        lifestyle_fit: 'Ideal for a family looking for a traditional detached home with a garden, willing to undertake renovations to personalize and update the property.',
        unique_selling_points: [
          'Classic bay window and fireplace in living room.',
          'Convenient downstairs cloakroom.',
          'Direct garden access from reception room.',
          'Integrated garage and off-street parking.'
        ],
        estimated_renovation_costs: 'Moderate to Significant (primarily kitchens and bathrooms)'
      },
      secondProperty: {
        strengths: [
          'Four bedrooms providing more accommodation.',
          'Dedicated home office space.',
          'Conservatory offering additional living/dining area.',
          'Spacious, potentially more modern kitchen/utility with island.',
          'Larger main bathroom with additional features (bidet, heated rail, separate shower).',
          'Large rear garden with paved patio and greenhouse potential.',
          'Detached house with garage and driveway.'
        ],
        weaknesses: [
          'Only one main bathroom listed for a 4-bedroom house.',
          'Specific design choices (tiled living room floor, terracotta kitchen tiles, glass block shower) may not appeal to everyone.',
          'Sloped ceilings in some bedrooms may limit usability.',
          'Lack of a separate downstairs WC/cloakroom.',
          'No traditional shed listed for garden storage.',
          'EPC information is not provided.'
        ],
        lifestyle_fit: 'Suitable for a larger family or buyers needing dedicated workspaces, who appreciate extra living areas and a spacious garden. Appeals to those seeking a home with more modern updates in key areas.',
        unique_selling_points: [
          'Increased space with 4 bedrooms, office, and conservatory.',
          'Spacious kitchen/utility area with island.',
          'Larger, well-equipped main bathroom.',
          'Large rear garden with greenhouse potential.'
        ],
        estimated_renovation_costs: 'Minor to Moderate (cosmetic updates, potential bathroom addition)'
      },
      firstPropertyInvestment: 'Good potential for value appreciation through renovation, especially updating kitchens and bathrooms. Solid rental potential as a family home in a likely suburban area. EPC rating \'D\' offers scope for improvement to \'B\', which could increase energy efficiency and potentially value.',
      secondPropertyInvestment: 'Higher entry cost but potentially higher end value due to size and features (4 beds, office, conservatory). Investment return depends on market demand for larger homes in the area and buyer acceptance of existing decor/fittings. Less scope for major value-add through renovation if key areas like kitchen/bathroom are already considered modern.',
      firstPropertyQuestions: [
        'When were the kitchens and bathrooms last updated?',
        'What is the age and service history of the boiler?',
        'Have there been any issues with the roof or drainage?',
        'Are the windows original or have they been replaced?',
        'What is the council tax band?',
        'Are there any known issues with the fireplace?',
        'Has the property undergone any significant structural changes?'
      ],
      secondPropertyQuestions: [
        'What is the age and service history of the boiler?',
        'When were the kitchen and bathroom last renovated?',
        'Are there permits for the conservatory?',
        'Have there been any issues with the conservatory roof or leaks?',
        'What is the council tax band?',
        'Are there any known issues with the plumbing or electrics?',
        'Is the greenhouse included in the sale?'
      ],
      buyerRecommendation: 'The choice between these two properties depends heavily on the buyer\'s specific needs, budget, and appetite for renovation. The second property offers significantly more space and dedicated areas like an office and conservatory, appealing to larger families or those prioritizing space and functionality. However, the main property provides a traditional layout with two bathrooms and substantial renovation potential. Buyers prioritizing size and modern amenities (kitchen, main bath) may lean towards the second property, while those seeking a project, a more traditional layout, or a downstairs WC may prefer the main property.',
      buyerConsiderations: [
        'Required number of bedrooms (3 vs 4).',
        'Need for a dedicated home office or additional reception spaces.',
        'Willingness and budget for renovation (extensive in first property, cosmetic/specific in second).',
        'Preference for modern vs. traditional style.',
        'Importance of multiple bathrooms (two in first property vs. one main in second).',
        'Preference for garden size and features (shed vs. greenhouse potential).'
      ]
    }
  }
}
</script>

<style scoped>
.comparison-card {
  max-width: 1200px;
  margin: 0 auto;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style>