<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">Property Room Comparisons</div>

    <q-card v-for="(room, index) in roomComparisons"
            :key="index"
            class="q-mb-md">
      <q-card-section>
        <div class="text-h6">{{ room.title }}</div>
      </q-card-section>

      <q-card-section>
        <div class="text-subtitle2">Comparison</div>
        <p>{{ room.comparison }}</p>
      </q-card-section>

      <q-card-section>
        <q-tabs v-model="activeTab[index]"
                dense
                class="text-teal"
                active-color="primary"
                indicator-color="primary"
                align="justify">
          <q-tab name="first"
                 :label="`Main Property (${room.first_property_part_slug})`" />
          <q-tab name="second"
                 :label="`Second Property (${room.second_property_part_slug || 'N/A'})`" />
        </q-tabs>

        <q-tab-panels v-model="activeTab[index]"
                      animated>
          <q-tab-panel name="first">
            <div class="text-subtitle2">Maintenance Concerns</div>
            <q-list>
              <q-item v-for="(concern, i) in room.maintenance_concerns.first_property"
                      :key="i">
                <q-item-section>
                  <q-item-label>{{ concern }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>

            <div class="text-subtitle2 q-mt-md">Improvement Suggestions</div>
            <q-list>
              <q-item v-for="(suggestion, i) in room.improvement_suggestions.first_property"
                      :key="i">
                <q-item-section>
                  <q-item-label>{{ suggestion }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-tab-panel>

          <q-tab-panel name="second">
            <div class="text-subtitle2">Maintenance Concerns</div>
            <q-list>
              <q-item v-for="(concern, i) in room.maintenance_concerns.second_property"
                      :key="i">
                <q-item-section>
                  <q-item-label>{{ concern }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>

            <div class="text-subtitle2 q-mt-md">Improvement Suggestions</div>
            <q-list>
              <q-item v-for="(suggestion, i) in room.improvement_suggestions.second_property"
                      :key="i">
                <q-item-section>
                  <q-item-label>{{ suggestion }}</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-tab-panel>
        </q-tab-panels>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat
               color="primary"
               :label="expanded[index] ? 'Collapse' : 'Expand Details'"
               @click="toggleExpand(index)" />
      </q-card-actions>

      <q-slide-transition>
        <div v-show="expanded[index]">
          <q-card-section class="once-bggrey2">
            <div class="row">
              <div class="col-6">
                <strong>First Property Part ID:</strong> {{ room.first_property_part_id }}
              </div>
              <div class="col-6">
                <strong>Second Property Part ID:</strong> {{ room.second_property_part_id || 'N/A' }}
              </div>
            </div>
          </q-card-section>
        </div>
      </q-slide-transition>
    </q-card>
  </q-page>
</template>

<script>
export default {
  name: 'RoomComparison',
  data() {
    return {
      activeTab: [],
      expanded: [],
      roomComparisons: [
        {
          "title": "Living Room Comforts Compared",
          "comparison": "The main property's living room is a spacious area featuring a classic bay window and a traditional fireplace, offering a potentially cozy yet bright space. Its red accent wall and carpet give it a distinct, albeit potentially dated, aesthetic. The second property's living room has a different feel with tiled flooring and a stone fireplace, alongside a unique arched window. It appears furnished and lived-in, suggesting a comfortable, perhaps slightly more modern, ambiance despite the tile. Both offer a primary reception space, but the second property's tiled floor and open feel contrast with the main property's carpeted, more enclosed style.",
          "maintenance_concerns": {
            "first_property": [
              "Condition of the fireplace flue.",
              "Potential wear on the carpet.",
              "Drafts from the bay window."
            ],
            "second_property": [
              "Condition of grout on tiled floor.",
              "Maintenance of the arched window seal.",
              "Potential for cracks in stone fireplace."
            ]
          },
          "first_property_part_id": 57,
          "improvement_suggestions": {
            "first_property": [
              "Repaint walls in a neutral color to broaden appeal.",
              "Replace carpet for a fresh look.",
              "Consider updating the fireplace surround."
            ],
            "second_property": [
              "Install underfloor heating if tiled floor feels cold.",
              "Soften the space with rugs to counteract the tiled floor.",
              "Update lighting fixtures for modern aesthetics."
            ]
          },
          "second_property_part_id": 77,
          "first_property_part_slug": "living-room",
          "second_property_part_slug": "living_room"
        },
        {
          "title": "Extra Living Space: Reception vs. Conservatory",
          "comparison": "The main property offers a separate reception room with wooden laminate flooring and patio doors leading to the garden. This provides a flexible second living area or formal dining space. The second property features a conservatory, a sun-filled extension with a glass roof and walls, also with garden access. This offers a bright, airy space often used for dining or relaxing. While the main property's reception is an internal room, the second property's conservatory provides a strong connection to the outdoors and is likely warmer in summer but potentially colder in winter, representing a different type of versatile space.",
          "maintenance_concerns": {
            "first_property": [
              "Condition of the patio doors.",
              "Wear on laminate flooring."
            ],
            "second_property": [
              "Leaks or drafts from glass panels/roof.",
              "Temperature regulation challenges.",
              "Condition of the conservatory frame."
            ]
          },
          "first_property_part_id": 58,
          "improvement_suggestions": {
            "first_property": [
              "Update laminate flooring or replace.",
              "Add modern blinds/curtains to patio doors.",
              "Consider opening up to adjacent rooms (subject to survey)."
            ],
            "second_property": [
              "Install high-performance glazing or roof panels for better insulation.",
              "Add thermal blinds to regulate temperature.",
              "Create a dedicated zone (e.g., dining or seating area)."
            ]
          },
          "second_property_part_id": 86,
          "first_property_part_slug": "reception-room",
          "second_property_part_slug": "conservatory"
        },
        {
          "title": "Kitchen Comparison: Two vs One Spacious",
          "comparison": "The main property has two distinct kitchen areas (Kitchen 1 and Kitchen 2), suggesting a less open-plan layout or perhaps an older extension. Kitchen 1 features classic wooden cabinets, dark countertops, and tiled backsplash/floor, appearing functional but dated. Kitchen 2 is smaller, with bright yellow walls and a visible boiler, likely a utility or secondary prep space. The second property combines kitchen and utility into one spacious area with terracotta-style floor tiles, light wood cabinets, a dark countertop, and a central island with a hob. It includes integrated appliances like a dishwasher and washing machine, offering a more modern, cohesive, and potentially larger workspace than the main property's separate areas.",
          "maintenance_concerns": {
            "first_property": [
              "Age and condition of kitchen appliances.",
              "Potential leaks around sink/boiler.",
              "Wear and tear on older cabinets/countertops."
            ],
            "second_property": [
              "Condition of grout on floor tiles.",
              "Age and efficiency of integrated appliances.",
              "Maintenance of the island hob/extractor."
            ]
          },
          "first_property_part_id": 59,
          "improvement_suggestions": {
            "first_property": [
              "Renovate Kitchen 1 with modern units and layout.",
              "Integrate or update Kitchen 2 space.",
              "Replace tired flooring and backsplash."
            ],
            "second_property": [
              "Consider updating terracotta floor tiles.",
              "Modernize cabinet hardware.",
              "Improve lighting over the island/workspaces."
            ]
          },
          "second_property_part_id": 76,
          "first_property_part_slug": "kitchen-1",
          "second_property_part_slug": "kitchen_utility"
        },
        {
          "title": "Secondary Kitchen Space vs. Integrated Utility",
          "comparison": "The main property's Kitchen 2 appears to be a smaller, possibly utility-focused area with bright yellow walls and a visible boiler. Its basic finish suggests it serves a functional rather than aesthetic purpose. The second property integrates its utility functions (dishwasher, washing machine, dryer) within its main spacious kitchen area, creating a more streamlined and convenient setup. This means the second property doesn't have a separate 'second kitchen' but a well-equipped, combined space. The main property's setup might allow for separation of noisy appliances, but the second property offers a more modern open-plan lifestyle.",
          "maintenance_concerns": {
            "first_property": [
              "Age and condition of the boiler.",
              "Potential for damp or ventilation issues."
            ],
            "second_property": [
              "Maintenance of integrated appliances.",
              "Drainage and plumbing connections."
            ]
          },
          "first_property_part_id": 60,
          "improvement_suggestions": {
            "first_property": [
              "Refurbish Kitchen 2 to be a dedicated utility room.",
              "Box in the visible boiler.",
              "Improve ventilation."
            ],
            "second_property": [
              "Consider zoning the utility area within the kitchen.",
              "Add specific utility-focused storage.",
              "Ensure adequate ventilation for appliances."
            ]
          },
          "second_property_part_id": 76,
          "first_property_part_slug": "kitchen-2",
          "second_property_part_slug": "kitchen_utility"
        },
        {
          "title": "Downstairs Cloakroom vs. Main Bathroom",
          "comparison": "The main property benefits from a small downstairs cloakroom with a toilet and sink, which is a practical addition for families and guests. It appears functional but basic. The second property lists only one bathroom part, which is a main upstairs bathroom featuring a bathtub, shower enclosure, bidet, and heated towel rack. This bathroom appears more modern and better equipped than either of the main property's bathrooms. While the second property's main bathroom is superior, the main property's inclusion of a separate downstairs cloakroom offers a valuable convenience not present in the listed parts of the second property.",
          "maintenance_concerns": {
            "first_property": [
              "Age of toilet and sink.",
              "Potential for leaks in a small space."
            ],
            "second_property": [
              "Condition of shower enclosure seals.",
              "Maintenance of bidet plumbing.",
              "Grout condition on tiled surfaces."
            ]
          },
          "first_property_part_id": 61,
          "improvement_suggestions": {
            "first_property": [
              "Update fixtures and fittings for a modern look.",
              "Install more efficient lighting.",
              "Add storage solutions."
            ],
            "second_property": [
              "Update the glass block shower wall if not desired.",
              "Consider adding a second bathroom (subject to space/feasibility).",
              "Enhance ventilation."
            ]
          },
          "second_property_part_id": 83,
          "first_property_part_slug": "bathroom-1-cloakroom",
          "second_property_part_slug": "bathroom_1"
        },
        {
          "title": "Main Bathroom Facilities Compared",
          "comparison": "The main property's main bathroom is located upstairs and features a bathtub with a shower screen, toilet, and sink. It has white tiled walls with a blue decorative border, suggesting a dated style that requires updating. The second property's main bathroom is also upstairs (implied) and presents a more modern suite including a bath, a dedicated shower enclosure with glass block walls, a bidet, and a heated towel rack. It offers a higher standard of fittings and appears more recently updated than the main property's main bathroom, providing superior facilities, although the style might be specific.",
          "maintenance_concerns": {
            "first_property": [
              "Condition of existing tiles and grout.",
              "Age of plumbing fixtures.",
              "Potential for leaks around the bath/shower."
            ],
            "second_property": [
              "Condition of shower enclosure seals.",
              "Functionality of the heated towel rack.",
              "Grout condition on tiled surfaces."
            ]
          },
          "first_property_part_id": 67,
          "improvement_suggestions": {
            "first_property": [
              "Complete renovation with new suite and tiles.",
              "Install a more powerful shower.",
              "Improve ventilation."
            ],
            "second_property": [
              "Consider replacing the glass block shower wall.",
              "Update vanity unit for increased storage.",
              "Change tile border if not to taste."
            ]
          },
          "second_property_part_id": 83,
          "first_property_part_slug": "bathroom-2-main",
          "second_property_part_slug": "bathroom_1"
        },
        {
          "title": "Entrance Hallway",
          "comparison": "The main property includes a described hallway area with wooden laminate flooring and the staircase visible. This serves as the formal entrance and circulation space. The second property does not list a dedicated 'hallway' asset part, though photo descriptions mention a staircase area open to the dining area and a landing. This suggests the second property might have a less defined or open-plan entrance/staircase area compared to the main property's traditional hallway.",
          "maintenance_concerns": {
            "first_property": [
              "Wear and tear on laminate flooring.",
              "Condition of staircase structure."
            ],
            "second_property": [
              "Condition of staircase structure and finish.",
              "Wear on flooring in high-traffic areas."
            ]
          },
          "first_property_part_id": 62,
          "improvement_suggestions": {
            "first_property": [
              "Replace laminate flooring with a more durable/appealing option.",
              "Update staircase banister/balusters.",
              "Improve entryway lighting."
            ],
            "second_property": [
              "Define the entrance area if open-plan.",
              "Add entryway storage solutions.",
              "Enhance lighting in the staircase area."
            ]
          },
          "second_property_part_id": null,
          "first_property_part_slug": "hallway",
          "second_property_part_slug": null
        },
        {
          "title": "Upstairs Landing Areas",
          "comparison": "Both properties feature a landing area at the top of the staircase. The main property's landing has carpeted flooring and a window, appearing functional and neutral. The second property's 'Landing Staircase' description covers both the staircase structure and upper landing areas, mentioning carpeted floors on the landing and a view overlooking a lower space, suggesting a potentially more open or interesting landing design. Both serve as access points to upstairs rooms, but the second property's description hints at a more visually integrated staircase/landing feature.",
          "maintenance_concerns": {
            "first_property": [
              "Wear and tear on carpet.",
              "Condition of banister/balusters."
            ],
            "second_property": [
              "Wear and tear on carpet.",
              "Condition of banister/balusters.",
              "Safety railing for the open overlooking area."
            ]
          },
          "first_property_part_id": 63,
          "improvement_suggestions": {
            "first_property": [
              "Update carpet and wall colors.",
              "Add a feature light fixture.",
              "Utilize landing space for a small reading nook or storage."
            ],
            "second_property": [
              "Update carpet and wall colors.",
              "Enhance lighting in the landing area.",
              "Style the open overlooking area."
            ]
          },
          "second_property_part_id": 85,
          "first_property_part_slug": "landing",
          "second_property_part_slug": "landing_staircase"
        },
        {
          "title": "Master Bedroom Comparison",
          "comparison": "The main property's Bedroom 1 is described as having neutral carpet, a large window, and radiator, with freestanding wardrobes visible in photos. It appears spacious and ready for personalization. The second property's Bedroom 1 is furnished with a bed, chest of drawers, and wardrobe, featuring floral bedding and a sloped ceiling. While furnished, the sloped ceiling is a key difference that might impact usable space and furniture arrangement compared to the main property's standard room shape. Both serve as primary bedrooms, but the second property's sloped ceiling adds a design element.",
          "maintenance_concerns": {
            "first_property": [
              "Condition of carpet.",
              "Efficiency of radiator."
            ],
            "second_property": [
              "Potential for damp or insulation issues in sloped ceiling.",
              "Wear and tear on flooring (not specified, assume carpet/wood).",
              "Window condition."
            ]
          },
          "first_property_part_id": 64,
          "improvement_suggestions": {
            "first_property": [
              "Install built-in wardrobes for better storage.",
              "Update window blinds/curtains.",
              "Choose a feature wall color."
            ],
            "second_property": [
              "Optimize furniture placement considering the sloped ceiling.",
              "Install custom storage solutions for sloped areas.",
              "Update decor to a personal style."
            ]
          },
          "second_property_part_id": 80,
          "first_property_part_slug": "bedroom-1",
          "second_property_part_slug": "bedroom_1"
        },
        {
          "title": "Second Bedroom Features",
          "comparison": "The main property's Bedroom 2 is described simply as an empty room with neutral walls and beige carpet, offering a blank canvas. The second property's Bedroom 2 is furnished with a bed and includes a wooden desk/dressing table and storage boxes, suggesting it's currently used as a combination bedroom/study space. Both are standard bedrooms, but the second property's inclusion of a desk area indicates its potential for multi-functional use, which might be appealing to buyers needing workspace within bedrooms.",
          "maintenance_concerns": {
            "first_property": [
              "Condition of carpet.",
              "Wall finish."
            ],
            "second_property": [
              "Condition of flooring (not specified).",
              "Window condition."
            ]
          },
          "first_property_part_id": 65,
          "improvement_suggestions": {
            "first_property": [
              "Add built-in storage.",
              "Personalize wall color/decor.",
              "Install suitable window coverings."
            ],
            "second_property": [
              "Define the desk area more clearly.",
              "Improve storage solutions.",
              "Update decor to a personal style."
            ]
          },
          "second_property_part_id": 81,
          "first_property_part_slug": "bedroom-2",
          "second_property_part_slug": "bedroom_2"
        },
        {
          "title": "Third Bedroom Condition",
          "comparison": "The main property's Bedroom 3 is noted as having neutral carpet showing some wear. It appears to be the smallest of the three bedrooms, needing attention to the flooring. The second property's Bedroom 3 is furnished and includes a large chest of drawers with a mirror, a desk area, and built-in wardrobe, also featuring sloped ceilings and two-toned walls. The second property's third bedroom offers more built-in functionality and storage despite the sloped ceiling challenge, while the main property's third bedroom requires immediate attention to the carpet.",
          "maintenance_concerns": {
            "first_property": [
              "Condition of worn carpet.",
              "Door condition."
            ],
            "second_property": [
              "Potential damp in sloped ceiling areas.",
              "Condition of built-in wardrobe.",
              "Window condition."
            ]
          },
          "first_property_part_id": 66,
          "improvement_suggestions": {
            "first_property": [
              "Replace worn carpet.",
              "Add built-in storage.",
              "Refresh wall paint."
            ],
            "second_property": [
              "Optimize layout due to sloped ceiling.",
              "Update wall colors.",
              "Assess functionality of built-in wardrobe."
            ]
          },
          "second_property_part_id": 82,
          "first_property_part_slug": "bedroom-3",
          "second_property_part_slug": "bedroom_3"
        },
        {
          "title": "Exterior Curb Appeal",
          "comparison": "Both properties are detached brick houses with tiled roofs, presenting a traditional appearance. The main property features a blue garage door and a white front door, with a driveway and front garden area including a tree. The second property also shows a brick facade, driveway leading to a garage, and front garden landscaping. Both offer off-street parking and garden space at the front, contributing to curb appeal. The specific style of garage door and front door are cosmetic differences, and the size/design of the front garden varies, but structurally they are similar detached homes.",
          "maintenance_concerns": {
            "first_property": [
              "Condition of roof tiles.",
              "Brickwork condition.",
              "Garage door mechanism."
            ],
            "second_property": [
              "Condition of roof tiles.",
              "Brickwork condition.",
              "Driveway surface condition."
            ]
          },
          "first_property_part_id": 68,
          "improvement_suggestions": {
            "first_property": [
              "Update garage door color/style.",
              "Enhance front garden landscaping.",
              "Repaint front door."
            ],
            "second_property": [
              "Enhance front garden landscaping.",
              "Improve pathway/driveway appearance.",
              "Consider updating the front door."
            ]
          },
          "second_property_part_id": 87,
          "first_property_part_slug": "front-of-building",
          "second_property_part_slug": "front_of_building"
        },
        {
          "title": "Driveway and Parking",
          "comparison": "The main property explicitly lists a driveway leading to the garage and front door, visible in front exterior photos and providing off-street parking. The second property also features a driveway leading to its garage, as shown in its front exterior photos, but does not list it as a separate 'driveway' asset part. Both properties offer the crucial feature of off-street parking and garage access via a driveway, which is a significant advantage. The surface material or size details are not provided, so a detailed comparison of the driveways themselves is limited.",
          "maintenance_concerns": {
            "first_property": [
              "Condition of the driveway surface (cracks, potholes)."
            ],
            "second_property": [
              "Condition of the driveway surface (cracks, potholes)."
            ]
          },
          "first_property_part_id": 69,
          "improvement_suggestions": {
            "first_property": [
              "Resurface or clean the driveway.",
              "Add border planting or lighting."
            ],
            "second_property": [
              "Resurface or clean the driveway.",
              "Add border planting or lighting."
            ]
          },
          "second_property_part_id": null,
          "first_property_part_slug": "driveway",
          "second_property_part_slug": null
        },
        {
          "title": "Garden Spaces: Front and Rear",
          "comparison": "The main property has both a front and rear garden listed. The front garden includes a tree and forms part of the curb appeal. The rear garden is a key feature with a grass lawn, concrete patio, wooden fence, shed, and mature trees, offering private outdoor space. The second property lists a single 'Garden' part, described as a rear garden with a large lawn, shrubs, fence, and potentially a greenhouse. While the main property specifies front and rear, the second property's description focuses on a substantial rear garden. Both offer valuable outdoor space, but the second property's garden appears larger and includes a potential greenhouse, while the main property offers a dedicated front garden space.",
          "maintenance_concerns": {
            "first_property": [
              "Maintenance of lawn and shrubs.",
              "Condition of the wooden fence.",
              "Condition of the concrete patio."
            ],
            "second_property": [
              "Maintenance of large lawn and shrubs.",
              "Condition of the wooden fence.",
              "Maintenance of the greenhouse structure."
            ]
          },
          "first_property_part_id": 70,
          "improvement_suggestions": {
            "first_property": [
              "Enhance front garden landscaping.",
              "Improve the rear patio area.",
              "Add planting borders to the rear lawn."
            ],
            "second_property": [
              "Develop the greenhouse area.",
              "Enhance planting borders and shrubbery.",
              "Add features like a pond or seating areas."
            ]
          },
          "second_property_part_id": 90,
          "first_property_part_slug": "garden-1-front",
          "second_property_part_slug": "garden"
        },
        {
          "title": "Rear Exterior Views",
          "comparison": "The rear exterior of the main property shows a two-story brick house with a tiled roof, featuring double patio doors accessing the garden and various windows. Satellite dishes are visible, common for residential properties. The rear of the second property is also a brick house with a tiled roof, but its key feature is the conservatory extension. It also has large patio doors (wooden-framed) and upper windows. The second property's rear view is dominated by the conservatory, indicating a significant difference in the building's footprint and interaction with the garden compared to the main property.",
          "maintenance_concerns": {
            "first_property": [
              "Condition of roof tiles and gutters.",
              "Seals around windows and patio doors."
            ],
            "second_property": [
              "Condition of roof tiles and gutters.",
              "Seals around windows, patio doors, and conservatory.",
              "Potential for issues where conservatory joins the main house."
            ]
          },
          "first_property_part_id": 71,
          "improvement_suggestions": {
            "first_property": [
              "Clean or update satellite dishes.",
              "Inspect external rendering/brickwork.",
              "Consider adding exterior lighting."
            ],
            "second_property": [
              "Maintain the conservatory exterior.",
              "Inspect external rendering/brickwork.",
              "Ensure gutters and drainage are clear, especially near the conservatory."
            ]
          },
          "second_property_part_id": 88,
          "first_property_part_slug": "back-of-building",
          "second_property_part_slug": "back_of_building"
        },
        {
          "title": "Rear Garden Comparison",
          "comparison": "The main property's rear garden features a grass lawn, concrete patio area, wooden fence, small shed, and mature trees, offering a private outdoor space for recreation and relaxation. The second property's 'Garden' part focuses on a large rear garden with a substantial lawn area bordered by shrubs, a wooden fence, and includes the potential for a greenhouse structure. While both have lawns and fences, the second property's garden appears larger and offers specific features like the potential greenhouse, appealing to keen gardeners, whereas the main property includes a shed and a more defined patio area.",
          "maintenance_concerns": {
            "first_property": [
              "Condition of the wooden fence.",
              "Maintenance of lawn and trees.",
              "Condition of the shed."
            ],
            "second_property": [
              "Maintenance of large lawn and shrubs.",
              "Condition of the wooden fence.",
              "Structural integrity of the greenhouse."
            ]
          },
          "first_property_part_id": 72,
          "improvement_suggestions": {
            "first_property": [
              "Replace concrete patio with modern paving.",
              "Upgrade or replace the garden shed.",
              "Add diverse planting for color and interest."
            ],
            "second_property": [
              "Formalize or remove the greenhouse structure.",
              "Create distinct zones (patio, lawn, planting).",
              "Improve boundary fencing."
            ]
          },
          "second_property_part_id": 90,
          "first_property_part_slug": "garden-2-rear",
          "second_property_part_slug": "garden"
        },
        {
          "title": "Outdoor Patio Areas",
          "comparison": "Both properties feature a patio area in the rear garden. The main property has a concrete patio area adjacent to the back of the house. The second property has a paved patio area near the house and conservatory, described as having large flagstones. The second property's patio appears more substantial and potentially better finished with flagstone paving compared to the main property's concrete area. Both provide essential outdoor seating/entertaining space, but the second property's patio seems superior in material and possibly size.",
          "maintenance_concerns": {
            "first_property": [
              "Cracks or unevenness in the concrete.",
              "Drainage issues."
            ],
            "second_property": [
              "Weeds between flagstones.",
              "Unevenness or loose flagstones.",
              "Drainage issues."
            ]
          },
          "first_property_part_id": 73,
          "improvement_suggestions": {
            "first_property": [
              "Replace concrete patio with modern paving.",
              "Extend the patio area.",
              "Add outdoor furniture and decor."
            ],
            "second_property": [
              "Clean and seal the flagstone paving.",
              "Add border planting around the patio.",
              "Install outdoor lighting."
            ]
          },
          "second_property_part_id": 89,
          "first_property_part_slug": "patio",
          "second_property_part_slug": "patio"
        },
        {
          "title": "Garden Storage: Shed vs. Greenhouse",
          "comparison": "The main property includes a small shed located in the rear garden, providing useful storage for garden tools and equipment. The second property does not list a shed but its garden description mentions a large greenhouse structure at the back. While both offer supplementary structures in the garden, they serve different purposes: a shed for general storage, and a greenhouse for cultivation. The second property lacks a traditional shed, which might mean storage needs would have to be met by the garage or internal space, while the main property provides this basic garden storage solution.",
          "maintenance_concerns": {
            "first_property": [
              "Structural integrity of the shed.",
              "Roof condition and leaks."
            ],
            "second_property": [
              "Maintenance of the greenhouse structure.",
              "Potential for storage issues without a shed."
            ]
          },
          "first_property_part_id": 74,
          "improvement_suggestions": {
            "first_property": [
              "Maintain or replace the shed.",
              "Organize shed interior for efficiency."
            ],
            "second_property": [
              "Consider adding a shed for general storage.",
              "Develop the greenhouse area for planting."
            ]
          },
          "second_property_part_id": null,
          "first_property_part_slug": "shed",
          "second_property_part_slug": null
        },
        {
          "title": "Energy Performance Rating",
          "comparison": "The main property provides an Energy Performance Certificate (EPC) showing a current rating of 'D' (59) with a potential rating of 'B' (88). This indicates the property has scope for significant energy efficiency improvements. No EPC information is provided for the second property. Without this data, it's impossible to compare their current energy efficiency or potential for improvement. The main property's EPC provides valuable insight into potential future costs and environmental impact, which is missing for the second property.",
          "maintenance_concerns": {
            "first_property": [
              "Cost and complexity of energy efficiency upgrades."
            ],
            "second_property": [
              "Unknown energy efficiency status and potential issues."
            ]
          },
          "first_property_part_id": 75,
          "improvement_suggestions": {
            "first_property": [
              "Implement recommended measures to improve EPC rating (e.g., insulation, boiler upgrade).",
              "Consider renewable energy sources."
            ],
            "second_property": [
              "Obtain an EPC to assess current energy efficiency.",
              "Identify potential areas for improvement based on the EPC report."
            ]
          },
          "second_property_part_id": null,
          "first_property_part_slug": "energy-performance-certificate",
          "second_property_part_slug": null
        }
      ]
    }
  },
  created() {
    // Initialize activeTab and expanded arrays
    this.activeTab = this.roomComparisons.map(() => 'first')
    this.expanded = this.roomComparisons.map(() => false)
  },
  methods: {
    toggleExpand(index) {
      this.expanded[index] = !this.expanded[index]
    }
  }
}
</script>

<style scoped>
.q-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>