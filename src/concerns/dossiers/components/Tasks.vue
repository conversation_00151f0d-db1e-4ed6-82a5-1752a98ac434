<template>
  <div class="tasks-container q-pa-md">
    <!-- Loading State -->
    <div v-if="isLoading"
         class="text-center q-pa-lg">
      <q-spinner color="primary"
                 size="2em" />
      <div class="q-mt-sm">Loading tasks...</div>
    </div>

    <!-- Error State -->
    <div v-else-if="error"
         class="text-center q-pa-lg text-negative">
      {{ error }}
    </div>

    <!-- Main Content -->
    <template v-else>
      <div>
        <h3 class="text-h5 q-mb-lg q-mt-md text-center h2c-underline"
            style="">Property Tasks</h3>
      </div>
      <div v-if="$route.name !== 'rTasks'">
        <router-view :realtyDossier="realtyDossier"
                     :saleListing="saleListing" />
      </div>
      <template v-else>

        <div class="row items-center justify-between q-mb-md">
          <q-btn color="primary"
                 icon="add"
                 label="Create New Task"
                 @click="$router.push({ name: 'rCreateTask' })" />
        </div>
        <!-- Filter Controls -->
        <div class="row q-mb-md">
          <div class="col-12">
            <q-select v-model="filterType"
                      :options="filterOptions"
                      label="Filter by"
                      outlined
                      emit-value
                      map-options
                      class="q-mb-md" />
          </div>
          <div class="col-12"
               v-if="
                filterType === 'comparison' && dossierAssetsComparisons.length > 0
              ">
            <q-select v-model="selectedComparison"
                      :options="comparisonOptions"
                      label="Select comparison"
                      outlined
                      emit-value
                      map-options />
          </div>
        </div>

        <!-- Task List -->
        <q-list bordered
                separator>
          <q-item v-for="task in filteredTasks"
                  :key="task.id"
                  class="q-pa-sm"
                  clickable
                  :to="{ name: 'rTaskDetails', params: { taskId: task.id } }">
            <q-item-section avatar>
              <q-checkbox :model-value="task.completed"
                          @update:model-value="() => toggleTaskCompletion(task.id)" />
            </q-item-section>
            <q-item-section>
              <q-item-label :class="{ 'text-strike': task.completed }">
                {{ task.text }}
              </q-item-label>
              <q-item-label caption>
                Added {{ formatDate(task.createdAt) }}
                <span v-if="task.isPrimary"
                      class="q-ml-sm">
                  • Related to: Northstar Property
                </span>
                <span v-else-if="task.comparisonId"
                      class="q-ml-sm">
                  • Related to: {{ getComparisonName(task.comparisonId) }}
                </span>
              </q-item-label>
            </q-item-section>
            <q-item-section side>
              <q-btn flat
                     round
                     dense
                     color="negative"
                     icon="delete"
                     @click.stop="() => handleDeleteTask(task.id)" />
            </q-item-section>
          </q-item>
        </q-list>

        <!-- Empty State -->
        <div v-if="filteredTasks.length === 0"
             class="text-center q-pa-lg text-grey-7">
          No tasks found. Create a new task to get started.
        </div>
      </template>
    </template>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue'
import { useQuasar } from 'quasar'
import { useDossierTasks } from '../composables/useDossierTasks'

export default defineComponent({
  name: 'PropertyTasks',

  props: {
    saleListing: {
      type: Object,
      required: true,
    },
    realtyDossier: {
      type: Object,
      required: true,
    },
  },

  setup(props) {
    const $q = useQuasar()
    const filterType = ref('all')
    const selectedComparison = ref(null)
    const {
      tasks,
      isLoading,
      error,
      deleteTask,
      toggleTaskCompletion,
      formatDate,
    } = useDossierTasks(props.realtyDossier, $q)

    // Get dossier assets comparisons
    const dossierAssetsComparisons = computed(() => {
      return props.realtyDossier?.dossier_assets_comparisons || []
    })

    // Create filter options
    const filterOptions = computed(() => {
      const options = [
        { label: 'All Tasks', value: 'all' },
        { label: 'Northstar Property Tasks', value: 'primary' },
      ]
      if (dossierAssetsComparisons.value.length > 0) {
        options.push({ label: 'Comparison Tasks', value: 'comparison' })
      }
      return options
    })

    // Create comparison options
    const comparisonOptions = computed(() => {
      return dossierAssetsComparisons.value.map((comparison) => ({
        label:
          comparison.right_side_property?.street_address ||
          comparison.right_side_property?.title ||
          'Unnamed Property',
        value: comparison.uuid,
      }))
    })

    // Filter tasks based on selected filter
    const filteredTasks = computed(() => {
      if (filterType.value === 'all') return tasks.value
      if (filterType.value === 'primary') {
        return tasks.value.filter((task) => task.isPrimary)
      }
      if (filterType.value === 'comparison') {
        if (!selectedComparison.value)
          return tasks.value.filter((task) => task.comparisonId)
        return tasks.value.filter(
          (task) => task.comparisonId === selectedComparison.value
        )
      }
      return tasks.value
    })

    // Get comparison name by ID
    const getComparisonName = (comparisonId) => {
      const comparison = dossierAssetsComparisons.value.find(
        (c) => c.uuid === comparisonId
      )
      return (
        comparison?.right_side_property?.street_address ||
        comparison?.right_side_property?.title ||
        'Unnamed Property'
      )
    }

    const handleDeleteTask = async (taskId) => {
      await deleteTask(taskId)
    }

    return {
      tasks,
      isLoading,
      error,
      filterType,
      selectedComparison,
      filterOptions,
      comparisonOptions,
      filteredTasks,
      dossierAssetsComparisons,
      handleDeleteTask,
      toggleTaskCompletion,
      formatDate,
      getComparisonName,
    }
  },
})
</script>

<style scoped>
.tasks-container {
  max-width: 800px;
  margin: 0 auto;
}
</style>
