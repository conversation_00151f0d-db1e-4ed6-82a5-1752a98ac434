<template>
  <div class="q-mx-none q-py-none SingleComparableDetails">
    <!-- based off ListingDetailsBlockMain -->
    <div class="q-py-sm row">
      <div>
        <div class="text-h6 col-xs-12 q-pb-md q-px-md">{{ evaluationDetails.catchy_title || evaluationDetails.title }}
        </div>
      </div>
      <!-- Carousel -->
      <div class="ldbm-carousel col-xs-12">
        <ListingCarousel :carouselSlides="carouselSlides"> </ListingCarousel>
      </div>

      <div v-if="showDossierHeader"
           class="q-px-md q-pt-md">
        <div class="col-xs-12 col-md-6">
          <div class="text-subtitle2">{{ evaluationDetails.street_name }}</div>
          <div class="text-subtitle2">{{ evaluationDetails.street_address }}</div>
          <div class="text-subtitle2">{{ evaluationDetails.postal_code }}</div>
        </div>
        <div class="col-xs-12 col-md-6">
          <ListingPriceAndRooms :evaluationDetails="evaluationDetails"></ListingPriceAndRooms>
        </div>
      </div>

      <!-- Property Description -->
      <div class="text-body1 q-py-lg col-xs-12 scd-exp-ctr">
        <q-expansion-item :default-opened="true"
                          aria-expanded="true"
                          icon="location_city"
                          dense
                          header-class="bg-primary text-white"
                          class="asset-part-group q-mb-md"
                          label="Property Description">
          <template v-slot:header>
            <div class="text-h6 q-pa-none text-capitalize full-width">

              <div class="q-py-sm"
                   style="display: flex;align-items: left;">
                <q-icon class="q-pr-sm"
                        name="dashboard" />
                <q-item-label class="q-pb-sm"
                              style="margin-top: -2px;">Property Description</q-item-label>
              </div>
              <div class="text-caption"
                   style="font-size: 0.65rem;margin-top:-6px">Click to expand/collapse</div>
            </div>
          </template>
          <div class="q-pa-sm">
            <div v-html="evaluationDetails.description"></div>
          </div>
        </q-expansion-item>
      </div>

      <!-- Notes Section -->
      <div class="notes-section q-mt-lg"
           style="width: 100%;">
        <q-expansion-item :default-opened="true"
                          icon="speaker_notes"
                          dense
                          header-class="bg-secondary text-white"
                          class="notes-expansion q-mb-md"
                          label="Property Notes">
          <template v-slot:header>
            <div class="text-h6 q-pa-none text-capitalize full-width">
              <div class="q-py-sm"
                   style="display: flex;align-items: left;">
                <q-icon class="q-pr-sm"
                        name="speaker_notes" />
                <q-item-label class="q-pb-sm"
                              style="margin-top: -2px;">
                  Property Notes
                  <q-badge v-if="filteredNotes.length > 0"
                           color="accent"
                           :label="filteredNotes.length"
                           class="q-ml-sm" />
                </q-item-label>
              </div>
              <div class="text-caption"
                   style="font-size: 0.65rem;margin-top:-6px">
                Click to expand/collapse
              </div>
            </div>
          </template>

          <div class="notes-content q-pa-none">
            <!-- Add Note Form -->
            <div class="add-note-form q-mb-lg">
              <q-input v-model="newNoteText"
                       type="textarea"
                       label="Add a note about this property..."
                       outlined
                       rows="3"
                       class="q-mb-md"
                       :loading="isSavingNote" />
              <div class="row justify-end">
                <q-btn label="Add Note"
                       color="primary"
                       icon="add_comment"
                       @click="handleAddNote"
                       :disable="!newNoteText.trim() || isSavingNote"
                       :loading="isSavingNote"
                       unelevated />
              </div>
            </div>

            <!-- Notes List -->
            <div v-if="isLoadingNotes"
                 class="text-center q-pa-lg">
              <q-spinner color="primary"
                         size="2em" />
              <div class="q-mt-md text-grey-7">Loading notes...</div>
            </div>

            <div v-else-if="notesError"
                 class="text-center q-pa-lg">
              <q-icon name="error_outline"
                      color="negative"
                      size="2em" />
              <div class="q-mt-sm text-negative">{{ notesError }}</div>
            </div>

            <div v-else-if="filteredNotes.length > 0"
                 class="notes-list">
              <div v-for="note in filteredNotes"
                   :key="note.id"
                   class="note-item q-mb-md">
                <q-card flat
                        bordered
                        class="note-card">
                  <q-card-section class="q-pa-md">
                    <!-- Edit Mode -->
                    <div v-if="editingNoteId === note.id">
                      <q-input v-model="editNoteText"
                               type="textarea"
                               outlined
                               rows="3"
                               class="q-mb-md" />
                      <div class="row justify-end q-gutter-sm">
                        <q-btn label="Cancel"
                               color="grey"
                               flat
                               @click="cancelEdit" />
                        <q-btn label="Save"
                               color="primary"
                               @click="handleUpdateNote(note.id)"
                               :loading="isUpdatingNote"
                               unelevated />
                      </div>
                    </div>

                    <!-- View Mode -->
                    <div v-else>
                      <div class="note-text q-mb-sm"
                           v-html="formatNoteText(note.text)"></div>
                      <div class="note-meta row items-center justify-between">
                        <div class="text-caption text-grey-6">
                          <q-icon name="person"
                                  size="xs"
                                  class="q-mr-xs" />
                          {{ note.jotCreatorName || 'Unknown User' }}
                          <span class="q-ml-md">
                            <q-icon name="schedule"
                                    size="xs"
                                    class="q-mr-xs" />
                            {{ formatRelativeTime(note.createdAt) }}
                          </span>
                        </div>
                        <div class="note-actions">
                          <q-btn flat
                                 round
                                 dense
                                 color="grey-7"
                                 icon="edit"
                                 size="sm"
                                 @click="startEdit(note)"
                                 class="q-mr-xs">
                            <q-tooltip>Edit Note</q-tooltip>
                          </q-btn>
                          <q-btn flat
                                 round
                                 dense
                                 color="negative"
                                 icon="delete_outline"
                                 size="sm"
                                 @click="confirmDeleteNote(note.id)">
                            <q-tooltip>Delete Note</q-tooltip>
                          </q-btn>
                        </div>
                      </div>
                    </div>
                  </q-card-section>
                </q-card>
              </div>
            </div>

            <div v-else
                 class="text-center q-pa-lg text-grey-7">
              <q-icon name="speaker_notes_off"
                      size="2em"
                      class="q-mb-sm" />
              <div>No notes for this property yet.</div>
              <div>Add a note above to get started!</div>
            </div>
          </div>
        </q-expansion-item>
      </div>

      <!-- Bullet Points -->
      <q-card-section class="q-pa-none">
        <q-list class="q-mb-lg q-mt-none dense">
          <q-item style="min-height: 22px;"
                  class="text-subtitle2 q-pa-none q-ma-none"
                  v-for="(point, index) in evaluationDetails.description_bullet_points"
                  :key="index">
            <q-item-section>
              <q-item-label>{{ point }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>

      <!-- Expandable Asset Parts Sections -->
      <div v-if="processedAssetParts.length"
           class="col-xs-12 q-py-sm">
        <q-expansion-item v-for="(group, groupIndex) in processedAssetParts"
                          :key="group.key"
                          :label="group.name || group.key.replaceAll('_', ' ')"
                          icon="category"
                          dense
                          header-class="bg-primary text-white"
                          class="asset-part-group q-mb-md"
                          :default-opened="false"
                          aria-expanded="false">
          <template v-slot:header>
            <div class="text-h6 q-pa-none text-capitalize full-width">
              {{ group.name || group.key.replaceAll('_', ' ') }}
              <div v-if="groupIndex === 0"
                   class="text-caption"
                   style="font-size: 0.65rem;margin-top:-6px">Click to expand/collapse</div>
            </div>

          </template>
          <!-- <q-card flat
                  bordered
                  class="q-mb-none">
            <q-card-section class="bg-primary text-white">
              <div class="text-h6 q-mb-none text-capitalize">{{ group.name || group.key.replaceAll('_', ' ') }}</div>
            </q-card-section>
          </q-card> -->
          <div v-for="part in group.parts"
               :key="part.id"
               :id="part.asset_part_slug"
               class="asset-part-section-outer q-pb-none q-pt-lg">
            <SingleAssetPart :assetPart="part"></SingleAssetPart>
          </div>
        </q-expansion-item>
      </div>


      <q-separator inset />
    </div>
  </div>
</template>

<script>
import SingleAssetPart from "src/concerns/dossiers/components/SingleAssetPart.vue";
import ListingPriceAndRooms from "src/concerns/for-sale-evaluations/components/listing-blocks/ListingPriceAndRooms.vue";
import ListingCarousel from "src/components/pics/ListingCarousel.vue";
import { scroll } from "quasar";
import { defineComponent, ref, computed } from 'vue';
import { useQuasar } from 'quasar';
import { formatDistanceToNowStrict } from 'date-fns';
import { useDossierNotesAndQueries } from 'src/concerns/dossiers/composables/useDossierNotesAndQueries';

const { getScrollTarget, setVerticalScrollPosition } = scroll;

export default defineComponent({
  components: {
    ListingCarousel,
    ListingPriceAndRooms,
    SingleAssetPart,
  },
  props: {
    evaluationDetails: {
      type: Object,
      default: () => { },
    },
    showDossierHeader: {
      type: Boolean,
      default: true,
    },
    assetParts: {
      type: Object,
      required: false,
    },
    realtyDossier: {
      type: Object,
      required: false,
    },
    comparisonUuid: {
      type: String,
      required: false,
    },
  },
  setup(props) {
    const $q = useQuasar();

    // Notes functionality
    const newNoteText = ref('');
    const editingNoteId = ref(null);
    const editNoteText = ref('');
    const isSavingNote = ref(false);
    const isUpdatingNote = ref(false);

    // Initialize notes composable only if we have a dossier
    let notesComposable = null;
    if (props.realtyDossier) {
      notesComposable = useDossierNotesAndQueries(props.realtyDossier, $q);
    }

    // Extract notes functionality
    const notesAndQueries = notesComposable?.notesAndQueries || ref([]);
    const isLoadingNotes = notesComposable?.isLoading || ref(false);
    const notesError = notesComposable?.error || ref(null);
    const addNoteOrQuery = notesComposable?.addNoteOrQuery;
    const updateNoteOrQuery = notesComposable?.updateNoteOrQuery;
    const deleteNoteOrQuery = notesComposable?.deleteNoteOrQuery;

    // Filter notes for this specific comparison
    const filteredNotes = computed(() => {
      if (!notesAndQueries.value) return [];

      if (props.comparisonUuid) {
        // Show notes for this specific comparison
        return notesAndQueries.value.filter(note =>
          note.assetsComparisonUuid === props.comparisonUuid
        );
      } else {
        // If no comparison UUID, show primary listing notes
        return notesAndQueries.value.filter(note => note.isPrimaryListingJot);
      }
    });

    // Note handling methods
    const handleAddNote = async () => {
      if (!newNoteText.value.trim() || !addNoteOrQuery) return;

      isSavingNote.value = true;
      try {
        const isPrimary = false // !props.comparisonUuid;
        await addNoteOrQuery(
          newNoteText.value.trim(),
          props.comparisonUuid || null,
          // props.comparisonUuid is actually dossierAssetsComparisonUuid
          isPrimary
        );

        newNoteText.value = '';
        $q.notify({
          type: 'positive',
          message: 'Note added successfully',
          position: 'top'
        });
      } catch (error) {
        console.error('Error adding note:', error);
        $q.notify({
          type: 'negative',
          message: 'Failed to add note',
          position: 'top'
        });
      } finally {
        isSavingNote.value = false;
      }
    };

    const startEdit = (note) => {
      editingNoteId.value = note.id;
      editNoteText.value = note.text;
    };

    const cancelEdit = () => {
      editingNoteId.value = null;
      editNoteText.value = '';
    };

    const handleUpdateNote = async (noteId) => {
      if (!editNoteText.value.trim() || !updateNoteOrQuery) return;

      isUpdatingNote.value = true;
      try {
        await updateNoteOrQuery(noteId, {
          text: editNoteText.value.trim()
        });

        cancelEdit();
        $q.notify({
          type: 'positive',
          message: 'Note updated successfully',
          position: 'top'
        });
      } catch (error) {
        console.error('Error updating note:', error);
        $q.notify({
          type: 'negative',
          message: 'Failed to update note',
          position: 'top'
        });
      } finally {
        isUpdatingNote.value = false;
      }
    };

    const confirmDeleteNote = (noteId) => {
      $q.dialog({
        title: 'Delete Note',
        message: 'Are you sure you want to delete this note? This action cannot be undone.',
        cancel: true,
        persistent: true
      }).onOk(() => {
        handleDeleteNote(noteId);
      });
    };

    const handleDeleteNote = async (noteId) => {
      if (!deleteNoteOrQuery) return;

      try {
        await deleteNoteOrQuery(noteId);
        $q.notify({
          type: 'positive',
          message: 'Note deleted successfully',
          position: 'top'
        });
      } catch (error) {
        console.error('Error deleting note:', error);
        // Error notification is handled in the composable
      }
    };

    const formatNoteText = (text) => {
      if (!text) return '';
      // Convert line breaks to HTML
      return text.replace(/\n/g, '<br>');
    };

    const formatRelativeTime = (dateString) => {
      if (!dateString) return '';
      try {
        return formatDistanceToNowStrict(new Date(dateString), { addSuffix: true });
      } catch (error) {
        return 'Unknown time';
      }
    };

    return {
      // Notes data
      newNoteText,
      editingNoteId,
      editNoteText,
      isSavingNote,
      isUpdatingNote,
      filteredNotes,
      isLoadingNotes,
      notesError,

      // Notes methods
      handleAddNote,
      startEdit,
      cancelEdit,
      handleUpdateNote,
      confirmDeleteNote,
      formatNoteText,
      formatRelativeTime
    };
  },
  computed: {
    carouselSlides() {
      let carouselSlides = [];
      let picsColl = this.evaluationDetails?.sale_listing_pics || [];
      picsColl.forEach(function (picObject) {
        let imageUrl = picObject.image_details.url;
        if (!picObject.flag_is_hidden) {
          carouselSlides.push({
            thumb: imageUrl,
            src: imageUrl,
            altText: picObject.photo_title,
          });
        }
      });
      return carouselSlides;
    },
    processedAssetParts() {
      if (!this.assetParts) return [];

      // Configuration for asset part types
      const assetPartConfig = {
        bedroom: { order: 1, name: "Bedrooms", display: true },
        bathroom: { order: 6, name: "Bathrooms", display: true },
        garage: { order: 35, name: "Garage", display: true },
        kitchen: { order: 2, name: "Kitchen", display: true },
        living_room: { order: 3, name: "Living Room", display: true },
        dining_room: { order: 4, name: "Dining Room", display: true },
        hallway: { order: 5, name: "Hallway", display: true },
        default: { order: 99, name: null, display: true },
        other: { order: 99, name: "Other", display: false },
      };

      // Process asset parts
      const processed = Object.entries(this.assetParts)
        .map(([key, parts]) => {
          // Use config if key exists, otherwise default to 'default'
          const config = assetPartConfig[key] || assetPartConfig.default;
          return {
            key, // Original key for uniqueness
            name: config.name, // Display name
            order: config.order, // Order for sorting
            display: config.display, // Whether to display
            parts, // Array of asset parts
          };
        })
        .filter(group => group.display) // Only include groups where display is true
        .sort((a, b) => a.order - b.order); // Sort by order

      return processed;
    },
  },
  methods: {
    scrollToSection(hash) {
      if (!hash) return;
      const element = document.getElementById(hash.slice(1));
      if (element) {
        const target = getScrollTarget(element);
        const offset = element.offsetTop - 80; // Adjust offset for header
        setVerticalScrollPosition(target, offset, 300); // 300ms animation
      }
    },
  },
  watch: {
    "$route.hash": {
      immediate: true,
      handler(newHash) {
        this.$nextTick(() => {
          this.scrollToSection(newHash);
        });
      },
    },
  },
});
</script>

<style scoped>
.q-item__section--side {
  color: white;
}

.asset-part-section {
  padding: 16px 0;
}

.asset-part-group {
  margin-bottom: 24px;
}

/* Notes Section Styles */
.notes-section {
  margin-top: 2rem;
}

.notes-expansion {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.notes-content {
  background-color: #fafafa;
}

.add-note-form {
  background-color: white;
  /* border-radius: 8px; */
  padding: 1rem;
  border: 1px solid #e0e0e0;
}

.notes-list {
  max-height: 400px;
  overflow-y: auto;
}

.note-card {
  transition: all 0.2s ease;
  border-left: 4px solid transparent;
}

.note-card:hover {
  border-left-color: var(--q-primary);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.note-text {
  line-height: 1.5;
  word-wrap: break-word;
}

.note-meta {
  border-top: 1px solid #f0f0f0;
  padding-top: 0.5rem;
  margin-top: 0.5rem;
}

.note-actions {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.note-card:hover .note-actions {
  opacity: 1;
}

.note-item:last-child {
  margin-bottom: 0;
}
</style>