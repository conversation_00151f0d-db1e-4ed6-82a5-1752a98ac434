<template>
  <div class="SingleComparablePhotos">
    <ViewOnlyImageGallery :images="images"
                          :initial-edit-mode="editMode"
                          @delete-images="$emit('delete-images', $event)"
                          @update-image="$emit('update-image', $event)"
                          @photo-uploaded="$emit('photo-uploaded', $event)" />
  </div>
</template>

<script>
// import EditableImageGallery from "./EditableImageGallery.vue"
import ViewOnlyImageGallery from "src/concerns/dossiers/components/images/ViewOnlyImageGallery.vue"

export default {
  name: 'SingleComparablePhotos',
  components: {
    ViewOnlyImageGallery
  },
  props: {
    // realtyDossier: {
    //   type: Object,
    //   required: false
    // },
    evaluationDetails: {
      type: Object,
      default: () => { },
    },
    // assetParts: {
    //   type: Array,
    //   required: false,
    // },
  },
  data() {
    return {
      editMode: false
    }
  },
  computed: {
    images() {
      return this.evaluationDetails?.sale_listing_pics || []
    }
  },
  mounted() {
    // if (this.$route.name === "rDossierPhotosEdit") {
    //   this.editMode = true
    // }
  }
}
</script>