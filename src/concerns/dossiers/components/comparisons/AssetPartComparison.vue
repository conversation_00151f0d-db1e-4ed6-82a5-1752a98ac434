<template>
  <q-card v-for="(room, index) in roomComparisons"
          :key="index"
          class="q-mb-md">
    <q-card-section>
      <div class="text-h6">{{ room.title }}</div>
    </q-card-section>

    <q-card-section>
      <div class="text-subtitle2">Comparison</div>
      <p>{{ room.comparison }}</p>
    </q-card-section>

    <q-card-section>
      <q-tabs v-model="activeTab[index]"
              dense
              class="text-teal"
              active-color="primary"
              indicator-color="primary"
              align="justify">
        <q-tab name="first"
               :label="`Main Property (${room.first_property_part_slug})`" />
        <q-tab name="second"
               :label="`Second Property (${room.second_property_part_slug || 'N/A'})`" />
      </q-tabs>

      <q-tab-panels v-model="activeTab[index]"
                    animated>
        <q-tab-panel name="first">
          <div class="text-subtitle2">Maintenance Concerns</div>
          <q-list>
            <q-item v-for="(concern, i) in room.maintenance_concerns.first_property"
                    :key="i">
              <q-item-section>
                <q-item-label>{{ concern }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>

          <div class="text-subtitle2 q-mt-md">Improvement Suggestions</div>
          <q-list>
            <q-item v-for="(suggestion, i) in room.improvement_suggestions.first_property"
                    :key="i">
              <q-item-section>
                <q-item-label>{{ suggestion }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-tab-panel>

        <q-tab-panel name="second">
          <div class="text-subtitle2">Maintenance Concerns</div>
          <q-list>
            <q-item v-for="(concern, i) in room.maintenance_concerns.second_property"
                    :key="i">
              <q-item-section>
                <q-item-label>{{ concern }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>

          <div class="text-subtitle2 q-mt-md">Improvement Suggestions</div>
          <q-list>
            <q-item v-for="(suggestion, i) in room.improvement_suggestions.second_property"
                    :key="i">
              <q-item-section>
                <q-item-label>{{ suggestion }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-tab-panel>
      </q-tab-panels>
    </q-card-section>

    <q-card-actions align="right">
      <q-btn flat
             color="primary"
             :label="expanded[index] ? 'Collapse' : 'Expand Details'"
             @click="toggleExpand(index)" />
    </q-card-actions>

    <q-slide-transition>
      <div v-show="expanded[index]">
        <q-card-section class="bg-grey-2">
          <div class="row">
            <div class="col-6">
              <strong>First Property Part ID:</strong> {{ room.first_property_part_id }}
            </div>
            <div class="col-6">
              <strong>Second Property Part ID:</strong> {{ room.second_property_part_id || 'N/A' }}
            </div>
          </div>
        </q-card-section>
      </div>
    </q-slide-transition>
  </q-card>
</template>

<script>
export default {
  name: 'RoomComparisons',
  props: {
    roomComparisons: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      activeTab: [],
      expanded: [],
    }
  },
  methods: {
    toggleExpand(index) {
      this.$set(this.expanded, index, !this.expanded[index])
    },
  },
}
</script>