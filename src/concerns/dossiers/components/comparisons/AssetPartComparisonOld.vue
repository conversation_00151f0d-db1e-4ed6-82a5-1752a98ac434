// May 2025 - no longer in use
<template>
  <q-card class="comparison-card AssetPartComparison">
    <q-card-section class="bg-primary text-white comparison-card-header"
                    :class="{ 'compact-header': !isExpanded }">
      <div class="row items-center justify-between">
        <h2 class="text-h5 q-my-none">
          {{ formattedTitle }}
        </h2>
        <q-btn flat
               round
               icon="expand_more"
               :class="{ 'rotate-180': isExpanded }"
               @click="toggleExpand"
               class="toggle-btn">
          <q-tooltip>{{ isExpanded ? 'Collapse' : 'Expand' }}</q-tooltip>
        </q-btn>
      </div>
      <div v-if="isExpanded"
           class="header-details q-mt-sm">
        <p class="text-subtitle2 q-my-none">{{ comparisonData.comparison_text }}</p>
      </div>
    </q-card-section>

    <q-card-section v-show="isExpanded">
      <div class="row q-col-gutter-md">
        <!-- Part A -->
        <div class="col-12 col-md-6">
          <q-card flat
                  bordered
                  class="property-card">
            <ListingCarouselSimple :carouselSlides="partASlides"
                                   v-model:slide="slideA" />

            <q-card-section>
              <h3 class="text-h6 q-mt-none">{{ comparisonData.part_a?.asset_part_title || 'Untitled' }}</h3>
              <q-list dense
                      v-if="comparisonData.part_a && Object.keys(comparisonData.part_a).length">
                <q-item v-if="firstPhotoA?.raw_ai_analysis?.sections_or_rooms_in_photo?.[0]?.dominant_color">
                  <q-item-section>
                    <q-item-label caption>Dominant Color</q-item-label>
                    <q-item-label>{{ firstPhotoA.raw_ai_analysis.sections_or_rooms_in_photo[0].dominant_color
                      }}</q-item-label>
                  </q-item-section>
                </q-item>
                <q-item v-if="firstPhotoA?.raw_ai_analysis?.sections_or_rooms_in_photo?.[0]?.estimated_area_sq_feet">
                  <q-item-section>
                    <q-item-label caption>Area</q-item-label>
                    <q-item-label>{{ firstPhotoA.raw_ai_analysis.sections_or_rooms_in_photo[0].estimated_area_sq_feet
                      }}</q-item-label>
                  </q-item-section>
                </q-item>
                <q-item v-if="firstPhotoA?.raw_ai_analysis?.sections_or_rooms_in_photo?.[0]?.significant_items?.length">
                  <q-item-section>
                    <q-item-label caption>Features</q-item-label>
                    <q-item-label>
                      <q-chip v-for="item in firstPhotoA.raw_ai_analysis.sections_or_rooms_in_photo[0].significant_items"
                              :key="item"
                              dense>
                        {{ item }}
                      </q-chip>
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>

        <!-- Part B -->
        <div class="col-12 col-md-6">
          <q-card flat
                  bordered
                  class="property-card">
            <ListingCarouselSimple :carouselSlides="partBSlides"
                                   v-model:slide="slideB" />

            <q-card-section>
              <h3 class="text-h6 q-mt-none">{{ comparisonData.part_b?.asset_part_title || 'Untitled' }}</h3>
              <q-list dense
                      v-if="comparisonData.part_b && Object.keys(comparisonData.part_b).length">
                <q-item v-if="firstPhotoB?.raw_ai_analysis?.sections_or_rooms_in_photo?.[0]?.dominant_color">
                  <q-item-section>
                    <q-item-label caption>Dominant Color</q-item-label>
                    <q-item-label>{{ firstPhotoB.raw_ai_analysis.sections_or_rooms_in_photo[0].dominant_color
                      }}</q-item-label>
                  </q-item-section>
                </q-item>
                <q-item v-if="firstPhotoB?.raw_ai_analysis?.sections_or_rooms_in_photo?.[0]?.estimated_area_sq_feet">
                  <q-item-section>
                    <q-item-label caption>Area</q-item-label>
                    <q-item-label>{{ firstPhotoB.raw_ai_analysis.sections_or_rooms_in_photo[0].estimated_area_sq_feet
                      }}</q-item-label>
                  </q-item-section>
                </q-item>
                <q-item v-if="firstPhotoB?.raw_ai_analysis?.sections_or_rooms_in_photo?.[0]?.significant_items?.length">
                  <q-item-section>
                    <q-item-label caption>Features</q-item-label>
                    <q-item-label>
                      <q-chip v-for="item in firstPhotoB.raw_ai_analysis.sections_or_rooms_in_photo[0].significant_items"
                              :key="item"
                              dense>
                        {{ item }}
                      </q-chip>
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script>
// import PhotoCarousel from './PhotoCarousel.vue' // Adjust path as needed
import ListingCarouselSimple from "src/components/pics/ListingCarouselSimple.vue"

export default {
  name: 'ComparisonCard',

  components: {
    ListingCarouselSimple
  },

  props: {
    comparisonData: {
      type: Object,
      required: true
    },
    titleKey: {
      type: String,
      required: true
    },
    initialExpanded: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      slideA: 0,
      slideB: 0,
      isExpanded: this.initialExpanded
    }
  },

  computed: {
    formattedTitle() {
      return this.titleKey
        .replace(/-vs-/g, ' vs ')
        .replace(/-/g, ' ')
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
    },
    partASlides() {
      return this.comparisonData?.part_a?.asset_part_photos_json_summary || []
    },
    partBSlides() {
      return this.comparisonData?.part_b?.asset_part_photos_json_summary || []
    },
    firstPhotoA() {
      return this.partASlides[0] || {}
    },
    firstPhotoB() {
      return this.partBSlides[0] || {}
    }
  },

  watch: {
    initialExpanded(newVal) {
      this.isExpanded = newVal
    }
  },

  methods: {
    toggleExpand() {
      this.isExpanded = !this.isExpanded
      this.$emit('toggle', this.isExpanded)
    }
  }
}
</script>

<style scoped>
.comparison-card {
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.comparison-card-header {
  transition: all 0.3s ease;
  padding: 16px;
}

.compact-header {
  padding: 8px 16px;
}

.header-details {
  margin-top: 8px;
}

.property-card {
  height: 100%;
}

.toggle-btn {
  transition: transform 0.3s ease;
}

.rotate-180 {
  transform: rotate(180deg);
}
</style>