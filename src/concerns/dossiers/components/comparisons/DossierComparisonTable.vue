<template>
  <div>
    <!-- apr 2025: Started working on this without realising that SideBySideListingsInRow Pretty much did the same thing -->
    <!-- 
   <q-markup-table class="simp-comp-tbl fixed-markup-table DossierComparisonTable"
                  separator="cell"
                  flat
                  bordered>
    <thead>
      <tr>
        <th colspan="2"
            class="text-center text-h6 header-cell"
            style="width: 50%;">
          Property A
        </th>
        <th class="text-center text-h6 header-cell">
          Property B
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td colspan="2"
            class="text-center title-cell">
          {{ comparisonDetails?.comparison?.high_level?.first_property?.title || 'N/A' }}
        </td>
        <td class="text-center title-cell">
          {{ comparisonDetails?.comparison?.high_level?.second_property?.title || 'N/A' }}
        </td>
      </tr>

      <tr>
        <td colspan="3"
            style="height: 0px; padding: 0px">
          <q-separator />
        </td>
      </tr>

      <tr>
        <td class="text-left data-cell"
            style="border-right: none;">Bedrooms</td>
        <td class="text-right numeric-cell"
            style="border-left: none;">
          {{ summaryFieldValue("first", "count_bedrooms") }}
        </td>
        <td class="text-left numeric-cell">
          {{ summaryFieldValue("second", "count_bedrooms") }}
        </td>
      </tr>

      <tr>
        <td class="text-left data-cell"
            style="border-right: none;">Bathrooms</td>
        <td class="text-right numeric-cell"
            style="border-left: none;">
          {{ summaryFieldValue("first", "count_bathrooms") }}
        </td>
        <td class="text-left numeric-cell">
          {{ summaryFieldValue("second", "count_bathrooms") }}
        </td>
      </tr>

      <tr v-if="areaShouldBeShown">
        <td class="text-left data-cell"
            style="border-right: none;">Area</td>
        <td class="text-right numeric-cell"
            style="border-left: none;">
          {{ summaryFieldValue("first", "constructed_area") }}
        </td>
        <td class="text-left numeric-cell">
          {{ summaryFieldValue("second", "constructed_area") }}
        </td>
      </tr>

      <tr>
        <td colspan="2"
            class="description-cell">
          <q-expansion-item expand-separator
                            dense
                            label="Description"
                            :caption="descCaption"
                            v-model="descExpandedTop">
            <q-card>
              <q-card-section class="q-pt-none">
                <div class="comp-lsting-desc-html text-body2"
                     v-html="comparisonDetails?.comparison?.high_level?.first_property?.description || 'No description available.'">
                </div>
              </q-card-section>
            </q-card>
          </q-expansion-item>
        </td>
        <td class="description-cell">
          <q-expansion-item expand-separator
                            dense
                            label="Description"
                            :caption="descCaption"
                            v-model="descExpandedBottom">
            <q-card>
              <q-card-section class="q-pt-none">
                <div class="comp-lsting-desc-html text-body2"
                     v-html="comparisonDetails?.comparison?.high_level?.second_property?.description || 'No description available.'">
                </div>
              </q-card-section>
            </q-card>
          </q-expansion-item>
        </td>
      </tr>
    </tbody>
  </q-markup-table>
   -->

  </div>
</template>

<script>
export default {
  name: 'DossierComparisonTable',
  data() {
    return {
      descExpandedTop: false,
      descExpandedBottom: false,
    }
  },
  methods: {
    summaryFieldValue(position, fieldName) {
      const property = position === 'first' ?
        this.comparisonDetails?.comparison?.high_level?.first_property :
        this.comparisonDetails?.comparison?.high_level?.second_property;

      const value = property?.[fieldName];
      // Special handling for constructed_area since it's 0 in the new data
      if (fieldName === 'constructed_area' && (!value || value === 0)) {
        return 'N/A';
      }
      return value !== undefined && value !== null ? value : 'N/A';
    },
  },
  computed: {
    areaShouldBeShown() {
      // Show area only if at least one property has a non-zero constructed_area
      const firstArea = this.comparisonDetails?.comparison?.high_level?.first_property?.constructed_area;
      const secondArea = this.comparisonDetails?.comparison?.high_level?.second_property?.constructed_area;
      return (firstArea && firstArea !== 0) || (secondArea && secondArea !== 0);
    },
    descCaption() {
      return "click to view...";
    },
  },
  props: {
    comparisonDetails: {
      type: Object,
      required: true,
      default: () => ({
        comparison: {
          high_level: {
            first_property: {
              title: "3 bedroom semi-detached house for sale",
              description: "<p>A 3-bedroom semi-detached house with lounge, kitchen/diner, and garden.</p>",
              count_bedrooms: 3,
              count_bathrooms: 1,
              constructed_area: 0
            },
            second_property: {
              title: "4 bedroom detached house for sale",
              description: "<p>A spacious 4-bedroom detached house with multiple living areas across three floors.</p>",
              count_bedrooms: 4,
              count_bathrooms: 2,
              constructed_area: 0
            }
          }
        }
      }),
    },
  },
}
</script>

<style scoped>
.fixed-markup-table table {
  table-layout: fixed;
  width: 100%;
}

.header-cell {
  padding: 8px;
  font-size: 1.1em;
  font-weight: bold;
  background-color: #f5f5f5;
}

.title-cell {
  padding: 8px 5px;
  font-weight: 500;
  vertical-align: top;
  word-break: break-word;
  white-space: pre-wrap;
  max-width: 50vw;
}

.data-cell {
  padding: 8px 5px;
  vertical-align: middle;
}

.numeric-cell {
  padding: 8px 5px;
  font-size: large;
  vertical-align: middle;
}

.description-cell {
  padding: 0;
  max-width: 50vw;
  word-break: break-word;
  white-space: pre-wrap;
  vertical-align: top;
}

.description-cell .q-item {
  padding: 0 8px;
}

.comp-lsting-desc-html {
  text-align: left;
  font-size: 0.9em;
  line-height: 1.4;
}

.simp-comp-tbl {
  border: 1px solid #e0e0e0;
}

.q-item__label--caption {
  color: #1976D2;
  font-size: 0.8em;
}
</style>