<!-- EditableImageGallery.vue -->
<template>
  <div class="image-gallery">

    <!-- Edit Mode Toggle and Add Photo Buttons -->
    <div class="q-mb-md row items-center justify-between">
      <q-toggle v-model="editMode"
                label="Edit Mode"
                color="primary" />
      <q-btn color="primary"
             label="Add Photo"
             icon="add_a_photo"
             @click="showUploadDialog = true" />
    </div>

    <!-- Image Grid -->
    <div class="row q-col-gutter-md">
      <template v-for="image in images"
                :key="image.id">
        <image-item v-if="notHiddenLocally(image)"
                    :image="image"
                    :edit-mode="editMode"
                    :selected="selectedImages.includes(image.id)"
                    @toggle-selection="toggleImageSelection"
                    @hide-image="hideImage"
                    @update-image="updateImage"
                    @tag-clicked="handleTagClick"
                    class="col-12 col-lg-6  col-xl-6 q-mb-lg" />
      </template>
    </div>

    <!-- Delete Selected Button -->
    <div class="q-mt-md"
         v-if="editMode && selectedImages.length">
      <q-btn color="red"
             label="Delete Selected"
             icon="delete_forever"
             @click="deleteSelectedImages" />
    </div>

    <!-- Upload Dialog -->
    <q-dialog v-model="showUploadDialog">
      <q-card>
        <q-card-section>
          <div class="text-h6">Upload New Photo</div>
        </q-card-section>
        <q-card-section>
          <q-file v-model="newPhoto"
                  label="Select photo"
                  accept="image/*"
                  @update:model-value="handleFileChange" />
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat
                 label="Cancel"
                 @click="showUploadDialog = false" />
          <q-btn color="primary"
                 label="Upload"
                 @click="uploadPhoto" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script>
import ImageItem from "src/concerns/dossiers/components/images/ImageItem.vue"

export default {
  name: 'EditableImageGallery',
  components: {
    ImageItem
  },
  props: {
    images: {
      type: Array,
      default: () => []
    },
    initialEditMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editMode: this.initialEditMode,
      selectedImages: [],
      locallyHiddenImageUuids: [],
      showUploadDialog: false,
      newPhoto: null
    }
  },
  methods: {
    notHiddenLocally(image) {
      return !this.locallyHiddenImageUuids.includes(image.uuid)
    },
    toggleImageSelection(imageId) {
      const index = this.selectedImages.indexOf(imageId)
      if (index === -1) {
        this.selectedImages.push(imageId)
      } else {
        this.selectedImages.splice(index, 1)
      }
    },
    hideImage(imageUuid) {
      this.locallyHiddenImageUuids.push(imageUuid)
    },
    deleteSelectedImages() {
      this.$emit('delete-images', this.selectedImages)
      this.selectedImages = []
    },
    updateImage(image) {
      this.$emit('update-image', image)
    },
    handleTagClick(tag) {
      console.log(`Tag clicked: ${tag}`)
    },
    handleFileChange(file) {
      this.newPhoto = file
    },
    uploadPhoto() {
      if (this.newPhoto) {
        console.log('Uploading photo:', this.newPhoto)
        const mockImage = {
          id: Date.now(),
          image_details: {
            url: URL.createObjectURL(this.newPhoto)
          },
          photo_title: this.newPhoto.name,
          photo_description: ''
        }
        this.$emit('photo-uploaded', mockImage)
        this.newPhoto = null
        this.showUploadDialog = false
      }
    }
  },
  watch: {
    initialEditMode(newVal) {
      this.editMode = newVal
    }
  }
}
</script>

<style scoped>
.image-gallery {
  max-width: 100%;
}
</style>