<template>
  <div class="image-container"
       :class="{ 'selected': isSelected && editMode }">
    <div class="image-wrapper">
      <!-- Clickable Image -->
      <div @click="showFullScreen = true">
        <q-img :src="image.image_details.url"
               style="width: 100%"
               :ratio="16 / 9"
               class="image"
               @click="editMode ? $emit('toggle-selection', image.id) : null" />
      </div>

      <!-- Full Screen Button -->
      <q-btn class="fullscreen-btn"
             flat
             round
             icon="fullscreen"
             @click="showFullScreen = true" />

      <!-- Delete But<PERSON> (visible in edit mode) -->
      <q-btn v-if="editMode"
             class="delete-btn"
             flat
             round
             icon="delete"
             color="red"
             @click="showDeleteConfirm = true" />
    </div>

    <!-- Editable Title -->
    <div class="q-mt-md q-px-sm">
      <q-input v-if="editMode"
               v-model="localImage.photo_title"
               dense
               outlined
               @update:model-value="checkChanges"
               @blur="updateImage" />
      <div v-else
           class="text-h6">
        {{ image.photo_title || image.raw_ai_analysis?.general_photo_analysis?.photo_catchy_title || 'Untitled' }}
      </div>
    </div>

    <!-- Editable Description -->
    <div class="q-mt-sm q-px-sm">
      <q-input v-if="editMode"
               v-model="localImage.photo_description"
               dense
               outlined
               type="textarea"
               @update:model-value="checkChanges"
               @blur="updateImage" />
      <div v-else
           class="text-body1">
        {{ photoDesc }}
      </div>
    </div>

    <!-- Save and Cancel Buttons -->
    <div class="q-mt-sm"
         v-if="editMode && hasChanges">
      <q-btn color="green"
             label="Save"
             size="sm"
             class="q-mr-sm"
             @click="saveChanges" />
      <q-btn color="grey"
             label="Cancel"
             size="sm"
             @click="cancelChanges" />
    </div>

    <!-- Tags -->
    <div class="tags-container q-mt-sm"
         v-if="tags.length">
      <q-chip v-for="tag in tags"
              :key="tag"
              clickable
              color="primary"
              text-color="white"
              class="q-mr-sm q-mb-sm"
              @click="$emit('tag-clicked', tag)">
        {{ tag }}
      </q-chip>
    </div>

    <!-- Full Screen Dialog -->
    <q-dialog v-model="showFullScreen"
              full-width
              full-height>
      <q-card>
        <q-img :src="image.image_details.url" />
        <q-btn flat
               round
               icon="close"
               class="close-btn"
               @click="showFullScreen = false" />
      </q-card>
    </q-dialog>

    <!-- Delete Confirmation Dialog -->
    <q-dialog v-model="showDeleteConfirm"
              persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning"
                    color="red"
                    text-color="white" />
          <span class="q-ml-sm">Are you sure you want to delete this image?</span>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat
                 label="Cancel"
                 color="primary"
                 v-close-popup />
          <q-btn flat
                 label="Delete"
                 color="red"
                 @click="confirmDelete"
                 v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script>
import usePhotoEdit from "src/compose/usePhotoEdit.js"
export default {
  name: 'ImageItem',
  setup(props) {
    const { discardPhoto, updatePhotoDetails } = usePhotoEdit()
    return {
      updatePhotoDetails,
      discardPhoto,
    }
  },
  props: {
    image: {
      type: Object,
      required: true
    },
    editMode: {
      type: Boolean,
      default: false
    },
    selected: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showFullScreen: false,
      showDeleteConfirm: false,
      localImage: { ...this.image }, // Local copy for editing
      hasChanges: false // Track if there are unsaved changes
    }
  },
  computed: {
    isSelected() {
      return this.selected;
    },
    photoDesc() {
      return this.image.photo_description ||
        this.image.raw_ai_analysis?.general_photo_analysis?.photo_general_description ||
        'No description available';
    },
    tags() {
      //  need to think about what from raw_ai_analysis I expose to the front  and
      return this.image.realty_asset_photo_tags?.length
        ? this.image.realty_asset_photo_tags
        : this.image.raw_ai_analysis?.general_photo_analysis?.photo_tags || [];
    }
  },
  watch: {
    image(newVal) {
      this.localImage = { ...newVal }; // Sync local copy with prop changes
      this.checkChanges(); // Recheck changes when image prop updates
    }
  },
  methods: {
    checkChanges() {
      const originalTitle = this.image.photo_title || '';
      const originalDesc = this.image.photo_description || '';
      const newTitle = this.localImage.photo_title || '';
      const newDesc = this.localImage.photo_description || '';
      this.hasChanges = originalTitle !== newTitle || originalDesc !== newDesc;
    },
    updateImage() {
      if (this.hasChanges) {
        this.$emit('update-image', this.localImage);
      }
    },
    async saveChanges() {
      try {
        const response = await this.updatePhotoDetails({
          picUuid: this.localImage.uuid,
          photo_title: this.localImage.photo_title,
          photo_description: this.localImage.photo_description,
          // editToken: '1111', // Uncomment and add if authentication is needed
        });

        const updatedImage = response.data; // Assuming the API returns the updated image data
        this.$emit('update-image', updatedImage);
        this.hasChanges = false;
        this.$q.notify({
          type: 'positive',
          message: 'Image details saved successfully'
        });
      } catch (error) {
        console.error('Error saving image:', error);
        this.$q.notify({
          type: 'negative',
          message: 'Failed to save image details'
        });
      }
      // try {
      //   const response = await fetch('https://api.example.com/photos/update', {
      //     method: 'POST',
      //     headers: {
      //       'Content-Type': 'application/json'
      //     },
      //     body: JSON.stringify({
      //       id: this.localImage.id,
      //       photo_title: this.localImage.photo_title,
      //       photo_description: this.localImage.photo_description
      //     })
      //   });

      //   if (!response.ok) {
      //     throw new Error('Failed to save changes');
      //   }

      //   const updatedImage = await response.json();
      //   this.$emit('update-image', updatedImage); // Emit updated image from server
      //   this.hasChanges = false; // Reset change flag
      //   this.$q.notify({
      //     type: 'positive',
      //     message: 'Image details saved successfully'
      //   });
      // } catch (error) {
      //   console.error('Error saving image:', error);
      //   this.$q.notify({
      //     type: 'negative',
      //     message: 'Failed to save image details'
      //   });
      // }
    },
    cancelChanges() {
      this.localImage = { ...this.image }; // Restore original values
      this.hasChanges = false; // Reset change flag
    },
    confirmDelete() {
      let updateObject = {
        // editToken: '1111',
        picUuid: this.localImage.uuid,
        // restoreOrRemove: 'remove',
      }
      this.discardPhoto(updateObject)
      this.$emit('hide-image', this.image.uuid);
      this.showDeleteConfirm = false;
    }
  }
}
</script>

<style>
.q-dialog__inner--minimized {
  padding: 0px;
}
</style>
<style scoped>
.image-container {
  position: relative;
}

.image-wrapper {
  position: relative;
}

.image {
  transition: opacity 0.3s;
  cursor: pointer;
}

.image:hover {
  opacity: 0.9;
}

.selected {
  border: 2px solid red;
}

.fullscreen-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
}

.delete-btn {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.5);
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
}
</style>