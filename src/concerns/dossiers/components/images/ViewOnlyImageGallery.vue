<!-- ViewOnlyImageGallery.vue -->
<template>
  <div class="view-only-image-gallery">
    <!-- Loading state for all jots -->
    <div v-if="isLoadingDossierJots"
         class="text-center q-pa-lg">
      <q-spinner color="primary"
                 size="3em" />
      <div class="q-mt-sm">Loading image comments...</div>
    </div>
    <div v-else-if="dossierJotsError"
         class="text-negative text-center q-pa-lg">
      <q-icon name="error_outline"
              class="q-mr-sm" /> {{ dossierJotsError }}
    </div>

    <!-- Image Grid -->
    <div v-else
         class="row q-col-gutter-md q-mt-md">
      <template v-for="image in images"
                :key="image.uuid"> <!-- Use image.uuid for key if id can change -->
        <ViewOnlyImageItem v-if="notHiddenLocally(image)"
                           :dossierAssetUuid="dossierAssetUuid"
                           :image="image"
                           :edit-mode="editMode"
                           :selected="selectedImages.includes(image.uuid)"
                           :dossierUuid="dossierUuid"
                           :assetsComparisonUuid="assetsComparisonUuid"
                           :allDossierJots="allDossierJots"
                           :allDossierRatings="allDossierRatings"
                           @toggle-selection="toggleImageSelection"
                           @hide-image="hideImage"
                           @update-image="updateImageInGallery"
                           @tag-clicked="handleTagClick"
                           class="col-12 col-lg-6 col-xl-6 q-mb-lg" />
      </template>
    </div>

    <!-- ... rest of your template (Delete Selected Button, Upload Dialog) ... -->
    <div class="q-mt-md"
         v-if="editMode && selectedImages.length">
      <q-btn color="red"
             label="Delete Selected Images"
             icon="delete_forever"
             @click="deleteSelectedImages" />
    </div>

    <q-dialog v-model="showUploadDialog">
      <q-card style="min-width: 350px">
        <q-card-section>
          <div class="text-h6">Upload New Photo</div>
        </q-card-section>
        <q-card-section class="q-pt-none">
          <q-file v-model="newPhotoFile"
                  label="Select photo"
                  accept="image/*"
                  outlined
                  dense
                  @update:model-value="handleFileChange" />
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat
                 label="Cancel"
                 v-close-popup />
          <q-btn color="primary"
                 label="Upload"
                 @click="uploadPhoto"
                 :disable="!newPhotoFile" />
        </q-card-actions>
      </q-card>
    </q-dialog>
  </div>
</template>

<script>
import { ref, onMounted, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import ViewOnlyImageItem from "src/concerns/dossiers/components/images/ViewOnlyImageItem.vue"; // Corrected name if it was ImageItem
import { htocAxiosApi } from 'boot/axios'; // For fetching all jots
import { pwbFlexConfig } from 'boot/pwb-flex-conf';

const NOTES_QUERIES_ENDPOINT_SLUG = 'jots';
const API_JOTS_LIST_KEY = 'dossier_jots'; // Key for list of jots in API response
const RATINGS_QUERIES_ENDPOINT_SLUG = 'photo_ratings';
const API_RATINGS_LIST_KEY = 'photo_ratings'; // Key for list of ratings in API response

export default {
  name: 'ViewOnlyImageGallery',
  components: {
    ViewOnlyImageItem
  },
  props: {
    images: {
      type: Array,
      default: () => []
    },
    initialEditMode: {
      type: Boolean,
      default: false
    },
    dossierAssetUuid: {
      type: String,
      default: null
    }
    // If dossierUuid is fixed for the gallery instance, pass it as a prop for clarity
    // instead of relying solely on $route.params inside the gallery.
    // dossierUuidFromProp: {
    // }
  },
  setup(props, { emit }) {
    const route = useRoute();
    const $q = useQuasar();

    const editMode = ref(props.initialEditMode);
    const selectedImages = ref([]);
    const locallyHiddenImageUuids = ref([]); // Keep this as ref
    const showUploadDialog = ref(false);
    const newPhotoFile = ref(null); // Renamed from newPhoto for clarity (File object)

    // Centralized comments (all jots for the dossier)
    const allDossierJots = ref(null); // Initialize as null
    const isLoadingDossierJots = ref(false);
    const dossierJotsError = ref(null);

    // Centralized ratings (all ratings for the dossier)
    const allDossierRatings = ref(null); // Initialize as null
    const isLoadingDossierRatings = ref(false);
    const dossierRatingsError = ref(null);

    // Determine dossierUuid (prefer prop, fallback to route)
    const dossierUuid = computed(() => {
      // return props.dossierUuidFromProp || route.params.dossierUuid;
      return route.params.dossierUuid; // Current implementation
    });

    const assetsComparisonUuid = computed(() => {
      // return props.dossierAssetUuidFromProp || route.params.assetsComparisonUuid;
      return route.params.assetsComparisonUuid; // Current implementation
    });

    const fetchAllDossierJots = async () => {
      if (!dossierUuid.value) {
        dossierJotsError.value = "Dossier UUID is not available to fetch comments.";
        allDossierJots.value = []; // Set to empty array to stop children from trying to fetch
        return;
      }
      isLoadingDossierJots.value = true;
      dossierJotsError.value = null;
      try {
        const response = await htocAxiosApi.get(
          `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${dossierUuid.value}/${NOTES_QUERIES_ENDPOINT_SLUG}`
        );
        allDossierJots.value = response.data[API_JOTS_LIST_KEY] || [];
      } catch (err) {
        console.error("Error fetching all dossier jots:", err);
        dossierJotsError.value = err.response?.data?.message || err.message || "Failed to load comments data.";
        allDossierJots.value = []; // Prevent children from fetching individually on error
      } finally {
        isLoadingDossierJots.value = false;
      }
    };

    const fetchAllDossierRatings = async () => {
      if (!dossierUuid.value) {
        dossierRatingsError.value = "Dossier UUID is not available to fetch ratings.";
        allDossierRatings.value = []; // Set to empty array to stop children from trying to fetch
        return;
      }
      isLoadingDossierRatings.value = true;
      dossierRatingsError.value = null;
      try {
        const response = await htocAxiosApi.get(
          `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${dossierUuid.value}/${RATINGS_QUERIES_ENDPOINT_SLUG}`
        );
        allDossierRatings.value = response.data[API_RATINGS_LIST_KEY] || [];
      } catch (err) {
        console.error("Error fetching all dossier ratings:", err);
        dossierRatingsError.value = err.response?.data?.message || err.message || "Failed to load ratings data.";
        allDossierRatings.value = []; // Prevent children from fetching individually on error
      } finally {
        isLoadingDossierRatings.value = false;
      }
    };

    onMounted(() => {
      if (dossierUuid.value) {
        fetchAllDossierJots();
        // fetchAllDossierRatings();
      }
    });

    // Watch for dossierUuid changes (e.g., if navigating between dossiers without full page reload)
    watch(dossierUuid, (newId, oldId) => {
      if (newId && newId !== oldId) {
        fetchAllDossierJots();
        // fetchAllDossierRatings();
      } else if (!newId) {
        allDossierJots.value = [];
        dossierJotsError.value = "Dossier UUID became undefined.";
        allDossierRatings.value = [];
        dossierRatingsError.value = "Dossier UUID became undefined.";
      }
    });


    const notHiddenLocally = (image) => !locallyHiddenImageUuids.value.includes(image.uuid);

    const toggleImageSelection = (imageId) => {
      const index = selectedImages.value.indexOf(imageId);
      if (index === -1) {
        selectedImages.value.push(imageId);
      } else {
        selectedImages.value.splice(index, 1);
      }
    };

    const hideImage = (imageUuid) => {
      if (!locallyHiddenImageUuids.value.includes(imageUuid)) {
        locallyHiddenImageUuids.value.push(imageUuid);
      }
    };

    const deleteSelectedImages = () => {
      // Important: The actual deletion API call should happen where `usePhotoEdit` (discardPhoto) is accessible
      // or this component needs access to a similar composable/service for batch deletion.
      // For now, emitting an event.
      if (selectedImages.value.length === 0) return;
      $q.dialog({
        title: 'Confirm Delete',
        message: `Are you sure you want to delete ${selectedImages.value.length} selected image(s)? This action might be irreversible.`,
        cancel: true,
        persistent: true
      }).onOk(() => {
        emit('delete-images', [...selectedImages.value]); // Emit a copy
        // Optimistically hide them. The parent should update the main `images` prop.
        selectedImages.value.forEach(uuid => hideImage(uuid));
        selectedImages.value = [];
        $q.notify({ type: 'info', message: 'Deletion request sent for selected images.' });
      });
    };

    const updateImageInGallery = (updatedImage) => { // Renamed
      emit('update-image', updatedImage);
      // If a comment was added/deleted via the child, the `allDossierJots` ref
      // in usePhotoComments (child) should have updated the one passed from here.
      // If that two-way binding on the ref isn't perfect, or for explicit refresh:
      // fetchAllDossierJots(); // Or a more targeted update if possible
    };

    const handleTagClick = (tag) => emit('tag-clicked', tag);

    const handleFileChange = (file) => {
      newPhotoFile.value = file;
    };

    const uploadPhoto = () => {
      // This should ideally use a proper upload service/composable
      if (newPhotoFile.value) {
        emit('photo-upload-started', newPhotoFile.value); // For parent to handle actual upload
        // Example: optimistic update or wait for parent to confirm upload and send new image list
        // For now, just closing dialog. Parent is responsible for updating `props.images`.
        newPhotoFile.value = null;
        showUploadDialog.value = false;
        $q.notify({ type: 'info', message: 'Photo upload process initiated.' })
      }
    };

    watch(() => props.initialEditMode, (newVal) => {
      editMode.value = newVal;
    });

    // If props.images itself is reactive and can change from parent
    watch(() => props.images, () => {
      // Potentially reset locallyHiddenImageUuids if the image list changes significantly
      // For example, if a new set of images for a different entity is loaded.
      // locallyHiddenImageUuids.value = [];
    }, { deep: true });

    return {
      assetsComparisonUuid,
      editMode,
      selectedImages,
      locallyHiddenImageUuids,
      showUploadDialog,
      newPhotoFile,
      dossierUuid, // Expose for binding in template if needed directly
      allDossierJots,
      isLoadingDossierJots,
      dossierJotsError,
      allDossierRatings,
      isLoadingDossierRatings,
      dossierRatingsError,
      fetchAllDossierJots, // Expose if manual refresh is needed from template
      fetchAllDossierRatings, // Expose if manual refresh is needed from template
      notHiddenLocally,
      toggleImageSelection,
      hideImage,
      deleteSelectedImages,
      updateImageInGallery,
      handleTagClick,
      handleFileChange,
      uploadPhoto,
    };
  }
}
</script>

<style scoped>
/* .view-only-image-gallery {} // Removed redundant class definition */
.image-gallery {
  /* This was in your original styles, keep if used by parent */
  max-width: 100%;
}
</style>