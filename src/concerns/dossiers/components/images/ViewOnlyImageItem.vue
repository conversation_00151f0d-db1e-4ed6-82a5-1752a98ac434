<template>
  <div class="view-only-image-container"
       :class="{ 'selected': isSelected && editMode }">

    <!-- Editable Title -->
    <div class="q-mt-md q-px-sm">
      <q-input v-if="editMode"
               v-model="localImage.photo_title"
               dense
               outlined
               @update:model-value="checkChanges"
               @blur="updateImage" />
      <div v-else
           class="text-h6">
        {{ image.photo_title || image.raw_ai_analysis?.general_photo_analysis?.photo_catchy_title || 'Untitled' }}
      </div>
    </div>
    <div class="image-wrapper">
      <!-- Clickable Image -->
      <div @click="showFullScreen = true">
        <q-img :src="image.image_details.url"
               class="image"
               @click="editMode ? $emit('toggle-selection', image.id) : null">
          <div class="absolute-bottom text-overlay mobile-hide">
            <div class="text-subtitle2 text-white q-mb-xs">{{ image.photo_description }}</div>
            <div v-if="image.realty_asset_photo_tags.length > 0"
                 class="q-mb-xs">
              <q-chip v-for="(tag, tagIndex) in image.realty_asset_photo_tags"
                      :key="tagIndex"
                      dense
                      color="primary"
                      text-color="white"
                      class="q-ma-xs">
                {{ tag }}
              </q-chip>
            </div>
            <div v-if="image.raw_ai_analysis && image.raw_ai_analysis.general_photo_analysis"
                 class="text-caption text-white">
            </div>
          </div>
        </q-img>
      </div>

      <!-- Full Screen Button -->
      <q-btn class="fullscreen-btn"
             flat
             round
             icon="fullscreen"
             @click="showFullScreen = true" />

      <!-- Delete Button (visible in edit mode) -->
      <q-btn v-if="editMode"
             class="delete-btn"
             flat
             round
             icon="delete"
             color="red"
             @click="showDeleteConfirm = true" />
    </div>

    <div class="q-pa-sm">
      <div class="text-subtitle2 text-black mobile-only q-mb-xs">{{ image.photo_description }}</div>
      <div v-if="image.realty_asset_photo_tags.length > 0"
           class="q-mb-xs">
        <q-chip v-for="(tag, tagIndex) in image.realty_asset_photo_tags"
                :key="tagIndex"
                dense
                color="primary"
                text-color="black"
                class="q-ma-xs">
          {{ tag }}
        </q-chip>
      </div>
      <div v-if="image.raw_ai_analysis && image.raw_ai_analysis.general_photo_analysis"
           class="text-caption text-black">
      </div>
    </div>

    <!-- Tags (already existing) -->
    <div class="tags-container q-mt-sm q-px-sm"
         v-if="tags.length">
      <q-chip v-for="tag in tags"
              :key="tag"
              clickable
              color="secondary"
              text-color="white"
              class="q-mr-sm q-mb-sm"
              @click="$emit('tag-clicked', tag)">
        {{ tag }}
      </q-chip>
    </div>

    <!-- Ratings Section -->
    <div class="q-mt-md q-pa-sm ratings-section">
      <div class="row items-center justify-between q-mb-sm">
        <div class="text-subtitle1">Rating</div>
        <q-btn flat
               dense
               round
               :icon="showRatingsSection ? 'expand_less' : 'expand_more'"
               @click="showRatingsSection = !showRatingsSection"
               aria-label="Toggle ratings section" />
      </div>

      <q-slide-transition>
        <div v-show="showRatingsSection">
          <!-- Add/Update Rating -->
          <div class="q-mb-md">
            <div class="row items-center">
              <div class="col-12 text-center">
                <q-rating v-model="currentUserRating"
                          size="2em"
                          color="amber"
                          icon="star_border"
                          icon-selected="star"
                          @update:model-value="handleRatingChange"
                          :max="5" />
              </div>
            </div>
          </div>

          <!-- Display Ratings Summary -->
          <q-separator class="q-mb-sm" />
          <div v-if="isLoadingRatings"
               class="text-center q-my-md">
            <q-spinner color="primary"
                       size="2em" />
            <div>Loading ratings...</div>
          </div>
          <div v-else-if="ratingsError"
               class="text-negative q-my-md text-center">
            <q-icon name="warning"
                    class="q-mr-sm" /> {{ ratingsError }}
          </div>
          <div v-else-if="ratings.length > 0"
               class="q-pa-md">
            <div class="text-subtitle2">Rating Summary</div>
            <div class="row items-center q-mt-sm">
              <div class="col-6">
                <div class="text-h6">{{(ratings.reduce((sum, r) => sum + r.rating, 0) / ratings.length).toFixed(1)}}
                </div>
                <div class="text-caption">Average rating</div>
              </div>
              <div class="col-6 text-right">
                <div class="text-h6">{{ ratings.length }}</div>
                <div class="text-caption">Total ratings</div>
              </div>
            </div>
          </div>
          <div v-else
               class="text-grey q-my-md text-center">
            No ratings yet. Be the first to rate this photo!
          </div>
        </div>
      </q-slide-transition>
    </div>

    <!-- Comments Section -->
    <div class="q-mt-md q-pa-sm comments-section">
      <div class="row items-center justify-between q-mb-sm">
        <div class="text-subtitle1">Comments ({{ comments.length }})</div>
        <q-btn flat
               dense
               round
               :icon="showCommentsSection ? 'expand_less' : 'expand_more'"
               @click="showCommentsSection = !showCommentsSection"
               aria-label="Toggle comments section" />
      </div>

      <q-slide-transition>
        <div v-show="showCommentsSection">
          <!-- Add Comment Input -->
          <div class="q-mb-md">
            <q-input v-model="newCommentText"
                     filled
                     autogrow
                     label="Add a comment..."
                     dense
                     :rules="[val => !!val && val.trim().length > 0 || 'Comment cannot be empty']"
                     hide-bottom-space>
              <template v-slot:append>
                <q-btn round
                       dense
                       flat
                       icon="send"
                       @click="handleAddComment"
                       :disable="!newCommentText || !newCommentText.trim()"
                       aria-label="Post comment" />
              </template>
            </q-input>
          </div>

          <!-- Display Comments -->
          <q-separator v-if="comments.length > 0"
                       class="q-mb-sm" />
          <div v-if="isLoadingComments"
               class="text-center q-my-md">
            <q-spinner color="primary"
                       size="2em" />
            <div>Loading comments...</div>
          </div>
          <div v-else-if="commentsError"
               class="text-negative q-my-md text-center">
            <q-icon name="warning"
                    class="q-mr-sm" /> {{ commentsError }}
          </div>
          <q-list v-else-if="comments.length > 0"
                  dense
                  separator>
            <q-item v-for="comment in comments"
                    :key="comment.id"
                    class="q-py-sm comment-item">
              <q-item-section>
                <q-item-label class="comment-text">{{ comment.text }}</q-item-label>
                <q-item-label caption
                              class="comment-meta">
                  Added {{ comment.user && (comment.user.full_name || comment.user.name) ? 'by ' +
                    (comment.user.full_name ||
                      comment.user.name) : 'by User' }}
                  on {{ formatDate(comment.createdAt) }}
                </q-item-label>
              </q-item-section>
              <q-item-section side
                              top>
                <q-btn flat
                       round
                       dense
                       icon="delete_outline"
                       color="grey-7"
                       @click="confirmDeleteComment(comment.id)"
                       size="sm"
                       aria-label="Delete comment" />
              </q-item-section>
            </q-item>
          </q-list>
          <div v-else
               class="text-grey q-my-md text-center">
            No comments yet. Be the first to comment!
          </div>
        </div>
      </q-slide-transition>
    </div>


    <!-- Full Screen Dialog -->
    <q-dialog v-model="showFullScreen"
              full-width
              full-height>
      <q-card class="bg-black text-white"
              style="height: 100%; width: 100%; position: relative;">
        <q-img :src="image.image_details.url"
               style="height: 100%; width: 100%;"
               fit="contain" />
        <q-btn flat
               round
               icon="close"
               class="close-btn"
               @click="showFullScreen = false" />
      </q-card>
    </q-dialog>

    <!-- Delete Image Confirmation Dialog (existing) -->
    <q-dialog v-model="showDeleteConfirm"
              persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning"
                    color="red"
                    text-color="white" />
          <span class="q-ml-sm">Are you sure you want to delete this image?</span>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat
                 label="Cancel"
                 color="primary"
                 v-close-popup />
          <q-btn flat
                 label="Delete"
                 color="red"
                 @click="confirmDeleteImage"
                 v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

    <!-- Delete Comment Confirmation Dialog -->
    <q-dialog v-model="showDeleteCommentConfirm"
              persistent>
      <q-card>
        <q-card-section class="row items-center">
          <q-avatar icon="warning"
                    color="orange"
                    text-color="white" />
          <span class="q-ml-sm">Are you sure you want to delete this comment?</span>
        </q-card-section>
        <q-card-actions align="right">
          <q-btn flat
                 label="Cancel"
                 color="primary"
                 @click="showDeleteCommentConfirm = false" />
          <q-btn flat
                 label="Delete"
                 color="red"
                 @click="executeDeleteComment"
                 v-close-popup />
        </q-card-actions>
      </q-card>
    </q-dialog>

  </div>
</template>
<script>
import { ref, watch, computed } from 'vue' // Removed onMounted for this optimization
import { useQuasar } from 'quasar'
import usePhotoEdit from "src/compose/usePhotoEdit.js"
import { usePhotoComments } from 'src/concerns/dossiers/composables/usePhotoComments'
import { usePhotoRatings } from 'src/concerns/dossiers/composables/usePhotoRatings'

export default {
  name: 'ImageItem', // Was ViewOnlyImageItem, consistency is good
  props: {
    image: {
      type: Object,
      required: true
    },
    dossierUuid: {
      type: String,
      required: true
    },
    assetsComparisonUuid: {
      type: String,
      default: null
    },
    editMode: { /* ... */ },
    selected: { /* ... */ },
    allDossierJots: { // New prop for prefetched comments/jots
      type: Array,
      default: null // Default to null, indicating they might need to be fetched individually
    },
    allDossierRatings: { // New prop for prefetched ratings
      type: Array,
      default: null // Default to null, indicating they might need to be fetched individually
    }
  },
  setup(props, { emit }) {
    const $q = useQuasar();
    const { discardPhoto, updatePhotoDetails } = usePhotoEdit();

    const dossierUuidRef = computed(() => props.dossierUuid);
    // const dossierUuidRef = computed(() => props.dossierUuid);
    const photoUuidRef = computed(() => props.image.uuid);
    const allDossierJotsRef = computed(() => props.allDossierJots); // Pass as a ref
    const allDossierRatingsRef = computed(() => props.allDossierRatings); // Pass as a ref

    const {
      comments,
      isLoadingComments,
      commentsError,
      loadIndividualPhotoComments, // We might call this if allDossierJots is null
      addPhotoComment,
      deletePhotoComment,
      formatDate
    } = usePhotoComments(props.assetsComparisonUuid, dossierUuidRef, photoUuidRef, $q, allDossierJotsRef);

    const {
      ratings,
      currentUserRating,
      isLoadingRatings,
      ratingsError,
      loadIndividualPhotoRatings,
      addOrUpdatePhotoRating,
      deletePhotoRating
    } = usePhotoRatings(props.assetsComparisonUuid, dossierUuidRef, photoUuidRef, $q, allDossierRatingsRef);

    const localImage = ref({ ...props.image });
    // ... other refs (hasChanges, showFullScreen, etc.) remain the same ...
    const hasChanges = ref(false);
    const showFullScreen = ref(false);



    const newCommentText = ref('');
    const showCommentsSection = ref(true);
    const showRatingsSection = ref(true);
    const showDeleteConfirm = ref(false);
    const showDeleteCommentConfirm = ref(false);
    const commentToDeleteId = ref(null);


    // If allDossierJots is not provided by the parent (gallery),
    // then this item might need to load its own comments.
    // The watch in usePhotoComments handles filtering if allDossierJotsRef is provided.
    // This explicit call is a fallback.
    if (!props.allDossierJots && dossierUuidRef.value && photoUuidRef.value) {
      // console.log(`ImageItem ${photoUuidRef.value}: No pre-fetched jots, attempting individual load.`);
      // loadIndividualPhotoComments(); // Call it on setup if no master list
    }
    // Watch for props.allDossierJots changing from null to an array,
    // in case the gallery loads them asynchronously after this component is mounted.
    // The watch within usePhotoComments on allDossierJotsRef should handle this already.

    watch(() => props.image, (newVal) => {
      localImage.value = { ...newVal };
      // checkChanges(); // checkChanges is likely related to title/desc, not comments directly
    }, { deep: true });


    const handleAddComment = async () => {
      if (!newCommentText.value || !newCommentText.value.trim()) return;
      const added = await addPhotoComment(newCommentText.value);
      if (added) {
        newCommentText.value = '';
        // If comments were managed centrally, gallery might need to refresh its master list.
        // The composable now tries to update the master list if provided.
        // Or emit an event to tell the gallery to refresh:
        // emit('comment-added', { photoUuid: photoUuidRef.value, comment: added });
      }
    };

    const confirmDeleteComment = (commentId) => { /* ... same ... */
      commentToDeleteId.value = commentId;
      showDeleteCommentConfirm.value = true;
    };

    const executeDeleteComment = async () => {
      if (commentToDeleteId.value) {
        await deletePhotoComment(commentToDeleteId.value);
        // emit('comment-deleted', { photoUuid: photoUuidRef.value, commentId: commentToDeleteId.value });
      }
      // ... same ...
      showDeleteCommentConfirm.value = false;
      commentToDeleteId.value = null;
    };

    const handleRatingChange = async (newRating) => {
      await addOrUpdatePhotoRating(newRating);
    };

    // ... rest of the setup (checkChanges, updateImage, saveChanges, etc.) ...
    const checkChanges = () => {
      const originalTitle = props.image.photo_title || '';
      const originalDesc = props.image.photo_description || '';
      const newTitle = localImage.value.photo_title || '';
      const newDesc = localImage.value.photo_description || '';
      hasChanges.value = originalTitle !== newTitle || originalDesc !== newDesc;
    };

    const updateImage = () => {
      if (hasChanges.value) {
        saveChanges();
      }
    };

    const saveChanges = async () => {
      try {
        const response = await updatePhotoDetails({
          picUuid: localImage.value.uuid,
          photo_title: localImage.value.photo_title,
          photo_description: localImage.value.photo_description,
        });
        const updatedImage = response.data;
        emit('update-image', updatedImage);
        localImage.value = { ...updatedImage };
        hasChanges.value = false;
        $q.notify({ type: 'positive', message: 'Image details saved successfully' });
      } catch (error) {
        console.error('Error saving image:', error);
        $q.notify({ type: 'negative', message: 'Failed to save image details' });
      }
    };

    const cancelChanges = () => {
      localImage.value = { ...props.image };
      hasChanges.value = false;
    };

    const confirmDeleteImage = () => {
      let updateObject = {
        picUuid: localImage.value.uuid,
      };
      discardPhoto(updateObject)
        .then(() => {
          emit('hide-image', props.image.uuid);
          $q.notify({ type: 'positive', message: 'Image deleted' });
        })
        .catch(error => {
          console.error('Error deleting image:', error);
          $q.notify({ type: 'negative', message: 'Failed to delete image' });
        });
      showDeleteConfirm.value = false;
    };
    const isSelected = computed(() => props.selected);
    const photoDesc = computed(() => {
      return props.image.photo_description ||
        props.image.raw_ai_analysis?.general_photo_analysis?.photo_general_description ||
        'No description available';
    });
    const tags = computed(() => {
      return props.image.realty_asset_photo_tags?.length
        ? props.image.realty_asset_photo_tags
        : props.image.raw_ai_analysis?.general_photo_analysis?.photo_tags || [];
    });


    return {
      localImage,
      hasChanges,
      showFullScreen,
      showDeleteConfirm,
      isSelected,
      photoDesc,
      tags,
      checkChanges,
      updateImage,
      saveChanges,
      cancelChanges,
      confirmDeleteImage,
      // Comments
      comments,
      isLoadingComments,
      commentsError,
      newCommentText,
      showCommentsSection,
      showDeleteCommentConfirm,
      commentToDeleteId,
      formatDate,
      handleAddComment,
      confirmDeleteComment,
      executeDeleteComment,
      // Ratings
      ratings,
      currentUserRating,
      isLoadingRatings,
      ratingsError,
      showRatingsSection,
      handleRatingChange,
    };
  },
}
</script>
<style>
.q-dialog__inner--minimized {
  padding: 0px;
}
</style>
<style scoped>
.view-only-image-container {
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 16px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.image-container {
  position: relative;
}

.image-wrapper {
  position: relative;
  background-color: #f0f0f0;
}

.image {
  transition: opacity 0.3s;
  cursor: pointer;
  display: block;
  width: 100%;
  height: auto;
  max-height: 500px;
  object-fit: cover;
}

.image:hover {
  opacity: 0.9;
}

.selected {
  border: 3px solid var(--q-color-primary, red);
  box-shadow: 0 0 10px var(--q-color-primary- Guzman, rgba(255, 0, 0, 0.5));
}

.fullscreen-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
}

.delete-btn {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.6);
}

.close-btn {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  z-index: 10;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
}

.text-overlay {
  background-color: rgba(0, 0, 0, 0.5);
  padding: 8px;
}

.ratings-section,
.comments-section {
  border-top: 1px solid #e0e0e0;
}

.comment-item {
  padding-top: 8px;
  padding-bottom: 8px;
}

.comment-text {
  word-break: break-word;
  white-space: pre-wrap;
}

.comment-meta {
  font-size: 0.75rem;
  color: #757575;
}

@media (max-width: 600px) {
  .mobile-hide {
    display: none !important;
  }
}

@media (min-width: 601px) {
  .mobile-only {
    display: none !important;
  }
}
</style>