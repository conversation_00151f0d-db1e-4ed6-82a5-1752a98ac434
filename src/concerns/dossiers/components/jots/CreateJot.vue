<template>
  <div class="create-note-query-container q-pa-md">
    <div class="text-h5 q-mb-md">Create New Note</div>

    <!-- Note Input Form -->
    <div class="row q-mb-md">
      <div class="col-12">
        <q-input v-model="newNoteOrQueryText"
                 filled
                 label="Note content"
                 type="textarea"
                 autogrow
                 class="q-mb-sm"
                 :error="!!error"
                 :error-message="error" />
      </div>
    </div>

    <!-- Association Selection -->
    <div class="row q-mb-md">
      <div class="col-12">
        <q-select v-model="itemAssociation"
                  :options="associationOptions"
                  label="Associate note with"
                  outlined
                  emit-value
                  map-options />
      </div>
    </div>

    <!-- Comparison Selection -->
    <div class="row q-mb-md"
         v-if="
          dossierAssetsComparisons.length > 0 && itemAssociation === 'comparison'
        ">
      <div class="col-12">
        <q-select v-model="selectedComparison"
                  :options="comparisonOptions"
                  label="Select comparison"
                  outlined
                  emit-value
                  map-options />
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="row q-mt-lg">
      <div class="col-12">
        <div class="flex justify-between">
          <q-btn flat
                 color="primary"
                 label="Cancel"
                 @click="$router.push({ name: 'rNotesAndQueries' })" />
          <q-btn color="primary"
                 label="Create Note"
                 @click="handleCreateNoteOrQuery"
                 :disable="!newNoteOrQueryText ||
                  (itemAssociation === 'comparison' && !selectedComparison)
                  " />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue'
import { useQuasar } from 'quasar'
import { useRouter } from 'vue-router'
// Ensure this path and composable name match your actual implementation
import { useDossierNotesAndQueries } from 'src/concerns/dossiers/composables/useDossierNotesAndQueries'

export default defineComponent({
  name: 'CreateNoteOrQuery', // Changed component name

  props: {
    saleListing: {
      type: Object,
      required: true,
    },
    realtyDossier: {
      type: Object,
      required: true,
    },
  },

  setup(props) {
    const $q = useQuasar()
    const $router = useRouter()
    const newNoteOrQueryText = ref('') // Renamed from newTask
    const itemAssociation = ref('primary') // Renamed from taskAssociation
    const selectedComparison = ref(null)

    // Updated to use the new composable and function name
    const { error, addNoteOrQuery } = useDossierNotesAndQueries(props.realtyDossier, $q)

    const dossierAssetsComparisons = computed(() => {
      return props.realtyDossier?.dossier_assets_comparisons || []
    })

    const associationOptions = computed(() => {
      const options = [{ label: 'Northstar Property', value: 'primary' }]
      if (dossierAssetsComparisons.value.length > 0) {
        options.push({ label: 'Property Comparison', value: 'comparison' })
      }
      return options
    })

    const comparisonOptions = computed(() => {
      return dossierAssetsComparisons.value.map((comparison) => ({
        label:
          comparison.right_side_property?.street_address ||
          comparison.right_side_property?.title ||
          'Unnamed Property',
        value: comparison.uuid,
      }))
    })

    // Renamed and updated to use addNoteOrQuery
    const handleCreateNoteOrQuery = async () => {
      const isPrimary = itemAssociation.value === 'primary'
      const comparisonId = isPrimary ? null : selectedComparison.value

      // Assuming addNoteOrQuery doesn't need dueDate or pictures from this form initially
      try {
        const newItem = await addNoteOrQuery(
          newNoteOrQueryText.value,
          comparisonId,
          isPrimary
          // If your addNoteOrQuery expects pictures, pass an empty array or handle picture uploads here
          // [] // example for pictures if needed
        )

        if (newItem && newItem.id) {
          $q.notify({
            color: 'positive',
            message: 'Note created successfully', // Updated message
            icon: 'check',
          })

          // Reset form
          newNoteOrQueryText.value = ''
          itemAssociation.value = 'primary'
          selectedComparison.value = null

          // Navigate to the new item's details page (adjust route name as needed)
          $router.push({ name: 'rNotesAndQueries', params: { itemId: newItem.id } })
        } else {
          // This case might occur if addNoteOrQuery resolves without a proper newItem or if an error was handled internally
          // and 'error' ref in composable was not set, or if it resolved with null/undefined.
          // The composable's error ref should ideally catch API errors.
          if (!error.value) { // If composable didn't set an error, show a generic one
            $q.notify({
              color: 'negative',
              message: 'Failed to create note. Please try again.',
              icon: 'warning',
            })
          }
          // If error.value is set by the composable, the q-input will display it.
        }
      } catch (e) {
        // This catch block handles errors thrown by addNoteOrQuery if it doesn't handle them internally
        // and set the 'error' ref. The composable is designed to set 'error.value'.
        console.error("Error during note creation:", e)
        if (!error.value) { // If composable's error ref isn't set by the thrown error
          $q.notify({
            color: 'negative',
            message: e.message || 'An unexpected error occurred while creating the note.',
            icon: 'warning',
          })
        }
      }
    }

    return {
      newNoteOrQueryText, // Renamed
      itemAssociation,    // Renamed
      selectedComparison,
      error,
      associationOptions,
      comparisonOptions,
      dossierAssetsComparisons,
      handleCreateNoteOrQuery, // Renamed
    }
  },
})
</script>

<style scoped>
/* Updated CSS class name */
.create-note-query-container {
  max-width: 600px;
  margin: 0 auto;
}
</style>