<template>
  <div class="create-note-query-container q-pa-md">
    <div class="text-h5 q-mb-md">Create New Note</div>

    <!-- Pre-selection Info -->
    <div v-if="preSelectProperty"
         class="pre-selection-info q-mb-md">
      <q-banner class="bg-blue-1 text-primary"
                rounded>
        <template v-slot:avatar>
          <q-icon name="info"
                  color="primary" />
        </template>
        <div class="text-body1">
          <strong>Creating note for:</strong>
          <span v-if="preSelectProperty === 'primary'">Primary Property (Northstar)</span>
          <span v-else-if="preSelectProperty === 'comparison'">{{ selectedComparisonName }}</span>
        </div>
        <template v-slot:action>
          <q-btn flat
                 color="primary"
                 label="Change"
                 @click="clearPreSelection" />
        </template>
      </q-banner>
    </div>

    <!-- Note Input Form -->
    <div class="row q-mb-md">
      <div class="col-12">
        <q-input v-model="newNoteOrQueryText"
                 filled
                 label="Note content"
                 type="textarea"
                 autogrow
                 class="q-mb-sm"
                 :error="!!error"
                 :error-message="error" />
      </div>
    </div>

    <!-- Association Selection -->
    <div class="row q-mb-md"
         v-if="!preSelectProperty">
      <div class="col-12">
        <q-select v-model="itemAssociation"
                  :options="associationOptions"
                  label="Associate note with"
                  outlined
                  emit-value
                  map-options />
      </div>
    </div>

    <!-- Comparison Selection -->
    <div class="row q-mb-md"
         v-if="dossierAssetsComparisons.length > 0 && itemAssociation === 'comparison' && !preSelectProperty">
      <div class="col-12">
        <q-select v-model="selectedComparison"
                  :options="comparisonOptions"
                  label="Select comparison"
                  outlined
                  emit-value
                  map-options />
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="row q-mt-lg">
      <div class="col-12">
        <div class="flex justify-between">
          <q-btn flat
                 color="primary"
                 label="Cancel"
                 @click="handleCancel" />
          <q-btn color="primary"
                 label="Create Note"
                 @click="handleCreateNoteOrQuery"
                 :disable="!newNoteOrQueryText ||
                  (itemAssociation === 'comparison' && !selectedComparison)
                  " />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'
import { useRouter, useRoute } from 'vue-router'
// Ensure this path and composable name match your actual implementation
import { useDossierNotesAndQueries } from 'src/concerns/dossiers/composables/useDossierNotesAndQueries'

export default defineComponent({
  name: 'CreateNoteOrQuery', // Changed component name

  props: {
    saleListing: {
      type: Object,
      required: true,
    },
    realtyDossier: {
      type: Object,
      required: true,
    },
    comparisonUuid: {
      type: String,
      required: false,
    },
  },

  setup(props) {
    const $q = useQuasar()
    const $router = useRouter()
    const $route = useRoute()
    const newNoteOrQueryText = ref('') // Renamed from newTask
    const itemAssociation = ref('primary') // Renamed from taskAssociation
    const selectedComparison = ref(null)

    // Updated to use the new composable and function name
    const { error, addNoteOrQuery } = useDossierNotesAndQueries(props.realtyDossier, $q)

    const dossierAssetsComparisons = computed(() => {
      return props.realtyDossier?.dossier_assets_comparisons || []
    })

    // Route-based pre-selection logic
    const preSelectProperty = computed(() => {
      return $route.meta?.preSelectProperty || null
    })

    const preSelectedComparisonUuid = computed(() => {
      return props.comparisonUuid || $route.params.comparisonUuid
    })

    // Initialize form based on route
    const initializeForm = () => {
      if (preSelectProperty.value === 'primary') {
        itemAssociation.value = 'primary'
        selectedComparison.value = null
      } else if (preSelectProperty.value === 'comparison' && preSelectedComparisonUuid.value) {
        itemAssociation.value = 'comparison'
        selectedComparison.value = preSelectedComparisonUuid.value
      }
    }

    // Watch for route changes to re-initialize form
    watch(() => $route.fullPath, () => {
      initializeForm()
    }, { immediate: true })

    // Watch for prop changes
    watch(() => props.comparisonUuid, (newUuid) => {
      if (newUuid && preSelectProperty.value === 'comparison') {
        selectedComparison.value = newUuid
      }
    }, { immediate: true })

    const associationOptions = computed(() => {
      const options = [{ label: 'Northstar Property', value: 'primary' }]
      if (dossierAssetsComparisons.value.length > 0) {
        options.push({ label: 'Property Comparison', value: 'comparison' })
      }
      return options
    })

    const comparisonOptions = computed(() => {
      return dossierAssetsComparisons.value.map((comparison) => ({
        label:
          comparison.right_side_property?.street_address ||
          comparison.right_side_property?.title ||
          'Unnamed Property',
        value: comparison.uuid,
      }))
    })

    // Get the selected comparison name for display
    const selectedComparisonName = computed(() => {
      if (!selectedComparison.value) return ''
      const comparison = dossierAssetsComparisons.value.find(c => c.uuid === selectedComparison.value)
      return comparison?.right_side_property?.street_address ||
        comparison?.right_side_property?.title ||
        'Unnamed Property'
    })

    // Renamed and updated to use addNoteOrQuery
    const handleCreateNoteOrQuery = async () => {
      const isPrimary = itemAssociation.value === 'primary'
      const comparisonId = isPrimary ? null : selectedComparison.value

      // Assuming addNoteOrQuery doesn't need dueDate or pictures from this form initially
      try {
        const newItem = await addNoteOrQuery(
          newNoteOrQueryText.value,
          comparisonId,
          isPrimary
          // If your addNoteOrQuery expects pictures, pass an empty array or handle picture uploads here
          // [] // example for pictures if needed
        )

        if (newItem && newItem.id) {
          $q.notify({
            color: 'positive',
            message: 'Note created successfully', // Updated message
            icon: 'check',
          })

          // Reset form
          newNoteOrQueryText.value = ''
          itemAssociation.value = 'primary'
          selectedComparison.value = null

          // Navigate back to the appropriate notes view based on what was created
          if (isPrimary) {
            $router.push({ name: 'rNotesAndQueriesPrimary' })
          } else if (comparisonId) {
            $router.push({ name: 'rNotesAndQueriesComparison', params: { comparisonUuid: comparisonId } })
          } else {
            $router.push({ name: 'rNotesAndQueries' })
          }
        } else {
          // This case might occur if addNoteOrQuery resolves without a proper newItem or if an error was handled internally
          // and 'error' ref in composable was not set, or if it resolved with null/undefined.
          // The composable's error ref should ideally catch API errors.
          if (!error.value) { // If composable didn't set an error, show a generic one
            $q.notify({
              color: 'negative',
              message: 'Failed to create note. Please try again.',
              icon: 'warning',
            })
          }
          // If error.value is set by the composable, the q-input will display it.
        }
      } catch (e) {
        // This catch block handles errors thrown by addNoteOrQuery if it doesn't handle them internally
        // and set the 'error' ref. The composable is designed to set 'error.value'.
        console.error("Error during note creation:", e)
        if (!error.value) { // If composable's error ref isn't set by the thrown error
          $q.notify({
            color: 'negative',
            message: e.message || 'An unexpected error occurred while creating the note.',
            icon: 'warning',
          })
        }
      }
    }

    // Clear pre-selection and navigate to general create form
    const clearPreSelection = () => {
      $router.push({ name: 'rCreateNoteOrQuery' })
    }

    // Determine the appropriate cancel navigation
    const handleCancel = () => {
      if (preSelectProperty.value === 'primary') {
        $router.push({ name: 'rNotesAndQueriesPrimary' })
      } else if (preSelectProperty.value === 'comparison' && preSelectedComparisonUuid.value) {
        $router.push({ name: 'rNotesAndQueriesComparison', params: { comparisonUuid: preSelectedComparisonUuid.value } })
      } else {
        $router.push({ name: 'rNotesAndQueries' })
      }
    }

    return {
      // Form data
      newNoteOrQueryText,
      itemAssociation,
      selectedComparison,
      error,

      // Options
      associationOptions,
      comparisonOptions,
      dossierAssetsComparisons,

      // Pre-selection
      preSelectProperty,
      preSelectedComparisonUuid,
      selectedComparisonName,

      // Methods
      handleCreateNoteOrQuery,
      clearPreSelection,
      handleCancel,
    }
  },
})
</script>

<style scoped>
/* Updated CSS class name */
.create-note-query-container {
  max-width: 600px;
  margin: 0 auto;
}
</style>