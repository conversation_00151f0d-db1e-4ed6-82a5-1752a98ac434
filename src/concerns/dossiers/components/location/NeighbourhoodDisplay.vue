<template>
  <q-page padding>
    <div v-if="!neighbourhoods || neighbourhoods.length === 0"
         class="text-center q-pa-md">
      <q-icon name="fas fa-info-circle"
              size="lg"
              class="q-mr-sm" />
      No neighbourhood data available.
    </div>

    <div v-else
         class="q-gutter-y-lg">
      <q-card v-for="hood in neighbourhoods"
              :key="hood.id"
              flat
              bordered
              class="neighbourhood-card">
        <q-card-section class="bg-primary text-white q-pa-md">
          <div class="row items-center no-wrap">
            <div class="col">
              <div class="text-h5">{{ hood.contxt_postcode }}</div>
              <div class="text-subtitle1">{{ hood.location_details?.town_city || hood.location_summary?.postcode ||
                'N/A' }}</div>
            </div>
            <q-btn flat
                   round
                   dense
                   icon="fas fa-external-link-alt"
                   :href="hood.record_source_url"
                   target="_blank"
                   aria-label="View source data">
              <q-tooltip>View original data source</q-tooltip>
            </q-btn>
          </div>
        </q-card-section>

        <q-separator />

        <q-card-section>
          <div class="text-h6 q-mb-sm">Location Summary</div>
          <p v-if="hood.location_summary?.full_text">{{ hood.location_summary.full_text }}</p>
          <q-chip dense
                  outline
                  color="secondary"
                  icon="fas fa-map-marker-alt">
            {{ hood.location_details?.street || 'N/A' }}, {{ hood.location_details?.locality || 'N/A' }}
          </q-chip>
          <q-chip dense
                  outline
                  color="secondary"
                  icon="fas fa-globe-europe">
            {{ hood.location_details?.country || 'N/A' }}
          </q-chip>
        </q-card-section>

        <!-- Maps -->
        <q-expansion-item v-if="hood.maps && hood.maps.length > 0"
                          icon="fas fa-map"
                          label="Maps"
                          header-class="text-primary"
                          default-opened>
          <q-card-section class="q-pt-none">
            <div class="row q-col-gutter-md">
              <div v-for="(map, index) in hood.maps"
                   :key="index"
                   class="col-12 col-md-6">
                <q-img :src="map.src"
                       :alt="map.alt"
                       spinner-color="primary"
                       style="max-height: 300px; border-radius: 4px;">
                  <div class="absolute-bottom text-subtitle2 text-center">
                    {{ map.title }}
                  </div>
                </q-img>
              </div>
            </div>
          </q-card-section>
        </q-expansion-item>

        <!-- Nearest Postcodes -->
        <q-expansion-item v-if="hood.nearest_postcodes && hood.nearest_postcodes.length > 0"
                          icon="fas fa-route"
                          label="Nearest Postcodes"
                          header-class="text-primary">
          <q-list bordered
                  separator>
            <q-item v-for="np in hood.nearest_postcodes"
                    :key="np.postcode"
                    clickable
                    v-ripple
                    :href="`https://www.getthedata.com${np.url}`"
                    target="_blank">
              <q-item-section avatar>
                <q-icon color="secondary"
                        name="fas fa-map-pin" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ np.postcode }}</q-item-label>
                <q-item-label caption>{{ np.street_or_locality || 'N/A' }}</q-item-label>
              </q-item-section>
              <q-item-section side>
                <q-chip dense
                        outline
                        color="info">{{ np.distance }}</q-chip>
              </q-item-section>
            </q-item>
          </q-list>
        </q-expansion-item>

        <!-- Broadband -->
        <q-expansion-item v-if="hood.broadband"
                          icon="fas fa-wifi"
                          label="Broadband Overview"
                          header-class="text-primary">
          <q-card-section>
            <div class="text-subtitle1 q-mb-sm">Access</div>
            <div v-if="hood.broadband.access">
              <div v-for="(value, key) in hood.broadband.access"
                   :key="key"
                   class="q-mb-xs">
                <q-item-label caption
                              class="text-capitalize">{{ key.replace(/_/g, ' ') }}</q-item-label>
                <q-linear-progress rounded
                                   size="20px"
                                   :value="parseFloat(value) / 100"
                                   color="accent"
                                   class="q-mt-xs">
                  <div class="absolute-full flex flex-center">
                    <q-badge color="white"
                             text-color="accent"
                             :label="value" />
                  </div>
                </q-linear-progress>
              </div>
            </div>
            <div v-else
                 class="text-caption text-grey">Access data not available.</div>

            <div class="text-subtitle1 q-mt-md q-mb-sm">Speeds</div>
            <div v-if="hood.broadband.speed"
                 class="row q-col-gutter-md">
              <div class="col-12 col-sm-6">
                <strong>Download:</strong>
                <ul class="q-pl-md">
                  <li>Median: {{ hood.broadband.speed.download?.median_download_speed || 'N/A' }}</li>
                  <li>Average: {{ hood.broadband.speed.download?.average_download_speed || 'N/A' }}</li>
                  <li>Max: {{ hood.broadband.speed.download?.maximum_download_speed || 'N/A' }}</li>
                </ul>
              </div>
              <div class="col-12 col-sm-6">
                <strong>Upload:</strong>
                <ul class="q-pl-md">
                  <li>Median: {{ hood.broadband.speed.upload?.median_upload_speed || 'N/A' }}</li>
                  <li>Average: {{ hood.broadband.speed.upload?.average_upload_speed || 'N/A' }}</li>
                  <li>Max: {{ hood.broadband.speed.upload?.maximum_upload_speed || 'N/A' }}</li>
                </ul>
              </div>
            </div>
            <div v-else
                 class="text-caption text-grey">Speed data not available.</div>
          </q-card-section>
        </q-expansion-item>

        <!-- Energy Consumption -->
        <q-expansion-item v-if="hood.energy_consumption && hood.energy_consumption.electricity"
                          icon="fas fa-bolt"
                          label="Energy Consumption (Electricity)"
                          header-class="text-primary">
          <q-card-section>
            <q-list dense>
              <q-item>
                <q-item-section avatar><q-icon name="fas fa-plug" /></q-item-section>
                <q-item-section>Total Consumption</q-item-section>
                <q-item-section side>{{ hood.energy_consumption.electricity.consumption_kwh }} kWh</q-item-section>
              </q-item>
              <q-item>
                <q-item-section avatar><q-icon name="fas fa-tachometer-alt" /></q-item-section>
                <q-item-section>Meter Count</q-item-section>
                <q-item-section side>{{ hood.energy_consumption.electricity.meter_count }}</q-item-section>
              </q-item>
              <q-item>
                <q-item-section avatar><q-icon name="fas fa-balance-scale-right" /></q-item-section>
                <q-item-section>Mean kWh/meter</q-item-section>
                <q-item-section side>{{ hood.energy_consumption.electricity['mean_kwh/meter'] }}</q-item-section>
              </q-item>
              <q-item>
                <q-item-section avatar><q-icon name="fas fa-chart-line" /></q-item-section>
                <q-item-section>Median kWh/meter</q-item-section>
                <q-item-section side>{{ hood.energy_consumption.electricity['median_kwh/meter'] }}</q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </q-expansion-item>

        <!-- Transport -->
        <q-expansion-item v-if="(hood.transport?.bus_stops && hood.transport.bus_stops.length > 0) || (hood.transport?.railway_stations && hood.transport.railway_stations.length > 0)"
                          icon="fas fa-bus-alt"
                          label="Transport Links"
                          header-class="text-primary">
          <q-card-section>
            <div v-if="hood.transport?.bus_stops && hood.transport.bus_stops.length > 0">
              <div class="text-subtitle1 q-mb-xs">Bus Stops</div>
              <q-list bordered
                      separator
                      dense>
                <q-item v-for="(stop, i) in hood.transport.bus_stops"
                        :key="`bus-${i}`">
                  <q-item-section avatar><q-icon color="info"
                            name="fas fa-bus" /></q-item-section>
                  <q-item-section>
                    <q-item-label>{{ stop.name }}</q-item-label>
                    <q-item-label caption>{{ stop.location }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>{{ stop.distance }}</q-item-section>
                </q-item>
              </q-list>
            </div>

            <div v-if="hood.transport?.railway_stations && hood.transport.railway_stations.length > 0"
                 class="q-mt-md">
              <div class="text-subtitle1 q-mb-xs">Railway Stations</div>
              <q-list bordered
                      separator
                      dense>
                <q-item v-for="(station, i) in hood.transport.railway_stations"
                        :key="`rail-${i}`">
                  <q-item-section avatar><q-icon color="info"
                            name="fas fa-train" /></q-item-section>
                  <q-item-section>
                    <q-item-label>{{ station.name }}</q-item-label>
                  </q-item-section>
                  <q-item-section side>{{ station.distance }}</q-item-section>
                </q-item>
              </q-list>
            </div>
          </q-card-section>
        </q-expansion-item>

        <!-- Food Standards Ratings -->
        <q-expansion-item v-if="hood.food_standards_ratings && hood.food_standards_ratings.length > 0"
                          icon="fas fa-utensils"
                          label="Food Standards Ratings"
                          header-class="text-primary">
          <q-list bordered
                  separator>
            <q-item v-for="(food, i) in hood.food_standards_ratings"
                    :key="`food-${i}`">
              <q-item-section avatar>
                <q-icon :name="getRatingIcon(food.rating)"
                        :color="getRatingColor(food.rating)"
                        size="md" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ food.name }}</q-item-label>
                <q-item-label caption>{{ food.address }}</q-item-label>
                <q-item-label caption>Distance: {{ food.distance }}</q-item-label>
              </q-item-section>
              <q-item-section side
                              top>
                <q-chip dense
                        :color="getRatingColor(food.rating)"
                        text-color="white">
                  {{ food.rating.replace('Food Hygiene Rating: ', '') }}
                </q-chip>
              </q-item-section>
            </q-item>
          </q-list>
        </q-expansion-item>

        <!-- House Prices -->
        <q-expansion-item v-if="hood.house_prices && hood.house_prices.length > 0"
                          icon="fas fa-home"
                          label="Recent House Sales"
                          header-class="text-primary">
          <q-list bordered
                  separator>
            <q-item v-for="(sale, i) in hood.house_prices"
                    :key="`sale-${i}`">
              <q-item-section avatar>
                <q-icon color="green-7"
                        name="fas fa-pound-sign" />
              </q-item-section>
              <q-item-section>
                <q-item-label class="text-weight-medium">{{ sale.price }}</q-item-label>
                <q-item-label caption
                              lines="2">{{ sale.address }}</q-item-label>
              </q-item-section>
              <q-item-section side
                              top>
                <q-item-label caption>{{ sale.date_sold }} {{ sale.year || new Date().getFullYear() /* Approx if year
                  null */ }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </q-expansion-item>

        <!-- Other Details -->
        <q-expansion-item icon="fas fa-ellipsis-h"
                          label="Other Details"
                          header-class="text-grey-7">
          <q-card-section>
            <q-list dense>
              <q-item v-if="hood.geodata">
                <q-item-section avatar><q-icon name="fas fa-compass"
                          color="teal" /></q-item-section>
                <q-item-section>
                  <q-item-label>Geodata</q-item-label>
                  <q-item-label caption>Lat: {{ hood.geodata.latitude || 'N/A' }}, Lon: {{ hood.geodata.longitude ||
                    'N/A' }}</q-item-label>
                  <q-item-label caption>Easting: {{ hood.geodata.easting || 'N/A' }}, Northing: {{ hood.geodata.northing
                    || 'N/A' }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item v-if="hood.elevation && (hood.elevation.metres || hood.elevation.feet)">
                <q-item-section avatar><q-icon name="fas fa-mountain"
                          color="brown" /></q-item-section>
                <q-item-section>
                  <q-item-label>Elevation</q-item-label>
                  <q-item-label caption>{{ hood.elevation.metres || 'N/A' }} ({{ hood.elevation.feet || 'N/A'
                  }})</q-item-label>
                </q-item-section>
              </q-item>
              <q-item v-if="hood.politics">
                <q-item-section avatar><q-icon name="fas fa-landmark"
                          color="indigo" /></q-item-section>
                <q-item-section>
                  <q-item-label>Politics</q-item-label>
                  <q-item-label caption>Ward: {{ hood.politics.ward || 'N/A' }}</q-item-label>
                  <q-item-label caption>Constituency: {{ hood.politics.constituency || 'N/A' }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item v-if="hood.deprivation">
                <q-item-section avatar><q-icon name="fas fa-chart-area"
                          color="orange" /></q-item-section>
                <q-item-section>
                  <q-item-label>Deprivation</q-item-label>
                  <q-item-label caption>% Less Deprived: {{ hood.deprivation.percentage_less_deprived || 'N/A'
                  }}</q-item-label>
                </q-item-section>
              </q-item>
              <q-item v-if="hood.nearest_post_boxes && hood.nearest_post_boxes.length > 0">
                <q-item-section avatar><q-icon name="fas fa-envelope"
                          color="red" /></q-item-section>
                <q-item-section>
                  <q-item-label>Nearest Post Boxes</q-item-label>
                  <div v-for="(pb, pbi) in hood.nearest_post_boxes"
                       :key="`pb-${pbi}`"
                       class="text-caption">
                    {{ pb.location }} ({{ pb.distance }}) - Last Collection: Mon-Fri {{ pb.last_collection_mon_fri }},
                    Sat
                    {{ pb.last_collection_sat }}
                  </div>
                </q-item-section>
              </q-item>
            </q-list>
          </q-card-section>
        </q-expansion-item>

        <q-card-actions align="right"
                        class="bg-grey-2 q-pa-sm">
          <q-btn flat
                 dense
                 color="grey-7"
                 :label="`ID: ${hood.id}`"
                 no-caps />
          <q-btn v-if="hood.page_metadata?.canonical_url"
                 flat
                 color="primary"
                 label="More Info"
                 icon-right="fas fa-arrow-right"
                 :href="hood.page_metadata.canonical_url"
                 target="_blank" />
        </q-card-actions>
      </q-card>
    </div>
  </q-page>
</template>

<script setup>
import { computed } from 'vue';
import { QPage, QCard, QCardSection, QCardActions, QSeparator, QExpansionItem, QList, QItem, QItemSection, QItemLabel, QIcon, QImg, QLinearProgress, QBadge, QChip, QBtn, QTooltip } from 'quasar';

const props = defineProps({
  neighbourhoods: {
    type: Object,
    required: true,
    // default()
  }
});

// const neighbourhoods = computed(() => props.neighbourhoodsData?.neighbourhoods || []);

const getRatingIcon = (ratingString) => {
  if (!ratingString) return 'fas fa-question-circle';
  if (ratingString.includes('5') || ratingString.toLowerCase().includes('very good')) return 'fas fa-star';
  if (ratingString.includes('4') || ratingString.toLowerCase().includes('good')) return 'fas fa-star-half-alt';
  if (ratingString.includes('3') || ratingString.toLowerCase().includes('satisfactory')) return 'far fa-star';
  // Add more for lower ratings if needed
  return 'fas fa-exclamation-triangle';
};

const getRatingColor = (ratingString) => {
  if (!ratingString) return 'grey';
  if (ratingString.includes('5') || ratingString.toLowerCase().includes('very good')) return 'green';
  if (ratingString.includes('4') || ratingString.toLowerCase().includes('good')) return 'light-green';
  if (ratingString.includes('3') || ratingString.toLowerCase().includes('satisfactory')) return 'orange';
  return 'red';
};

</script>

<style lang="scss" scoped>
.neighbourhood-card {
  // Add any specific card styling if needed
  // e.g., box-shadow for more depth, though `bordered` is used.
}

.q-item__label--caption {
  font-size: 0.8rem;
}

ul.q-pl-md {
  padding-left: 20px; // Adjust as needed for list indentation
  list-style-type: disc;
  margin-top: 4px;
  margin-bottom: 4px;

  li {
    margin-bottom: 2px;
  }
}
</style>