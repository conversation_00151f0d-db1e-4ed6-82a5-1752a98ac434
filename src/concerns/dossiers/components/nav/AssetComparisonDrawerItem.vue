<!-- AssetComparisonDrawerItem.vue -->
<template>
  <q-expansion-item
    v-if="assetParts && assetParts.length > 0"
    :to="{ name: routeName }"
    exact
    clickable
    v-model="isExpanded"
    v-ripple
    active-class="q-item-h2c-active"
    expand-icon-class="text-white"
    header-class="text-white"
    default-opened
  >
    <!-- Header content -->
    <template v-slot:header>
      <q-item-section avatar>
        <q-icon :name="icon" />
      </q-item-section>
      <q-item-section @click="isExpanded = !isExpanded">
        <q-item-label>{{ labelText }}</q-item-label>
      </q-item-section>
    </template>
    <!-- Submenu for Asset Parts -->
    <template v-slot:default>
      <q-list class="q-pl-none">
        <q-item
          v-for="part in assetParts"
          :key="part.id"
          :to="{
            name: childRouteName,
            params: { assetsComparisonUuid: part.uuid },
            hash: `#${part.asset_part_slug}`,
          }"
          clickable
          v-ripple
          active-class="q-item-h2c-active"
        >
          <q-item-section>
            <q-item-label>{{
              part.right_side_property?.street_address ||
              part.right_side_property?.title
            }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </template>
  </q-expansion-item>
  <q-item
    v-else
    :to="{ name: routeName }"
    exact
    clickable
    v-ripple
    active-class="q-item-h2c-active"
  >
    <q-item-section avatar>
      <q-icon :name="icon" />
    </q-item-section>
    <q-item-section>
      <q-item-label>{{ labelText }}</q-item-label>
    </q-item-section>
  </q-item>
</template>

<script>
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'AssetComparisonDrawerItem',
  props: {
    assetParts: {
      type: Array,
      default: () => [],
    },
    routeName: {
      type: String,
      required: true,
    },
    childRouteName: {
      type: String,
      required: false,
    },
    labelText: {
      type: String,
      required: true,
    },
    icon: {
      type: String,
      default: 'dashboard',
    },
  },
  data: () => ({
    isExpanded: false,
  }),
  watch: {
    '$route.name': {
      immediate: true,
      handler(newHash) {
        if (
          [
            'rComparables',
            'rComparableSbs',
            'rComparableSolo',
            'rComparableSoloPhotos',
            'rComparableSbsParts',
          ].includes(newHash)
        ) {
          this.isExpanded = true
        } else {
          this.isExpanded = false
        }
      },
    },
  },
})
</script>

<style scoped>
.q-item-h2c-active {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff !important;
}
</style>
