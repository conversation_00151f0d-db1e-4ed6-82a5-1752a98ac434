<!-- 13 Apr - currently not in use..-->
<template>
  <q-expansion-item v-if="assetParts && assetParts.length > 0"
                    :to="{ name: routeName }"
                    exact
                    clickable
                    v-model="isExpanded"
                    v-ripple
                    active-class="q-item-h2c-active"
                    expand-icon-class="text-white"
                    header-class="text-white"
                    default-opened>
    <!-- Header content -->
    <template v-slot:header>
      <q-item-section avatar>
        <q-icon :name="icon" />
      </q-item-section>
      <q-item-section @click="isExpanded = !isExpanded">
        <q-item-label>{{ labelText }}</q-item-label>
      </q-item-section>
    </template>
    <!-- Submenu for Asset Parts -->
    <template v-slot:default>
      <q-list v-if="routeName === 'rDossierOverview'"
              class="q-pl-none">

        <q-item :to="{ name: routeName, hash: '#top' }"
                exact
                clickable
                v-ripple
                active-class="q-item-h2c-active">
          <q-item-section>
            <q-item-label>Overview</q-item-label>
          </q-item-section>
        </q-item>
        <q-item v-for="part in assetParts"
                :key="part.id"
                :to="{ name: routeName, hash: `#group-${part.key}` }"
                clickable
                v-ripple
                active-class="q-item-h2c-active">
          <q-item-section>
            <q-item-label>{{ part.asset_part_title }}</q-item-label>
            <q-item-label class="text-capitalize">{{ part.name || part.key?.replaceAll('_', ' ') ||
              part.comparison_title }}</q-item-label>
          </q-item-section>
        </q-item>
        <q-item :to="{ name: routeName, hash: '#main-dossier-map' }"
                exact
                clickable
                v-ripple
                active-class="q-item-h2c-active">
          <q-item-section>
            <q-item-label>Map</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
      <q-list v-else
              class="q-pl-none">
        <q-item v-for="part in assetParts"
                :key="part.id"
                :to="{ name: childRouteName, params: { assetsComparisonUuid: part.uuid }, hash: `#${part.asset_part_slug}` }"
                clickable
                v-ripple
                active-class="q-item-h2c-active">
          <q-item-section>
            <q-item-label>{{ part.comparison_title }}</q-item-label>
          </q-item-section>
        </q-item>
      </q-list>
    </template>
  </q-expansion-item>
  <q-item v-else
          :to="{ name: routeName }"
          exact
          clickable
          v-ripple
          active-class="q-item-h2c-active">
    <q-item-section avatar>
      <q-icon :name="icon" />
    </q-item-section>
    <q-item-section>
      <q-item-label>{{ labelText }}</q-item-label>
    </q-item-section>
  </q-item>
</template>

<script>
import { hash } from "ol/tilecoord";
import { defineComponent } from "vue";

export default defineComponent({
  name: "SingleExpandableDrawerItem",
  props: {
    assetParts: {
      type: Array,
      default: () => [],
    },
    routeName: {
      type: String,
      required: true,
    },
    childRouteName: {
      type: String,
      required: false,
    },
    labelText: {
      type: String,
      required: true,
    },
    icon: {
      type: String,
      default: "dashboard",
    },
  },
  data: () => ({
    isExpanded: false,
  }),
});
</script>

<style scoped>
.q-item-h2c-active {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff !important;
}
</style>