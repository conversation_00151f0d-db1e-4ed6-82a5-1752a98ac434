<template>
  <q-card class="SideBySideListingsInRow">
    <q-card-section class="q-pa-none sbs-card-section">
      <q-markup-table v-if="leftColItem"
                      class="simp-regular-comp-tbl no-flashing-caret fixed-markup-table"
                      separator="cell"
                      flat
                      bordered>
        <thead>
          <tr>
            <th colspan="2"
                class="text-center text-h5"
                style="width: 50%; padding: 0px">
              <div class="q-pa-sm"
                   style="
                  max-width: 50vw;
                  font-size: medium;
                  font-weight: 600;
                  overflow-wrap: break-word;
                  white-space: normal;
                ">
                {{ leftColItem.title }}
              </div>
              <div class="q-pa-sm text-left text-body1"
                   style="
                  max-width: 50vw;
                  word-break: break-word;
                  white-space: normal;
                ">
                {{ leftColItem.street_address }}
                <br />
                {{ leftColItem.postal_code }}
              </div>
            </th>
            <th class="text-center"
                style="padding: 0px">
              <div class="q-pa-sm"
                   style="
                  max-width: 50vw;
                  font-size: medium;
                  font-weight: 600;
                  overflow-wrap: break-word;
                  white-space: normal;
                ">
                {{ rightColItem.title }}
              </div>
              <div class="q-pa-sm text-left text-body1"
                   style="max-width: 50vw; white-space: normal">
                {{ rightColItem.street_address }}
                <br />
                {{ rightColItem.postal_code }}
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr class="mobile-hide">
            <td colspan="2"
                class="text-center text-h5"
                style="
                max-width: 50vw;
                word-break: break-word;
                white-space: pre-wrap;
                padding: 0px;
                background-color: #f0f0f0;
              ">
              <div class="text-center text-body1 comp-lsting-carousel-ctr text-weight-bold">
                <q-carousel v-model="leftImgSlide"
                            animated
                            arrows
                            navigation
                            infinite
                            class="bg-grey-1">
                  <q-carousel-slide class="comp-lsting-carousel-slide q-pa-none"
                                    v-for="(img, index) in leftColItem.sale_listing_pics"
                                    :key="index"
                                    :name="index">
                    <q-img :src="img.image_details.url"
                           class="full-height"
                           fit="cover" />
                  </q-carousel-slide>
                </q-carousel>
              </div>
            </td>
            <td class="text-center text-h5"
                style="
                max-width: 50vw;
                word-break: break-word;
                white-space: pre-wrap;
                padding: 0px;
                background-color: #f0f0f0;
              ">
              <div class="text-center text-body1 comp-lsting-carousel-ctr text-weight-bold">
                <q-carousel v-model="rightImgSlide"
                            animated
                            arrows
                            navigation
                            infinite
                            class="bg-grey-1">
                  <q-carousel-slide class="comp-lsting-carousel-slide q-pa-none"
                                    v-for="(img, index) in carouselSlides"
                                    :key="index"
                                    :name="index">
                    <q-img :src="img.src"
                           class="full-height"
                           fit="cover" />
                  </q-carousel-slide>
                </q-carousel>
              </div>
            </td>
          </tr>
          <tr v-if="showPriceCol"
              class="simple-comp-price-row">
            <td class="text-left"
                style="border-right: none; border-bottom: none; font-weight: 500">
              Price:
            </td>
            <td class="text-right text-h6"
                style="
                overflow: auto;
                padding-left: 0px;
                border-bottom: none;
                border-left: none;
              ">
              <span style="font-weight: 600; color: #4caf50">{{
                leftColItem.formatted_display_price
                }}</span>
            </td>
            <td class="overflow: auto;text-left text-h6"
                style="font-weight: 600; color: #2196f3; border-bottom: none">
              {{ rightColItem.formatted_display_price }}
            </td>
          </tr>
          <tr class="price-comp-prompt-row q-pa-none q-ma-none">
            <td colspan="3"
                style="height: 0px; padding: 0px">
              <q-separator />
            </td>
          </tr>
          <tr>
            <td class="text-left"
                style="border-right: none; font-weight: 500">
              Bedrooms:
            </td>
            <td class="large-font text-right"
                style="border-left: none; font-weight: 600">
              {{ leftColItem.count_bedrooms }}
            </td>
            <td class="large-font text-left"
                style="font-weight: 600">
              {{ rightColItem.count_bedrooms }}
            </td>
          </tr>
          <tr>
            <td class="text-left"
                style="border-right: none; font-weight: 500">
              Bathrooms:
            </td>
            <td class="large-font text-right"
                style="border-left: none; font-weight: 600">
              {{ leftColItem.count_bathrooms }}
            </td>
            <td class="large-font text-left"
                style="font-weight: 600">
              {{ rightColItem.count_bathrooms }}
            </td>
          </tr>
          <tr v-if="areaShouldBeShown">
            <td class="text-left"
                style="border-right: none; font-weight: 500">
              Area:
            </td>
            <td class="large-font text-right"
                style="border-left: none; font-weight: 600">
              {{ leftColItem.formatted_constructed_area }}
            </td>
            <td class="large-font text-left"
                style="font-weight: 600">
              {{ rightColItem.formatted_constructed_area }}
            </td>
          </tr>
          <tr>
            <td colspan="2"
                class="text-body2"
                style="
                max-width: 50vw;
                word-break: break-word;
                white-space: pre-wrap;
                font-size: large;
                padding-left: 0px;
              ">
              <q-expansion-item expand-separator
                                icon=""
                                label="Description"
                                :caption="descCaption"
                                v-model="descExpanded"
                                class="desc-exp-item-ctr"
                                style="">
                <q-card class="desc-exp-item">
                  <q-card-section>
                    <div class="comp-lsting-desc-html text-body2">
                      <div v-html="leftColItem.description"></div>
                    </div>
                  </q-card-section>
                </q-card>
              </q-expansion-item>
            </td>
            <td class="text-body2"
                style="
                max-width: 50vw;
                word-break: break-word;
                white-space: pre-wrap;
                font-size: large;
                padding: 0px;
              ">
              <q-expansion-item expand-separator
                                icon=""
                                label="Description"
                                :caption="descCaption"
                                v-model="descExpanded"
                                class="desc-exp-item-ctr"
                                style="">
                <q-card class="desc-exp-item">
                  <q-card-section>
                    <div class="comp-lsting-desc-html text-body2">
                      <div v-html="rightColItem.description"></div>
                    </div>
                  </q-card-section>
                </q-card>
              </q-expansion-item>
            </td>
          </tr>
          <tr class="mobile-only">
            <td colspan="3"
                class="text-center text-h5"
                style="
                max-width: 50vw;
                word-break: break-word;
                white-space: pre-wrap;
                padding: 0px;
                background-color: #f0f0f0;
              ">
              <div class="text-center text-body1 comp-lsting-carousel-ctr text-weight-bold">
                <q-carousel v-model="leftImgSlide"
                            animated
                            arrows
                            navigation
                            infinite
                            class="bg-grey-1">
                  <q-carousel-slide class="comp-lsting-carousel-slide q-pa-none"
                                    v-for="(img, index) in leftColItem.sale_listing_pics"
                                    :key="index"
                                    :name="index">
                    <q-img :src="img.image_details.url"
                           class="full-height"
                           fit="cover" />
                  </q-carousel-slide>
                </q-carousel>
              </div>
            </td>
          </tr>
          <tr class="mobile-only">
            <td colspan="3"
                class="text-center text-h5"
                style="
                max-width: 50vw;
                word-break: break-word;
                white-space: pre-wrap;
                padding: 0px;
                background-color: #f0f0f0;
              ">
              <div class="text-center text-body1 comp-lsting-carousel-ctr text-weight-bold">
                <q-carousel v-model="rightImgSlide"
                            animated
                            arrows
                            navigation
                            infinite
                            class="bg-grey-1">
                  <q-carousel-slide class="comp-lsting-carousel-slide q-pa-none"
                                    v-for="(img, index) in carouselSlides"
                                    :key="index"
                                    :name="index">
                    <q-img :src="img.src"
                           class="full-height"
                           fit="cover" />
                  </q-carousel-slide>
                </q-carousel>
              </div>
            </td>
          </tr>
          <tr>
            <td colspan="2"
                class="text-left text-body2"
                style="border-right: none; word-break: break-word">
              <div class="col-xs-12">
                <span style="white-space: normal">Original Listing: </span>
                <div style="overflow: hidden">
                  <a target="_blank"
                     :href="leftColItem.import_url">
                    {{ leftColItem.import_url }}</a>
                </div>
              </div>
            </td>
            <td class="large-font text-left text-body2"
                style="word-break: break-word">
              <div class="col-xs-12">
                <span style="white-space: normal">Original Listing: </span>
                <div style="overflow: hidden">
                  <a target="_blank"
                     :href="rightColItem.import_url">
                    {{ rightColItem.import_url }}</a>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </q-markup-table>
    </q-card-section>
    <div v-if="showAssetPartComparison"
         class="q-pa-sm">
      <AssetPartComparison :room-comparisons="roomComparisons" />
    </div>
  </q-card>
</template>

<script>
import AssetPartComparison from "src/concerns/dossiers/components/comparisons/AssetPartComparison.vue"

export default {
  components: {
    AssetPartComparison,
  },
  data() {
    return {
      leftImgSlide: 0,
      rightImgSlide: 0,
      descExpanded: true,
    }
  },
  computed: {
    roomComparisons() {
      return this.detailedComparisons?.room_comparisons || []
    },
    carouselSlides() {
      let carouselSlides = []
      let picsColl = this.listingInRow?.sale_listing_pics || []

      picsColl.forEach(function (picObject, index) {
        let imageUrl = picObject.image_details?.url
        if (!picObject.flag_is_hidden && imageUrl) {
          carouselSlides.push({
            src: imageUrl,
            altText: picObject.photo_title || `Property image ${index + 1}`,
            sortOrder: picObject.sort_order || index,
          })
        }
      })
      return carouselSlides.sort((a, b) => a.sortOrder - b.sortOrder)
    },
    rightColItem() {
      return this.listingInRow || {}
    },
    currencyConversionEnabled() {
      return true
    },
    showPriceCol() {
      return true
    },
    areaShouldBeShown() {
      return false
    },
    showAssetPartComparison() {
      return false
    },
    descCaption() {
      return this.descExpanded ? '' : 'Read More...'
    },
  },
  props: {
    listingInRow: {
      type: Object,
      default: () => ({ main_listing_details: { title: '' } }),
    },
    leftColItem: {
      type: Object,
      default: () => ({ title: '' }),
    },
    detailedComparisons: {
      type: Object,
      default: () => ({}),
    },
  },
}
</script>

<style>
.q-table tbody td {
  font-size: initial;
}

.desc-exp-item {
  max-height: 200px;
  overflow: scroll;
}

.fixed-markup-table table {
  table-layout: fixed;
}

.q-item__label--caption {
  color: #2196f3;
}
</style>