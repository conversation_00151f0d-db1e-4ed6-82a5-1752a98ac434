<template>
  <!-- 4 june 2025 - not in use ATM -->
  <div v-if="listingInRow">
    <q-card class="property-card SingleInteractiveListingInRow row q-ma-md">
      <div class="col-md-4 col-sm-12 col-xs-12 q-pr-md">
        <ListingCarouselSimple :carouselSlides="carouselSlides" />
      </div>

      <div class="col-md-6 col-sm-12 col-xs-12 q-pl-md">
        <div class="row items-start q-gutter-y-sm">
          <!-- ... (existing property details) ... -->
          <div class="col-12">
            <div class="text-h6 q-pt-md">{{ listingInRow.title }}</div>
            <div class="text-subtitle1">{{ listingInRow.street_address }}</div>
          </div>

          <div class="col-auto q-pb-md">
            <q-badge color="primary"
                     class="text-bold text-h6 q-pa-sm">
              {{ listingInRow.formatted_display_price }}
            </q-badge>
          </div>

          <div class="col-12 row q-col-gutter-md">
            <div class="col-auto">
              <q-item class="bg-blue-1 rounded-borders q-pa-sm">
                <q-item-section avatar>
                  <q-icon name="bed"
                          color="primary"
                          size="sm" />
                </q-item-section>
                <q-item-section>
                  <q-item-label class="text-bold">{{ listingInRow.count_bedrooms }}</q-item-label>
                  <q-item-label caption>Beds</q-item-label>
                </q-item-section>
              </q-item>
            </div>

            <div class="col-auto">
              <q-item class="bg-blue-1 rounded-borders q-pa-sm">
                <q-item-section avatar>
                  <q-icon name="bathroom"
                          color="primary"
                          size="sm" />
                </q-item-section>
                <q-item-section>
                  <q-item-label class="text-bold">{{ listingInRow.count_bathrooms }}</q-item-label>
                  <q-item-label caption>Baths</q-item-label>
                </q-item-section>
              </q-item>
            </div>

            <div class="col-auto">
              <q-item class="bg-blue-1 rounded-borders q-pa-sm">
                <q-item-section avatar>
                  <q-icon name="home"
                          color="primary"
                          size="sm" />
                </q-item-section>
                <q-item-section>
                  <!-- Assuming 'Detached' is an example, you might need a dynamic field -->
                  <q-item-label class="text-bold">{{ listingInRow.property_type_label || 'N/A' }}</q-item-label>
                  <q-item-label caption>Type</q-item-label>
                </q-item-section>
              </q-item>
            </div>
          </div>


          <div v-if="listingInRow.realty_agent_details"
               class="col-12 row items-center q-gutter-sm q-pt-sm">
            <q-item-section avatar
                            v-if="listingInRow.realty_agent_details['display-logo']">
              <q-img :src="listingInRow.realty_agent_details['display-logo'].url"
                     width="40px"
                     height="40px" />
            </q-item-section>
            <q-item-section>
              <q-item-label class="text-bold">{{ listingInRow.realty_agent_details.name }}</q-item-label>
              <q-item-label caption>
                <q-icon name="phone"
                        size="xs" /> {{ listingInRow.realty_agent_details.telephone }}
              </q-item-label>
            </q-item-section>
          </div>

          <!-- Notes Section -->
          <div class="col-12 q-mt-md">
            <q-separator />
            <div class="q-mt-md">
              <q-input v-model="userNoteText"
                       filled
                       label="Add a new note about this property"
                       type="textarea"
                       autogrow
                       :readonly="isSavingNote"
                       :rows="3"
                       class="notes-input"
                       :error="!!notesError"
                       :error-message="notesError?.message">
                <template v-slot:append>
                  <q-btn round
                         flat
                         icon="save"
                         :loading="isSavingNote"
                         :disable="!userNoteText || userNoteText === lastSavedNoteText"
                         @click="handleSaveNote">
                    <q-tooltip>Save Note</q-tooltip>
                  </q-btn>
                </template>
              </q-input>
              <div v-if="lastSavedNoteText && !userNoteText"
                   class="text-caption text-grey q-mt-sm">
                Last note added: {{ lastSavedNoteText }}
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div v-if="isMainProperty"
               class="col-12 row justify-between q-mt-md">
            <q-btn flat
                   outline
                   color="secondary"
                   :to="{ name: 'rDossierOverview' }">
              See Details
            </q-btn>
            <q-btn flat
                   outline
                   color="secondary"
                   :to="{ name: 'rDossierLocation', query: { listing: `main_${listingInRow.uuid}` } }">
              See on Map
            </q-btn>
          </div>
          <div v-else
               class="col-12 row justify-between q-mt-md">
            <q-btn flat
                   outline
                   color="secondary"
                   :to="{ name: 'rComparableSolo', params: { assetsComparisonUuid: comparableUuid } }">
              See Details
            </q-btn>
            <q-btn flat
                   outline
                   color="secondary"
                   :to="{ name: 'rDossierLocation', query: { listing: `comp_${listingInRow.uuid}` } }">
              See on Map
            </q-btn>
            <q-btn flat
                   outline
                   color="secondary"
                   :to="{ name: 'rComparableSbs', params: { assetsComparisonUuid: comparableUuid } }">
              Compare
            </q-btn>
          </div>
        </div>
      </div>
    </q-card>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'
import { useRouter } from 'vue-router'
import ListingCarouselSimple from "src/components/pics/ListingCarouselSimple.vue"
// Adjust path if necessary
import { useDossierNotesAndQueries } from 'src/concerns/dossiers/composables/useDossierNotesAndQueries'

export default defineComponent({
  name: 'SingleInteractiveListingInRow',
  components: {
    ListingCarouselSimple
  },
  props: {
    // realtyDossier: { // Essential for the composable
    //   type: Object,
    //   required: true // Make it required if the notes functionality is core
    // },
    comparableUuid: {
      type: String,
      required: false
    },
    listingInRow: {
      type: Object,
      required: true // Changed to true as it's fundamental for the component
    },
    isMainProperty: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const $q = useQuasar()
    const $router = useRouter()
    const realtyDossierLite = {
      uuid: $router.currentRoute.value.params.dossierUuid
    }
    // Initialize the composable
    // The composable might be loading existing notes/queries for the dossier in its `loadNotesAndQueries`
    // We are only focusing on *adding* a new note here.
    const {
      addNoteOrQuery,
      isLoading: isComposableLoading, // you might use this for a general loading state
      error: composableError // error from the composable (e.g., loading existing notes)
    } = useDossierNotesAndQueries(realtyDossierLite, $q)

    const userNoteText = ref('')
    const lastSavedNoteText = ref('') // To store the text of the last successfully saved note by this instance
    const isSavingNote = ref(false)
    const notesError = ref(null) // Specific error for the notes saving operation in this component

    const carouselSlides = computed(() => {
      let slides = []
      let picsColl = props.listingInRow?.sale_listing_pics || []

      picsColl.forEach(function (picObject, index) {
        let imageUrl = picObject.image_details?.url
        if (!picObject.flag_is_hidden && imageUrl) {
          slides.push({
            thumb: imageUrl,
            src: imageUrl,
            altText: picObject.photo_title || `Property image ${index + 1}`,
            sortOrder: picObject.sort_order || index
          })
        }
      })
      return slides.sort((a, b) => a.sortOrder - b.sortOrder)
    })

    const handleSaveNote = async () => {
      if (!userNoteText.value || userNoteText.value === lastSavedNoteText.value) {
        return;
      }

      isSavingNote.value = true
      notesError.value = null // Clear previous errors

      try {
        const isPrimaryNote = props.isMainProperty
        const comparisonIdForNote = isPrimaryNote ? null : props.comparableUuid

        // Ensure a comparison UUID is present if it's not a Northstar Property note
        if (!isPrimaryNote && !comparisonIdForNote) {
          console.error('Cannot save note: Comparison UUID is missing for a non-Northstar Property.')
          $q.notify({
            type: 'negative',
            message: 'Cannot save note: Missing comparison information.'
          })
          notesError.value = { message: 'Missing comparison information.' }
          isSavingNote.value = false
          return
        }

        const newNote = await addNoteOrQuery(
          userNoteText.value,
          comparisonIdForNote,
          isPrimaryNote
          // pictures: [] // Pass empty array or handle picture uploads if needed
        )

        if (newNote && newNote.id) {
          lastSavedNoteText.value = userNoteText.value
          // Optionally clear the input after successful save, or leave it for user to see
          // userNoteText.value = '';

          $q.notify({
            type: 'positive',
            message: 'Note saved successfully!'
          })
          // Note: We are not emitting 'notes-saved' anymore as the composable handles persistence.
          // If parent needs to react, it should observe the notes list from the composable directly,
          // or you can emit a simpler event like 'note-added'.
        } else {
          // This case might happen if addNoteOrQuery resolves but doesn't return a valid newNote
          // or if the composable's 'error' ref was set.
          if (composableError.value) {
            notesError.value = composableError.value; // Show error from composable
            // Notification is likely handled by composable or here
          } else {
            $q.notify({ type: 'negative', message: 'Failed to save note. Please try again.' });
            notesError.value = { message: 'An unknown error occurred.' }
          }
        }
      } catch (error) {
        console.error('Error saving note:', error)
        notesError.value = { message: error.response?.data?.message || error.message || 'Failed to save note.' }
        $q.notify({
          type: 'negative',
          message: notesError.value.message
        })
      } finally {
        isSavingNote.value = false
      }
    }

    watch(() => props.listingInRow?.uuid, () => {
      // When the listing context changes, clear the input and the "last saved" text for this instance.
      userNoteText.value = ''
      lastSavedNoteText.value = ''
      notesError.value = null // Clear any previous errors specific to the notes input
    }, { immediate: true }) // immediate: true might be useful if you want to clear on initial load too


    // mounted() logic would go into onMounted hook from 'vue' if needed
    // import { onMounted } from 'vue'
    // onMounted(() => { /* ... */ })

    return {
      userNoteText,
      lastSavedNoteText,
      isSavingNote,
      notesError, // For displaying error directly in the input field
      carouselSlides,
      handleSaveNote,
      // You can expose isComposableLoading and composableError if needed for other UI elements
      isComposableLoading,
      // composableError // This is the general error from the composable, notesError is specific to saving here
    }
  }
})
</script>

<style scoped>
.bg-blue-1 {
  background-color: #e3f2fd;
}

.notes-input {
  padding-right: 16px;
}

.notes-input .q-field__control {
  padding-right: 48px;
  /* Adjust if needed for your save button size */
}
</style>