<template>
  <q-card class="q-pa-sm q-ma-md"
          flat
          bordered
          style="max-width: 800px; margin: auto;">
    <q-form @submit.prevent="onSubmit"
            class="q-gutter-y-lg"> <!-- q-gutter-y-lg for vertical spacing between sections -->

      <!-- Location Preferences Section -->
      <q-expansion-item group="preferences"
                        icon="place"
                        label="Location Preferences"
                        header-class="text-primary text-h6"
                        default-opened
                        expand-separator>
        <q-card>
          <q-card-section class="q-gutter-md">
            <q-input filled
                     dense
                     v-model="editableScoot.preferred_vicinity_name"
                     label="Preferred Vicinity"
                     hint="e.g., 'Cambridge, UK'"
                     clearable>
              <template v-slot:prepend>
                <q-icon name="search" />
              </template>
            </q-input>

            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6">
                <q-input filled
                         dense
                         type="number"
                         v-model.number="editableScoot.max_distance_from_vicinity_preferred"
                         label="Max Preferred Distance"
                         :suffix="distanceUnitShortText(editableScoot.preferred_distance_unit)" />
              </div>
              <div class="col-12 col-md-6">
                <q-input filled
                         dense
                         type="number"
                         v-model.number="editableScoot.max_distance_from_vicinity_absolute"
                         label="Max Absolute Distance"
                         :suffix="distanceUnitShortText(editableScoot.preferred_distance_unit)" />
              </div>
            </div>

            <q-select filled
                      dense
                      v-model="editableScoot.preferred_distance_unit"
                      :options="distanceUnitOptions"
                      label="Distance Unit"
                      emit-value
                      map-options>
              <template v-slot:prepend>
                <q-icon name="straighten" />
              </template>
            </q-select>

            <q-select filled
                      dense
                      v-model="editableScoot.country_code"
                      :options="countryOptions"
                      label="Country"
                      emit-value
                      map-options
                      hint="Default search country">
              <template v-slot:prepend>
                <q-icon name="public" />
              </template>
            </q-select>
          </q-card-section>
        </q-card>
      </q-expansion-item>

      <!-- Property Size & Type Section -->
      <q-expansion-item group="preferences"
                        icon="home_work"
                        label="Property Size & Type"
                        header-class="text-primary text-h6"
                        default-opened
                        expand-separator>
        <q-card>
          <q-card-section class="q-gutter-md">
            <q-input filled
                     dense
                     v-model="editableScoot.preferred_property_type"
                     label="Preferred Property Type"
                     hint="e.g., 'Detached House', 'Apartment'"
                     clearable>
              <template v-slot:prepend>
                <q-icon name="category" />
              </template>
            </q-input>

            <div class="text-subtitle1 q-mt-sm">Bedrooms</div>
            <q-range v-model="bedroomPreferredRange"
                     :min="0"
                     :max="10"
                     :step="1"
                     label-always
                     markers
                     class="q-px-sm"
                     color="secondary"
                     label-color="primary"
                     :left-label-value="`${bedroomPreferredRange.min} bed${bedroomPreferredRange.min === 1 ? '' : 's'} (pref min)`"
                     :right-label-value="`${bedroomPreferredRange.max} bed${bedroomPreferredRange.max === 1 ? '' : 's'} (pref max)`" />
            <div class="row q-col-gutter-sm q-mt-xs">
              <div class="col">
                <q-input filled
                         dense
                         type="number"
                         v-model.number="editableScoot.bedrooms_min_absolute"
                         label="Min Abs. Beds"
                         :min="0" />
              </div>
              <div class="col">
                <q-input filled
                         dense
                         type="number"
                         v-model.number="editableScoot.bedrooms_max_absolute"
                         label="Max Abs. Beds"
                         :min="0" />
              </div>
            </div>

            <div class="text-subtitle1 q-mt-md">Bathrooms</div>
            <q-range v-model="bathroomPreferredRange"
                     :min="0"
                     :max="10"
                     :step="1"
                     label-always
                     markers
                     class="q-px-sm"
                     color="secondary"
                     label-color="primary"
                     :left-label-value="`${bathroomPreferredRange.min} bath${bathroomPreferredRange.min === 1 ? '' : 's'} (pref min)`"
                     :right-label-value="`${bathroomPreferredRange.max} bath${bathroomPreferredRange.max === 1 ? '' : 's'} (pref max)`" />
            <div class="row q-col-gutter-sm q-mt-xs">
              <div class="col">
                <q-input filled
                         dense
                         type="number"
                         v-model.number="editableScoot.bathrooms_min_absolute"
                         label="Min Abs. Baths"
                         :min="0" />
              </div>
              <div class="col">
                <q-input filled
                         dense
                         type="number"
                         v-model.number="editableScoot.bathrooms_max_absolute"
                         label="Max Abs. Baths"
                         :min="0" />
              </div>
            </div>

            <div class="text-subtitle1 q-mt-md">Indoor Area</div>
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6">
                <q-input filled
                         dense
                         type="number"
                         v-model.number="editableScoot.indoor_area_min_absolute"
                         label="Min Indoor Area"
                         :suffix="areaUnitShortText(editableScoot.preferred_area_unit)"
                         :min="0" />
              </div>
              <div class="col-12 col-md-6">
                <q-input filled
                         dense
                         type="number"
                         v-model.number="editableScoot.indoor_area_max_absolute"
                         label="Max Indoor Area"
                         :suffix="areaUnitShortText(editableScoot.preferred_area_unit)"
                         :min="0" />
              </div>
            </div>

            <div class="text-subtitle1 q-mt-md">Plot Area</div>
            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6">
                <q-input filled
                         dense
                         type="number"
                         v-model.number="editableScoot.plot_area_min_absolute"
                         label="Min Plot Area"
                         :suffix="areaUnitShortText(editableScoot.preferred_area_unit)"
                         :min="0" />
              </div>
              <div class="col-12 col-md-6">
                <q-input filled
                         dense
                         type="number"
                         v-model.number="editableScoot.plot_area_max_absolute"
                         label="Max Plot Area"
                         :suffix="areaUnitShortText(editableScoot.preferred_area_unit)"
                         :min="0" />
              </div>
            </div>
            <q-select filled
                      dense
                      v-model="editableScoot.preferred_area_unit"
                      :options="areaUnitOptions"
                      label="Area Unit"
                      emit-value
                      map-options>
              <template v-slot:prepend>
                <q-icon name="square_foot" />
              </template>
            </q-select>
          </q-card-section>
        </q-card>
      </q-expansion-item>

      <!-- Price Section -->
      <q-expansion-item group="preferences"
                        icon="payments"
                        label="Price Preferences"
                        header-class="text-primary text-h6"
                        default-opened
                        expand-separator>
        <q-card>
          <q-card-section class="q-gutter-md">
            <q-select filled
                      dense
                      v-model="editableScoot.preferred_currency"
                      :options="currencyOptions"
                      label="Preferred Currency"
                      emit-value
                      map-options>
              <template v-slot:prepend>
                <q-icon name="monetization_on" />
              </template>
            </q-select>

            <div class="text-subtitle1 q-mt-sm">Preferred Price Range</div>
            <div class="q-mb-sm text-caption text-grey-8">
              Range: {{ formatCurrency(pricePreferredRange.min * 100, editableScoot.preferred_currency) }} - {{
                formatCurrency(pricePreferredRange.max * 100, editableScoot.preferred_currency) }}
            </div>
            <q-range v-model="pricePreferredRange"
                     :min="priceRangeOptions.min"
                     :max="priceRangeOptions.max"
                     :step="priceRangeOptions.step"
                     label-always
                     markers
                     class="q-px-sm"
                     color="secondary"
                     label-color="primary"
                     :left-label-value="`Min: ${formatCurrency(pricePreferredRange.min * 100, '', false)}`"
                     :right-label-value="`Max: ${formatCurrency(pricePreferredRange.max * 100, '', false)}`" />
            <div class="row q-col-gutter-sm q-mt-xs">
              <div class="col">
                <q-input filled
                         dense
                         type="number"
                         v-model.number="priceMinAbsolute"
                         label="Min Absolute Price"
                         :prefix="currencySymbol(editableScoot.preferred_currency)"
                         :step="priceRangeOptions.step"
                         :min="0" />
              </div>
              <div class="col">
                <q-input filled
                         dense
                         type="number"
                         v-model.number="priceMaxAbsolute"
                         label="Max Absolute Price"
                         :prefix="currencySymbol(editableScoot.preferred_currency)"
                         :step="priceRangeOptions.step"
                         :min="0" />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </q-expansion-item>

      <!-- Features Section -->
      <q-expansion-item group="preferences"
                        icon="tune"
                        label="Feature Preferences"
                        header-class="text-primary text-h6"
                        default-opened
                        expand-separator>
        <q-card>
          <q-card-section class="q-gutter-md">
            <div class="text-subtitle2 q-mb-xs">Select desired features:</div>
            <div class="q-gutter-sm row items-center">
              <q-checkbox dense
                          v-model="featureGarden"
                          label="Garden"
                          @update:model-value="val => updateFeature('garden', val)"
                          color="secondary" />
              <q-checkbox dense
                          v-model="featureParking"
                          label="Parking"
                          @update:model-value="val => updateFeature('parking', val)"
                          color="secondary" />
              <q-checkbox dense
                          v-model="featureBalcony"
                          label="Balcony"
                          @update:model-value="val => updateFeature('balcony', val)"
                          color="secondary" />
              <q-checkbox dense
                          v-model="featurePetFriendly"
                          label="Pet Friendly"
                          @update:model-value="val => updateFeature('pet_friendly', val)"
                          color="secondary" />
            </div>
            <q-input filled
                     dense
                     autogrow
                     v-model="rawFeaturePreferences"
                     label="Advanced: Raw Feature JSON"
                     type="textarea"
                     hint="Edit JSON directly for other features"
                     class="q-mt-md">
              <template v-slot:prepend>
                <q-icon name="data_object" />
              </template>
            </q-input>
          </q-card-section>
        </q-card>
      </q-expansion-item>

      <!-- Other Preferences Section -->
      <q-expansion-item group="preferences"
                        icon="settings"
                        label="Other Preferences"
                        header-class="text-primary text-h6"
                        default-opened
                        expand-separator>
        <q-card>
          <q-card-section class="q-gutter-md">
            <q-select filled
                      dense
                      v-model="editableScoot.preferred_locale"
                      :options="localeOptions"
                      label="Preferred Locale"
                      emit-value
                      map-options>
              <template v-slot:prepend>
                <q-icon name="language" />
              </template>
            </q-select>
          </q-card-section>
        </q-card>
      </q-expansion-item>

      <!-- Actions -->
      <q-card-actions align="right"
                      class="q-pa-md">
        <q-btn label="Reset"
               type="button"
               color="grey-7"
               flat
               @click="resetForm"
               icon="undo" />
        <q-btn label="Save Preferences"
               type="submit"
               color="primary"
               unelevated
               icon="save" />
      </q-card-actions>

    </q-form>
  </q-card>
</template>

<script setup>
// ... (Your existing script setup remains largely the same)
// Add these new helper functions within your <script setup>:

const distanceUnitShortText = (value) => {
  const option = distanceUnitOptions.find(opt => opt.value === value);
  return option ? option.label.split('(')[1].replace(')', '') : 'units';
};

const areaUnitShortText = (value) => {
  const option = areaUnitOptions.find(opt => opt.value === value);
  return option ? option.label.split('(')[1].replace(')', '') : 'units';
};

const currencySymbol = (currencyCode) => {
  const option = currencyOptions.find(opt => opt.value === currencyCode);
  // Extract symbol, e.g., "GBP (£)" -> "£"
  if (option && option.label.includes('(') && option.label.includes(')')) {
    return option.label.substring(option.label.indexOf('(') + 1, option.label.indexOf(')'));
  }
  return currencyCode; // Fallback to code
};

// Make sure these are defined if not already (from previous script)
import { ref, watch, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'

const $q = useQuasar()

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
    default: () => ({
      country_code: "UK",
      bathrooms_max_absolute: 0,
      bathrooms_max_preferred: 0,
      bathrooms_min_absolute: 0,
      bathrooms_min_preferred: 0,
      bedrooms_max_absolute: 0,
      bedrooms_max_preferred: 0,
      bedrooms_min_absolute: 0,
      bedrooms_min_preferred: 0,
      feature_preferences: {},
      indoor_area_max_absolute: 0,
      indoor_area_min_absolute: 0,
      max_distance_from_vicinity_absolute: null,
      max_distance_from_vicinity_preferred: null,
      plot_area_max_absolute: 0,
      plot_area_min_absolute: 0,
      preferred_area_unit: 0,
      preferred_currency: "GBP",
      preferred_distance_unit: 0,
      preferred_locale: "en-UK",
      preferred_property_type: "",
      preferred_vicinity_name: "",
      price_max_absolute_cents: 50000000,
      price_max_preferred_cents: 40000000,
      price_min_absolute_cents: 10000000,
      price_min_preferred_cents: 10000000,
    })
  }
})

const emit = defineEmits(['update:modelValue', 'save'])

const editableScoot = ref(JSON.parse(JSON.stringify(props.modelValue)))

watch(() => props.modelValue, (newValue) => {
  editableScoot.value = JSON.parse(JSON.stringify(newValue))
  initializeRanges();
  initializeFeatures();
}, { deep: true })

const formatCurrency = (valueInCents, currencyCode, includeSymbol = true) => {
  if (valueInCents === null || valueInCents === undefined) return '';
  const amount = valueInCents / 100;
  try {
    return new Intl.NumberFormat(editableScoot.value.preferred_locale || 'en-UK', {
      style: includeSymbol ? 'currency' : 'decimal',
      currency: includeSymbol ? currencyCode : undefined,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  } catch (e) {
    const symbol = includeSymbol ? currencySymbol(currencyCode) : '';
    return `${symbol} ${amount.toFixed(0)}`.trim();
  }
}

const bedroomPreferredRange = ref({ min: 0, max: 0 })
const bathroomPreferredRange = ref({ min: 0, max: 0 })
const pricePreferredRange = ref({ min: 0, max: 0 })

const initializeRanges = () => {
  bedroomPreferredRange.value = {
    min: editableScoot.value.bedrooms_min_preferred || 0,
    max: editableScoot.value.bedrooms_max_preferred || 5
  }
  bathroomPreferredRange.value = {
    min: editableScoot.value.bathrooms_min_preferred || 0,
    max: editableScoot.value.bathrooms_max_preferred || 3
  }
  pricePreferredRange.value = {
    min: (editableScoot.value.price_min_preferred_cents || 0) / 100,
    max: (editableScoot.value.price_max_preferred_cents || 500000) / 100
  }
}

const priceMinAbsolute = computed({
  get: () => (editableScoot.value.price_min_absolute_cents || 0) / 100,
  set: (val) => editableScoot.value.price_min_absolute_cents = Math.round(val * 100)
})
const priceMaxAbsolute = computed({
  get: () => (editableScoot.value.price_max_absolute_cents || 0) / 100,
  set: (val) => editableScoot.value.price_max_absolute_cents = Math.round(val * 100)
})

watch(bedroomPreferredRange, (newVal) => {
  editableScoot.value.bedrooms_min_preferred = newVal.min
  editableScoot.value.bedrooms_max_preferred = newVal.max
}, { deep: true })

watch(bathroomPreferredRange, (newVal) => {
  editableScoot.value.bathrooms_min_preferred = newVal.min
  editableScoot.value.bathrooms_max_preferred = newVal.max
}, { deep: true })

watch(pricePreferredRange, (newVal) => {
  editableScoot.value.price_min_preferred_cents = Math.round(newVal.min * 100)
  editableScoot.value.price_max_preferred_cents = Math.round(newVal.max * 100)
}, { deep: true })

const countryOptions = [
  { label: 'United Kingdom', value: 'UK' }, { label: 'United States', value: 'US' },
  { label: 'Canada', value: 'CA' }, { label: 'Australia', value: 'AU' },
]
const distanceUnitOptions = [
  { label: 'Kilometers (km)', value: 0 }, { label: 'Miles (mi)', value: 1 }
]
const areaUnitOptions = [
  { label: 'Square Meters (m²)', value: 0 }, { label: 'Square Feet (ft²)', value: 1 }
]
const currencyOptions = [
  { label: 'GBP (£)', value: 'GBP' }, { label: 'USD ($)', value: 'USD' }, { label: 'EUR (€)', value: 'EUR' },
]
const localeOptions = [
  { label: 'English (UK)', value: 'en-UK' }, { label: 'English (US)', value: 'en-US' },
]

const priceRangeOptions = computed(() => {
  const currency = editableScoot.value.preferred_currency;
  if (currency === 'GBP') return { min: 50000 / 100, max: 2000000 / 100, step: 10000 / 100 };
  if (currency === 'USD') return { min: 75000 / 100, max: 2500000 / 100, step: 10000 / 100 };
  if (currency === 'EUR') return { min: 60000 / 100, max: 2200000 / 100, step: 10000 / 100 };
  return { min: 0, max: 1000000 / 100, step: 1000 / 100 };
});

const distanceUnitText = (value) => distanceUnitOptions.find(opt => opt.value === value)?.label || 'units' // Kept for any remaining internal use
const areaUnitText = (value) => areaUnitOptions.find(opt => opt.value === value)?.label.split('(')[1].replace(')', '') || 'units' // Kept

const featureGarden = ref(false)
const featureParking = ref(false)
const featureBalcony = ref(false)
const featurePetFriendly = ref(false)
const rawFeaturePreferences = ref('')

const initializeFeatures = () => {
  const prefs = editableScoot.value.feature_preferences || {}
  featureGarden.value = !!prefs.garden
  featureParking.value = !!prefs.parking
  featureBalcony.value = !!prefs.balcony
  featurePetFriendly.value = !!prefs.pet_friendly
  rawFeaturePreferences.value = JSON.stringify(prefs, null, 2)
}

const updateFeature = (featureName, value) => {
  if (!editableScoot.value.feature_preferences) {
    editableScoot.value.feature_preferences = {}
  }
  editableScoot.value.feature_preferences[featureName] = value
  rawFeaturePreferences.value = JSON.stringify(editableScoot.value.feature_preferences, null, 2)
}

watch(rawFeaturePreferences, (newVal) => {
  try {
    const parsed = JSON.parse(newVal)
    editableScoot.value.feature_preferences = parsed
    featureGarden.value = !!parsed.garden
    featureParking.value = !!parsed.parking
    featureBalcony.value = !!parsed.balcony
    featurePetFriendly.value = !!parsed.pet_friendly
  } catch (e) { /* console.warn("Invalid JSON") */ }
})

const onSubmit = () => {
  try {
    if (rawFeaturePreferences.value.trim() !== '') {
      editableScoot.value.feature_preferences = JSON.parse(rawFeaturePreferences.value);
    } else {
      editableScoot.value.feature_preferences = {};
    }
  } catch (e) {
    $q.notify({ type: 'negative', message: 'Feature Preferences JSON is invalid.', position: 'top' })
    return;
  }
  emit('update:modelValue', JSON.parse(JSON.stringify(editableScoot.value)))
  emit('save', JSON.parse(JSON.stringify(editableScoot.value)))
  $q.notify({ color: 'positive', message: 'Preferences Saved!', icon: 'check' })
}

const resetForm = () => {
  editableScoot.value = JSON.parse(JSON.stringify(props.modelValue))
  initializeRanges()
  initializeFeatures()
  $q.notify({ message: 'Form Reset', icon: 'undo' })
}

onMounted(() => {
  initializeRanges()
  initializeFeatures()
})

</script>

<style scoped>
/* Optional: If you want to style the expansion item headers further */
.q-expansion-item .q-item__section--main {
  font-weight: 500;
  /* Example */
}

/* More subtle subtitle */
.text-subtitle1 {
  color: #555;
  /* Darker grey for subtitles under expansion items */
  font-weight: 500;
  margin-bottom: 4px;
  /* Little space below subtitle */
}

/* Adjustments for range labels to ensure they are clear */
.q-range .q-slider__text-container--h {
  font-size: 0.8rem;
}
</style>