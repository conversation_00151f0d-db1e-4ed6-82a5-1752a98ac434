<template>
  <div class="scoot-preferences-view">
    <div class="max-ctr q-pa-lg">
      <!-- Header -->
      <div class="preferences-header q-mb-xl">
        <div class="row items-center justify-between">
          <div>
            <h1 class="text-h4 text-weight-bold text-primary q-mb-sm">Property Search Preferences</h1>
            <p class="text-body1 text-grey-7">Your saved search criteria and preferences</p>
          </div>
          <q-btn 
            color="primary" 
            icon="edit" 
            label="Edit Preferences"
            unelevated
            rounded
            @click="$router.push({ name: 'rHunterProfileEdit' })"
          />
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="text-center q-pa-xl">
        <q-spinner color="primary" size="3em" />
        <div class="q-mt-md text-grey-7">Loading preferences...</div>
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center q-pa-xl">
        <q-icon name="error_outline" color="negative" size="3em" />
        <div class="q-mt-md text-negative">{{ error }}</div>
      </div>

      <!-- No Data State -->
      <div v-else-if="!preferences" class="text-center q-pa-xl">
        <q-icon name="search_off" color="grey-5" size="3em" />
        <div class="q-mt-md text-grey-7">No preferences set yet</div>
        <q-btn 
          color="primary" 
          label="Set Up Preferences" 
          class="q-mt-md"
          @click="$router.push({ name: 'rHunterProfileEdit' })"
        />
      </div>

      <!-- Preferences Content -->
      <div v-else class="preferences-content">
        <!-- Location Preferences -->
        <q-card class="preference-section q-mb-lg" flat bordered>
          <q-card-section class="section-header bg-grey-1">
            <div class="row items-center">
              <q-icon name="place" color="primary" size="md" class="q-mr-md" />
              <div class="text-h6 text-weight-medium">Location Preferences</div>
            </div>
          </q-card-section>
          <q-card-section class="q-pa-lg">
            <div class="row q-col-gutter-lg">
              <div class="col-12 col-md-6">
                <div class="preference-item">
                  <div class="preference-label">Preferred Area</div>
                  <div class="preference-value">{{ preferences.preferred_vicinity_name || 'Not specified' }}</div>
                </div>
              </div>
              <div class="col-12 col-md-3">
                <div class="preference-item">
                  <div class="preference-label">Max Preferred Distance</div>
                  <div class="preference-value">
                    {{ preferences.max_distance_from_vicinity_preferred || 'Not set' }}
                    {{ preferences.max_distance_from_vicinity_preferred ? distanceUnitShortText(preferences.preferred_distance_unit) : '' }}
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-3">
                <div class="preference-item">
                  <div class="preference-label">Max Absolute Distance</div>
                  <div class="preference-value">
                    {{ preferences.max_distance_from_vicinity_absolute || 'Not set' }}
                    {{ preferences.max_distance_from_vicinity_absolute ? distanceUnitShortText(preferences.preferred_distance_unit) : '' }}
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-6">
                <div class="preference-item">
                  <div class="preference-label">Country</div>
                  <div class="preference-value">{{ getCountryName(preferences.country_code) }}</div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Property Size & Type -->
        <q-card class="preference-section q-mb-lg" flat bordered>
          <q-card-section class="section-header bg-grey-1">
            <div class="row items-center">
              <q-icon name="home_work" color="primary" size="md" class="q-mr-md" />
              <div class="text-h6 text-weight-medium">Property Size & Type</div>
            </div>
          </q-card-section>
          <q-card-section class="q-pa-lg">
            <div class="row q-col-gutter-lg">
              <div class="col-12">
                <div class="preference-item">
                  <div class="preference-label">Property Type</div>
                  <div class="preference-value">{{ preferences.preferred_property_type || 'Any type' }}</div>
                </div>
              </div>
              <div class="col-12 col-md-6">
                <div class="preference-item">
                  <div class="preference-label">Bedrooms</div>
                  <div class="preference-value">
                    <div class="range-display">
                      <span class="range-label">Preferred:</span>
                      {{ preferences.bedrooms_min_preferred || 0 }} - {{ preferences.bedrooms_max_preferred || 0 }} beds
                    </div>
                    <div class="range-display">
                      <span class="range-label">Absolute:</span>
                      {{ preferences.bedrooms_min_absolute || 0 }} - {{ preferences.bedrooms_max_absolute || 0 }} beds
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-6">
                <div class="preference-item">
                  <div class="preference-label">Bathrooms</div>
                  <div class="preference-value">
                    <div class="range-display">
                      <span class="range-label">Preferred:</span>
                      {{ preferences.bathrooms_min_preferred || 0 }} - {{ preferences.bathrooms_max_preferred || 0 }} baths
                    </div>
                    <div class="range-display">
                      <span class="range-label">Absolute:</span>
                      {{ preferences.bathrooms_min_absolute || 0 }} - {{ preferences.bathrooms_max_absolute || 0 }} baths
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Price Preferences -->
        <q-card class="preference-section q-mb-lg" flat bordered>
          <q-card-section class="section-header bg-grey-1">
            <div class="row items-center">
              <q-icon name="payments" color="primary" size="md" class="q-mr-md" />
              <div class="text-h6 text-weight-medium">Price Preferences</div>
            </div>
          </q-card-section>
          <q-card-section class="q-pa-lg">
            <div class="row q-col-gutter-lg">
              <div class="col-12 col-md-6">
                <div class="preference-item">
                  <div class="preference-label">Preferred Price Range</div>
                  <div class="preference-value">
                    {{ formatCurrency(preferences.price_min_preferred_cents, preferences.preferred_currency) }} - 
                    {{ formatCurrency(preferences.price_max_preferred_cents, preferences.preferred_currency) }}
                  </div>
                </div>
              </div>
              <div class="col-12 col-md-6">
                <div class="preference-item">
                  <div class="preference-label">Absolute Price Range</div>
                  <div class="preference-value">
                    {{ formatCurrency(preferences.price_min_absolute_cents, preferences.preferred_currency) }} - 
                    {{ formatCurrency(preferences.price_max_absolute_cents, preferences.preferred_currency) }}
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Features -->
        <q-card class="preference-section q-mb-lg" flat bordered>
          <q-card-section class="section-header bg-grey-1">
            <div class="row items-center">
              <q-icon name="tune" color="primary" size="md" class="q-mr-md" />
              <div class="text-h6 text-weight-medium">Desired Features</div>
            </div>
          </q-card-section>
          <q-card-section class="q-pa-lg">
            <div class="features-grid">
              <q-chip 
                v-for="(value, key) in preferences.feature_preferences" 
                :key="key"
                :color="value ? 'positive' : 'grey-4'"
                :text-color="value ? 'white' : 'grey-8'"
                :icon="value ? 'check' : 'close'"
                class="feature-chip"
              >
                {{ formatFeatureName(key) }}
              </q-chip>
              <div v-if="!preferences.feature_preferences || Object.keys(preferences.feature_preferences).length === 0" 
                   class="text-grey-6">
                No specific features selected
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  preferences: {
    type: Object,
    default: () => null
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: null
  }
})

// Helper functions
const distanceUnitShortText = (value) => {
  const units = { 0: 'km', 1: 'mi' }
  return units[value] || 'units'
}

const getCountryName = (code) => {
  const countries = {
    'UK': 'United Kingdom',
    'US': 'United States',
    'CA': 'Canada',
    'AU': 'Australia'
  }
  return countries[code] || code
}

const formatCurrency = (valueInCents, currencyCode) => {
  if (!valueInCents) return 'Not set'
  const amount = valueInCents / 100
  try {
    return new Intl.NumberFormat('en-UK', {
      style: 'currency',
      currency: currencyCode || 'GBP',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  } catch (e) {
    return `${currencyCode || '£'} ${amount.toFixed(0)}`
  }
}

const formatFeatureName = (key) => {
  return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}
</script>

<style scoped>
.scoot-preferences-view {
  background-color: #fafafa;
  min-height: 100vh;
}

.max-ctr {
  max-width: 1200px;
  margin: 0 auto;
}

.preferences-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.preference-section {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
}

.preference-section:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.section-header {
  border-bottom: 1px solid #e0e0e0;
}

.preference-item {
  margin-bottom: 1rem;
}

.preference-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #666;
  margin-bottom: 0.25rem;
}

.preference-value {
  font-size: 1rem;
  color: #333;
  font-weight: 500;
}

.range-display {
  margin-bottom: 0.25rem;
}

.range-label {
  font-size: 0.8rem;
  color: #888;
  margin-right: 0.5rem;
}

.features-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.feature-chip {
  margin: 0;
}
</style>
