<template>
  <div class="task-details q-pa-md">
    <div class="row q-mb-md">
      <q-btn flat
             color="primary"
             icon="arrow_back"
             @click="$router.push({ name: 'rTasks' })"
             label="Back to Tasks" />
    </div>

    <div v-if="task"
         class="task-content">
      <!-- Property Association Display -->
      <div class="row q-mb-md">
        <div class="col-12">
          <div class="text-subtitle2">
            <q-icon name="home"
                    class="q-mr-sm" />
            Associated with: {{ getAssociatedPropertyName }}
          </div>
        </div>
      </div>

      <div class="row q-mb-md">
        <div class="col-12">
          <q-input v-model="task.text"
                   label="Task Description"
                   filled
                   @update:model-value="updateTask" />
        </div>
      </div>

      <div class="row q-mb-md">
        <div class="col-12">
          <q-input v-model="task.dueDate"
                   filled
                   label="Due Date"
                   type="date"
                   @update:model-value="updateTask" />
        </div>
      </div>

      <div class="row q-mb-md">
        <div class="col-12">
          <q-toggle v-model="task.completed"
                    label="Completed"
                    @update:model-value="updateTask" />
        </div>
      </div>

      <div class="row q-mb-md">
        <div class="col-12">
          <div class="text-h6 q-mb-sm">Pictures</div>
          <div class="row q-gutter-sm">
            <div v-for="(picture, index) in task.pictures"
                 :key="index"
                 class="picture-thumbnail">
              <q-img :src="picture.url"
                     :ratio="1"
                     style="width: 100px; height: 100px">
                <div class="absolute-top-right">
                  <q-btn flat
                         round
                         dense
                         color="white"
                         icon="close"
                         @click="removePicture(index)" />
                </div>
              </q-img>
            </div>
            <div v-if="propertyPhotos.length > 0"
                 class="picture-select">
              <q-btn color="primary"
                     icon="add_photo_alternate"
                     label="Add Photo"
                     @click="showPhotoDialog = true" />
            </div>
          </div>
        </div>
      </div>

      <!-- Photo Selection Dialog -->
      <q-dialog v-model="showPhotoDialog">
        <q-card style="min-width: 70vw">
          <q-card-section>
            <div class="row items-center justify-between">
              <div class="text-h6">Select Photos</div>
              <div class="text-subtitle2"
                   v-if="selectedPhotos.length">
                {{ selectedPhotos.length }} photo(s) selected
              </div>
            </div>
          </q-card-section>

          <q-card-section class="q-pt-none">
            <div class="row q-col-gutter-md">
              <div v-for="photo in propertyPhotos"
                   :key="photo.value"
                   class="col-6 col-md-4 col-lg-3">
                <q-card class="photo-card cursor-pointer"
                        :class="{ selected: isPhotoSelected(photo) }"
                        @click="togglePhotoSelection(photo)">
                  <q-img :src="photo.url"
                         :ratio="1"
                         class="photo-image" />
                  <q-card-section>
                    <div class="text-subtitle2">{{ photo.title }}</div>
                  </q-card-section>
                  <div class="absolute-top-right q-pa-sm">
                    <q-icon v-if="isPhotoSelected(photo)"
                            name="check_circle"
                            color="primary"
                            size="24px" />
                  </div>
                </q-card>
              </div>
            </div>
          </q-card-section>

          <q-card-actions align="right">
            <q-btn flat
                   label="Cancel"
                   color="primary"
                   v-close-popup />
            <q-btn flat
                   label="Add Selected"
                   color="primary"
                   :disable="!selectedPhotos.length"
                   @click="addSelectedPhoto(selectedPhotos)"
                   v-close-popup />
          </q-card-actions>
        </q-card>
      </q-dialog>

      <div class="row q-mt-md">
        <div class="col-12">
          <q-btn color="negative"
                 label="Delete Task"
                 @click="confirmDelete" />
        </div>
      </div>
    </div>
    <div v-else
         class="text-center q-pa-lg">
      <!-- <q-spinner color="primary"
                 size="2em" /> -->
      <div class="q-mt-sm">Sorry, unable to find task.</div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, computed, watch } from 'vue'
import { useQuasar } from 'quasar'
import { useDossierTasks } from 'src/concerns/dossiers/composables/useDossierTasks'
import { useRouter } from 'vue-router'
export default defineComponent({
  name: 'TaskDetails',
  props: {
    taskId: {
      type: [String, Number],
      required: true,
    },
    saleListing: {
      type: Object,
      required: true,
    },
    realtyDossier: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    const $q = useQuasar()
    const $router = useRouter()
    const {
      getTaskById,
      updateTask: updateTaskInStore,
      deleteTask,
      addPictureToTask,
      removePictureFromTask,
      tasks,
    } = useDossierTasks(props.realtyDossier, $q)
    const task = ref(null)
    const selectedPhotos = ref([])
    const showPhotoDialog = ref(false)

    // Watch for tasks to be loaded from parent
    watch(
      tasks,
      (newTasks) => {
        if (newTasks && newTasks.length > 0) {
          task.value = getTaskById(props.taskId)
        }
      },
      { immediate: true }
    )

    // Get associated property name
    const getAssociatedPropertyName = computed(() => {
      if (task.value?.isPrimary) {
        return props.saleListing?.street_address || 'Northstar Property'
      }
      if (task.value?.comparisonId) {
        const comparison =
          props.realtyDossier?.dossier_assets_comparisons?.find(
            (c) => c.uuid === task.value.comparisonId
          )
        return (
          comparison?.right_side_property?.street_address ||
          comparison?.right_side_property?.title ||
          'Unnamed Property'
        )
      }
      return 'No Property Associated'
    })

    // Get available property photos
    const propertyPhotos = computed(() => {
      if (task.value?.isPrimary) {
        return (
          props.saleListing?.sale_listing_pics?.map((photo) => ({
            label: photo.photo_title || 'Untitled',
            value: photo.id,
            url: photo.image_details?.url,
            title: photo.photo_title || 'Untitled',
            description: photo.photo_description,
            uuid: photo.uuid,
          })) || []
        )
      }
      if (task.value?.comparisonId) {
        const comparison =
          props.realtyDossier?.dossier_assets_comparisons?.find(
            (c) => c.uuid === task.value.comparisonId
          )
        return (
          comparison?.right_side_property?.sale_listing_pics?.map((photo) => ({
            label: photo.photo_title || 'Untitled',
            value: photo.id,
            url: photo.image_details?.url,
            title: photo.photo_title || 'Untitled',
            description: photo.photo_description,
            uuid: photo.uuid,
          })) || []
        )
      }
      return []
    })

    const updateTask = () => {
      if (task.value) {
        updateTaskInStore(task.value.id, task.value)
      }
    }

    const addSelectedPhoto = async (photos) => {
      if (task.value && photos?.length) {
        for (const photo of photos) {
          await addPictureToTask(task.value.id, {
            uuid: photo.uuid,
            url: photo.url,
            name: photo.title,
          })
        }
        selectedPhotos.value = []
        // Refresh the task to show the new photos
        task.value = getTaskById(props.taskId)
      }
    }

    const togglePhotoSelection = (photo) => {
      const index = selectedPhotos.value.findIndex(
        (p) => p.value === photo.value
      )
      if (index === -1) {
        selectedPhotos.value.push(photo)
      } else {
        selectedPhotos.value.splice(index, 1)
      }
    }

    const isPhotoSelected = (photo) => {
      return selectedPhotos.value.some((p) => p.value === photo.value)
    }

    const removePicture = (index) => {
      if (task.value) {
        removePictureFromTask(task.value.id, index)
      }
    }

    const confirmDelete = () => {
      $q.dialog({
        title: 'Confirm Delete',
        message: 'Are you sure you want to delete this task?',
        cancel: true,
        persistent: true,
      }).onOk(() => {
        deleteTask(task.value.id)
        $router.push({ name: 'rTasks' })
      })
    }

    return {
      task,
      updateTask,
      removePicture,
      confirmDelete,
      getAssociatedPropertyName,
      propertyPhotos,
      selectedPhotos,
      addSelectedPhoto,
      showPhotoDialog,
      togglePhotoSelection,
      isPhotoSelected,
    }
  },
})
</script>

<style scoped>
.task-details {
  max-width: 800px;
  margin: 0 auto;
}

.picture-thumbnail {
  position: relative;
  width: 100px;
  height: 100px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.picture-select {
  width: 100px;
  height: 100px;
  border: 1px dashed #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-card {
  transition: all 0.3s ease;
  position: relative;
}

.photo-card:hover {
  transform: scale(1.02);
}

.photo-card.selected {
  border: 2px solid var(--q-primary);
}

.photo-image {
  border-radius: 4px;
}

.photo-card .q-icon {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
}
</style>
