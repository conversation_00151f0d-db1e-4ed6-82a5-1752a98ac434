import { ref, computed } from 'vue'
// import axios from 'axios'
import { htocAxiosApi } from 'boot/axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

// !!! IMPORTANT: Replace 'jots' with your actual API endpoint slug if different !!!
const NOTES_QUERIES_ENDPOINT_SLUG = 'jots'
// !!! IMPORTANT: Replace 'dossier_notes_queries' and 'dossier_jot' if your API response keys are different !!!
const API_LIST_KEY = 'dossier_jots'
const API_SINGLE_ITEM_KEY = 'dossier_jot'

export const useDossierNotesAndQueries = (realtyDossier, $q) => {
  const notesAndQueries = ref([])
  const isLoading = ref(false)
  const error = ref(null)
  // const STORAGE_KEY = `dossier_notes_queries_${realtyDossier.uuid}` // Example if you bring back localStorage

  // Load notes and queries from API
  const loadNotesAndQueries = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await htocAxiosApi.get(
        `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${realtyDossier.uuid}/${NOTES_QUERIES_ENDPOINT_SLUG}`
      )
      // Ensure the response key matches your API
      notesAndQueries.value = response.data[API_LIST_KEY].map((item) => ({
        id: item.uuid,
        text: item.text || item.jot_text, // Prefer 'text', fallback to 'title' if API still uses it
        // 'completed' field is removed
        primPhotoUuid: item.primary_photo_uuid,
        comparisonId: item.dossier_asset_uuid,
        isPrimary: item.is_primary_listing_jot,
        // 'dueDate' field is removed
        pictures: item.photo_uuids || [],
        createdAt: item.created_at,
      }))
    } catch (err) {
      error.value = err.message
      console.error('Error loading notes/queries:', err)
      // // Fallback to localStorage if API fails (if you re-implement)
      // const savedItems = localStorage.getItem(STORAGE_KEY)
      // if (savedItems) {
      //   notesAndQueries.value = JSON.parse(savedItems)
      // }
    } finally {
      isLoading.value = false
    }
  }

  // Save notes/queries to localStorage as backup (if you re-implement)
  // const saveNotesAndQueries = () => {
  //   localStorage.setItem(STORAGE_KEY, JSON.stringify(notesAndQueries.value))
  // }

  // Add a new note or query
  const addNoteOrQuery = async (
    itemText,
    comparisonId = null,
    isPrimary = false,
    // dueDate = null, // Removed dueDate
    pictures = []
  ) => {
    try {
      const response = await htocAxiosApi.post(
        `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${realtyDossier.uuid}/${NOTES_QUERIES_ENDPOINT_SLUG}`,
        {
          jot_text: itemText, // Assuming backend expects 'text'
          // text: itemText, // Assuming backend expects 'text'
          // completed: false, // Removed completed
          dossier_asset_uuid: comparisonId,
          is_primary_listing_jot: isPrimary,
          // due_date: dueDate, // Removed due_date
          photo_uuids: pictures,
        }
      )

      // Ensure the response key matches your API
      const newItemData = response.data[API_SINGLE_ITEM_KEY]
      const newItem = {
        id: newItemData.uuid,
        text: newItemData.text || newItemData.jot_text,
        // completed: newItemData.completed, // Removed
        comparisonId: newItemData.dossier_asset_uuid,
        isPrimary: newItemData.is_primary_listing_jot,
        // dueDate: newItemData.due_date, // Removed
        pictures: newItemData.photo_uuids || [],
        createdAt: newItemData.created_at,
      }

      notesAndQueries.value.push(newItem)
      // saveNotesAndQueries() // if re-implementing localStorage
      return newItem
    } catch (err) {
      error.value = err.message
      console.error('Error adding note:', err)
      throw err
    }
  }

  // Update a note or query
  const updateNoteOrQuery = async (itemId, updates) => {
    try {
      const response = await htocAxiosApi.put(
        `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${realtyDossier.uuid}/${NOTES_QUERIES_ENDPOINT_SLUG}/${itemId}`,
        {
          description: updates.text, // Assuming backend expects 'text'
          // text: updates.text, // Assuming backend expects 'text'
          // completed: updates.completed, // Removed
          dossier_asset_uuid: updates.comparisonId,
          is_primary_listing_jot: updates.isPrimary,
          // due_date: updates.dueDate, // Removed
          pictures: updates.photo_uuids,
        }
      )

      // Ensure the response key matches your API
      const updatedItemData = response.data[API_SINGLE_ITEM_KEY]
      const updatedItem = {
        id: updatedItemData.uuid,
        description: updatedItemData.text || updatedItemData.jot_text,
        // text: updatedItemData.text || updatedItemData.jot_text,
        // completed: updatedItemData.completed, // Removed
        comparisonId: updatedItemData.dossier_asset_uuid,
        isPrimary: updatedItemData.is_primary_listing_jot,
        // dueDate: updatedItemData.due_date, // Removed
        pictures: updatedItemData.photo_uuids || [],
        createdAt: updatedItemData.created_at,
      }

      const itemIndex = notesAndQueries.value.findIndex(
        (item) => item.id === itemId
      )
      if (itemIndex !== -1) {
        notesAndQueries.value[itemIndex] = updatedItem
        // saveNotesAndQueries() // if re-implementing localStorage
      }
      return updatedItem
    } catch (err) {
      error.value = err.message
      console.error('Error updating note:', err)
      throw err
    }
  }

  // toggleTask function is removed as 'completed' status is not typical for notes/queries.
  // If queries have a resolvable state, a new specific function would be needed.

  // Delete a note or query
  const deleteNoteOrQuery = async (itemId) => {
    try {
      await htocAxiosApi.delete(
        `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${realtyDossier.uuid}/${NOTES_QUERIES_ENDPOINT_SLUG}/${itemId}`
      )
      const itemIndex = notesAndQueries.value.findIndex(
        (item) => item.id === itemId
      )
      if (itemIndex !== -1) {
        notesAndQueries.value.splice(itemIndex, 1)
        // saveNotesAndQueries() // if re-implementing localStorage
      }
    } catch (err) {
      error.value = err.message
      console.error('Error deleting note:', err)
      $q.notify({
        type: 'negative',
        message:
          err.response?.data?.message ||
          err.message ||
          'Failed to delete item.',
      })
      throw err
    }
  }

  // Get notes/queries for a specific comparison
  const getNotesAndQueriesForComparison = (comparisonId) => {
    return notesAndQueries.value.filter(
      (item) => item.comparisonId === comparisonId
    )
  }

  // Get Northstar Property notes/queries
  const getPrimaryNotesAndQueries = () => {
    return notesAndQueries.value.filter((item) => item.isPrimary)
  }

  // Get note by ID
  const getNoteOrQueryById = (itemId) => {
    return notesAndQueries.value.find((item) => item.id === itemId)
  }

  // Add picture to note
  const addPictureToNoteOrQuery = async (itemId, pictureUrl) => {
    // Assuming picture is a URL or identifier
    const item = getNoteOrQueryById(itemId)
    if (item) {
      try {
        // This typically involves a separate API call to associate picture with the item,
        // or updating the item with the new picture array if your API supports it directly.
        // The example below assumes updating the item with the new picture array.
        const updatedPictures = [...(item.photo_uuids || []), pictureUrl]
        // You might need a specific API call to add a picture if just updating the array isn't enough
        // For example:
        // await htocAxiosApi.post(`${pwbFlexConfig.dataApiBase}/api_guest/v4/${NOTES_QUERIES_ENDPOINT_SLUG}/${itemId}/pictures`, { picture_url: pictureUrl });
        // item.photo_uuids = updatedPictures; // Update local state after successful API call
        // notesAndQueries.value = [...notesAndQueries.value]; // Trigger reactivity if needed
        // For simplicity, reusing updateNoteOrQuery, but this might not be ideal for just pictures
        await updateNoteOrQuery(itemId, { ...item, pictures: updatedPictures })
      } catch (err) {
        // error.value is already set by updateNoteOrQuery
        console.error('Error adding picture to note:', err)
        throw err // Re-throw so UI can handle it
      }
    }
  }

  // Remove picture from note
  const removePictureFromNoteOrQuery = async (itemId, pictureToRemove) => {
    // pictureToRemove could be URL or index
    const item = getNoteOrQueryById(itemId)
    if (item && item.photo_uuids) {
      try {
        let updatedPictures
        if (typeof pictureToRemove === 'number') {
          // if it's an index
          updatedPictures = [...item.photo_uuids]
          updatedPictures.splice(pictureToRemove, 1)
        } else {
          // if it's a value (e.g., URL)
          updatedPictures = item.photo_uuids.filter(
            (p) => p !== pictureToRemove
          )
        }

        // Similar to addPicture, this might need a specific API call.
        // For example:
        // await htocAxiosApi.delete(`${pwbFlexConfig.dataApiBase}/api_guest/v4/${NOTES_QUERIES_ENDPOINT_SLUG}/${itemId}/pictures/${encodeURIComponent(pictureIdentifier)}`);
        // item.photo_uuids = updatedPictures;
        // notesAndQueries.value = [...notesAndQueries.value];
        await updateNoteOrQuery(itemId, { ...item, pictures: updatedPictures })
      } catch (err) {
        console.error('Error removing picture from note:', err)
        throw err
      }
    }
  }

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    try {
      return new Date(dateString).toLocaleDateString(
        // Consider using a more robust date formatting library if complex needs arise
        typeof navigator !== 'undefined' ? navigator.language : 'en-GB', // Use browser's locale or default
        {
          day: 'numeric',
          month: 'short',
          year: 'numeric',
        }
      )
    } catch (e) {
      console.warn('Invalid date string for formatting:', dateString, e)
      return dateString // Return original string if parsing fails
    }
  }

  // Initialize notes and queries
  if (realtyDossier && realtyDossier.uuid) {
    loadNotesAndQueries()
  } else {
    console.warn(
      'Realty Dossier or its UUID is undefined. Cannot load notes and queries.'
    )
    error.value = 'Realty Dossier information is missing.'
    isLoading.value = false // Ensure loading is false if we don't attempt to load
  }

  return {
    notesAndQueries,
    isLoading,
    error,
    addNoteOrQuery,
    updateNoteOrQuery,
    // toggleTask is removed
    deleteNoteOrQuery,
    getNotesAndQueriesForComparison,
    getPrimaryNotesAndQueries,
    getNoteOrQueryById,
    addPictureToNoteOrQuery,
    removePictureFromNoteOrQuery,
    formatDate,
    reload: loadNotesAndQueries, // Expose a reload function
  }
}
