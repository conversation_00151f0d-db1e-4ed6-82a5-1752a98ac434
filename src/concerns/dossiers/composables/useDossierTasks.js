import { ref, computed } from 'vue'
// import axios from 'axios'
import { htocAxiosApi } from 'boot/axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export const useDossierTasks = (realtyDossier, $q) => {
  const tasks = ref([])
  const isLoading = ref(false)
  const error = ref(null)
  // const STORAGE_KEY = `dossier_tasks_${realtyDossier.uuid}`

  // Load tasks from API
  const loadTasks = async () => {
    isLoading.value = true
    error.value = null
    try {
      const response = await htocAxiosApi.get(
        `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${realtyDossier.uuid}/tasks`
      )
      tasks.value = response.data.dossier_tasks.map((task) => ({
        id: task.uuid,
        text: task.title,
        completed: task.completed,
        comparisonId: task.comparison_id,
        isPrimary: task.is_primary,
        dueDate: task.due_date,
        pictures: task.pictures || [],
        createdAt: task.created_at,
      }))
    } catch (err) {
      error.value = err.message
      console.error('Error loading tasks:', err)
      // // Fallback to localStorage if API fails
      // const savedTasks = localStorage.getItem(STORAGE_KEY)
      // if (savedTasks) {
      //   tasks.value = JSON.parse(savedTasks)
      // }
    } finally {
      isLoading.value = false
    }
  }

  // Save tasks to localStorage as backup
  // const saveTasks = () => {
  //   localStorage.setItem(STORAGE_KEY, JSON.stringify(tasks.value))
  // }

  // Add a new task
  const addTask = async (
    taskText,
    comparisonId = null,
    isPrimary = false,
    dueDate = null,
    pictures = []
  ) => {
    try {
      const response = await htocAxiosApi.post(
        `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${realtyDossier.uuid}/tasks`,
        {
          text: taskText,
          completed: false,
          comparison_id: comparisonId,
          is_primary: isPrimary,
          due_date: dueDate,
          pictures: pictures,
        }
      )

      const newTask = {
        id: response.data.dossier_task.uuid,
        text: response.data.dossier_task.title,
        completed: response.data.dossier_task.completed,
        comparisonId: response.data.dossier_task.comparison_id,
        isPrimary: response.data.dossier_task.is_primary,
        dueDate: response.data.dossier_task.due_date,
        pictures: response.data.dossier_task.pictures || [],
        createdAt: response.data.dossier_task.created_at,
      }

      tasks.value.push(newTask)
      // saveTasks()
      return newTask
    } catch (err) {
      error.value = err.message
      console.error('Error adding task:', err)
      throw err
    }
  }

  // Update a task
  const updateTask = async (taskId, updates) => {
    try {
      const response = await htocAxiosApi.put(
        `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${realtyDossier.uuid}/tasks/${taskId}`,
        {
          text: updates.text,
          completed: updates.completed,
          comparison_id: updates.comparisonId,
          is_primary: updates.isPrimary,
          due_date: updates.dueDate,
          pictures: updates.pictures,
        }
      )

      const updatedTask = {
        id: response.data.dossier_task.uuid,
        text: response.data.dossier_task.title,
        completed: response.data.dossier_task.completed,
        comparisonId: response.data.dossier_task.comparison_id,
        isPrimary: response.data.dossier_task.is_primary,
        dueDate: response.data.dossier_task.due_date,
        pictures: response.data.dossier_task.pictures || [],
        createdAt: response.data.dossier_task.created_at,
      }

      const taskIndex = tasks.value.findIndex((task) => task.id === taskId)
      if (taskIndex !== -1) {
        tasks.value[taskIndex] = updatedTask
        // saveTasks()
      }
      return updatedTask
    } catch (err) {
      error.value = err.message
      console.error('Error updating task:', err)
      throw err
    }
  }

  // Toggle task completion
  const toggleTask = async (taskId) => {
    const task = tasks.value.find((task) => task.id === taskId)
    if (task) {
      try {
        await updateTask(taskId, { ...task, completed: !task.completed })
      } catch (err) {
        error.value = err.message
        console.error('Error toggling task:', err)
        throw err
      }
    }
  }

  // Delete a task
  const deleteTask = async (taskId) => {
    try {
      await htocAxiosApi.delete(
        `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${realtyDossier.uuid}/tasks/${taskId}`
      )
      const taskIndex = tasks.value.findIndex((task) => task.id === taskId)
      if (taskIndex !== -1) {
        tasks.value.splice(taskIndex, 1)
        // saveTasks()
      }
    } catch (err) {
      error.value = err.message
      console.error('Error deleting task:', err)
      throw err
    }
  }

  // Get tasks for a specific comparison
  const getTasksForComparison = (comparisonId) => {
    return tasks.value.filter((task) => task.comparisonId === comparisonId)
  }

  // Get Northstar Property tasks
  const getPrimaryTasks = () => {
    return tasks.value.filter((task) => task.isPrimary)
  }

  // Get task by ID
  const getTaskById = (taskId) => {
    return tasks.value.find((task) => task.id === taskId)
  }

  // Add picture to task
  const addPictureToTask = async (taskId, picture) => {
    const task = getTaskById(taskId)
    if (task) {
      try {
        const updatedPictures = [...task.pictures, picture]
        await updateTask(taskId, { ...task, pictures: updatedPictures })
      } catch (err) {
        error.value = err.message
        console.error('Error adding picture to task:', err)
        throw err
      }
    }
  }

  // Remove picture from task
  const removePictureFromTask = async (taskId, pictureIndex) => {
    const task = getTaskById(taskId)
    if (task) {
      try {
        const updatedPictures = [...task.pictures]
        updatedPictures.splice(pictureIndex, 1)
        await updateTask(taskId, { ...task, pictures: updatedPictures })
      } catch (err) {
        error.value = err.message
        console.error('Error removing picture from task:', err)
        throw err
      }
    }
  }

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    })
  }

  // Initialize tasks
  loadTasks()

  return {
    tasks,
    isLoading,
    error,
    addTask,
    updateTask,
    toggleTask,
    deleteTask,
    getTasksForComparison,
    getPrimaryTasks,
    getTaskById,
    addPictureToTask,
    removePictureFromTask,
    formatDate,
  }
}
