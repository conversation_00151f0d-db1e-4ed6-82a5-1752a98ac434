// src/composables/usePhotoComments.js
import { ref, computed, watch } from 'vue' // Added watch
import { htocAxiosApi } from 'boot/axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
import { useQuasar } from 'quasar'

const NOTES_QUERIES_ENDPOINT_SLUG = 'jots'
const API_SINGLE_ITEM_KEY = 'dossier_jot' // Only needed for add/delete response
const PHOTO_COMMENT_LABEL = 'Photo Comment'

export function usePhotoComments(
  assetsComparisonUuid,
  dossierUuidRef,
  photoUuidRef,
  quasarInstance,
  allDossierJotsRef = ref(null)
) {
  const $q = quasarInstance || useQuasar()

  const comments = ref([])
  const isLoadingComments = ref(false) // Will be true if fetching individually
  const commentsError = ref(null)

  const mapApiItemToComment = (item) => ({
    id: item.uuid,
    text: item.text || item.jot_text,
    createdAt: item.created_at,
    user:
      item.user_details ||
      (item.user
        ? { name: item.user.name, full_name: item.user.full_name }
        : null),
  })

  const filterAndSetComments = () => {
    const currentPhotoUuid =
      typeof photoUuidRef === 'function' ? photoUuidRef() : photoUuidRef.value
    const allJots =
      typeof allDossierJotsRef === 'function'
        ? allDossierJotsRef()
        : allDossierJotsRef.value

    if (allJots && currentPhotoUuid) {
      comments.value = allJots
        .filter(
          (item) => item.primary_photo_uuid === currentPhotoUuid
          // &&
          //   item.label === PHOTO_COMMENT_LABEL
        )
        .map(mapApiItemToComment)
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      isLoadingComments.value = false
      commentsError.value = null
    } else if (!allJots && currentPhotoUuid) {
      // Fallback: If allDossierJotsRef is not provided, allow individual fetching (though gallery aims to avoid this)
      // This part makes the composable still usable if an item is displayed outside the gallery.
      // For the gallery optimization, this 'else if' block would ideally not be hit often.
      // loadIndividualPhotoComments(); // We'll call this explicitly if needed from component
    } else {
      comments.value = []
    }
  }

  // Watch for changes in the provided allDossierJotsRef or the photoUuidRef to re-filter
  watch([allDossierJotsRef, photoUuidRef], filterAndSetComments, {
    immediate: true,
    deep: true,
  })

  // Function to load comments specifically for THIS photo if not provided by gallery
  // This is a fallback and not the primary path for gallery optimization.
  const loadIndividualPhotoComments = async () => {
    const currentDossierUuid =
      typeof dossierUuidRef === 'function'
        ? dossierUuidRef()
        : dossierUuidRef.value
    const currentPhotoUuid =
      typeof photoUuidRef === 'function' ? photoUuidRef() : photoUuidRef.value

    if (allDossierJotsRef.value) return // Don't fetch if master list is already provided

    if (!currentDossierUuid || !currentPhotoUuid) {
      commentsError.value =
        'Dossier or Photo UUID missing for individual comment load.'
      comments.value = []
      return
    }

    isLoadingComments.value = true
    commentsError.value = null
    try {
      // This fetches ALL jots for the dossier, then filters.
      // A more optimized API would allow fetching jots for a specific primary_photo_uuid.
      const response = await htocAxiosApi.get(
        `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${currentDossierUuid}/${NOTES_QUERIES_ENDPOINT_SLUG}`,
        {
          params: {
            primary_photo_uuid: currentPhotoUuid,
            // label: PHOTO_COMMENT_LABEL,
          },
        } // TRY OPTIMIZED API
      )
      // IF OPTIMIZED API WORKS, response.data[API_LIST_KEY] would be pre-filtered
      // const photoSpecificJots = response.data[API_LIST_KEY] || [];
      // comments.value = photoSpecificJots.map(mapApiItemToComment)
      //   .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

      // Fallback if the above optimized API call doesn't work or isn't available
      // You would fetch all and filter as before:
      const allJotsResponse = await htocAxiosApi.get(
        `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${currentDossierUuid}/${NOTES_QUERIES_ENDPOINT_SLUG}`
      )
      const allDossierJots = allJotsResponse.data['dossier_jots'] || [] // Use actual list key
      comments.value = allDossierJots
        .filter(
          (item) => item.primary_photo_uuid === currentPhotoUuid
          // &&
          //   item.label === PHOTO_COMMENT_LABEL
        )
        .map(mapApiItemToComment)
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    } catch (err) {
      commentsError.value =
        err.response?.data?.message || err.message || 'Failed to load comments.'
      console.error('Error loading individual photo comments:', err)
    } finally {
      isLoadingComments.value = false
    }
  }

  const addPhotoComment = async (commentText) => {
    // ... (add logic remains mostly the same, still posts to the main endpoint)
    // It should ideally re-trigger a refresh of the central allDossierJotsRef in the gallery
    // or optimistically add to its local comments.value and emit an event.
    const currentDossierUuid =
      typeof dossierUuidRef === 'function'
        ? dossierUuidRef()
        : dossierUuidRef.value
    const currentPhotoUuid =
      typeof photoUuidRef === 'function' ? photoUuidRef() : photoUuidRef.value

    if (!currentDossierUuid || !currentPhotoUuid) {
      /* ... error handling ... */ return null
    }
    if (!commentText || !commentText.trim()) {
      /* ... error handling ... */ return null
    }

    try {
      const payload = {
        jot_text: commentText,
        dossier_asset_uuid: currentDossierUuid,
        primary_photo_uuid: currentPhotoUuid,
        dossier_assets_comparison_uuid: assetsComparisonUuid,
        // label: PHOTO_COMMENT_LABEL,
        // is_primary: false,
      }
      const response = await htocAxiosApi.post(
        `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${currentDossierUuid}/${NOTES_QUERIES_ENDPOINT_SLUG}`,
        payload
      )
      const newCommentData = response.data[API_SINGLE_ITEM_KEY]
      if (newCommentData) {
        const newComment = mapApiItemToComment(newCommentData)
        // Optimistically add to local comments. The gallery should also update its master list.
        comments.value.unshift(newComment)
        if (allDossierJotsRef.value) {
          // If master list exists, update it too
          allDossierJotsRef.value.unshift(newCommentData) // Add raw item
        }
        $q.notify({ type: 'positive', message: 'Comment added!' })
        return newComment
      } else {
        throw new Error('API did not return new comment data.')
      }
    } catch (err) {
      /* ... error handling ... */ return null
    }
  }

  const deletePhotoComment = async (commentId) => {
    // ... (delete logic remains mostly the same)
    // Similar to add, it should ideally refresh the gallery's master list or emit.
    const currentDossierUuid =
      typeof dossierUuidRef === 'function'
        ? dossierUuidRef()
        : dossierUuidRef.value
    if (!currentDossierUuid) {
      /* ... error handling ... */ return
    }

    try {
      await htocAxiosApi.delete(
        `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${currentDossierUuid}/${NOTES_QUERIES_ENDPOINT_SLUG}/${commentId}`
      )
      comments.value = comments.value.filter(
        (comment) => comment.id !== commentId
      )
      if (allDossierJotsRef.value) {
        // If master list exists, update it too
        const index = allDossierJotsRef.value.findIndex(
          (jot) => jot.uuid === commentId
        )
        if (index > -1) allDossierJotsRef.value.splice(index, 1)
      }
      $q.notify({ type: 'positive', message: 'Comment deleted.' })
    } catch (err) {
      /* ... error handling ... */
    }
  }

  const formatDate = (dateString) => {
    /* ... same ... */
    if (!dateString) return 'N/A'
    try {
      return new Date(dateString).toLocaleDateString(
        typeof navigator !== 'undefined' ? navigator.language : 'en-GB',
        {
          day: 'numeric',
          month: 'short',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
        }
      )
    } catch (e) {
      console.warn('Invalid date string for formatting:', dateString, e)
      return dateString
    }
  }

  // Initial call to filter if data is already present
  // filterAndSetComments(); // watch immediate:true handles this

  return {
    comments,
    isLoadingComments, // This will now mostly reflect if allDossierJotsRef is null and individual fetch is happening
    commentsError,
    // loadPhotoComments is now more of an internal trigger or for individual load
    loadIndividualPhotoComments, // Expose if explicit individual load is needed
    addPhotoComment,
    deletePhotoComment,
    formatDate,
  }
}
