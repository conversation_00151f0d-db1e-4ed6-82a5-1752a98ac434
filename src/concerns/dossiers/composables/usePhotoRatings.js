// src/concerns/dossiers/composables/usePhotoRatings.js
import { ref, computed, watch } from 'vue'
import { htocAxiosApi } from 'boot/axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
import { useQuasar } from 'quasar'

const RATINGS_QUERIES_ENDPOINT_SLUG = 'photo_ratings'
const API_SINGLE_ITEM_KEY = 'photo_rating'
const PHOTO_RATING_LABEL = 'Photo Rating'

export function usePhotoRatings(
  assetsComparisonUuid,
  dossierUuidRef,
  photoUuidRef,
  quasarInstance,
  allDossierRatingsRef = ref(null)
) {
  const $q = quasarInstance || useQuasar()

  const ratings = ref([])
  const currentUserRating = ref(0)
  const isLoadingRatings = ref(false)
  const ratingsError = ref(null)

  const mapApiItemToRating = (item) => ({
    id: item.uuid,
    rating: item.rating_value || 0,
    createdAt: item.created_at,
    updatedAt: item.updated_at,
    user:
      item.user_details ||
      (item.user
        ? { name: item.user.name, full_name: item.user.full_name }
        : null),
  })

  const filterAndSetRatings = () => {
    const currentPhotoUuid =
      typeof photoUuidRef === 'function' ? photoUuidRef() : photoUuidRef.value
    const allRatings =
      typeof allDossierRatingsRef === 'function'
        ? allDossierRatingsRef()
        : allDossierRatingsRef.value

    if (allRatings && currentPhotoUuid) {
      const filteredRatings = allRatings
        .filter((item) => item.primary_photo_uuid === currentPhotoUuid)
        .map(mapApiItemToRating)
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))

      ratings.value = filteredRatings

      // Set current user's rating if available
      const userRating = filteredRatings.find(
        (rating) =>
          rating.user &&
          rating.user.name === $q.localStorage.getItem('currentUserName')
      )
      currentUserRating.value = userRating ? userRating.rating : 0

      isLoadingRatings.value = false
      ratingsError.value = null
    } else if (!allRatings && currentPhotoUuid) {
      // Fallback: If allDossierRatingsRef is not provided, allow individual fetching
    } else {
      ratings.value = []
      currentUserRating.value = 0
    }
  }

  // Watch for changes in the provided allDossierRatingsRef or the photoUuidRef to re-filter
  watch([allDossierRatingsRef, photoUuidRef], filterAndSetRatings, {
    immediate: true,
    deep: true,
  })

  // Function to load ratings specifically for THIS photo if not provided by gallery
  const loadIndividualPhotoRatings = async () => {
    const currentDossierUuid =
      typeof dossierUuidRef === 'function'
        ? dossierUuidRef()
        : dossierUuidRef.value
    const currentPhotoUuid =
      typeof photoUuidRef === 'function' ? photoUuidRef() : photoUuidRef.value

    if (allDossierRatingsRef.value) return // Don't fetch if master list is already provided

    if (!currentDossierUuid || !currentPhotoUuid) {
      ratingsError.value =
        'Dossier or Photo UUID missing for individual rating load.'
      ratings.value = []
      currentUserRating.value = 0
      return
    }

    isLoadingRatings.value = true
    ratingsError.value = null
    try {
      const response = await htocAxiosApi.get(
        `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${currentDossierUuid}/${RATINGS_QUERIES_ENDPOINT_SLUG}`,
        {
          params: {
            primary_photo_uuid: currentPhotoUuid,
          },
        }
      )

      const allDossierRatings = response.data['photo_ratings'] || []
      const filteredRatings = allDossierRatings
        .filter((item) => item.primary_photo_uuid === currentPhotoUuid)
        .map(mapApiItemToRating)
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))

      ratings.value = filteredRatings

      // Set current user's rating if available
      const userRating = filteredRatings.find(
        (rating) =>
          rating.user &&
          rating.user.name === $q.localStorage.getItem('currentUserName')
      )
      currentUserRating.value = userRating ? userRating.rating : 0
    } catch (err) {
      ratingsError.value =
        err.response?.data?.message || err.message || 'Failed to load ratings.'
      console.error('Error loading individual photo ratings:', err)
    } finally {
      isLoadingRatings.value = false
    }
  }

  const addOrUpdatePhotoRating = async (ratingValue) => {
    const currentDossierUuid =
      typeof dossierUuidRef === 'function'
        ? dossierUuidRef()
        : dossierUuidRef.value
    const currentPhotoUuid =
      typeof photoUuidRef === 'function' ? photoUuidRef() : photoUuidRef.value

    if (!currentDossierUuid || !currentPhotoUuid) {
      $q.notify({
        type: 'negative',
        message: 'Missing dossier or photo information',
      })
      return null
    }

    if (ratingValue < 0 || ratingValue > 5) {
      $q.notify({ type: 'negative', message: 'Rating must be between 0 and 5' })
      return null
    }

    try {
      const payload = {
        rating_value: ratingValue,
        dossier_asset_uuid: currentDossierUuid,
        primary_photo_uuid: currentPhotoUuid,
        dossier_assets_comparison_uuid: assetsComparisonUuid,
        label: PHOTO_RATING_LABEL,
      }

      // Check if user already has a rating for this photo
      const existingRating = ratings.value.find(
        (rating) =>
          rating.user &&
          rating.user.name === $q.localStorage.getItem('currentUserName')
      )

      let response

      if (existingRating) {
        // Update existing rating
        response = await htocAxiosApi.put(
          `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${currentDossierUuid}/${RATINGS_QUERIES_ENDPOINT_SLUG}/${existingRating.id}`,
          payload
        )
      } else {
        // Create new rating
        response = await htocAxiosApi.post(
          `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${currentDossierUuid}/${RATINGS_QUERIES_ENDPOINT_SLUG}`,
          payload
        )
      }

      const newRatingData = response.data[API_SINGLE_ITEM_KEY]

      if (newRatingData) {
        const newRating = mapApiItemToRating(newRatingData)

        if (existingRating) {
          // Replace existing rating
          const index = ratings.value.findIndex(
            (r) => r.id === existingRating.id
          )
          if (index !== -1) {
            ratings.value.splice(index, 1, newRating)
          }

          if (allDossierRatingsRef.value) {
            // Update in master list too
            const masterIndex = allDossierRatingsRef.value.findIndex(
              (r) => r.uuid === existingRating.id
            )
            if (masterIndex !== -1) {
              allDossierRatingsRef.value.splice(masterIndex, 1, newRatingData)
            }
          }
        } else {
          // Add new rating
          ratings.value.unshift(newRating)

          if (allDossierRatingsRef.value) {
            // Add to master list too
            allDossierRatingsRef.value.unshift(newRatingData)
          }
        }

        currentUserRating.value = ratingValue
        $q.notify({ type: 'positive', message: 'Rating saved!' })
        return newRating
      } else {
        throw new Error('API did not return new rating data.')
      }
    } catch (err) {
      console.error('Error saving rating:', err)
      $q.notify({
        type: 'negative',
        message:
          err.response?.data?.message ||
          err.message ||
          'Failed to save rating.',
      })
      return null
    }
  }

  const deletePhotoRating = async (ratingId) => {
    const currentDossierUuid =
      typeof dossierUuidRef === 'function'
        ? dossierUuidRef()
        : dossierUuidRef.value
    if (!currentDossierUuid) {
      $q.notify({ type: 'negative', message: 'Missing dossier information' })
      return
    }

    try {
      await htocAxiosApi.delete(
        `${pwbFlexConfig.dataApiBase}/api_guest/v4/dossiers/${currentDossierUuid}/${RATINGS_QUERIES_ENDPOINT_SLUG}/${ratingId}`
      )

      ratings.value = ratings.value.filter((rating) => rating.id !== ratingId)

      if (allDossierRatingsRef.value) {
        // Update master list too
        const index = allDossierRatingsRef.value.findIndex(
          (rating) => rating.uuid === ratingId
        )
        if (index > -1) allDossierRatingsRef.value.splice(index, 1)
      }

      // Reset current user rating if it was the user's rating that was deleted
      const userRating = ratings.value.find(
        (rating) =>
          rating.user &&
          rating.user.name === $q.localStorage.getItem('currentUserName')
      )
      currentUserRating.value = userRating ? userRating.rating : 0

      $q.notify({ type: 'positive', message: 'Rating deleted.' })
    } catch (err) {
      console.error('Error deleting rating:', err)
      $q.notify({
        type: 'negative',
        message:
          err.response?.data?.message ||
          err.message ||
          'Failed to delete rating.',
      })
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    try {
      return new Date(dateString).toLocaleDateString(
        typeof navigator !== 'undefined' ? navigator.language : 'en-GB',
        {
          day: 'numeric',
          month: 'short',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
        }
      )
    } catch (e) {
      console.warn('Invalid date string for formatting:', dateString, e)
      return dateString
    }
  }

  return {
    ratings,
    currentUserRating,
    isLoadingRatings,
    ratingsError,
    loadIndividualPhotoRatings,
    addOrUpdatePhotoRating,
    deletePhotoRating,
    formatDate,
  }
}
