// composables/useScootPreferences.js
import { ref, watch } from 'vue'
import { htocAxiosApi } from 'boot/axios' // Assuming this is your configured Axios instance
import { pwbFlexConfig } from 'boot/pwb-flex-conf' // If you still need this for base URL

// Use your Rails API base URL. If pwbFlexConfig.dataApiBase is still relevant, use it.
// Otherwise, define it directly or from another config.
// For this example, I'll assume it might be part of pwbFlexConfig or you'll define it.
const API_BASE_URL = pwbFlexConfig.dataApiBase || '/api' // Adjust as needed
// const API_BASE_URL = '' // Assuming htocAxiosApi has baseURL set or paths are absolute from root

const SCOOT_API_ENDPOINT = (scootUuid) =>
  `${API_BASE_URL}/api_guest/v4/scoots/${scootUuid}`

export const useScootPreferences = (initialScootUuid, $q) => {
  const scootPreferences = ref(null) // Will hold the scoot data object
  const isLoading = ref(false)
  const error = ref(null)
  const currentScootUuid = ref(initialScootUuid)

  // Default scoot structure, similar to what ScootPreferencesForm expects
  // This can be used if a new scoot is being configured or as a fallback.
  const getDefaultScootStructure = () => ({
    country_code: 'UK',
    bathrooms_max_absolute: 0,
    bathrooms_max_preferred: 0,
    bathrooms_min_absolute: 0,
    bathrooms_min_preferred: 0,
    bedrooms_max_absolute: 0,
    bedrooms_max_preferred: 0,
    bedrooms_min_absolute: 0,
    bedrooms_min_preferred: 0,
    feature_preferences: {},
    indoor_area_max_absolute: 0,
    indoor_area_min_absolute: 0,
    max_distance_from_vicinity_absolute: null,
    max_distance_from_vicinity_preferred: null,
    plot_area_max_absolute: 0,
    plot_area_min_absolute: 0,
    preferred_area_unit: 0,
    preferred_currency: 'GBP',
    preferred_distance_unit: 0,
    preferred_locale: 'en-UK',
    preferred_property_type: '',
    preferred_vicinity_name: '',
    price_max_absolute_cents: 50000000,
    price_max_preferred_cents: 40000000,
    price_min_absolute_cents: 10000000,
    price_min_preferred_cents: 10000000,
    // Add any other fields your form might rely on for its initial state
    // or that you want to ensure are part of the object.
  })

  // --- Fetch Scoot Preferences ---
  const loadScootPreferences = async (scootUuidToLoad) => {
    if (!scootUuidToLoad) {
      error.value = 'Scoot UUID is required to load preferences.'
      // Potentially set scootPreferences to default if creating a new one
      // scootPreferences.value = getDefaultScootStructure();
      isLoading.value = false
      return
    }

    isLoading.value = true
    error.value = null
    try {
      const response = await htocAxiosApi.get(
        SCOOT_API_ENDPOINT(scootUuidToLoad)
      )
      // The Rails controller directly returns the scoot object
      scootPreferences.value = {
        ...getDefaultScootStructure(),
        ...response.data,
      }
    } catch (err) {
      error.value =
        err.response?.data?.error ||
        err.message ||
        'Failed to load scoot preferences.'
      console.error('Error loading scoot preferences:', err)
      if ($q) {
        $q.notify({
          type: 'negative',
          message: error.value,
        })
      }
      // Optionally, set to default on load failure if that's desired behavior
      // scootPreferences.value = getDefaultScootStructure();
    } finally {
      isLoading.value = false
    }
  }

  // --- Update Scoot Preferences ---
  const updateScootPreferences = async (preferencesToSave) => {
    if (!currentScootUuid.value) {
      error.value = 'Scoot UUID is missing. Cannot update preferences.'
      if ($q) {
        $q.notify({ type: 'negative', message: error.value })
      }
      return null // Indicate failure
    }
    if (!preferencesToSave) {
      error.value = 'No preference data provided to save.'
      if ($q) {
        $q.notify({ type: 'negative', message: error.value })
      }
      return null
    }

    isLoading.value = true
    error.value = null
    try {
      // The Rails controller expects the data nested under 'scoot' key
      const payload = { scoot: preferencesToSave }
      const response = await htocAxiosApi.put(
        SCOOT_API_ENDPOINT(currentScootUuid.value),
        payload
      )

      scootPreferences.value = {
        ...getDefaultScootStructure(),
        ...response.data,
      } // Update local state with server response

      if ($q) {
        $q.notify({
          color: 'positive',
          message: 'Preferences saved successfully!',
          icon: 'check',
        })
      }
      return scootPreferences.value // Return the updated preferences
    } catch (err) {
      error.value =
        err.response?.data?.errors?.join(', ') ||
        err.response?.data?.error ||
        err.message ||
        'Failed to update scoot preferences.'
      console.error('Error updating scoot preferences:', err)
      if ($q) {
        $q.notify({
          type: 'negative',
          message: error.value,
        })
      }
      return null // Indicate failure
    } finally {
      isLoading.value = false
    }
  }

  // Watch for changes in initialScootUuid if it's a ref, or allow manual reload
  watch(
    () => initialScootUuid, // If initialScootUuid can change reactively
    (newUuid) => {
      if (newUuid) {
        currentScootUuid.value = newUuid
        loadScootPreferences(newUuid)
      } else {
        // Handle case where UUID becomes null/undefined if needed
        scootPreferences.value = getDefaultScootStructure() // Or null
        error.value = 'Scoot UUID became invalid.'
      }
    },
    { immediate: true } // Load immediately when the composable is used
  )

  // If initialScootUuid is not a ref and you only want to load once:
  // if (initialScootUuid) {
  //   loadScootPreferences(initialScootUuid);
  // } else {
  //   // Initialize with defaults if no UUID is provided at creation,
  //   // e.g., for a "new scoot preferences" form.
  //   scootPreferences.value = getDefaultScootStructure();
  //   // error.value = 'No Scoot UUID provided on initialization.';
  //   // isLoading.value = false;
  // }

  return {
    scootPreferences,
    isLoading,
    error,
    loadScootPreferences, // Expose for manual reload or loading with a new UUID
    updateScootPreferences,
    currentScootUuid, // Expose current UUID if needed by the parent
    getDefaultScootStructure, // Expose if parent needs to initialize form with defaults
  }
}
