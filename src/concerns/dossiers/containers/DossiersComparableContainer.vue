<template>
  <div class="DossiersComparableContainer">
    <div>
      <div class="text-h5 q-pb-sm q-px-sm">Comparing with:</div>
      <h3 class="text-subtitle1 q-mb-sm q-mt-none text-left q-px-sm">
        {{ comparisonData.comparison?.right_side_details?.listing?.title }}
      </h3>
      <div class="row q-gutter-md">
        <div class="col-xs-12 q-my-sm q-px-sm">
          <div class="text-subtitle1 text-left">
            {{ rightSideProperty.street_address }}
          </div>
          <!-- <div class="text-subtitle1 text-left">
            {{ rightSideProperty.postal_code }}
          </div> -->
        </div>
      </div>
    </div>

    <!-- <div class="row items-center justify-left q-mb-md">
      <q-toggle v-model="showSideBySide"
                color="primary"
                @update:model-value="toggleAll"
                :label="showSideBySide ? 'Show Beside Main Property' : 'Show Comparable Only'"
                class="q-ml-none">
      </q-toggle>
    </div> -->
    <!-- <div v-if="isGuestRoute">
        <q-breadcrumbs class="q-py-md q-px-sm text-blue">
          <template v-slot:separator>
          </template>
<q-breadcrumbs-el :to="{ name: 'rMyPurchaseEvaluations' }" label="Back To Your Listings" icon="widgets" />
<q-icon size="1.5em" name="chevron_left" color="primary" />
</q-breadcrumbs>
</div> -->
    <!-- <keep-alive>
          <router-view :saleListing="saleListing" />
        </keep-alive> -->
    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component"
                   :comparisonData="comparisonData"
                   :saleListing="saleListing"
                   :realtyDossier="realtyDossier" />
      </keep-alive>
    </router-view>
  </div>
</template>
<script>
import useDossiers from 'src/compose/useDossiers.js'
export default {
  components: {},
  methods: {
    toggleAll() {
      if (this.$route.name === 'rComparableSbs') {
        this.$router.push({ name: 'rComparableSolo' })
      } else {
        this.$router.push({ name: 'rComparableSbs' })
      }
    },
    // Method to fetch comparison data
    async fetchComparisonData(uuid) {
      // Only proceed if uuid is truthy (not null, undefined, empty string)
      if (!uuid) {
        console.warn('No assetsComparisonUuid provided. Skipping fetch.')
        this.comparisonData = {} // Clear data if no UUID
        this.error = null
        return
      }

      // console.log(`Workspaceing comparison data for UUID: ${uuid}`);
      this.isLoading = true
      this.error = null
      const retrievalObject = { assetsComparisonUuid: uuid }

      try {
        const responseObject = await this.getDossierComparison(retrievalObject)
        this.comparisonData = responseObject.data || {} // Use default empty object if data is null/undefined
      } catch (error) {
        console.error('Error fetching dossier comparison:', error)
        this.error = 'Failed to load comparison data.' // Store error message
        this.comparisonData = {} // Clear data on error
      } finally {
        this.isLoading = false
      }
    },
  },
  setup(props) {
    const { getDossierComparison } = useDossiers()
    return {
      getDossierComparison,
    }
  },
  created() {
    // Initial fetch attempt when the component is created
    this.fetchComparisonData(this.currentAssetsComparisonUuid)
  },
  props: {
    saleListing: {
      type: Object,
      required: false,
    },
    realtyDossier: {
      type: Object,
      required: false,
    },
  },
  computed: {
    rightSideProperty() {
      return this.comparisonData?.comparison?.right_side_details?.listing || {}
    },
    showSideBySide() {
      if (this.$route.name === 'rComparableSbs') {
        return true
      } else {
        return false
      }
    },
    // Computed property to easily access the UUID
    currentAssetsComparisonUuid() {
      return this.$route.params.assetsComparisonUuid
    },
    // isGuestRoute() {
    //   return true
    // },
  },
  data() {
    return {
      // showSideBySide: false,
      comparisonData: {},
    }
  },
}
</script>
<style></style>
