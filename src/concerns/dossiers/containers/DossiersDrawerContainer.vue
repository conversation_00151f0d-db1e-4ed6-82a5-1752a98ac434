<template>
  <q-no-ssr>
    <q-layout class="DossiersDrawerContainer"
              view="lHh Lpr lFf">
      <!-- Header -->
      <q-header elevated
                class="bg-primary text-white">
        <q-toolbar>
          <q-btn flat
                 dense
                 round
                 @click="toggleLeftDrawer"
                 icon="menu"
                 aria-label="Menu" />
          <q-btn flat
                 :to="{ name: 'rDossierHome' }"
                 icon="home"
                 class="flex items-center no-uppercase font-bold text-2xl lg:text-4xl text-white">
          </q-btn>
          <q-btn flat
                 :to="{ name: 'rSubdomainRoot' }"
                 class="flex items-center no-uppercase font-bold text-2xl lg:text-4xl text-white">
            <span style="height: 100%; display: inline-flex; font-size: large">
              Homes<span class="text-secondary">To</span>Compare
            </span>
          </q-btn>
          <q-space />
          <div class="q-gutter-sm row items-center no-wrap">
            <q-btn round
                   dense
                   flat
                   color="white"
                   :icon="$q.fullscreen.isActive ? 'fullscreen_exit' : 'fullscreen'"
                   @click="$q.fullscreen.toggle()"
                   v-if="$q.screen.gt.sm" />
          </div>
        </q-toolbar>
      </q-header>

      <!-- Drawer -->
      <!-- Drawer -->

      <!-- Drawer -->
      <q-drawer v-model="leftDrawerOpen"
                show-if-above
                bordered
                class="bg-primary text-white h2c-drawer"
                style="height: 100vh">
        <q-img v-if="drawerImgSrc"
               class="absolute-top"
               :src="drawerImgSrc"
               style="height: 240px">
          <div class="absolute-top text-overlay"
               style="padding: 5px">
            <ListingPriceAndRoomsMin :evaluationDetails="saleListing" />
          </div>
        </q-img>
        <q-scroll-area class="absolute-top bg-primary text-white"
                       style="height: 100%; margin-top: 240px; border-right: 1px solid #ddd">
          <q-list class="dossier-drawer-inner-list"
                  style="margin-bottom: 500px">
            <q-item :to="{ name: 'rDossierHome' }"
                    exact
                    clickable
                    v-ripple
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="home" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Dossier Home</q-item-label>
              </q-item-section>
            </q-item>
            <q-item :to="{ name: 'rDossierOverview' }"
                    exact
                    clickable
                    v-ripple
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Property Details</q-item-label>
              </q-item-section>
            </q-item>
            <!-- Use the new OverviewMenu component -->
            <!-- <DossierOverviewDrawerItem :asset-parts="summaryGroupedAssetParts"
                                       route-name="rDossierOverview"
                                       label-text="Property Details"
                                       icon="dashboard"></DossierOverviewDrawerItem>
 -->
            <!-- Other Menu Items -->
            <q-item v-for="item in drawerMenuItems"
                    :key="item.name"
                    :to="{ name: item.routeName }"
                    exact
                    clickable
                    v-ripple
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon :name="item.icon" />
              </q-item-section>
              <q-item-section>
                <q-item-label>{{ item.label }}</q-item-label>
              </q-item-section>
            </q-item>
            <AssetComparisonDrawerItem :asset-parts="dossierAssetsComparisons"
                                       route-name="rComparables"
                                       child-route-name="rComparableSbs"
                                       label-text="Comparables"
                                       icon="compare">
            </AssetComparisonDrawerItem>
            <q-item :to="{ name: 'rConversations' }"
                    exact
                    clickable
                    v-ripple
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="chat" />
              </q-item-section>
              <q-item-section>
                <q-item-label>AI Chat</q-item-label>
              </q-item-section>
            </q-item>
            <q-item :to="{ name: 'rNotesAndQueries' }"
                    exact
                    clickable
                    v-ripple
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="notes" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Notes & Comments</q-item-label>
              </q-item-section>
            </q-item>
            <q-item :to="{ name: 'rNotifications' }"
                    exact
                    clickable
                    v-ripple
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="notifications" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Notifications</q-item-label>
              </q-item-section>
            </q-item>
            <q-item :to="{ name: 'rTasks' }"
                    exact
                    clickable
                    v-ripple
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="assignment" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Tasks</q-item-label>
              </q-item-section>
            </q-item>
            <q-item :to="{ name: 'rTaskCalendar' }"
                    exact
                    clickable
                    v-ripple
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="event" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Calendar</q-item-label>
              </q-item-section>
            </q-item>

            <!-- <q-item :to="{ name: 'rUsefulLinks' }"
                    exact
                    clickable
                    v-ripple
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="link" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Useful Links</q-item-label>
              </q-item-section>
            </q-item> -->
          </q-list>
        </q-scroll-area>
      </q-drawer>

      <!-- Page Content -->
      <q-page-container class="bg-grey-2 DossiersDrawerContainer-page"
                        style="min-height: 100%">
        <div v-if="saleListing"
             class="q-px-none q-pt-lg q-pb-xl dossier-page-container max-ctr"
             style="min-height: 100vh">
          <!-- <div class="q-pb-lg">
            <div class="text-h5 q-pb-sm q-px-sm">
              Dossier for:
            </div>
            <div class="row q-gutter-md q-px-sm">
              <div class="col-xs-12 col-md-6">
                <h3 class="text-subtitle1 q-mb-none q-mt-none text-left">
                  {{ saleListing.title || saleListing.catchy_title }}
                </h3>
                <div class="text-subtitle1">{{ saleListing.street_name }}</div>
                <div class="text-subtitle1">
                  {{ saleListing.street_address }}
                </div>
              </div>
              <div style="display: none"
                   class="col-xs-12 col-md-6">
              </div>
            </div>
          </div> -->

          <!-- <q-separator /> -->

          <div class="q-py-none ddc-router-view">
            <router-view :saleListing="saleListing"
                         :realtyDossier="realtyDossier" />
          </div>
        </div>
        <div style="min-height: 100vh"
             v-else
             class="q-pa-md text-center text-grey-7">
          Sorry, we could not find the dossier you are looking for.
        </div>
      </q-page-container>
    </q-layout>
  </q-no-ssr>
</template>

<script>
// 24 May - DossierOverviewDrawerItem was an overly clever idea to be able
// to navigate to sections of the property - will discard after it
// confused Kajal during testing
// import DossierOverviewDrawerItem from 'src/concerns/dossiers/components/nav/DossierOverviewDrawerItem.vue'
import AssetComparisonDrawerItem from 'src/concerns/dossiers/components/nav/AssetComparisonDrawerItem.vue'
import ListingPriceAndRoomsMin from 'src/concerns/for-sale-evaluations/components/listing-blocks/ListingPriceAndRoomsMin.vue'
import { defineComponent, ref } from 'vue'

export default defineComponent({
  name: 'DossiersDrawerContainer',
  components: {
    // DossierOverviewDrawerItem,
    AssetComparisonDrawerItem,
    ListingPriceAndRoomsMin,
  },
  data: () => ({
    isOverviewExpanded: false,
    drawerMenuItems: [
      // {
      //   name: 'recentSales',
      //   routeName: 'rRecentSales',
      //   icon: 'sell',
      //   label: 'Recent Sales'
      // },
      // {
      //   name: 'comparables',
      //   routeName: 'rComparables',
      //   icon: 'compare',
      //   label: 'Comparables'
      // },
      // {
      //   name: 'charts',
      //   routeName: 'rDossierCharts',
      //   icon: 'bar_chart',
      //   label: 'Charts'
      // },
      {
        name: 'photos',
        routeName: 'rDossierPhotos',
        icon: 'photo',
        label: 'Photos',
      },
      {
        name: 'location',
        routeName: 'rDossierLocation', // Assuming 'rPlaces' is the correct route for the map
        icon: 'map', // Using 'map' icon, change if needed
        label: 'Location',
      },
      // {
      //   name: 'aiFeedback',
      //   routeName: 'rDossierLlmFeedback',
      //   icon: 'psychology',
      //   label: 'AI Feedback',
      // },
      // {
      //   name: 'documents',
      //   routeName: 'rDocuments',
      //   icon: 'description',
      //   label: 'Documents'
      // },
      // {
      //   name: 'places',
      //   routeName: 'rPlaces',
      //   icon: 'place',
      //   label: 'Places'
      // },
      // {
      //   name: 'tasks',
      //   routeName: 'rTasks',
      //   icon: 'assignment',
      //   label: 'Tasks',
      // },
      // {
      //   name: 'calendar',
      //   routeName: 'rTaskCalendar',
      //   icon: 'event',
      //   label: 'Calendar',
      // },
      // {
      //   name: 'nearbyDevelopments',
      //   routeName: 'rNearbyDevelopments',
      //   icon: 'apartment',
      //   label: 'Nearby Developments'
      // },
      // {
      //   name: 'kanban',
      //   routeName: 'rKanban',
      //   icon: 'view_kanban',
      //   label: 'Kanban'
      // },
      // {
      //   name: 'settings',
      //   routeName: 'rSettings',
      //   icon: 'settings',
      //   label: 'Settings'
      // },
      // {
      //   name: 'energyCertificates',
      //   routeName: 'rEnergyCertificates',
      //   icon: 'energy_savings_leaf',
      //   label: 'Energy Performance Certificates'
      // },
      // {
      //   name: 'audioSummary',
      //   routeName: 'rAudioSummary',
      //   icon: 'audiotrack',
      //   label: 'Audio Summary'
      // },
      // {
      //   name: 'neighbourhood',
      //   routeName: 'rNeighbourhood',
      //   icon: 'location_city',
      //   label: 'Neighbourhood'
      // },
      // {
      //   name: 'floorplans',
      //   routeName: 'rFloorplans',
      //   icon: 'home_work',  // Note: 'floorplan' isn't a standard Material Icon, using as placeholder
      //   label: 'Floorplans'
      // }
      // Have a section for your home sale/chain?
    ],
  }),
  props: {
    saleListing: {
      type: Object,
      required: false,
    },
    realtyDossier: {
      type: Object,
      required: false,
    },
  },
  computed: {
    drawerImgSrc() {
      return this.realtyDossier?.dossier_main_photo?.url
    },
    viewRouteDetails() {
      return {
        name: 'rDossierDetails',
        params: {},
      }
    },
    assetParts() {
      return (
        // this.realtyDossier?.primary_dossier_asset?.detailed_asset_parts || {}
        // 20 may 2025 - prefer below to above
        this.realtyDossier?.dossier_sale_listing?.detailed_asset_parts || {}
      )
    },
    summaryGroupedAssetParts() {
      if (!this.assetParts) return []

      // Configuration for asset part types
      const assetPartConfig = {
        bedroom: { order: 1, name: 'Bedrooms', display: true },
        bathroom: { order: 6, name: 'Bathrooms', display: true },
        garage: { order: 35, name: 'Garage', display: true },
        kitchen: { order: 2, name: 'Kitchen', display: true },
        living_room: { order: 3, name: 'Living Room', display: true },
        dining_room: { order: 4, name: 'Dining Room', display: true },
        hallway: { order: 5, name: 'Hallway', display: true },
        default: { order: 99, name: null, display: true },
        other: { order: 99, name: 'Other', display: false },
      }

      // Process asset parts
      const processed = Object.entries(this.assetParts)
        .map(([key, parts]) => {
          // Use config if key exists, otherwise default to 'default'
          const config = assetPartConfig[key] || assetPartConfig.default
          return {
            key, // Original key for uniqueness
            name: config.name, // Display name
            order: config.order, // Order for sorting
            display: config.display, // Whether to display
            // parts: Array.isArray(parts) ? parts : [parts], // Ensure parts is an array
          }
        })
        .filter((group) => group.display) // Only include groups where display is true
        .sort((a, b) => a.order - b.order) // Sort by order
      return processed
    },
    dossierAssetsComparisons() {
      return this.realtyDossier?.dossier_assets_comparisons || []
    },
  },
  setup() {
    const leftDrawerOpen = ref(true)

    return {
      leftDrawerOpen,
      toggleLeftDrawer() {
        leftDrawerOpen.value = !leftDrawerOpen.value
      },
    }
  },
})
</script>

<style scoped>
.h2c-drawer {
  background: linear-gradient(180deg, #1976d2 0%, #115293 100%);
}

.dossier-drawer-inner-list .q-item {
  color: #fff;
  transition: background-color 0.3s;
}

.dossier-drawer-inner-list .q-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.q-item-h2c-active {
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff !important;
}

.q-page-container {
  padding-top: 20px;
}
</style>
