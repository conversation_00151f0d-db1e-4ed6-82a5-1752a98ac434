<template>
  <div class="DossiersMainContainer">
    <!-- <div v-if="isGuestRoute">
        <q-breadcrumbs class="q-py-md q-px-sm text-blue">
          <template v-slot:separator>
          </template>
<q-breadcrumbs-el :to="{ name: 'rMyPurchaseEvaluations' }" label="Back To Your Listings" icon="widgets" />
<q-icon size="1.5em" name="chevron_left" color="primary" />
</q-breadcrumbs>
</div> -->
    <!-- <keep-alive>
          <router-view :saleListing="saleListing" />
        </keep-alive> -->
    <router-view v-slot="{ Component }">
      <keep-alive>
        <component :is="Component"
                   :saleListing="saleListing"
                   :realtyDossier="realtyDossier" />
      </keep-alive>
    </router-view>
  </div>
</template>
<script>
import useDossiers from "src/compose/useDossiers.js"
export default {
  components: {},
  methods: {
  },
  setup(props) {
    const { getDossier } = useDossiers()
    return {
      getDossier,
    }
  },
  props: {},
  created() {
    let retrievalObject = {
      dossierUuid: this.$route.params.dossierUuid
    }
    this.getDossier(retrievalObject).then((responseObject) => {
      this.realtyDossier = responseObject.data.dossier
      this.saleListing = responseObject.data.dossier.dossier_sale_listing
    }).catch((error) => {
      if (error.response && error.response.status === 404) {
        console.error('Dossier not found (404):', retrievalObject);
        // Handle the 404 error appropriately:
        // - Display a user-friendly message
        // - Redirect to a "not found" page
        // - Set default values
        // - Log the error for debugging.
        this.realtyDossier = null; // Or some default value
        this.saleListing = null; // Or some default value
        // Example of setting a user message:
        // this.errorMessage = "The requested dossier was not found.";
      }
      else if (error.response && error.response.status === 401) {
        this.$router.push({ name: 'rSubdomainAccessError', params: {} })
      }
      else {
        // Handle other types of errors (network issues, server errors, etc.)
        console.error('Error retrieving dossier:', error);
        // Optionally, set error messages for the user.
        // this.errorMessage = "An unexpected error occurred.";
      }
    })
  },
  computed: {
    // isGuestRoute() {
    //   return true
    // },
  },
  data() {
    return {
      activeTab: null,
      saleListing: null,
      realtyDossier: null,
    }
  },
}
</script>
<style></style>
