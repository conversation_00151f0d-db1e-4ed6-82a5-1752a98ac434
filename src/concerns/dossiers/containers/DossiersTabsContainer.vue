<template>
  <div class="single-bex-edit-layout-ctr">

    <div v-if="saleListing"
         class="q-mt-lg SingleSubdomainListingLayout-tabs">
      <div class="q-pb-lg row">
        <div class="text-h6 col-xs-12 q-pb-md">{{ saleListing.catchy_title }}</div>
        <div class="col-xs-12 col-md-6">
          <div class="text-subtitle2">{{ saleListing.street_name }}</div>
          <div class="text-subtitle2">{{ saleListing.street_address }}</div>
          <div class="text-subtitle2">{{ saleListing.postal_code }}</div>
        </div>
        <div class="col-xs-12 col-md-6">
          <ListingPriceAndRooms :evaluationDetails="saleListing"></ListingPriceAndRooms>
        </div>
      </div>
      <q-tabs v-model="activeTab"
              dense
              mobile-arrows
              class="text-grey"
              active-color="primary"
              indicator-color="primary"
              align="justify"
              narrow-indicator
              outside-arrows>
        <!-- <q-route-tab name="preview"
                     :to="viewRouteDetails"
                     label="Preview" /> -->
        <q-route-tab name="overview"
                     :to="{ name: 'rDossierOverview' }"
                     :exact="false"
                     label="Overview" />
        <q-route-tab name="llm-feedback"
                     :to="{ name: 'rDossierLlmFeedback' }"
                     :exact="false"
                     label="Ai Feedback" />
        <q-route-tab name="charts"
                     :to="{ name: 'rDossierCharts' }"
                     :exact="false"
                     label="Charts" />
        <q-route-tab name="recent-sales"
                     :to="{ name: 'rRecentSales' }"
                     :exact="false"
                     label="Recent Sales" />
        <q-route-tab name="comparables"
                     :to="{ name: 'rComparables' }"
                     :exact="false"
                     label="Comparables" />
      </q-tabs>

      <q-separator />

      <q-tab-panels transition-duration="
                     1000"
                    transition-prev="slide-right"
                    transition-next="slide-left"
                    :infinite="false"
                    v-model="activeTab"
                    animated>
        <q-tab-panel class="q-px-xs"
                     name="overview">
          <router-view :saleListing="saleListing"
                       :realtyDossier="realtyDossier" />
        </q-tab-panel>
        <q-tab-panel class="q-px-xs"
                     name="llm-feedback">
          <router-view :saleListing="saleListing"
                       :realtyDossier="realtyDossier" />
        </q-tab-panel>
        <q-tab-panel class="q-px-xs"
                     name="charts">
          <router-view :saleListing="saleListing"
                       :realtyDossier="realtyDossier" />
        </q-tab-panel>
        <q-tab-panel class="q-px-xs"
                     name="recent-sales">
          <router-view :saleListing="saleListing"
                       :realtyDossier="realtyDossier" />
        </q-tab-panel>
        <q-tab-panel class="q-px-xs"
                     name="comparables">
          <router-view :saleListing="saleListing"
                       :realtyDossier="realtyDossier" />
        </q-tab-panel>
      </q-tab-panels>
    </div>
    <div v-else>
      <div>
        Sorry, we could not find the dossier you are looking for.
      </div>
    </div>
  </div>
</template>
<script>
import ListingPriceAndRooms from "src/concerns/for-sale-evaluations/components/listing-blocks/ListingPriceAndRooms.vue"
export default {
  components: { ListingPriceAndRooms },
  methods: {

  },

  props: {
    saleListing: {
      type: Object,
      required: false
    },
    realtyDossier: {
      type: Object,
      required: false
    }
  },
  computed: {
    editEvaluationRouteDetails() {
      let routeName = "rSingleEvaluationEdit"
      let editEvaluationRouteDetails = {
        name: routeName,
        params: {},
      }
      return editEvaluationRouteDetails
    },
    viewRouteDetails() {
      let routeName = "rDossierDetails"
      let viewRouteDetails = {
        name: routeName,
        params: {},
      }
      return viewRouteDetails
    },
  },
  data() {
    return {
      activeTab: null,
    }
  },
}
</script>
<style></style>
