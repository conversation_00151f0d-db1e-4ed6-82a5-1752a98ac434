<template>
  <q-layout class="SuperwiserDossiersMainContainer">
    <q-page-container>
      <q-page padding>
        <q-list bordered
                padding>
          <q-item-label header
                        class="text-h5 text-center">Realty Dossiers</q-item-label>
          <q-separator spaced />

        </q-list>

        <div class="DossierPhotos">
          <EditableImageGallery :images="images"
                                :initial-edit-mode="editMode"
                                @delete-images="$emit('delete-images', $event)"
                                @update-image="$emit('update-image', $event)"
                                @photo-uploaded="$emit('photo-uploaded', $event)" />
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script>
import useDossiers from "src/compose/useDossiers.js"
import { defineComponent, ref } from "vue"
import EditableImageGallery from "src/concerns/dossiers/components/images/EditableImageGallery.vue"

export default {
  name: 'SuperwiserDossiersMainContainer',
  components: {
    EditableImageGallery
  },
  methods: {
    fullCompositePhotoUrl(composite_photo_url) {
      return `http://damp-violet.lvh.me:3333${composite_photo_url}`
    },
    screenShotView(dossier, listing_uuid) {
      let ddd = `http://damp-violet.lvh.me:3333/api_public/v4/dossiers/show_image/${dossier.uuid}/listing/${listing_uuid}`
      return ddd
    }
  },
  setup(props) {
    const { getDossierAsset } = useDossiers()
    return {
      getDossierAsset,
    }
  },
  created() {
    let retrievalObject = {
      dossierAssetId: this.$route.params.dossierAssetId,
    }
    this.getDossierAsset(retrievalObject).then((response) => {
      this.dossierAsset = response.data.dossier_asset
    }).catch((error) => {
      console.error('Error retrieving dossiers:', error)
    })
  },
  computed: {
    images() {
      return this.dossierAsset?.default_sale_listing?.sale_listing_pics || []
    },
    editMode() {
      return this.$route.name === "rDossierPhotosEdit"
    },
  },
  data() {
    return {
      dossierAsset: {},
    }
  },
}
</script>

<style scoped>
.q-card {
  max-width: 600px;
  margin: auto;
}

.q-card .q-img {
  height: 200px;
  object-fit: cover;
}

.q-btn {
  margin-top: 10px;
}
</style>