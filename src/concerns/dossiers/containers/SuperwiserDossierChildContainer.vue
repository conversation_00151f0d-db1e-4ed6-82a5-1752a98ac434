<template>
  <q-card class="q-mb-md">
    <div class="row">
      <div class="col-sm-12 col-md-6 col-lg-4">
        <q-img v-if="dossier.dossier_main_photo"
               :src="dossier.dossier_main_photo.url"
               class="q-card-section" />
      </div>
      <div class="col-sm-12 col-md-6 col-lg-4">
        <div>
          <div class="text-h6">{{ dossier.id }}</div>
          <div class="text-h6">{{ dossier.dossier_display_title }}</div>
          <div class="text-subtitle2">{{ dossier.dossier_city }}, {{ dossier.dossier_postcode }}</div>
          <q-separator spaced />
          <div>Original Listing:</div>
          <a :href="dossier.dossier_starting_url">{{ dossier.dossier_starting_url }}</a>
          <div>Dossier view:</div>
          <router-link :to="{ name: 'rDossierDetails', params: { dossierUuid: dossier.uuid } }"
                       label="More Details"
                       color="primary"
                       class="full-width">
            {{ $router.resolve({ name: 'rDossierDetails', params: { dossierUuid: dossier.uuid } }).href }}
          </router-link>
        </div>
      </div>
      <div class="col-sm-12 col-md-6 col-lg-4">
        <q-card-section>

          <div class="q-mt-lg text-h5 text-center">Primary listing</div>
          <div class="q-mt-lg">Screenshot view for primary listing:</div>
          <a :href="screenShotView(dossier, dossier?.primary_dossier_asset?.default_sale_listing_uuid)">
            {{ screenShotView(dossier, dossier?.primary_dossier_asset?.default_sale_listing_uuid) }}
          </a>
          <div class="q-mt-lg">Screenshot:</div>
          {{ fullCompositePhotoUrl(dossier?.primary_dossier_asset?.composite_photo_url) }}
          <q-img :src="fullCompositePhotoUrl(dossier?.primary_dossier_asset?.composite_photo_url)"
                 alt="User Success Story" />
          <div class="q-mt-lg text-h5 text-center">Secondary listings</div>
          <div v-for="asset in dossier.secondary_dossier_assets"
               :key="asset.default_sale_listing_uuid">
            <div class="q-mt-lg">Screenshot view:</div>
            <a :href="screenShotView(dossier, asset.default_sale_listing_uuid)">
              {{ screenShotView(dossier, asset.default_sale_listing_uuid) }}
            </a>
            <div class="q-mt-lg">Screenshot:</div>
            {{ fullCompositePhotoUrl(asset.composite_photo_url) }}
            <q-img :src="fullCompositePhotoUrl(asset.composite_photo_url)"
                   alt="User Success Story" />
          </div>
        </q-card-section>
      </div>
    </div>


  </q-card>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
  name: "DossierCard",
  props: {
    dossier: {
      type: Object,
      required: true,
    },
  },
  methods: {
    fullCompositePhotoUrl(composite_photo_url) {
      return `http://damp-violet.lvh.me:3333${composite_photo_url}`;
    },
    screenShotView(dossier, listing_uuid) {
      return `http://damp-violet.lvh.me:3333/api_public/v4/dossiers/show_image/${dossier.uuid}/listing/${listing_uuid}`;
    },
  },
});
</script>

<style scoped>
.q-card .q-img {
  height: 200px;
  object-fit: cover;
}
</style>