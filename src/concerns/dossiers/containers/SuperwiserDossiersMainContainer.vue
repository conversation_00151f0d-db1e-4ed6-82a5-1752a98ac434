<template>
  <q-layout class="SuperwiserDossiersMainContainer">
    <q-page-container>
      <q-page padding>
        <q-list bordered
                padding>
          <q-item-label header
                        class="text-h5 text-center">Realty Dossiers</q-item-label>
          <q-separator spaced />

          <div v-for="dossier in realty_dossiers"
               :key="dossier.uuid"
               class="q-my-lg">
            <SuperwiserDossierChildContainer :dossier="dossier" />

          </div>

          <!-- <q-card v-for="dossier in realty_dossiers"
                  :key="dossier.uuid"
                  class="q-mb-md">
            <q-img v-if="dossier.dossier_main_photo"
                   :src="dossier.dossier_main_photo.url"
                   class="q-card-section" />
            <q-card-section>
              <div class="text-h6">{{ dossier.id }}</div>
              <div class="text-h6">{{ dossier.dossier_display_title }}</div>
              <div class="text-subtitle2">{{ dossier.dossier_city }}, {{ dossier.dossier_postcode }}</div>
              <q-separator spaced />
              <q-separator spaced />
              <div>Original Listing:</div>
              <a :href="dossier.dossier_starting_url">{{ dossier.dossier_starting_url }}</a>
              <div>Dossier view:</div>
              <router-link :to="{ name: 'rDossierDetails', params: { dossierUuid: dossier.uuid } }"
                           label="More Details"
                           color="primary"
                           class="full-width">
                {{ $router.resolve({ name: 'rDossierDetails', params: { dossierUuid: dossier.uuid } }).href }}
              </router-link>
              <div class="q-mt-lg text-h5 text-center">Primary listing</div>
              <div class="q-mt-lg">Screenshot view for primary listing:</div>
              <a :href="screenShotView(dossier, dossier?.primary_dossier_asset?.default_sale_listing_uuid)">{{
                screenShotView(dossier,
                  dossier?.primary_dossier_asset?.default_sale_listing_uuid)
              }}</a>
              <div class="q-mt-lg">Screenshot:</div>
              {{ fullCompositePhotoUrl(dossier?.primary_dossier_asset?.composite_photo_url) }}
              <q-img :src="fullCompositePhotoUrl(dossier?.primary_dossier_asset?.composite_photo_url)"
                     alt="User Success Story" />
              


              <div class="q-mt-lg text-h5 text-center">Secondary listings</div>
              <div v-for="asset in dossier.secondary_dossier_assets"
                   :key="asset.default_sale_listing_uuid">
                <div class="q-mt-lg">Screenshot view:</div>
                <a :href="screenShotView(dossier, asset.default_sale_listing_uuid)">{{ screenShotView(dossier,
                  asset.default_sale_listing_uuid) }}</a>
                <div class="q-mt-lg">Screenshot:</div>
                {{ fullCompositePhotoUrl(asset.composite_photo_url) }}
                <q-img :src="fullCompositePhotoUrl(asset.composite_photo_url)"
                       alt="User Success Story" />

              </div>
            </q-card-section>
          </q-card> -->
        </q-list>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script>
import useDossiers from "src/compose/useDossiers.js"
import { defineComponent, ref } from "vue"
import SuperwiserDossierChildContainer from "src/concerns/dossiers/containers/SuperwiserDossierChildContainer.vue"
export default {
  name: 'SuperwiserDossiersMainContainer',
  components: {
    SuperwiserDossierChildContainer
  },
  methods: {
    fullCompositePhotoUrl(composite_photo_url) {
      return `http://damp-violet.lvh.me:3333${composite_photo_url}`
    },
    screenShotView(dossier, listing_uuid) {
      let ddd = `http://damp-violet.lvh.me:3333/api_public/v4/dossiers/show_image/${dossier.uuid}/listing/${listing_uuid}`
      return ddd
    }
  },
  setup() {
    const { getAllDossiers } = useDossiers()
    const realty_dossiers = ref([])

    getAllDossiers().then((response) => {
      realty_dossiers.value = response.data.realty_dossiers
    }).catch((error) => {
      console.error('Error retrieving dossiers:', error)
    })

    return {
      realty_dossiers
    }
  }
}
</script>

<style scoped>
.q-btn {
  margin-top: 10px;
}
</style>