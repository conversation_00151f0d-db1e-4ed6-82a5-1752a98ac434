<template>
  <div class="use-case-item q-pa-md"
       @click="navigateToRoute">
    <div class="use-case-item-box q-pa-md bg-white rounded-borders shadow-2">
      <div class="row items-center">
        <!-- <div class="use-case-img col-12 col-md-3 q-mr-md-md">
          <div class="img-data-viz">
            <div class="label deco viz _1">
              <span v-for="(icon, index) in icons"
                    :key="index"
                    class="label deco brand q-mr-sm">{{ icon }}</span>
            </div>
          </div>
        </div> -->
        <div class="use-case-title col-12 col-md">
          <h3 class="text-h6 text-grey-9 q-my-sm">{{ title }}</h3>
          <div class="text-body2 text-grey-7 text-center-md">
            {{ description }}
          </div>
          <!-- <q-btn flat
                 class="q-mt-sm"
                 outline
                 color="secondary"
                 :to="{ name: route }">
            Go To {{ title }}
          </q-btn> -->
          <div class="q-mt-md text-center">
            <q-btn color="primary"
                   :icon="mainIcon"
                   rounded
                   :to="{ name: route }">
              <div class="q-px-lg"> Go To {{ title }}
              </div>
            </q-btn>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent } from 'vue';
import { useRouter } from 'vue-router';

export default defineComponent({
  name: 'UseCaseItem',
  props: {
    mainIcon: {
      type: String,
      default: "add_circle"
    },
    title: {
      type: String,
      required: true
    },
    description: {
      type: String,
      required: true
    },
    icons: {
      type: Array,
      default: () => []
    },
    route: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const router = useRouter();

    const navigateToRoute = () => {
      if (props.route) {
        router.push({
          name: props.route
        });
      }
    };

    return {
      navigateToRoute
    };
  }
});
</script>

<style scoped>
.use-case-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.use-case-item-box {
  transition: transform 0.3s ease;
  width: 100%;
}

.use-case-item-box:hover {
  transform: translateY(-5px);
}

.img-data-viz {
  font-size: 1.5rem;
  line-height: 1.5;
}

@media (min-width: 768px) {
  .use-case-item-box {
    max-width: 600px;
  }

  .img-data-viz {
    font-size: 1.25rem;
  }

  .text-center-md {
    text-align: left;
  }
}

@media (max-width: 767px) {
  .use-case-img {
    margin-bottom: 1rem;
    text-align: center;
  }
}
</style>