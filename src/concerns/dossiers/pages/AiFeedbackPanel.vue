<template>
  <div v-if="realtyDossier"
       flat
       bordered
       class="AiFeedbackPanel-panel">

    <h3 class="text-h5 q-mb-lg q-mt-md text-center h2c-underline"
        style="">Feedback from our AI</h3>

    <AiEvaluationFeedback :showMainFeedback="true"
                          :llmFeedback="llmFeedback"></AiEvaluationFeedback>


  </div>
</template>

<script>
import AiEvaluationFeedback from "src/concerns/for-sale-evaluations/components/ai/AiEvaluationFeedback.vue"
export default {
  name: 'AiFeedbackPanel',
  components: {
    AiEvaluationFeedback,
  },
  data() {
    return {}
  },
  props: {
    saleListing: {
      type: Object,
      required: false
    },
    realtyDossier: {
      type: Object,
      required: false
    }
  },
  computed: {
    llmFeedback() {
      return this.realtyDossier?.primary_dossier_asset?.main_eval || {}
    },
  },
  methods: {
  }
};
</script>

<style scoped></style>
