<template>
  <div class="ComparableFocusPanel">
    <!-- <div>
      <h1 class="text-h5 q-mb-lg text-center">{{ comparisonData?.comparison?.comparison_title }}</h1>
    </div> -->
    <!-- Navigation tabs -->
    <q-tabs v-model="activeTab"
            class="text-grey-8 navigation-tabs"
            active-color="primary"
            indicator-color="primary"
            align="center"
            inline-label
            outside-arrows>
      <q-route-tab name="soloDetails"
                   label="Details"
                   :to="{ name: 'rComparableSolo' }"
                   class="text-weight-bold" />
      <q-route-tab name="sideBySide"
                   label="Side By Side"
                   :to="{ name: 'rComparableSbs' }"
                   class="text-weight-bold" />
      <!-- <q-route-tab name="rooms-sections"
                   label="Rooms / Sections"
                   :to="{ name: 'rComparableSbsParts' }"
                   class="text-weight-bold" /> -->
      <q-route-tab name="photos"
                   label="Photos Only"
                   :to="{ name: 'rComparableSoloPhotos' }"
                   class="text-weight-bold" />
      <!-- <q-route-tab name="map"
                   label="Map"
                   :to="{ name: 'rComparableSbsMap' }"
                   class="text-weight-bold" /> -->
    </q-tabs>

    <!-- Tab panels with left-right transition -->
    <q-tab-panels v-model="activeTab"
                  animated
                  transition-prev="slide-right"
                  transition-next="slide-left"
                  class="panel-content cfp-tab-panels q-mx-none q-px-none">
      <q-tab-panel name="soloDetails"
                   class="soloDetails-tab-panel q-px-none q-mt-none">
        <div class="soloDetails-inner"
             v-if="listingInRow">
          <SingleComparableDetails :assetParts="assetParts"
                                   :evaluationDetails="listingInRow"
                                   :realtyDossier="realtyDossier"
                                   :comparisonUuid="currentAssetsComparisonUuid" />
        </div>
      </q-tab-panel>
      <q-tab-panel name="sideBySide"
                   class="q-pa-none sbs-tab-panel">
        <SideBySideListingsInRow :detailedComparisons="detailedComparisons"
                                 :leftColItem="dossierListing"
                                 :listingInRow="listingInRow" />
      </q-tab-panel>
      <q-tab-panel name="photos">
        <div class="soloPhotos-inner"
             v-if="listingInRow">
          <SingleComparablePhotos :assetParts="assetParts"
                                  :evaluationDetails="listingInRow" />
        </div>
      </q-tab-panel>

      <!-- 2 tabs below not currently in use -->
      <q-tab-panel name="rooms-sections">
        <div v-if="comparisonData && comparisonData.comparison?.full_comparisons">
          <!-- <SingleComparisonFocus :comparisonData="comparisonData" /> -->
        </div>
      </q-tab-panel>

      <q-tab-panel name="map">
        <h1 class="text-h4 q-mb-lg text-center">Map</h1>
        <!-- Add your map content here -->
        <p>Map view would be rendered here.</p>
      </q-tab-panel>

      <!-- <q-tab-panel name="analysis">
        <h1 class="text-h4 q-mb-lg text-center">Analysis</h1>
        <p>Analysis data would be shown here.</p>
      </q-tab-panel> -->
    </q-tab-panels>
  </div>
</template>

<script>
// import DossierComparisonTable from "src/concerns/dossiers/components/comparisons/DossierComparisonTable.vue"
import SingleComparableDetails from 'src/concerns/dossiers/components/comparables/SingleComparableDetails.vue'
import SingleComparablePhotos from 'src/concerns/dossiers/components/comparables/SingleComparablePhotos.vue'
import useDossiers from 'src/compose/useDossiers.js'
// // import SingleComparisonFocus from 'src/concerns/dossiers/components/SingleComparisonFocus.vue'
import SideBySideListingsInRow from 'src/concerns/dossiers/components/row/SideBySideListingsInRow.vue'

export default {
  components: {
    // SingleComparisonFocus,
    SingleComparablePhotos,
    SideBySideListingsInRow,
    // DossierComparisonTable,
    SingleComparableDetails,
  },
  name: 'ComparableFocusPanel',
  props: {
    comparisonData: {
      type: Object,
      required: false,
    },
    saleListing: {
      type: Object,
      required: false,
    },
    realtyDossier: {
      type: Object,
      required: false,
    },
  },
  computed: {
    detailedComparisons() {
      return this.comparisonData?.comparison?.detailed_comparison
    },
    assetParts() {
      return this.comparisonData?.comparison?.right_side_details
        ?.detailed_asset_parts
    },
    listingInRow() {
      return this.comparisonData?.comparison?.right_side_details?.listing
      // below was dumb...
      // this.realtyDossier?.secondary_dossier_assets[0]
    },
    dossierListing() {
      return this.realtyDossier?.dossier_sale_listing
    },
    // Computed property to easily access the UUID
    currentAssetsComparisonUuid() {
      return this.$route.params.assetsComparisonUuid
    },
  },
  data() {
    return {
      // comparisonData: {},
      // comparisonDetails: {
      //   comparison_summary: {},
      // },
      // leftColItem: {},
      // rightColItem: {},
      activeTab: 'soloDetails', // Set a default, will be updated in mounted/watch
      isLoading: false, // Optional: for loading state
      error: null, // Optional: for error handling
    }
  },
  setup() {
    // Expose getDossierComparison from the composable
    const { getDossierComparison } = useDossiers()
    return {
      getDossierComparison,
    }
  },
  methods: {
    // Method to update the active tab based on the route name
    updateActiveTab(routeName) {
      const routeToTabMap = {
        rComparableSolo: 'soloDetails',
        rComparableSbs: 'sideBySide',
        rComparableSoloPhotos: 'photos',
        rComparableSbsMap: 'map',
        rComparableSbsParts: 'rooms-sections',
      }
      this.activeTab = routeToTabMap[routeName] || 'soloDetails' // Default to 'soloDetails' if no match
    },
  },
  // created() {
  //   // Initial fetch attempt when the component is created
  //   this.fetchComparisonData(this.currentAssetsComparisonUuid);
  // },
  watch: {
    // // Watch the computed property tracking the route parameter
    // currentAssetsComparisonUuid(newUuid, oldUuid) {
    //   // console.log(`assetsComparisonUuid changed from ${oldUuid} to ${newUuid}`);
    //   // Fetch data only if the UUID has actually changed
    //   if (newUuid !== oldUuid) {
    //     this.fetchComparisonData(newUuid);
    //   }
    // },
    // Watch the route name to update the active tab
    '$route.name'(newName) {
      this.updateActiveTab(newName)
    },
  },
  mounted() {
    // Set the initial active tab when the component mounts
    this.updateActiveTab(this.$route.name)
  },
}
</script>

<style scoped>
/* Style tabs as a clear navigation element */
.navigation-tabs {
  margin-bottom: 32px;
  border-bottom: 1px solid #e0e0e0;
}

.q-tab {
  font-size: 16px;
  padding: 12px 24px;
  text-transform: uppercase;
  transition: color 0.3s ease;
}

.q-tab:hover {
  color: #1976d2;
}

.q-tab--active {
  color: #1976d2;
  font-weight: 700;
}

.q-tabs__indicator {
  height: 3px;
  background-color: #1976d2;
  border-radius: 2px 2px 0 0;
}

/* Style for tab panels */
.panel-content {
  /* padding: 16px; */
  min-height: 400px;
  /* Ensure enough space for content */
}
</style>
