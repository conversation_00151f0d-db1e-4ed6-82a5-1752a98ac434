<template>
  <div class="ComparableSideBySidePanel">
    <!-- <div>
      <h1 class="text-h5 q-mb-lg text-center">{{ comparisonData?.comparison?.comparison_title }}</h1>
    </div> -->
    <!-- Navigation tabs -->
    <q-tabs v-model="activeTab"
            class="text-grey-8 navigation-tabs"
            active-color="primary"
            indicator-color="primary"
            align="center"
            inline-label
            outside-arrows>
      <q-route-tab name="details"
                   label="Comparison Overview"
                   :to="{ name: 'rComparableSbs' }"
                   class="text-weight-bold" />
      <q-route-tab name="rooms-sections"
                   label="Rooms / Sections Comparison"
                   :to="{ name: 'rComparableSbsParts' }"
                   class="text-weight-bold" />
      <!-- <q-route-tab name="photos"
                   label="Photos Only"
                   :to="{ name: 'rComparableSbsPhotos' }"
                   class="text-weight-bold" /> -->
      <q-route-tab name="map"
                   label="Map"
                   :to="{ name: 'rComparableSbsMap' }"
                   class="text-weight-bold" />
      <!-- <q-route-tab name="analysis"
                   label="Analysis"
                   :to="{ name: 'rComparableSbsAnalysis' }"
                   class="text-weight-bold" /> -->
    </q-tabs>

    <!-- Tab panels with left-right transition -->
    <q-tab-panels v-model="activeTab"
                  animated
                  transition-prev="slide-right"
                  transition-next="slide-left"
                  class="panel-content">
      <q-tab-panel name="details">
        <SideBySideListingsInRow :leftColItem="dossierListing"
                                 :listingInRow="listingInRow" />
      </q-tab-panel>

      <q-tab-panel name="rooms-sections">
        <div v-if="comparisonData && comparisonData.comparison?.full_comparisons">
          <SingleComparisonFocus :comparisonData="comparisonData" />
        </div>
      </q-tab-panel>

      <!-- <q-tab-panel name="photos">
        <h1 class="text-h4 q-mb-lg text-center">Photos</h1>
        <p>Photo gallery would be displayed here....</p>
      </q-tab-panel> -->

      <q-tab-panel name="map">
        <h1 class="text-h4 q-mb-lg text-center">Map</h1>
        <!-- Add your map content here -->
        <p>Map view would be rendered here.</p>
      </q-tab-panel>

      <!-- <q-tab-panel name="analysis">
        <h1 class="text-h4 q-mb-lg text-center">Analysis</h1>
        <p>Analysis data would be shown here.</p>
      </q-tab-panel> -->
    </q-tab-panels>
  </div>
</template>

<script>
// import DossierComparisonTable from "src/concerns/dossiers/components/comparisons/DossierComparisonTable.vue"
import SideBySideListingsInRow from "src/concerns/dossiers/components/row/SideBySideListingsInRow.vue"
import useDossiers from "src/compose/useDossiers.js"
import SingleComparisonFocus from "src/concerns/dossiers/components/SingleComparisonFocus.vue"

export default {
  components: {
    SingleComparisonFocus,
    // DossierComparisonTable,
    SideBySideListingsInRow
  },
  name: 'ComparableSideBySidePanel',
  props: {
    comparisonData: {
      type: Object,
      required: false
    },
    saleListing: {
      type: Object,
      required: false
    },
    realtyDossier: {
      type: Object,
      required: false
    }
  },
  computed: {
    listingInRow() {
      return this.comparisonData?.comparison?.right_side_details?.listing
      // this was dumb...
      //  this.realtyDossier?.secondary_dossier_assets[0]
    },
    dossierListing() {
      return this.realtyDossier?.dossier_sale_listing
    },
    // Computed property to easily access the UUID
    currentAssetsComparisonUuid() {
      return this.$route.params.assetsComparisonUuid;
    }
  },
  data() {
    return {
      // comparisonData: {},
      // comparisonDetails: {
      //   comparison_summary: {},
      // },
      // leftColItem: {},
      // rightColItem: {},
      activeTab: 'details', // Set a default, will be updated in mounted/watch
      isLoading: false, // Optional: for loading state
      error: null      // Optional: for error handling
    }
  },
  setup() {
    // Expose getDossierComparison from the composable
    const { getDossierComparison } = useDossiers()
    return {
      getDossierComparison,
    }
  },
  methods: {
    // // Method to fetch comparison data
    // async fetchComparisonData(uuid) {
    //   // Only proceed if uuid is truthy (not null, undefined, empty string)
    //   if (!uuid) {
    //     console.warn('No assetsComparisonUuid provided. Skipping fetch.');
    //     this.comparisonData = {}; // Clear data if no UUID
    //     this.error = null;
    //     return;
    //   }

    //   // console.log(`Workspaceing comparison data for UUID: ${uuid}`);
    //   this.isLoading = true;
    //   this.error = null;
    //   const retrievalObject = { assetsComparisonUuid: uuid };

    //   try {
    //     const responseObject = await this.getDossierComparison(retrievalObject);
    //     this.comparisonData = responseObject.data || {}; // Use default empty object if data is null/undefined
    //   } catch (error) {
    //     console.error("Error fetching dossier comparison:", error);
    //     this.error = "Failed to load comparison data."; // Store error message
    //     this.comparisonData = {}; // Clear data on error
    //   } finally {
    //     this.isLoading = false;
    //   }
    // },

    // Method to update the active tab based on the route name
    updateActiveTab(routeName) {
      const routeToTabMap = {
        'rComparableSbs': 'details',
        'rComparableSbsPhotos': 'photos',
        'rComparableSbsMap': 'map',
        'rComparableSbsParts': 'rooms-sections'
      };
      this.activeTab = routeToTabMap[routeName] || 'details'; // Default to 'details' if no match
    }
  },
  // created() {
  //   // Initial fetch attempt when the component is created
  //   this.fetchComparisonData(this.currentAssetsComparisonUuid);
  // },
  watch: {
    // // Watch the computed property tracking the route parameter
    // currentAssetsComparisonUuid(newUuid, oldUuid) {
    //   // console.log(`assetsComparisonUuid changed from ${oldUuid} to ${newUuid}`);
    //   // Fetch data only if the UUID has actually changed
    //   if (newUuid !== oldUuid) {
    //     this.fetchComparisonData(newUuid);
    //   }
    // },
    // Watch the route name to update the active tab
    '$route.name'(newName) {
      this.updateActiveTab(newName);
    }
  },
  mounted() {
    // Set the initial active tab when the component mounts
    this.updateActiveTab(this.$route.name);
  }
}
</script>

<style scoped>
/* Style tabs as a clear navigation element */
.navigation-tabs {
  margin-bottom: 32px;
  border-bottom: 1px solid #e0e0e0;
}

.q-tab {
  font-size: 16px;
  padding: 12px 24px;
  text-transform: uppercase;
  transition: color 0.3s ease;
}

.q-tab:hover {
  color: #1976D2;
}

.q-tab--active {
  color: #1976D2;
  font-weight: 700;
}

.q-tabs__indicator {
  height: 3px;
  background-color: #1976D2;
  border-radius: 2px 2px 0 0;
}

/* Style for tab panels */
.panel-content {
  padding: 16px;
  min-height: 400px;
  /* Ensure enough space for content */
}
</style>
