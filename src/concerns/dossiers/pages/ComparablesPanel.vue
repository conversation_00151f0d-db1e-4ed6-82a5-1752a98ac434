<template>
  <div class="ComparablesPanel q-py-lg">
    <div>
      <div class="col-sm-12">
        <h3 class="text-h5 q-mb-lg q-mt-sm text-center h2c-underline">Northstar Property</h3>

        <SingleListingInRow :comparableUuid="saleListing.uuid"
                            :isMainProperty="true"
                            :listingInRow="saleListing"></SingleListingInRow>
      </div>
    </div>
    <div v-if="dossierAssetsComparisons?.length > 0"
         class="">
      <h3 class="text-h5 q-mb-md q-mt-xl text-center h2c-underline">Comparable Properties</h3>

      <div v-for="property in dossierAssetsComparisons"
           :key="property.id">
        <!-- <SingleComparableRow :dossierListing="saleListing"
                             :comparableInRow="property"></SingleComparableRow> -->
        <SingleListingInRow :comparableUuid="property.uuid"
                            :listingInRow="property?.right_side_property"></SingleListingInRow>
      </div>
    </div>
    <div v-else
         class="q-mt-xl">
      <h3 class="text-h5 q-mb-md text-center">No comparables found</h3>
    </div>
    <div class="q-mt-md text-center">
      <q-btn color="primary"
             label="Find More Comparables"
             icon="add_circle"
             rounded
             class="q-px-lg"
             @click="showPaidAccountDialog" />
    </div>
  </div>
</template>

<script>
import SingleListingInRow from "src/concerns/dossiers/components/row/SingleListingInRow.vue"
// import SingleComparableRow from "src/concerns/dossiers/components/SingleComparableRow.vue"
export default {
  components: {
    SingleListingInRow,
    // SingleComparableRow
  },
  name: 'ComparablesPanel',
  props: {
    saleListing: {
      type: Object,
      required: false
    },
    realtyDossier: {
      type: Object,
      required: false
    }
  },
  computed: {
    dossierAssetsComparisons() {
      return this.realtyDossier?.dossier_assets_comparisons || []
    },
  },
  data() {
    return {}
  },
  methods: {
    showPaidAccountDialog() {
      this.$q.dialog({
        title: 'Paid Account Required',
        message: 'Finding more comparable properties requires a paid account. Please upgrade your account to access this feature.',
        ok: {
          label: 'OK',
          color: 'primary'
        },
        cancel: false,
        persistent: true
      })
    }
  },
  mounted() {
  }
}
</script>

<style scoped></style>