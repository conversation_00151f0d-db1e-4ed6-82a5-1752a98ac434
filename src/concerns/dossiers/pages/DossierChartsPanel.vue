<template>
  <div v-if="realtyDossier"
       flat
       bordered
       class="dossier-charts-panel">


    <div>
      <ListingDetailsChartDisplay :chartName="targetChartName"
                                  @passChartSeries="passChartSeries"
                                  @openChartModal="openChartModal" />

    </div>

  </div>
</template>

<script>
import ListingDetailsChartDisplay from "src/concerns/charts/components/ListingDetailsChartDisplay.vue"
export default {
  name: 'DossierChartsPanel',
  components: {
    ListingDetailsChartDisplay
  },
  data() {
    return {}
  },
  props: {
    saleListing: {
      type: Object,
      required: false
    },
    realtyDossier: {
      type: Object,
      required: false
    }
  },
  computed: {
    targetChartName() {
      return this.$route.params.targetChartName || 'price_by_floor_area'
    },
  },
  methods: {
    // capitalizeFirstLetter(string) {
    //   return string.charAt(0).toUpperCase() + string.slice(1).replace(/_/g, ' ')
    // },
    modalStateChanged(newVal) {
      this.modalOpen = newVal;
    },
    openChartModal(selectedPoint) {
      this.selectedPoint = selectedPoint
      this.modalOpen = true;
      const queryString = new URLSearchParams({ actionId: selectedPoint.SoldTransactionId }).toString();
      window.history.pushState(null, '', `?${queryString}`);
      // window.dispatchEvent(new Event('popstate'))
    },
    passChartSeries(chartSeries) {
      this.soldDataPoints = chartSeries[0].data
    },
  }
};
</script>

<style scoped></style>
