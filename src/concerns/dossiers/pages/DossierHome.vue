<template>
  <q-page class="q-pa-none DossierHome">
    <div class="full-width-card">
      <q-card-section>
        <div class="text-h4 text-center">Your "Northstar" Property Dossier</div>
        <div class="text-subtitle1 text-center">
          <!-- Your Comprehensive Guide to Making An Informed Purchasing Decision! -->
          <!-- {{ saleListing.title }} -->
        </div>
      </q-card-section>
      <q-separator />

      <div class="row q-mt-lg">
        <div class="col-12 col-sm-10 offset-md-1 col-md-8 offset-md-2 col-lg-6 offset-md-3">
          <div class="q-mb-none text-subtitle1 text-left q-pa-sm">
            <p class="">
              HomesToCompare helps you make the best home-buying decision by guiding your choices through the lens of
              a 'North Star' property.
            </p>
            <p class="">
              While it is possible to change your Northstar property at any time. We encourage you to stick to one for
              as long as possible.
            </p>
            <p>Navigate through the sections below to access key information, track important updates, and
              connect with
              essential resources—all in one place.</p>
          </div>
        </div>
      </div>

      <div class="dossier-use-case-container q-mb-xl q-pb-xl">
        <div class="row q-mt-lg">
          <div class="col-12 col-md-10 offset-md-1">
            <HomeNavSection title="Property Details"
                            mainIcon='dashboard'
                            route="rDossierOverview"
                            description="Get a comprehensive overview of the property, including key features, specifications, and essential information to help you make an informed decision."
                            :icons="['🏠', '📏', '💰']" />
          </div>
        </div>
        <div class="row">
          <div class="col-12 col-md-10 offset-md-1">
            <HomeNavSection title="Photos"
                            mainIcon='photo'
                            route="rDossierPhotos"
                            description="Explore high-quality images of the property, including detailed room views, exterior shots, and special features to help you visualize the space and its potential."
                            :icons="['📸', '🖼️', '🔍']" />
          </div>
        </div>
        <div class="row">
          <div class="col-12 col-md-10 offset-md-1">
            <HomeNavSection title="Locations"
                            mainIcon='map'
                            route="rDossierLocation"
                            description="View detailed information about the property's location, including nearby amenities, transportation options, and neighborhood highlights to assess its convenience and appeal."
                            :icons="['📍', '🗺️', '🏬']" />
          </div>
        </div>
        <!-- <div class="row">
          <div class="col-12 col-md-10 offset-md-1">

            <HomeNavSection title="AI Feedback"
                            mainIcon='psychology'
                            route="rDossierLlmFeedback"
                            description="Receive intelligent insights and analysis about the property, including market trends, potential value, and key considerations to help you evaluate the property objectively."
                            :icons="['🤖', '💡', '📊']" />
          </div>
        </div> -->
        <div class="row">
          <div class="col-12 col-md-10 offset-md-1">

            <HomeNavSection title="Comparables"
                            mainIcon='compare'
                            route="rComparables"
                            description="Compare this property with similar listings in the area to understand its market value, unique features, and competitive advantages in the current market."
                            :icons="['⚖️', '📈', '🏘️']" />
          </div>
        </div>

        <div class="row">
          <div class="col-12 col-md-10 offset-md-1">
            <HomeNavSection title="Tasks"
                            mainIcon='assignment'
                            route="rTasks"
                            description="Manage and track tasks related to the property, such as inspections, document submissions, or negotiations, to stay organized and on top of your property journey."
                            :icons="['✅', '📋', '⏰']" />
          </div>
        </div>
        <div class="row">
          <div class="col-12 col-md-10 offset-md-1">
            <HomeNavSection title="Calendar"
                            mainIcon='event'
                            route="rTaskCalendar"
                            description="Keep track of important dates and events related to the property, such as open houses, closing dates, or scheduled meetings, all in one convenient calendar view."
                            :icons="['📅', '🕒', '📌']" />
          </div>
        </div>
        <!-- <div class="row">
          <div class="col-12 col-md-10 offset-md-1">
            <HomeNavSection title="Useful Links"
                            route="rDossierUsefulLinks"
                            description="Access a curated list of helpful resources and links related to the property, including official documents, local services, and relevant websites for further information."
                            :icons="['🔗', '🌐', '📄']" />
          </div>
        </div>
        <div class="row">
          <div class="col-12 col-md-10 offset-md-1">
            <HomeNavSection title="Alerts"
                            route="rDossierAlerts"
                            description="Stay informed with real-time notifications and updates about the property, including price changes, new listings, or important deadlines to keep you ahead of the curve."
                            :icons="['🔔', '📢', '⚠️']" />
          </div>
        </div> -->
      </div>

    </div>
  </q-page>
</template>

<script>
import HomeNavSection from 'src/concerns/dossiers/home/<USER>'
export default {
  components: {
    HomeNavSection,
  },
  methods: {
    closeDialog() {
      this.$emit('close')
    },
  },
  props: {
    saleListing: {
      type: Object,
      required: false
    },
    realtyDossier: {
      type: Object,
      required: false
    }
  },
}
</script>

<style scoped>
.full-width-card {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

.use-case-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  padding: 2rem;
}

.use-case-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.use-case-item-box {
  background: #fff;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.use-case-item-box:hover {
  transform: translateY(-5px);
}

.use-case-img {
  margin-bottom: 1.5rem;
}

.img-data-viz {
  font-size: 2rem;
  line-height: 1.5;
}

.use-case-title {
  margin-bottom: 1.5rem;
}

.h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 1rem;
}

.p.small.centred {
  font-size: 1rem;
  color: #666;
  line-height: 1.5;
}

/* .info-section,
.areas-section,
.features-section {
  padding: 2rem;
}

.section-title {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
  font-size: 1.8rem;
}

.info-grid,
.areas-grid,
.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.info-item,
.area-item,
.feature-item {
  background: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  text-align: center;
}

.info-item:hover,
.area-item:hover,
.feature-item:hover {
  transform: translateY(-5px);
}

.info-icon,
.area-icon,
.feature-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.info-title,
.area-title,
.feature-title {
  color: #333;
  font-size: 1.2rem;
  margin-bottom: 0.5rem;
}

.info-description,
.area-description,
.feature-description {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
}

.q-btn {
  width: 100%;
  padding: 12px;
  font-size: 16px;
  margin-top: 2rem;
}

.text-h4,
.text-h6 {
  color: #333;
}

.text-subtitle1 {
  color: #555;
} */
</style>
