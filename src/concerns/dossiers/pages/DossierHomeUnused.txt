<!-- <q-separator class="q-my-md" /> -->

<!-- <div class="info-section">
        <div class="info-grid">
          <div class="info-item">
            <div class="info-icon">📍</div>
            <h4 class="info-title">Property Location</h4>
            <p class="info-description">
              Quickly find the name and location of the property, with easy
              access to neighborhood information.
            </p>
          </div>
          <div class="info-item">
            <div class="info-icon">💷</div>
            <h4 class="info-title">Pricing Details</h4>
            <p class="info-description">
              See the listing price upfront, along with any relevant market
              trends and price history.
            </p>
          </div>
          <div class="info-item">
            <div class="info-icon">🔑</div>
            <h4 class="info-title">Key Features</h4>
            <p class="info-description">
              Learn about important features like the number of bedrooms,
              property type, and parking details.
            </p>
          </div>
          <div class="info-item">
            <div class="info-icon">🖼️</div>
            <h4 class="info-title">Image Gallery</h4>
            <p class="info-description">
              Enjoy a slideshow of property images with detailed descriptions of
              each space.
            </p>
          </div>
        </div>
      </div>

      <q-separator class="q-my-md" /> -->

<!-- <div class="areas-section">
        <h3 class="section-title">Explore Every Corner</h3>
        <div class="areas-grid">
          <div class="area-item">
            <div class="area-icon">🍳</div>
            <h4 class="area-title">Kitchen & Dining</h4>
            <p class="area-description">
              Detailed descriptions and photos of cooking and dining spaces.
            </p>
          </div>
          <div class="area-item">
            <div class="area-icon">🛏️</div>
            <h4 class="area-title">Bedrooms</h4>
            <p class="area-description">
              Comfortable living spaces with detailed measurements and features.
            </p>
          </div>
          <div class="area-item">
            <div class="area-icon">🌳</div>
            <h4 class="area-title">Garden & Outdoor</h4>
            <p class="area-description">
              Beautiful outdoor spaces with detailed descriptions and images.
            </p>
          </div>
          <div class="area-item">
            <div class="area-icon">⚡</div>
            <h4 class="area-title">Energy Rating</h4>
            <p class="area-description">
              Comprehensive EPC details and energy efficiency information.
            </p>
          </div>
          <div class="area-item">
            <div class="area-icon">🏠</div>
            <h4 class="area-title">Living Areas</h4>
            <p class="area-description">
              Open plan living spaces with detailed layouts and features.
            </p>
          </div>
          <div class="area-item">
            <div class="area-icon">🏡</div>
            <h4 class="area-title">Exterior</h4>
            <p class="area-description">
              Front and side views with detailed exterior features.
            </p>
          </div>
        </div>
      </div>

      <q-separator class="q-my-md" />

      <div class="features-section">
        <h3 class="section-title">Stay Connected</h3>
        <div class="features-grid">
          <div class="feature-item">
            <div class="feature-icon">🗺️</div>
            <h4 class="feature-title">Interactive Map</h4>
            <p class="feature-description">
              See the exact location of the property and explore the surrounding
              area.
            </p>
          </div>
          <div class="feature-item">
            <div class="feature-icon">🔗</div>
            <h4 class="feature-title">Original Listing</h4>
            <p class="feature-description">
              Access the original property listing for additional information
              and updates.
            </p>
          </div>
        </div>
      </div>

      <q-card-actions align="center">
        <q-btn label="Start Exploring!"
               color="primary"
               @click="closeDialog" />
      </q-card-actions> -->