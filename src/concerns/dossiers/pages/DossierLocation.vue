<!-- DossierLocation.vue -->
<template>
  <div class="DossierLocation">
    <div>
      <h3 class="text-h5 q-mb-md q-mt-md text-center h2c-underline"
          style="">Property Location</h3>
    </div>
    <div class="q-my-lg shc-map-ctr"
         v-if="saleListing">
      <ListingDetailsGoogleMap :dossierAssetsComparisons="dossierAssetsComparisons"
                               :evaluationDetails="saleListing"
                               :propIdToZoomTo="propIdToZoomTo" />
    </div>
  </div>
</template>

<script>
import ListingDetailsGoogleMap from "src/concerns/maps/components/ListingDetailsGoogleMap.vue"
export default {
  name: 'DossierLocation',
  components: {
    ListingDetailsGoogleMap
  },
  props: {
    saleListing: {
      type: Object,
      required: false
    },
    realtyDossier: {
      type: Object,
      required: false
    }
  },
  data() {
    return {
      editMode: false
    }
  },
  computed: {
    dossierAssetsComparisons() {
      return this.realtyDossier?.dossier_assets_comparisons || []
    },
    propIdToZoomTo() {
      return this.$route.query?.listing || null
    },
    // initialProperty() {
    //   // return this.saleListing
    //   return {
    //     bgImage: this.saleListing.sale_listing_pics[0]?.image_details?.url || 'https://dummyimage.com/200x100?text=.',
    //     id: `main_${this.saleListing.uuid || 'default'}`,
    //     type: 'main',
    //     title: this.saleListing.title || 'Main Property',
    //     address: this.saleListing.street_address || 'N/A',
    //     price: this.saleListing.formatted_display_price || 'N/A',
    //     lat: this.saleListing.latitude,
    //     lng: this.saleListing.longitude,
    //     icon: 'http://maps.google.com/mapfiles/ms/icons/green-dot.png',
    //   }
    // }
  },
  mounted() {

  }
}
</script>