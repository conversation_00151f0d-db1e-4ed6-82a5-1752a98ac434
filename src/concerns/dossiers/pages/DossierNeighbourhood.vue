<template>
  <q-page class="q-pa-md">
    <div>
      <NeighbourhoodDisplay :neighbourhoods="neighbourhoods"></NeighbourhoodDisplay>
    </div>
  </q-page>
</template>

<script>
import { ref, computed } from 'vue'
import NeighbourhoodDisplay from 'src/concerns/dossiers/components/location/NeighbourhoodDisplay.vue'

export default {
  name: 'NeighbourhoodInfo',
  components: {
    NeighbourhoodDisplay
  },
  setup() {
    const neighbourhoods = [
      {
        id: 12,
        contxt_postcode: 'TR16 5RQ',
        postcode_header: 'TR16 5RQ maps, stats, and open data',
        location_summary: {
          full_text: 'TR16 5RQ lies on Menakarne in Carharrack, Redruth. TR16 5RQ is located in the Redruth Central, Carharrack & St Day electoral ward, within the unitary authority of Cornwall and the English Parliamentary constituency of Camborne and Redruth. The Sub Integrated Care Board (ICB) Location is NHS Cornwall and the Isles of Scilly ICB - 11N and the police force is Devon & Cornwall. This postcode has been in use since January 1980.',
          postcode: 'TR16'
        },
        location_details: {
          street: 'Menakarne',
          locality: 'Carharrack',
          'town/city': 'Redruth',
          country: 'England',
          postcode_district: 'TR16'
        },
        maps: [
          {
            title: 'TR16 5RQ map - OS OpenMap – Local (Ordnance Survey)',
            alt: 'TR16 5RQ map - OS OpenMap – Local (Ordnance Survey)',
            src: 'https://map.getthedata.com/local/tr16-5rq.png'
          },
          {
            title: 'TR16 5RQ map - OS VectorMap District (Ordnance Survey)',
            alt: 'TR16 5RQ map - OS VectorMap District (Ordnance Survey)',
            src: 'https://map.getthedata.com/district/tr16-5rq.png'
          }
        ],
        geodata: {
          easting: '173203',
          northing: '41681',
          latitude: '50.231518',
          longitude: '-5.181487'
        },
        nearest_post_boxes: [
          { location: 'Carharrack Post Office', last_collection_mon_fri: '16:45', last_collection_sat: '11:15', distance: '244m' },
          { location: 'United Road Carharrack', last_collection_mon_fri: '16:45', last_collection_sat: '08:20', distance: '350m' },
          { location: 'St Day Post Office', last_collection_mon_fri: '17:15', last_collection_sat: '11:30', distance: '858m' }
        ],
        food_standards_ratings: [
          { name: 'GQ Chinese Takeaway', rating: 'Food Hygiene Rating: 5 (Very Good)', address: '9 Church Street', distance: '232m' },
          { name: 'The Fat Pony Cafe', rating: 'Food Hygiene Rating: 5 (Very Good)', address: 'Chenhale Equestrian Restaurant', distance: '250m' },
          { name: 'Sweet Treats', rating: 'Food Hygiene Rating: 5 (Very Good)', address: '10 Pound Caravan Site', distance: '307m' }
        ],
        transport: {
          bus_stops: [
            { name: 'The Band Hall', location: 'Carharrack', distance: '195m' },
            { name: 'The Band Hall', location: 'Carharrack', distance: '202m' },
            { name: 'The Carharrack Stars (Higher Albion Row)', location: 'Carharrack', distance: '217m' },
            { name: 'The Carharrack Stars (Higher Albion Row)', location: 'Carharrack', distance: '217m' },
            { name: 'Gwennap Pit Turn (Church Street)', location: 'Carharrack', distance: '334m' }
          ],
          railway_stations: [
            { name: 'Redruth Station', distance: '3.2km' },
            { name: 'Perranwell Station', distance: '5.2km' }
          ]
        },
        house_prices: [
          { address: 'FOX\'S DEN, 1, MENAKARNE, CARHARRACK, REDRUTH, TR16 5RQ', date_sold: '24 AUG', price: '£215,000' },
          { address: '32, MENAKARNE, CARHARRACK, REDRUTH, TR16 5RQ', date_sold: '13 MAY', price: '£222,500' },
          { address: '25, MENAKARNE, CARHARRACK, REDRUTH, TR16 5RQ', date_sold: '3 JUL', price: '£245,000' },
          { address: '25, MENAKARNE, CARHARRACK, REDRUTH, TR16 5RQ', date_sold: '29 JUN', price: '£210,000' },
          { address: 'DENIA, 5, MENAKARNE, CARHARRACK, REDRUTH, TR16 5RQ', date_sold: '22 JAN', price: '£140,000' },
          { address: '23, MENAKARNE, CARHARRACK, REDRUTH, TR16 5RQ', date_sold: '17 APR', price: '£175,000' },
          { address: '30, MENAKARNE, CARHARRACK, REDRUTH, TR16 5RQ', date_sold: '14 JUL', price: '£145,000' },
          { address: '23, MENAKARNE, CARHARRACK, REDRUTH, TR16 5RQ', date_sold: '17 JUL', price: '£174,950' },
          { address: '30, MENAKARNE, CARHARRACK, REDRUTH, TR16 5RQ', date_sold: '31 AUG', price: '£168,000' },
          { address: 'DENIA, 5, MENAKARNE, CARHARRACK, REDRUTH, TR16 5RQ', date_sold: '23 FEB', price: '£135,000' }
        ],
        energy_consumption: {
          electricity: {
            consumption_kwh: '122,747',
            meter_count: '15',
            mean_kwh_meter: '8,183',
            median_kwh_meter: '8,413'
          }
        },
        broadband: {
          access: {
            percentage_of_properties_with_next_generation_access: '100.0%',
            percentage_of_properties_with_superfast_broadband: '100.0%',
            percentage_of_properties_with_ultrafast_broadband: '0.0%',
            percentage_of_properties_with_full_fibre_broadband: '0.0%'
          },
          speed: {
            download: {
              median_download_speed: '27.4Mbps',
              average_download_speed: '31.2Mbps',
              maximum_download_speed: '76.60Mbps'
            },
            upload: {
              median_upload_speed: '7.6Mbps',
              average_upload_speed: '6.8Mbps',
              maximum_upload_speed: '19.53Mbps'
            }
          }
        }
      },
      {
        id: 13,
        contxt_postcode: 'TR15 3BA',
        postcode_header: 'TR15 3BA maps, stats, and open data',
        location_summary: {
          full_text: 'TR15 3BA is in Carn Brea Village, Redruth. TR15 3BA is located in the Four Lanes, Beacon & Troon electoral ward, within the unitary authority of Cornwall and the English Parliamentary constituency of Camborne and Redruth. The Sub Integrated Care Board (ICB) Location is NHS Cornwall and the Isles of Scilly ICB - 11N and the police force is Devon & Cornwall. This postcode has been in use since January 1980.',
          postcode: 'TR15'
        },
        location_details: {},
        maps: [
          {
            title: 'TR15 3BA map - OS OpenMap – Local (Ordnance Survey)',
            alt: 'TR15 3BA map - OS OpenMap – Local (Ordnance Survey)',
            src: 'https://map.getthedata.com/local/tr15-3ba.png'
          },
          {
            title: 'TR15 3BA map - OS VectorMap District (Ordnance Survey)',
            alt: 'TR15 3BA map - OS VectorMap District (Ordnance Survey)',
            src: 'https://map.getthedata.com/district/tr15-3ba.png'
          }
        ],
        geodata: {
          easting: '168834',
          northing: '41424',
          latitude: '50.227528',
          longitude: '-5.242441'
        },
        nearest_post_boxes: [
          { location: 'Churchtown', last_collection_mon_fri: '17:45', last_collection_sat: '09:30', distance: '240m' },
          { location: 'Barncoose Terrace', last_collection_mon_fri: '17:30', last_collection_sat: '09:00', distance: '535m' },
          { location: 'West Trewirgie', last_collection_mon_fri: '17:45', last_collection_sat: '09:00', distance: '715m' }
        ],
        food_standards_ratings: [
          { name: 'Camborne Redruth Community Hospital', rating: 'Food Hygiene Rating: 5 (Very Good)', address: 'Barncoose', distance: '254m' },
          { name: 'Tremethick House', rating: 'Food Hygiene Rating: 4 (Good)', address: 'Meadowside', distance: '453m' },
          { name: 'Windmill Day Nursery', rating: 'Food Hygiene Rating: 5 (Very Good)', address: 'Windmill Nursery', distance: '474m' }
        ],
        transport: {
          bus_stops: [
            { name: 'Barncoose Hospital (Car Park)', location: 'Redruth', distance: '219m' },
            { name: 'Euny Close', location: 'Redruth', distance: '320m' },
            { name: 'South Park (Blowinghouse Hill)', location: 'Redruth', distance: '372m' },
            { name: 'South Park (Blowinghouse Hill)', location: 'Redruth', distance: '396m' },
            { name: 'Barncoose Terrace', location: 'Redruth', distance: '461m' }
          ],
          railway_stations: [
            { name: 'Redruth Station', distance: '1.3km' },
            { name: 'Camborne Station', distance: '4.4km' }
          ]
        },
        house_prices: [],
        energy_consumption: {
          electricity: {
            consumption_kwh: '35,467',
            meter_count: '11',
            mean_kwh_meter: '3,224',
            median_kwh_meter: '2,610'
          }
        },
        broadband: {
          access: {
            percentage_of_properties_with_next_generation_access: '100.0%',
            percentage_of_properties_with_superfast_broadband: '100.0%',
            percentage_of_properties_with_ultrafast_broadband: '0.0%',
            percentage_of_properties_with_full_fibre_broadband: '0.0%'
          },
          speed: {
            download: {
              median_download_speed: '39.1Mbps',
              average_download_speed: '38.2Mbps',
              maximum_download_speed: '80.00Mbps'
            },
            upload: {
              median_upload_speed: '7.6Mbps',
              average_upload_speed: '7.9Mbps',
              maximum_upload_speed: '15.44Mbps'
            }
          }
        }
      }
    ]

    const selectedPostcode = ref(neighbourhoods[0].contxt_postcode)
    const activeTab = ref('overview')
    const slide = ref(0)

    const postcodeOptions = computed(() =>
      neighbourhoods.map(n => ({
        label: n.contxt_postcode,
        value: n.contxt_postcode
      }))
    )

    const selectedNeighbourhood = computed(() =>
      neighbourhoods.find(n => n.contxt_postcode === selectedPostcode.value) || neighbourhoods[0]
    )

    const housePriceColumns = [
      { name: 'address', label: 'Address', field: 'address', align: 'left' },
      { name: 'date_sold', label: 'Date Sold', field: 'date_sold', align: 'center' },
      { name: 'price', label: 'Price', field: 'price', align: 'center' }
    ]

    const updateSelectedNeighbourhood = () => {
      slide.value = 0 // Reset carousel when changing postcode
    }

    return {
      neighbourhoods,
      selectedPostcode,
      activeTab,
      slide,
      postcodeOptions,
      selectedNeighbourhood,
      housePriceColumns,
      updateSelectedNeighbourhood
    }
  }
}
</script>

<style scoped>
.my-card {
  max-width: 900px;
  margin: 0 auto;
}
</style>