<!-- DossierPhotos.vue -->
<template>
  <div class="DossierPhotos">
    <h3 class="text-h5 q-mb-lg q-mt-lg text-center h2c-underline">Photos of your Northstar Property</h3>
    <ViewOnlyImageGallery :images="images"
                          :dossierAssetUuid="dossierAssetUuid"
                          :initial-edit-mode="editMode"
                          @delete-images="$emit('delete-images', $event)"
                          @update-image="$emit('update-image', $event)"
                          @photo-uploaded="$emit('photo-uploaded', $event)" />
  </div>
</template>

<script>
// import EditableImageGallery from "src/concerns/dossiers/components/images/EditableImageGallery.vue"
import ViewOnlyImageGallery from "src/concerns/dossiers/components/images/ViewOnlyImageGallery.vue"

export default {
  name: 'DossierPhotos',
  components: {
    ViewOnlyImageGallery
  },
  props: {
    realtyDossier: {
      type: Object,
      required: false
    }
  },
  data() {
    return {
      editMode: false
    }
  },
  computed: {
    images() {
      return this.realtyDossier?.dossier_sale_listing?.sale_listing_pics || []
    },
    dossierAssetUuid() {
      return this.realtyDossier?.primary_dossier_asset?.primary_dossier_asset_uuid
    }
  },
  mounted() {
    if (this.$route.name === "rDossierPhotosEdit") {
      this.editMode = true
    }
  }
}
</script>