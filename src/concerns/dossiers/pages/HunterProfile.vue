<template>
  <q-page class="hunter-profile">
    <ScootPreferencesView :preferences="scootPreferences"
                          :is-loading="isLoading"
                          :error="error" />
  </q-page>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { useRoute } from 'vue-router'
import ScootPreferencesView from 'src/concerns/dossiers/components/scoots/ScootPreferencesView.vue'
import { useScootPreferences } from 'src/concerns/dossiers/composables/useScootPreferences'

const $q = useQuasar()
const route = useRoute()

// Get scoot UUID from route params
const scootIdFromRoute = ref(route.params.dossierUuid || null)

// Initialize composable
const {
  scootPreferences,
  isLoading,
  error,
  loadScootPreferences
} = useScootPreferences(scootIdFromRoute, $q)

// Load preferences on mount
onMounted(() => {
  if (scootIdFromRoute.value) {
    loadScootPreferences(scootIdFromRoute.value)
  }
})
</script>