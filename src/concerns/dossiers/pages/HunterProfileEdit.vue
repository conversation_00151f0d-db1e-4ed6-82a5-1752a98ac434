<template>
  <q-page class="hunter-profile-edit">
    <!-- Loading State -->
    <div v-if="isLoading" class="text-center q-pa-xl">
      <q-spinner color="primary" size="3em" />
      <div class="q-mt-md text-grey-7">Loading preferences...</div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="text-center q-pa-xl">
      <q-icon name="error_outline" color="negative" size="3em" />
      <div class="q-mt-md text-negative">{{ error }}</div>
      <q-btn 
        color="primary" 
        label="Back to Profile" 
        class="q-mt-md"
        @click="$router.push({ name: 'rHunterProfile' })"
      />
    </div>

    <!-- Edit Form -->
    <ScootPreferencesForm 
      v-else-if="scootPreferences"
      :model-value="scootPreferences"
      @update:model-value="handleFormUpdate"
      @save="handleSave" 
    />

    <!-- No Data State -->
    <div v-else class="text-center q-pa-xl">
      <q-icon name="search_off" color="grey-5" size="3em" />
      <div class="q-mt-md text-grey-7">No preferences found</div>
      <q-btn 
        color="primary" 
        label="Initialize Preferences" 
        class="q-mt-md"
        @click="initializeNewPreferences"
      />
    </div>
  </q-page>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { useRoute, useRouter } from 'vue-router'
import ScootPreferencesForm from 'src/concerns/dossiers/components/scoots/ScootPreferencesForm.vue'
import { useScootPreferences } from 'src/concerns/dossiers/composables/useScootPreferences'

const $q = useQuasar()
const $route = useRoute()
const $router = useRouter()

// Get scoot UUID from route params or use a default
const scootIdFromRoute = ref($route.params.dossierUuid || null)

// Initialize composable
const {
  scootPreferences,
  isLoading,
  error,
  loadScootPreferences,
  updateScootPreferences,
  getDefaultScootStructure
} = useScootPreferences(scootIdFromRoute, $q)

// Handle form updates
const handleFormUpdate = (updatedData) => {
  // This is called when the form data changes
  // You might want to save this to local state or debounce updates
  console.log('Form updated:', updatedData)
}

// Handle save
const handleSave = async (formDataFromComponent) => {
  try {
    if (scootIdFromRoute.value) {
      await updateScootPreferences(formDataFromComponent)
      $q.notify({
        color: 'positive',
        message: 'Preferences saved successfully!',
        icon: 'check'
      })
      // Navigate back to view mode
      $router.push({ name: 'rHunterProfile' })
    } else {
      $q.notify({
        type: 'warning',
        message: 'Cannot save: Scoot ID is not set. Implement create logic.'
      })
    }
  } catch (error) {
    console.error('Error saving preferences:', error)
    $q.notify({
      color: 'negative',
      message: 'Failed to save preferences. Please try again.',
      icon: 'error'
    })
  }
}

// Initialize new preferences
const initializeNewPreferences = () => {
  const defaultPrefs = getDefaultScootStructure()
  scootPreferences.value = defaultPrefs
  $q.notify({
    message: 'Initialized with default preferences',
    icon: 'info'
  })
}

// Load preferences on mount
onMounted(() => {
  if (scootIdFromRoute.value) {
    loadScootPreferences(scootIdFromRoute.value)
  } else {
    // For development/testing - initialize with defaults
    initializeNewPreferences()
  }
})
</script>

<style scoped>
.hunter-profile-edit {
  background-color: #fafafa;
  min-height: 100vh;
}
</style>
