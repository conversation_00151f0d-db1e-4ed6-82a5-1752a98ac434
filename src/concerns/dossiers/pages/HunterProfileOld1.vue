<template>
  <q-page padding>
    <div v-if="isLoading"
         class="text-center q-pa-md">
      <q-spinner-dots color="primary"
                      size="40px" />
      <p>Loading preferences...</p>
    </div>

    <div v-else-if="error"
         class="text-negative q-pa-md">
      <q-icon name="error" /> {{ error }}
      <q-btn label="Retry"
             @click="() => loadScootPreferences(scootIdFromRoute)"
             flat
             color="primary" />
    </div>

    <!-- Ensure scootPreferences is not null before rendering the form -->
    <ScootPreferencesForm v-if="scootPreferences"
                          :model-value="scootPreferences"
                          @update:model-value="handleFormUpdate"
                          @save="handleSave" />
    <div v-else-if="!isLoading && !error">
      <p>No preferences data to display. This might be a new scoot.</p>
      <!-- Optionally, allow creating/initializing preferences -->
      <q-btn label="Initialize New Preferences"
             @click="initializeNewPreferences"
             color="primary" />
    </div>

    <q-card class="q-mt-xl"
            v-if="scootPreferences">
      <q-card-section>
        <div class="text-h6">Composable Scoot Data (for debugging):</div>
        <pre>{{ scootPreferences }}</pre>
      </q-card-section>
    </q-card>
  </q-page>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useQuasar } from 'quasar'
import { useRoute } from 'vue-router' // If scoot UUID comes from route params
// import ScootPreferencesForm from 'src/components/ScootPreferencesForm.vue'
import ScootPreferencesForm from 'src/concerns/dossiers/components/scoots/ScootPreferencesForm.vue' // Adjust path if needed
import { useScootPreferences } from 'src/concerns/dossiers/composables/useScootPreferences' // Adjust path

const $q = useQuasar()
const route = useRoute()

// Example: Get scoot UUID from route params or props
// const scootIdFromRoute = ref(route.params.scootUuid || 'default-scoot-uuid-if-any');
const scootIdFromRoute = ref(null) // Start with null or a valid UUID

// --- Initialize Composable ---
// Pass the reactive scootIdFromRoute so the watcher in the composable can react
const {
  scootPreferences,
  isLoading,
  error,
  loadScootPreferences, // Now takes UUID as argument
  updateScootPreferences,
  getDefaultScootStructure
} = useScootPreferences(scootIdFromRoute, $q)


onMounted(() => {
  // Simulate getting UUID, e.g., from route or user session
  const actualScootId = route.params.scootUuid || 'some-valid-scoot-uuid-for-testing'; // Replace with actual logic
  if (actualScootId) {
    scootIdFromRoute.value = actualScootId;
    // The watcher in useScootPreferences will trigger loadScootPreferences
  } else {
    // Handle case where no UUID is available initially
    // Maybe initialize for a new scoot
    $q.notify({ message: 'No Scoot UUID found. Ready to initialize new preferences.', color: 'info' });
    // scootPreferences.value = getDefaultScootStructure(); // Or call initializeNewPreferences
  }
})

const initializeNewPreferences = () => {
  // This assumes you have a way to create a new scoot record on the backend
  // or that you want to work with a default local structure first.
  // For now, just sets local state to defaults.
  scootPreferences.value = getDefaultScootStructure();
  // You might need a "create scoot" API call here if one doesn't exist yet.
  // And then set `scootIdFromRoute.value` to the new UUID.
  $q.notify({ message: 'Preferences initialized with defaults. Save to create/update.', color: 'info' });
};


// This function is called by ScootPreferencesForm's `update:model-value`
// It's good practice to keep the composable's state as the source of truth
// and only update it via its methods or when data is loaded.
// However, v-model on the form directly modifies the object, which is often fine for forms.
const handleFormUpdate = (updatedFormData) => {
  // If you want strict control, you could do:
  // scootPreferences.value = { ...scootPreferences.value, ...updatedFormData };
  // But since ScootPreferencesForm modifies the object directly via v-model,
  // scootPreferences is already updated. This handler is more for awareness.
  // console.log('Form data changed (v-model):', updatedFormData)
}

const handleSave = async (formDataFromComponent) => {
  // formDataFromComponent is the data emitted by ScootPreferencesForm on save
  // It should be the complete, updated preferences object.
  if (scootIdFromRoute.value) {
    await updateScootPreferences(formDataFromComponent)
  } else {
    // Handle scenario: Saving for the first time (creating a new Scoot)
    // This would likely involve a POST request to a create endpoint.
    // For now, we'll assume `updateScootPreferences` can handle it if the backend
    // does an "upsert" or if you set `scootIdFromRoute.value` after a create call.
    $q.notify({ type: 'warning', message: 'Cannot save: Scoot ID is not set. Implement create logic.' });
    // Example of what create logic might look like:
    // const newScoot = await createScootOnBackend(formDataFromComponent);
    // if (newScoot && newScoot.uuid) {
    //   scootIdFromRoute.value = newScoot.uuid;
    //   scootPreferences.value = newScoot; // Update local state
    //   $q.notify({ type: 'positive', message: 'New preferences created and saved!' });
    // }
  }
}
</script>