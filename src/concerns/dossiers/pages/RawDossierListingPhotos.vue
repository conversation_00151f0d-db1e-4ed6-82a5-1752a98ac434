<template>
  <!-- Intended for screenshot by playwright -->
  <div class="image-gallery RawDossierPhotos q-ma-md">
    <!-- Image Grid -->
    <div v-if="realtyDossier"
         class="row q-col-gutter-md">
      <div class="col-xs-12">
        <div v-html="targetSaleListing.description"></div>
        <div class="text-subtitle2">{{ targetSaleListing.street_name }}</div>
        <div class="text-subtitle2">{{ targetSaleListing.street_address }}</div>
        <div class="text-subtitle2">{{ targetSaleListing.postal_code }}</div>
      </div>
      <div class="col-xs-12">
        <span>Original Listing: </span>
        <a target="_blank"
           :href="targetSaleListing.import_url">
          {{ targetSaleListing.import_url }}
        </a>
      </div>

      <!-- Updated image container with border -->
      <div v-for="image in acceptableImages"
           :key="image.id"
           class="image-container col-6 q-mb-lg">
        <div class="image-wrapper q-pa-none"
             style="border: 2px solid #ccc; border-radius: 4px;">
          <q-img :src="image.image_details.url"
                 loading="eager"
                 :ratio="16 / 9"
                 class="image" />
          <div class="image-id text-h6 q-mt-sm"
               style=" padding: 4px; border-radius: 4px; display: inline-block;">
            Image ID: {{ image.id }}
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Selected Button (visible when images are selected) -->
    <div class="q-mt-md"
         v-if="editMode && selectedImages.length">
      <q-btn color="red"
             label="Delete Selected"
             icon="delete_forever"
             @click="deleteSelectedImages" />
    </div>
  </div>
</template>

<script>
// import ImageItem from "src/concerns/dossiers/components/images/ImageItem.vue"
export default {
  name: 'ImageGallery',
  components: {
    // ImageItem
  },
  props: {
    realtyDossier: {
      type: Object,
      required: false
    }
  },
  mounted: function () {
    if (this.$route.name === "rDossierPhotosEdit") {
      this.editMode = true;
    }
  },
  data() {
    return {
      editMode: false,
      selectedImages: [],
      locallyHiddenImageUuids: [],
      showUploadDialog: false,
      newPhoto: null
    }
  },
  computed: {
    targetSaleListing() {
      let tsl = null
      if (this.$route.params.listingUuid === this.realtyDossier?.dossier_sale_listing.uuid) {
        tsl = this.realtyDossier?.dossier_sale_listing || null;
      }
      else {
        this.realtyDossier?.secondary_dossier_assets.forEach((asset) => {
          if (asset.main_listing_details.uuid === this.$route.params.listingUuid) {
            tsl = asset.main_listing_details;
          }
        });
      }
      return tsl
    },
    acceptableImages() {
      // Filter out images with "480x320" in the URL - they are typically
      // wrong images on OTM that leak in during scraping
      let filteredPics = this.targetSaleListing?.sale_listing_pics?.filter(pic =>
        !pic.image_details?.url?.includes("480x320")
      )
      return filteredPics
      // return this.targetSaleListing?.sale_listing_pics || []
    }
  },
  methods: {
    notHiddenLocally(image) {
      return !this.locallyHiddenImageUuids.includes(image.uuid);
    },
    toggleImageSelection(imageId) {
      const index = this.selectedImages.indexOf(imageId);
      if (index === -1) {
        this.selectedImages.push(imageId);
      } else {
        this.selectedImages.splice(index, 1);
      }
    },
    hideImage(imageUuid) {
      this.locallyHiddenImageUuids.push(imageUuid);
    },
    deleteSelectedImages() {
      this.$emit('delete-images', this.selectedImages);
      this.selectedImages = [];
    },
    updateImage(image) {
      this.$emit('update-image', image);
    },
    handleTagClick(tag) {
      console.log(`Tag clicked: ${tag}`);
    },
    handleFileChange(file) {
      this.newPhoto = file;
    },
    uploadPhoto() {
      if (this.newPhoto) {
        console.log('Uploading photo:', this.newPhoto);
        const mockImage = {
          id: Date.now(),
          image_details: {
            url: URL.createObjectURL(this.newPhoto)
          },
          photo_title: this.newPhoto.name,
          photo_description: ''
        };
        this.$emit('photo-uploaded', mockImage);
        this.newPhoto = null;
        this.showUploadDialog = false;
      }
    }
  }
}
</script>

<style scoped>
.image-gallery {
  max-width: 100%;
}

.image-container {
  display: flex;
  flex-direction: column;
}

.image-wrapper {
  background: #f5f5f5;
}

.image-id {
  text-align: center;
  background: #fff;
}
</style>