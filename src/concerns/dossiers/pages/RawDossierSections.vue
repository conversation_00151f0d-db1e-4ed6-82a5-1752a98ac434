<template>
  <div v-if="saleListing"
       flat
       bordered
       class="dossier-sections">

    <div>
      <div class="col-xs-12 q-pa-md">
        <div v-for="part in assetParts"
             :key="part.id"
             :id="part.asset_part_slug"
             class="asset-part-section">
          <SingleAssetPart :assetPart="part"></SingleAssetPart>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SingleAssetPart from "src/concerns/dossiers/components/SingleAssetPart.vue"
export default {
  name: 'RawDossierSections',
  components: {
    SingleAssetPart
  },
  data() {
    return {}
  },
  props: {
    saleListing: {
      type: Object,
      required: false
    },
    realtyDossier: {
      type: Object,
      required: false
    }
  },
  computed: {
    assetParts() {
      return this.realtyDossier?.primary_dossier_asset?.asset_parts || []
    },
    relevantSoldTransactions() {
      if (this.realtyDossier?.recent_sales_analysis?.other_recent_sales) {
        return [
          this.realtyDossier?.recent_sales_analysis?.most_similar_recent_sale,
          ...this.realtyDossier?.recent_sales_analysis?.other_recent_sales
        ]
      }
      else {
        return []
      }
    },
    targetChartName() {
      return this.$route.params.targetChartName || 'price_by_floor_area'
    },
  },
  methods: {
    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1).replace(/_/g, ' ')
    },
    modalStateChanged(newVal) {
      this.modalOpen = newVal;
    },
    openChartModal(selectedPoint) {
      this.selectedPoint = selectedPoint
      this.modalOpen = true;
      const queryString = new URLSearchParams({ actionId: selectedPoint.SoldTransactionId }).toString();
      window.history.pushState(null, '', `?${queryString}`);
      // window.dispatchEvent(new Event('popstate'))
    },
    passChartSeries(chartSeries) {
      this.soldDataPoints = chartSeries[0].data
      // transactionLatitude = chartSeries[0].data[0].Latitude
    },
  }
};
</script>

<style scoped></style>
