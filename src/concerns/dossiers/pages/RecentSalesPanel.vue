<template>
  <div v-if="realtyDossier"
       flat
       bordered
       class="RecentSalesPanel">


    <ShowRecentSales :mostSimilarRecentSale="mostSimilarRecentSale"
                     :otherRecentSales="otherRecentSales"></ShowRecentSales>


  </div>
</template>

<script>
import ShowRecentSales from "src/concerns/for-sale-evaluations/components/ShowRecentSales.vue"
export default {
  name: 'RecentSalesPanel',
  components: {
    ShowRecentSales,
  },
  data() {
    return {}
  },
  props: {
    // saleListing: {
    //   type: Object,
    //   required: false
    // },
    realtyDossier: {
      type: Object,
      required: false
    }
  },
  computed: {
    mostSimilarRecentSale() {
      return this.realtyDossier?.recent_sales_analysis?.most_similar_recent_sale || {}
    },
    otherRecentSales() {
      return this.realtyDossier?.recent_sales_analysis?.other_recent_sales || []
    },
  },
  methods: {

  }
};
</script>

<style scoped></style>
