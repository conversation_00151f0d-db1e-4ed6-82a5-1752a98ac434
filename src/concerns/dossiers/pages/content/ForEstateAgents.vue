<template>
  <div>
    <div class="py-4 px-8 bg-white shadow-xl rounded-lg my-20">
      <div>
        <h2 class="q-mt-xl q-mb-sm info-text-h2 text-gray-800 font-semibold">
          For Estate Agents
        </h2>
        <!-- <p style="text-align: center">Last update: August 5th, 2023</p> -->
        <div class="Box-body px-5 pb-5 text-body1 text-info-1">
          <div></div>

          <article class="markdown-body entry-content container-lg"
                   itemprop="text">
            <!-- <div class="parag">
              listings as well as their insights into local market conditions. It also
              helps them make more engaging and informed recommendations to their clients.
            </div> -->
            <div class="parag info-parag">
              We help estate agents showcase their best listings along with their unique
              insights into local market conditions.
            </div>
            <div class="parag info-parag">
              In the future we plan to also provide assistance with other aspects of the
              home buying process, such as helping to find a lender or mortgage broker and
              connecting with other professionals such as home inspectors and real estate
              attorneys.
            </div>
          </article>
        </div>
        <div class="Box-body px-5 pb-5">
          <div></div>

          <article class="markdown-body entry-content container-lg"
                   itemprop="text"></article>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent } from "vue"

export default defineComponent({
  created() { },
  computed: {},
})
</script>
