<template>
  <div>
    <div class="py-4 px-8 bg-white shadow-xl rounded-lg my-20">
      <div>
        <h2 class="q-mt-xl q-mb-sm info-text-h2 text-gray-800 font-semibold">
          For Home Buyers
        </h2>
        <!-- <h4 class="text-gray-800 text-3xl text-center font-semibold">
          Buying a home is a massive learning experience - don't waste it!!
        </h4> -->
        <!-- <p style="text-align: center">Last update: August 5th, 2023</p> -->
        <div class="Box-body px-5 pb-5 text-body2 text-info-1">
          <article class="markdown-body entry-content container-lg"
                   itemprop="text">
            <div class="parag info-parag">
              The journey towards home ownership begins long before you walk into your
              dream home.
              {{ whitelabelNameDisplay }} is the smart way to learn about the
              property market even before you start actively searching
            </div>
            <div class="parag info-parag">
              Understanding house prices is valuable for anyone who has plans to buy a
              property sometime. We make it fun and easy to gain insights into house
              prices with interesting, unexpected and sometimes plain whacky comparisons.
            </div>
            <!-- <div class="parag info-parag">
              We assist home buyers with the process of finding and purchasing a new home.
            </div>
            <div class="parag info-parag">
              A home buyer might want to use a website that allows them to compare two
              homes side by side in order to more easily identify the differences between
              the properties and to make a more informed decision about which home to
              purchase. By comparing the size, age, condition, and location of the homes,
              as well as other features such as the number of bedrooms and bathrooms, the
              buyer can get a better sense of which property is the better fit for their
              needs and budget. This can help the buyer to make a more confident and
              informed purchase decision, and to feel more comfortable with the property
              they ultimately choose.
            </div> -->
            <!-- <div class="parag info-parag">
              <ul>
                <li>
                  Identifying properties that match the buyer's search criteria: This can
                  include searching listings, touring properties, and providing
                  recommendations to the buyer. Providing information about the local real
                  estate market:
                </li>
              </ul>
              A house hunt concierge can provide insights and data about local market
              conditions, including information about home prices, demand, and trends.
              Assisting with negotiations: A concierge can help the buyer to negotiate the
              terms of a purchase agreement, including the price, closing date, and other
              terms.
            </div> -->
            <div class="parag info-parag">
              When you are house hunting it is always a good idea to let your friends and
              family know - they are a good source of feedback and advice.
              {{ whitelabelNameDisplay }} makes it easy for you to get help
              from them
            </div>
            <div class="parag info-parag">
              We will soon be adding features to provide assistance with more aspects of
              the home buying process, such as helping to find a lender or mortgage broker
              and connecting with other professionals such as home inspectors and real
              estate attorneys.
            </div>
          </article>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent } from "vue"

export default defineComponent({
  props: {
    serviceEmail: {
      type: String,
      default: "",
    },
    whitelabelNameDisplay: {
      type: String,
      default: "",
    }
  },
  created() { },
  computed: {},
})
</script>
