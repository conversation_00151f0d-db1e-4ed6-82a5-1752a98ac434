<template>
  <div>
    <div class="py-4 px-8 bg-white shadow-xl rounded-lg my-20">
      <div>
        <h2 class="q-mt-xl q-mb-sm info-text-h2 text-gray-800 font-semibold">
          For Home Sellers
        </h2>
        <!-- <p style="text-align: center">Last update: August 5th, 2023</p> -->
        <div class="Box-body px-5 pb-5 text-body2 text-info-1">
          <div class="parag info-parag">
            A key aspect of selling a home is understanding the market.
            {{ whitelabelNameDisplay }} lets you gain insights about the
            property market that will empower you whenever you decide to sell your home.
          </div>
          <div class="parag info-parag">
            If you used {{ whitelabelNameDisplay }} when you bought your
            property, you will already have important information about it online that you
            can draw on. You will be able to look at the other properties you looked at
            when you were househunting and check how they performed on the market.
          </div>
          <div class="parag info-parag">
            Even if you have never used {{ whitelabelNameDisplay }} before
            you can still use it to better understand the competition in the local real
            estate market.
          </div>
          <div class="parag info-parag">
            You can also use {{ whitelabelNameDisplay }} to show off your
            property to more people. Simply share a comparison of your home with another
            home on the market that shows yours in a good light.
          </div>

          <div>
            <!-- A person selling a home might want to use a website that allows them to
            compare two homes side by side in order to better understand the competition
            in the local real estate market. By comparing the features and characteristics
            of their own home to similar properties that are also for sale, the seller can
            get a better sense of how their home stacks up against other properties in
            terms of size, age, condition, and location. This can help the seller to
            identify any potential weaknesses or areas for improvement in their own home,
            and to make any necessary changes or renovations in order to make their home
            more attractive to potential buyers. It can also help the seller to set a more
            competitive and realistic asking price for their home. -->
          </div>

          <article class="markdown-body entry-content container-lg"
                   itemprop="text"></article>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent } from "vue"
export default defineComponent({
  props: {
    serviceEmail: {
      type: String,
      default: "",
    },
    whitelabelNameDisplay: {
      type: String,
      default: "",
    }
  },
  created() { },
  computed: {},
})
</script>
