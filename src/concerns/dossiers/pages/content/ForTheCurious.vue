<template>
  <div>
    <div class="py-4 px-8 bg-white shadow-xl rounded-lg my-20">
      <div>
        <h2 class="text-gray-800 text-3xl text-center font-semibold">For The Curious</h2>
        <p style="text-align: center">Last update: August 5th, 2023</p>
        <div class="Box-body px-5 pb-5">
          <div></div>

          <article class="markdown-body entry-content container-lg"
                   itemprop="text"></article>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent } from "vue"
export default defineComponent({
  created() { },
  computed: {},
})
</script>
