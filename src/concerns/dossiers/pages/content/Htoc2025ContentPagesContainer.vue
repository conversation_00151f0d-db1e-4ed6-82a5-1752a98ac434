<template>
  <q-page class="container">
    <div class="q-pa-md">
      <component :is="contentPageComponent"
                 :whitelabelNameDisplay="whitelabelNameDisplay"
                 :serviceEmail="serviceEmail"></component>
    </div>
  </q-page>
</template>
<script>
import { defineComponent } from "vue"
// import ForHomeBuyers from "src/concerns/dossiers/pages/content/ForHomeBuyers.vue"
// import ForHomeSellers from "src/concerns/dossiers/pages/content/ForHomeSellers.vue"
// import ForEstateAgents from "src/concerns/dossiers/pages/content/ForEstateAgents.vue"
// import ForTheCurious from "src/concerns/dossiers/pages/content/ForTheCurious.vue"
import PrivacyPolicy from "src/concerns/dossiers/pages/content/PrivacyPolicy.vue"
import TermsOfService from "src/concerns/dossiers/pages/content/TermsOfService.vue"
// import SiteFaqs from "src/concerns/dossiers/pages/content/SiteFaqs.vue"
export default defineComponent({
  name: "Htoc2025ContentPagesContainer",
  props: {
    serviceEmail: {
      type: String,
      default: "<EMAIL>",
    },
    whitelabelNameDisplay: {
      type: String,
      default: "HomesToCompare",
    }
  },
  components: {
    PrivacyPolicy,
    TermsOfService,
    // ForHomeBuyers,
    // ForHomeSellers,
    // ForEstateAgents,
    // ForTheCurious,
    // SiteFaqs,
  },
  computed: {
    contentPageComponent() {
      // return "TermsOfService"
      if (this.$route.params.page_slug === "terms-of-service") {
        return "TermsOfService"
        // } else if (this.$route.params.page_slug === "for-home-buyers") {
        //   return "ForHomeBuyers"
        // } else if (this.$route.params.page_slug === "faqs") {
        //   return "SiteFaqs"
        // } else if (this.$route.params.page_slug === "for-home-sellers") {
        //   return "ForHomeSellers"
        // } else if (this.$route.params.page_slug === "for-estate-agents") {
        //   return "ForEstateAgents"
        // } else if (this.$route.params.page_slug === "for-the-curious") {
        //   return "ForTheCurious"
      } else {
        return "PrivacyPolicy"
      }
    },
  },
})
</script>
