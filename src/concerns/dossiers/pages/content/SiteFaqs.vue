<template>
  <div>
    <div class="q-pt-lg bg-white shadow-xl rounded-lg my-20">
      <div>
        <h2 class="text-gray-800 text-3xl text-center font-semibold">FAQS</h2>

        <!-- <h2 class="q-mt-xl q-mb-sm info-text-h2 text-gray-800 font-semibold">
          For Home Buyers
        </h2> -->

        <div>
          <q-toggle @update:model-value="expandedValueChanged"
                    :modelValue="expandAllFaqs"
                    label="Expand All"
                    class="q-mb-md"
                    color="accent" />
        </div>
        <div class="q-pa-none"
             style="">
          <ExpandableList :faqItems="faqItems"></ExpandableList>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent, ref } from "vue"
import ExpandableList from "src/components/content/ExpandableList.vue"
export default defineComponent({
  components: {
    ExpandableList,
  },
  props: {
    serviceEmail: {
      type: String,
      default: "",
    },
    whitelabelNameDisplay: {
      type: String,
      default: "",
    }
  },
  methods: {
    expandedValueChanged(newExpVal) {
      this.faqItems.forEach((faqItem) => {
        faqItem.isFaqExpanded = newExpVal
      })
      this.expandAllFaqs = newExpVal
    },
  },
  setup() {
    return {
      expandAllFaqs: ref(false),
    }
  },
  data() {
    return {
      faqItems: [
        {
          isFaqExpanded: false,
          faqLabel: "What are real estate comparables?",
          faqContent: `Real estate comparables, commonly known as 'comps' refer to properties
                  that are similar to the subject property being evaluated. These
                  comparables serve as reference points for determining the fair market
                  value of a property based on its characteristics, location, and recent
                  sales data. Real estate professionals, appraisers, and buyers use
                  comparables to assess the value of a property, whether for buying,
                  selling, or refinancing purposes.`,
        },
        {
          isFaqExpanded: false,
          faqLabel: "How can comparables help when househunting?",
          faqContent: ` Here are some of the ways that comparables can help you when house
                  hunting:
                  <ul>
                    <li>
                      <strong>Determine the fair market value of a property.</strong> As
                      mentioned above, comparables can help you to get an accurate idea of
                      what a property is worth. This is important because you don't want
                      to overspend on a home.
                    </li>
                    <li>
                      <strong>Negotiate a better price.</strong> If you know what similar
                      properties have sold for, you can use this information to negotiate
                      a better price on the home you are interested in buying.
                    </li>
                    <li>
                      <strong>Identify potential problems with a property.</strong> If you
                      compare the features of a property to comparables, you may be able
                      to identify potential problems. For example, if the property you are
                      considering buying is priced significantly lower than comparables,
                      there may be something wrong with it.
                    </li>
                  </ul>

                  By using comparables, you can make an informed decision about whether or
                  not to buy a property. They can help you to get a fair price on a home
                  and to identify potential problems.`,
        },
        {
          isFaqExpanded: false,
          faqLabel: `How can ${this.whitelabelNameDisplay} help me make use of comparables?`,
          faqContent: `<p>
                    ${this.whitelabelNameDisplay} has many examples of
                    interesting home comparisons that can serve as an inspiration.
                  </p>`,
        },
      ],
    }
  },
})
</script>
