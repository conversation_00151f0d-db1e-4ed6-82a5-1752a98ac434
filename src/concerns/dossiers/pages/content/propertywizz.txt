

<PERSON><PERSON><PERSON> (movingnextdoor.. / savemefrommakingabadmistake…..) :
The equivalent of “does anyone know of any reason why x and y should not get married”..
“Buying a house is a major life event - share it with your friends!! You never know what tips and advice you might get back”
( could even be interesting to encourage people to share their thoughts about wanting to rent or sell a property... )
Question and answer about property listings
For people who are close to making a decision
First screen you put in the url for a property you are interested in
Second screen you detail the question you want to ask
With a list of popular questions to chose from (or freetype 
Find out if an ad sounds fake
Have propertywizza like functionality
https://www.linkedin.com/in/robin-keith-29291934
<EMAIL>


--
propertywizza seem to have given up but looks like they got some good feedback here:
https://www.housepricecrash.co.uk/forum/index.php?/topic/165361-property-wizza/


-The links used by property<PERSON><PERSON>
(also saved in About.md in pickingplaces backend


[{ "name": "School League<br/>Tables", "icon": "morter.png", "tip": "", "url": "http://www.education.gov.uk/cgi-bin/schools/performance/search.pl?postcode=%postcode+%&distance=1&phase=all&searchType=postcode", "run": "", "status": "", "rights": "" },
  { "name": "Ofsted Reports", "icon": "ofsted.png", "tip": "", "url": "http://www.ofsted.gov.uk/inspection-reports/find-inspection-report/results/any/any/any/any/any/any/any/%POSTCODE%/5/any/0/0#search2", "run": "", "status": "", "rights": "" },
  { "name": "Crime Statistics", "icon": "crime-xl.png", "url": "http://www.crime-statistics.co.uk/postcode/%POSTCODE%", "status": "", "rights": "" },
  { "name": "Sold House Prices<br/><small>by Zoopla</small>", "icon": "auction.png", "url": "http://www.houseprices.co.uk/e.php?q=%POSTCODE+%", "status": "", "rights": "http://www.houseprices.co.uk/contact-us/form/" },
  { "name": "Valuation Estimates<br/><small>by MousePrice</small>", "icon": "estimate.png", "tip": "You need to register with MousePrice to use this - it's free!", "url": "http://www.mouseprice.com/house-prices/%POSTCODE-%", "run": "form_search.submit();", "status": "cleared", "rights": "http://www.mouseprice.com/terms_conditions.aspx" }
]


[{ "name": "Maps & Streetview", "icon": "multimap.png", "url": "http://data.mapchannels.com/dualmaps3/map.htm?z=16&gm=2&ve=5&gc=0&bz=1&bd=0…svz=0&svm=2&svf=1&sve=1&sb=0&traffic=1&photo=1&wikipedia=1&addr=%POSTCODE%", "status": "contacted", "rights": "" },
  { "name": "Old Maps", "icon": "treasuremap.png", "tip": "Still in testing - type in the postcode and press GO", "url": "http://www.old-maps.co.uk/maps.html?txtPostCode=%POSTCODE%", "status": "contacted", "rights": "http://www.old-maps.co.uk/contact.html" },
  { "name": "Council Tax", "icon": "coins-xl.png", "tip": "View Council Tax charges for property in this postcode", "url": "http://www.mycounciltax.org.uk/results?postcode=%POSTCODE+%", "run": "", "status": "", "rights": "" },
  { "name": "Noise Map", "icon": "megaphone.png", "tip": "", "url": "http://www.scottishnoisemapping.org/public/view-map.aspx", "run": "", "status": "", "rights": "", "regions": "Scotland" },
  { "name": "Flood Risk", "icon": "fish.png", "tip": "plus: Air pollution, Aquifers, Bathing water quality, Historic Landfill, Pollution Incidents", "url": "http://maps.environment-agency.gov.uk/wiyby/wiybyController?value=%POSTCODE…%09&lang=_e&ep=map&topic=floodmap&layerGroups=default&scale=7&textonly=off", "status": "contacted", "rights": "" },
  { "name": "Pollution<br/>Reports", "icon": "pollution.png", "tip": "plus: Air pollution, Aquifers, Bathing water quality, Historic Landfill, Pollution Incidents", "url": "http://maps.environment-agency.gov.uk/wiyby/wiybyController?value=%POSTCODE…09&lang=_e&ep=map&topic=pollution&layerGroups=default&scale=7&textonly=off", "status": "contacted", "rights": "" },
  { "name": "Flood Risk, Scotland", "icon": "fish-2-xl.png", "tip": "", "url": "http://go.mappoint.net/sepa?value=%POSTCODE%", "textlink": true, "status": "contacted", "rights": "", "regions": "Scotland" },
  { "name": "Neighbourhood Statistics", "icon": "charts.png", "tip": "Health, employment, deprivation and other statistics", "url": "http://www.neighbourhood.statistics.gov.uk/dissemination/NeighbourhoodSumma…366&a=7&r=1&i=1001&m=0&enc=1&profileSearchText=%POSTCODE+%&searchProfiles=" },
  { "name": "Planning Applications", "icon": "planning.png", "tip": "Planning Alerts has closed, so we've switched to an alternative service - but no automatic lookup as yet.", "textlink": true, "url": "http://www.planningfinder.co.uk/search/near?postcode=%POSTCODE+%&radius=5", "status": "", "rights": "" },
  { "name": "Local Government", "icon": "crown.png", "url": "http://openlylocal.com/areas/search?postcode=%POSTCODE+%&submit=Search+by+postcode", "status": "", "rights": "" },
  { "name": "Local Pub Guide", "icon": "beer-glass.png", "tip": "The best local brews from Beer In The Evening", "url": "http://www.beerintheevening.com/pubs/results.shtml?l=%POSTCODE+%", "status": "contacted", "rights": "ok" },
  { "name": "Local Problems<br/>& Responses", "icon": "traffic.png", "tip": "Get a feel for the neighbourhood - reports of grafitti and minor repairs, and the council's promptness", "url": "http://www.fixmystreet.com/?pc=%POSTCODE%", "status": "contacted", "rights": "" },
  { "name": "Parking", "icon": "parking.png", "tip": "Short of parking space? Local parking, including private spaces for rent", "url": "http://en.parkopedia.co.uk/parking/%POSTCODE%/", "run": "", "status": "", "rights": "" },
  { "name": "Nearest Postbox", "icon": "postbox.png", "tip": "Dracos lists nearby post boxes", "url": "http://www.dracos.co.uk/play/locating-postboxes/nearest/?s=%POSTCODE+%", "status": "", "rights": "" },
  { "name": "Local Information", "icon": "info.png", "url": "http://www.scoutlocal.co.uk/postcode/%POSTCODENS%", "status": "", "rights": "" },
  { "name": "Affluence", "icon": "money-bag.png", "tip": "social classification, affluence and other financial indicators", "url": "http://www.checkmyarea.com/%POSTCODENS%.htm", "run": "", "status": "", "rights": "" },
  { "name": "Public Notices", "icon": "purchase-order-xl.png", "tip": "Public Notices including Bankruptcies in the area", "url": "http://www.thegazette.co.uk/all-notices/notice?text=%POSTCODE%", "status": "", "rights": "" },
  { "name": "Valuation Rolls", "icon": "estimate.png", "tip": "Rateable Values", "url": "http://www.saa.gov.uk/search.php?SEARCHED=1&SEARCH_TABLE=valuation_roll&SEARCH_TERM=%POSTCODE+%&x=12&y=11#results", "run": "", "status": "", "rights": "", "regions": "Scotland" },
  { "name": "Council Tax, Scotland", "icon": "coins-xl.png", "tip": "", "url": "http://www.saa.gov.uk/search.php?SEARCHED=1&SEARCH_TABLE=council_tax&SEARCH_TERM=%POSTCODE+%&x=12&y=5#results", "run": "", "status": "", "rights": "", "regions": "Scotland" },
  { "name": "Broadband<br/>Coverage", "icon": "notebook-xl.png", "tip": "View estimated broadband speeds from Think Broadband - Please wait while we lookup the postcode.", "url": "http://maps.thinkbroadband.com/?utm_source=propertywizza#!lat=%LAT%&lng=%LONG%&zoom=13&type=terrain&speed-cluster&estimated-speeds", "run": "", "status": "", "rights": "ok", "region": "" },
  { "name": "Doctors", "icon": "doctor.png", "tip": "View local NHS Doctors on NHSchoices.", "url": "http://beta.nhs.uk/gp-choice/searches/%POSTCODE%", "run": "", "status": "", "rights": "ok", "region": "" },
  { "name": "Dentists", "icon": "tooth.png", "tip": "View local NHS Dentists on NHSchoices.", "url": "http://www.nhs.uk/Scorecard/Pages/Results.aspx?OrgType=2&Coords=3051%2c6383…es=False&OriginalLookupType=1&ServiceLaunchFrom=&Filters=&TopLevelFilters=", "run": "", "status": "", "rights": "ok", "region": "" },
  { "name": "Local Housing<br/>Allowance Rates", "icon": "lha.png", "tip": "Local Housing Allowance rates", "url": "https://lha-direct.voa.gov.uk/SearchResults.aspx?Postcode=%%POSTCODE%%&LHACategory=999&Month=6&Year=2011&SearchPageParameters=true", "run": "", "status": "OK", "rights": "Gov" }
]
