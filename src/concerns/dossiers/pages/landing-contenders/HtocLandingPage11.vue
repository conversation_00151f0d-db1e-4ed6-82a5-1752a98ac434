<template>
  <q-page class="modern-landing-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-gradient"></div>
        <div class="hero-pattern"></div>
      </div>
      <div class="max-ctr">
        <div class="hero-content q-pa-xl">
          <div class="row items-center min-height-screen">
            <div class="col-12 col-md-6 q-pr-xl">
              <div class="hero-text">
                <div class="hero-badge q-mb-md">
                  <q-chip color="accent"
                          text-color="white"
                          icon="star"
                          class="q-px-md">
                    North Star Guided Search
                  </q-chip>
                </div>
                <h1 class="hero-title animate-slide-up">
                  Find Your Perfect Home with
                  <span class="text-accent">Focused Comparison</span>
                </h1>
                <p class="hero-subtitle animate-fade-in-delayed text-h6 q-mt-md q-mb-xl">
                  HomesToCompare empowers you to evaluate properties against your ideal "North Star" home, with tools
                  for notes, collaboration, and organized decision-making.
                </p>
                <div class="hero-cta-buttons q-gutter-md">
                  <q-btn label="Start Your Search"
                         color="accent"
                         size="lg"
                         unelevated
                         rounded
                         class="cta-primary q-px-xl"
                         @click="scrollToDemo">
                    <q-icon name="search"
                            class="q-ml-sm" />
                  </q-btn>
                  <q-btn label="Watch Demo"
                         color="white"
                         text-color="primary"
                         size="lg"
                         outline
                         rounded
                         class="cta-secondary q-px-xl"
                         @click="playDemo">
                    <q-icon name="play_arrow"
                            class="q-ml-sm" />
                  </q-btn>
                </div>
                <div class="hero-stats q-mt-xl">
                  <div class="row q-gutter-xl">
                    <div class="stat-item">
                      <div class="stat-number text-h4 text-weight-bold text-accent">10K+</div>
                      <div class="stat-label text-grey-6">Properties Compared</div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-number text-h4 text-weight-bold text-accent">95%</div>
                      <div class="stat-label text-grey-6">User Satisfaction</div>
                    </div>
                    <div class="stat-item">
                      <div class="stat-number text-h4 text-weight-bold text-accent">2.5x</div>
                      <div class="stat-label text-grey-6">Faster Decisions</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-12 col-md-6">
              <div class="hero-visual">
                <div class="property-card-demo animate-float">
                  <q-card class="demo-card shadow-10">
                    <q-img src="https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=400&h=250&fit=crop"
                           height="200px"
                           class="rounded-borders-top">
                      <div class="absolute-top-right q-ma-sm">
                        <q-chip color="green"
                                text-color="white"
                                dense>
                          <q-icon name="star"
                                  size="sm"
                                  class="q-mr-xs" />
                          North Star Property
                        </q-chip>
                      </div>
                    </q-img>
                    <q-card-section>
                      <div class="text-h6 text-weight-bold">Modern Family Home</div>
                      <div class="text-subtitle2 text-grey-6 q-mb-sm">Nuneaton, CV11</div>
                      <div class="row items-center q-gutter-sm">
                        <q-chip size="sm"
                                outline
                                color="primary">4 beds</q-chip>
                        <q-chip size="sm"
                                outline
                                color="primary">2 baths</q-chip>
                        <q-chip size="sm"
                                outline
                                color="primary">Garden</q-chip>
                      </div>
                      <div class="q-mt-md">
                        <div class="text-h5 text-weight-bold text-accent">£285,000</div>
                        <div class="text-caption text-green">↗ 12% above market avg</div>
                      </div>
                    </q-card-section>
                  </q-card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section q-py-xl bg-grey-1">
      <div class="max-ctr q-px-xl">
        <div class="text-center q-mb-xl">
          <h2 class="text-h3 text-weight-bold text-primary q-mb-md">
            Why Choose <span class="text-accent">HomesToCompare</span>?
          </h2>
          <p class="text-h6 text-grey-7 max-width-md mx-auto">
            Focus your home search with a North Star property and collaborate seamlessly to make confident decisions.
          </p>
        </div>

        <div class="row q-col-gutter-xl">
          <div class="col-12 col-md-4"
               v-for="feature in features"
               :key="feature.title">
            <q-card class="feature-card h-full hover-lift"
                    flat
                    bordered>
              <q-card-section class="text-center q-pa-xl">
                <div class="feature-icon q-mb-lg">
                  <q-avatar size="80px"
                            :color="feature.color"
                            text-color="white">
                    <q-icon :name="feature.icon"
                            size="40px" />
                  </q-avatar>
                </div>
                <h3 class="text-h5 text-weight-bold q-mb-md">{{ feature.title }}</h3>
                <p class="text-body1 text-grey-7">{{ feature.description }}</p>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </section>

    <!-- How It Works Section -->
    <section class="how-it-works-section q-py-xl bg-white">
      <div class="max-ctr q-px-xl">
        <div class="text-center q-mb-xl">
          <h2 class="text-h3 text-weight-bold text-primary q-mb-md">
            How It Works
          </h2>
          <p class="text-h6 text-grey-7 max-width-md mx-auto">
            Three simple steps to evaluate properties with clarity and collaboration
          </p>
        </div>

        <div class="row q-col-gutter-xl items-stretch">
          <div class="col-12 col-md-4"
               v-for="(step, index) in howItWorksSteps"
               :key="step.title">
            <div class="step-card text-center q-pa-lg">
              <div class="step-number-badge q-mb-lg">
                <q-avatar size="60px"
                          color="accent"
                          text-color="white"
                          class="text-h4 text-weight-bold">
                  {{ index + 1 }}
                </q-avatar>
              </div>
              <div class="step-icon q-mb-md">
                <q-icon :name="step.icon"
                        size="48px"
                        :color="step.color" />
              </div>
              <h3 class="text-h5 text-weight-bold q-mb-md">{{ step.title }}</h3>
              <p class="text-body1 text-grey-7">{{ step.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Social Proof Section -->
    <section class="social-proof-section q-py-xl bg-grey-1">
      <div class="max-ctr q-px-xl">
        <div class="text-center q-mb-xl">
          <h2 class="text-h3 text-weight-bold text-primary q-mb-md">
            Trusted by Home Buyers
          </h2>
          <p class="text-h6 text-grey-7">
            Join thousands who've streamlined their home-buying decisions
          </p>
        </div>

        <div class="row q-col-gutter-lg">
          <div class="col-12 col-md-4"
               v-for="testimonial in testimonials"
               :key="testimonial.name">
            <q-card class="testimonial-card h-full"
                    flat
                    bordered>
              <q-card-section class="q-pa-lg">
                <div class="row items-center q-mb-md">
                  <q-rating v-model="testimonial.rating"
                            readonly
                            color="yellow-8"
                            size="sm" />
                </div>
                <p class="text-body1 q-mb-md">"{{ testimonial.quote }}"</p>
                <div class="row items-center">
                  <q-avatar size="40px"
                            color="primary"
                            text-color="white">
                    {{ testimonial.name.charAt(0) }}
                  </q-avatar>
                  <div class="q-ml-md">
                    <div class="text-weight-bold">{{ testimonial.name }}</div>
                    <div class="text-caption text-grey-6">{{ testimonial.role }}</div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </section>

    <!-- Benefits Section -->
    <section class="benefits-section q-py-xl bg-primary text-white">
      <div class="max-ctr q-px-xl">
        <div class="row items-center">
          <div class="col-12 col-md-6 q-pr-xl">
            <h2 class="text-h3 text-weight-bold q-mb-md">
              Make Confident Decisions with Clarity
            </h2>
            <p class="text-h6 q-mb-lg">
              Define your North Star property and use our tools to evaluate, collaborate, and organize your home-buying
              journey with ease.
            </p>
            <div class="benefits-list">
              <div class="benefit-item q-mb-md"
                   v-for="benefit in keyBenefits"
                   :key="benefit">
                <q-icon name="check_circle"
                        color="accent"
                        size="sm"
                        class="q-mr-sm" />
                <span class="text-body1">{{ benefit }}</span>
              </div>
            </div>
            <q-btn label="Get Started Today"
                   color="accent"
                   size="lg"
                   unelevated
                   rounded
                   class="q-mt-lg q-px-xl" />
          </div>
          <div class="col-12 col-md-6">
            <div class="benefits-visual">
              <q-img src="https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=500&h=400&fit=crop"
                     class="rounded-borders shadow-5"
                     style="max-width: 100%" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section q-py-xl bg-grey-1">
      <div class="max-ctr q-px-xl">
        <div class="text-center q-mb-xl">
          <h2 class="text-h3 text-weight-bold text-primary q-mb-md">
            Frequently Asked Questions
          </h2>
          <p class="text-h6 text-grey-7">
            Everything you need to know about HomesToCompare
          </p>
        </div>
        <div class="row justify-center">
          <div class="col-12 col-md-8">
            <q-list class="faq-list">
              <q-expansion-item v-for="(faq, index) in faqs"
                                :key="index"
                                :label="faq.question"
                                header-class="text-h6 text-weight-medium"
                                expand-icon-class="text-accent"
                                class="faq-item q-mb-md">
                <template v-slot:header>
                  <q-item-section>
                    <q-item-label class="text-h6 text-weight-medium">
                      {{ faq.question }}
                    </q-item-label>
                  </q-item-section>
                </template>
                <q-card flat
                        class="bg-white">
                  <q-card-section class="q-pa-lg">
                    <p class="text-body1 text-grey-8">{{ faq.answer }}</p>
                  </q-card-section>
                </q-card>
              </q-expansion-item>
            </q-list>
          </div>
        </div>
      </div>
    </section>

    <!-- Final CTA Section -->
    <section class="final-cta-section q-py-xl bg-gradient-modern text-white">
      <div class="max-ctr q-px-xl">
        <div class="text-center">
          <h2 class="text-h3 text-weight-bold q-mb-md">
            Ready to Find Your Dream Home?
          </h2>
          <p class="text-h5 q-mb-xl max-width-md mx-auto">
            Start evaluating properties with your North Star in focus. Collaborate, organize, and make confident
            decisions
            today.
          </p>
          <div class="cta-buttons q-gutter-md">
            <q-btn label="Start Free Trial"
                   color="white"
                   text-color="primary"
                   size="xl"
                   unelevated
                   rounded
                   class="q-px-xl q-py-md">
              <q-icon name="rocket_launch"
                      class="q-ml-sm" />
            </q-btn>
            <q-btn label="Schedule Demo"
                   color="transparent"
                   text-color="white"
                   size="xl"
                   outline
                   rounded
                   class="q-px-xl q-py-md">
              <q-icon name="calendar_today"
                      class="q-ml-sm" />
            </q-btn>
          </div>
          <div class="cta-features q-mt-xl">
            <div class="row justify-center q-gutter-lg">
              <div class="feature-badge">
                <q-icon name="check"
                        color="accent"
                        size="sm"
                        class="q-mr-xs" />
                <span class="text-body2">No credit card required</span>
              </div>
              <div class="feature-badge">
                <q-icon name="check"
                        color="accent"
                        size="sm"
                        class="q-mr-xs" />
                <span class="text-body2">14-day free trial</span>
              </div>
              <div class="feature-badge">
                <q-icon name="check"
                        color="accent"
                        size="sm"
                        class="q-mr-xs" />
                <span class="text-body2">Cancel anytime</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </q-page>
</template>

<script>
export default {
  name: 'ModernLandingPage',
  data() {
    return {
      features: [
        {
          title: 'North Star Comparison',
          description: 'Define your ideal "North Star" property to focus your evaluation, making it easier to compare homes against what matters most to you.',
          icon: 'star',
          color: 'accent'
        },
        {
          title: 'Collaborative Tools',
          description: 'Share your evaluation space with others using an access code. Add notes, comment on photos, and rate properties together without needing accounts.',
          icon: 'group',
          color: 'primary'
        },
        {
          title: 'Organized Decision-Making',
          description: 'Create tasks and add events to a shared calendar to keep your home-buying process on track and aligned with your team.',
          icon: 'event',
          color: 'secondary'
        }
      ],
      howItWorksSteps: [
        {
          title: 'Set Your North Star',
          description: 'Choose your ideal property as a reference point to guide your evaluations and keep your search focused.',
          icon: 'star',
          color: 'primary'
        },
        {
          title: 'Evaluate with Insights',
          description: 'Use AI-generated property profiles from photos, descriptions, and market data to inform your personalized evaluations.',
          icon: 'description',
          color: 'accent'
        },
        {
          title: 'Collaborate & Decide',
          description: 'Take notes, rate properties, and share your space with collaborators to make confident, informed decisions together.',
          icon: 'group_add',
          color: 'secondary'
        }
      ],
      testimonials: [
        {
          name: 'Sarah Johnson',
          role: 'First-time Buyer',
          quote: 'HomesToCompare made it so easy to stay focused on what I wanted in a home. The North Star concept and collaboration tools were game-changers!',
          rating: 5
        },
        {
          name: 'Michael Chen',
          role: 'Home Buyer',
          quote: 'Being able to share notes and rate properties with my partner without needing accounts was seamless and helped us decide faster.',
          rating: 5
        },
        {
          name: 'Emma Williams',
          role: 'Real Estate Agent',
          quote: 'My clients love the shared calendar and task features. It keeps everyone aligned and makes the process so much smoother.',
          rating: 5
        }
      ],
      keyBenefits: [
        'Focus your search with a North Star property',
        'Collaborate effortlessly with shared notes and ratings',
        'Stay organized with tasks and a shared calendar',
        'Use AI insights to inform your evaluations',
        'Make confident decisions with your team'
      ],
      faqs: [
        {
          question: 'What is HomesToCompare?',
          answer: 'HomesToCompare is a platform that helps you evaluate properties against your ideal "North Star" home. It provides AI-generated property profiles and tools for note-taking, collaboration, rating, and organizing tasks and events.'
        },
        {
          question: 'What is a North Star property?',
          answer: 'A North Star property is your ideal home that serves as a reference point for evaluating other properties, helping you stay focused on what matters most to you.'
        },
        {
          question: 'Do collaborators need an account?',
          answer: 'No, collaborators can join your evaluation space using a secure access code without needing to register or create an account.'
        },
        {
          question: 'Is there a free trial?',
          answer: 'Yes! We offer a 14-day free trial with full access to all features. No credit card required to get started.'
        },
        {
          question: 'How does the AI assist in the process?',
          answer: 'Our AI analyzes photos, descriptions, and market data to create detailed property profiles, which you can use to inform your evaluations and comparisons.'
        }
      ]
    }
  },
  methods: {
    scrollToDemo() {
      this.$q.scroll.setScrollPosition(window, 800, 500)
    },
    playDemo() {
      this.$q.notify({
        message: 'Demo coming soon!',
        color: 'accent',
        position: 'top'
      })
    }
  }
}
</script>

<style scoped>
/* Modern Landing Page Styles */
.modern-landing-page {
  overflow-x: hidden;
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.hero-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1f3393 0%, #9c27b0 50%, #26a69a 100%);
  opacity: 0.95;
}

.hero-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
  background-size: 60px 60px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  color: white;
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  line-height: 1.6;
}

.min-height-screen {
  min-height: 100vh;
}

/* Animations */
.animate-slide-up {
  animation: slideUp 0.8s ease-out forwards;
  opacity: 0;
}

.animate-fade-in-delayed {
  animation: fadeInDelayed 1s ease-out 0.3s forwards;
  opacity: 0;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDelayed {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-20px);
  }
}

/* CTA Buttons */
.cta-primary {
  box-shadow: 0 8px 25px rgba(156, 39, 176, 0.3);
  transition: all 0.3s ease;
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(156, 39, 176, 0.4);
}

.cta-secondary {
  transition: all 0.3s ease;
}

.cta-secondary:hover {
  transform: translateY(-2px);
  background-color: rgba(255, 255, 255, 0.1);
}

/* Demo Card */
.demo-card {
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
  transition: all 0.3s ease;
}

.demo-card:hover {
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

/* Feature Cards */
.feature-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(31, 51, 147, 0.1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Testimonial Cards */
.testimonial-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(31, 51, 147, 0.1);
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* FAQ Section */
.faq-item {
  background: white;
  border-radius: 12px;
  border: 1px solid rgba(31, 51, 147, 0.1);
  transition: all 0.3s ease;
}

.faq-item:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Final CTA */
.bg-gradient-modern {
  background: linear-gradient(135deg, #1f3393 0%, #9c27b0 50%, #26a69a 100%);
}

.feature-badge {
  display: flex;
  align-items: center;
  opacity: 0.9;
}

/* Utility Classes */
.max-width-md {
  max-width: 600px;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.h-full {
  height: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .min-height-screen {
    min-height: auto;
    padding: 4rem 0;
  }

  .demo-card {
    transform: none;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .cta-buttons .q-btn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .stat-item {
    text-align: center;
  }

  .feature-badge {
    justify-content: center;
  }
}
</style>