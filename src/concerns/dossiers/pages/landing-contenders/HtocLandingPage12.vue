<template>
  <q-page class="modern-landing-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-background">
        <div class="hero-gradient"></div>
        <div class="hero-pattern"></div>
      </div>
      <div class="max-ctr">
        <div class="hero-content q-pa-xl">
          <div class="row items-center min-height-screen">
            <div class="col-12 col-md-6 q-pr-xl q-pr-md-mobile">
              <div class="hero-text">
                <div class="hero-badge q-mb-md">
                  <q-chip color="accent"
                          text-color="white"
                          icon="star"
                          class="q-px-md">
                    Your North Star for Home Buying
                  </q-chip>
                </div>
                <h1 class="hero-title animate-slide-up"
                    aria-label="Navigate Your Home Search with a Clear North Star">
                  Navigate Your Home Search with a Clear <span class="text-accent">North Star</span>
                </h1>
                <p class="hero-subtitle animate-fade-in-delayed text-h6 q-mt-md q-mb-xl">
                  Define your ideal property – your 'North Star' – and let HomesToCompare guide your evaluation of every
                  option. AI assists with initial insights, you and your collaborators make the informed choice.
                </p>
                <div class="hero-cta-buttons q-gutter-md">
                  <q-btn label="Define Your North Star"
                         color="accent"
                         size="lg"
                         unelevated
                         rounded
                         class="cta-primary q-px-xl"
                         aria-label="Define Your North Star and Explore"
                         @click="scrollToSection('how-it-works-section')">
                    <q-icon name="explore"
                            class="q-ml-sm" />
                  </q-btn>
                  <q-btn label="Learn More"
                         color="white"
                         text-color="primary"
                         size="lg"
                         outline
                         rounded
                         class="cta-secondary q-px-xl"
                         aria-label="Learn More About HomesToCompare"
                         @click="playDemo">
                    <q-icon name="info"
                            class="q-ml-sm" />
                  </q-btn>
                </div>
              </div>
            </div>
            <div class="col-12 col-md-6">
              <div class="hero-visual">
                <div class="property-card-demo animate-float">
                  <q-card class="demo-card shadow-10">
                    <q-img src="/assets/images/sample-home.jpg"
                           height="200px"
                           class="rounded-borders-top"
                           alt="Charming Family Home in Anysville" />
                    <q-card-section>
                      <div class="text-h6 text-weight-bold">Charming Family Home</div>
                      <div class="text-subtitle2 text-grey-6 q-mb-sm">Anysville, AV1 2BC</div>
                      <div class="row items-center q-gutter-sm">
                        <q-chip size="sm"
                                outline
                                color="primary">3 beds</q-chip>
                        <q-chip size="sm"
                                outline
                                color="primary">2 baths</q-chip>
                        <q-chip size="sm"
                                outline
                                color="primary">Large Garden</q-chip>
                      </div>
                      <div class="q-mt-md">
                        <div class="text-h5 text-weight-bold text-accent">£310,000</div>
                        <div class="text-caption text-grey-7">Notes: 20+ | Collaborators: 3</div>
                      </div>
                    </q-card-section>
                  </q-card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section q-py-xl bg-grey-1">
      <div class="max-ctr q-px-xl">
        <div class="text-center q-mb-xl">
          <h2 class="text-h3 text-weight-bold text-primary q-mb-md">
            Focus Your Hunt with <span class="text-accent">HomesToCompare</span>
          </h2>
          <p class="text-h6 text-grey-7 max-width-md mx-auto">
            Empowering your property search with clear benchmarks, collaborative tools, and organized evaluation.
          </p>
        </div>

        <div class="row q-col-gutter-xl">
          <div class="col-12 col-md-4"
               v-for="feature in features"
               :key="feature.title">
            <q-card class="feature-card h-full hover-lift"
                    flat
                    bordered>
              <q-card-section class="text-center q-pa-xl">
                <div class="feature-icon q-mb-lg">
                  <q-avatar size="80px"
                            :color="feature.color"
                            text-color="white">
                    <q-icon :name="feature.icon"
                            size="40px" />
                  </q-avatar>
                </div>
                <h3 class="text-h5 text-weight-bold q-mb-md">{{ feature.title }}</h3>
                <p class="text-body1 text-grey-7">{{ feature.description }}</p>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </section>

    <!-- How It Works Section -->
    <section class="how-it-works-section q-py-xl bg-white">
      <div class="max-ctr q-px-xl">
        <div class="text-center q-mb-xl">
          <h2 class="text-h3 text-weight-bold text-primary q-mb-md">
            Your Path to the Perfect Home
          </h2>
          <p class="text-h6 text-grey-7 max-width-md mx-auto">
            Three simple steps to a focused and collaborative home evaluation.
          </p>
        </div>

        <div class="row q-col-gutter-xl items-stretch">
          <div class="col-12 col-md-4"
               v-for="(step, index) in howItWorksSteps"
               :key="step.title">
            <div class="step-card text-center q-pa-lg">
              <div class="step-number-badge q-mb-lg">
                <q-avatar size="60px"
                          color="accent"
                          text-color="white"
                          class="text-h4 text-weight-bold">
                  {{ index + 1 }}
                </q-avatar>
              </div>
              <div class="step-icon q-mb-md">
                <q-icon :name="step.icon"
                        size="48px"
                        :color="step.color" />
              </div>
              <h3 class="text-h5 text-weight-bold q-mb-md">{{ step.title }}</h3>
              <p class="text-body1 text-grey-7">{{ step.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Social Proof Section -->
    <section class="social-proof-section q-py-xl bg-grey-1">
      <div class="max-ctr q-px-xl">
        <div class="text-center q-mb-xl">
          <h2 class="text-h3 text-weight-bold text-primary q-mb-md">
            Trusted by Savvy Home Buyers
          </h2>
          <p class="text-h6 text-grey-7">
            See how HomesToCompare simplifies complex decisions.
          </p>
        </div>

        <div class="row q-col-gutter-lg">
          <div class="col-12 col-md-4"
               v-for="testimonial in testimonials"
               :key="testimonial.name">
            <q-card class="testimonial-card h-full"
                    flat
                    bordered>
              <q-card-section class="q-pa-lg">
                <div class="row items-center q-mb-md">
                  <q-rating :model-value="testimonial.rating"
                            readonly
                            color="yellow-8"
                            size="sm" />
                </div>
                <p class="text-body1 q-mb-md">"{{ testimonial.quote }}"</p>
                <div class="row items-center">
                  <q-avatar size="40px"
                            color="primary"
                            text-color="white">
                    {{ testimonial.name.charAt(0) }}
                  </q-avatar>
                  <div class="q-ml-md">
                    <div class="text-weight-bold">{{ testimonial.name }}</div>
                    <div class="text-caption text-grey-6">{{ testimonial.role }}</div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </section>

    <!-- Benefits Section -->
    <section class="benefits-section q-py-xl bg-primary text-white">
      <div class="max-ctr q-px-xl">
        <div class="row items-center">
          <div class="col-12 col-md-6 q-pr-xl q-pr-md-mobile">
            <h2 class="text-h3 text-weight-bold q-mb-md">
              Make Clearer Decisions, Together
            </h2>
            <p class="text-h6 q-mb-lg">
              Stop sifting aimlessly. Focus your search with a 'North Star' property, leverage AI for initial data, and
              use collaborative tools to gain comprehensive perspectives.
            </p>
            <div class="benefits-list">
              <div class="benefit-item q-mb-md"
                   v-for="benefit in keyBenefits"
                   :key="benefit">
                <q-icon name="check_circle"
                        color="accent"
                        size="sm"
                        class="q-mr-sm" />
                <span class="text-body1">{{ benefit }}</span>
              </div>
            </div>
            <q-btn label="Start Comparing Today"
                   color="accent"
                   size="lg"
                   unelevated
                   rounded
                   class="q-mt-lg q-px-xl"
                   aria-label="Start Comparing Homes Today"
                   @click="scrollToSection('final-cta-section')" />
          </div>
          <div class="col-12 col-md-6">
            <div class="benefits-visual">
              <q-img src="/assets/images/collaboration.jpg"
                     class="rounded-borders shadow-5"
                     style="max-width: 100%"
                     alt="Team collaborating on home selection" />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section q-py-xl bg-grey-1">
      <div class="max-ctr q-px-xl">
        <div class="text-center q-mb-xl">
          <h2 class="text-h3 text-weight-bold text-primary q-mb-md">
            Frequently Asked Questions
          </h2>
          <p class="text-h6 text-grey-7">
            Everything you need to know about HomesToCompare
          </p>
        </div>
        <div class="row justify-center">
          <div class="col-12 col-md-8">
            <q-list class="faq-list">
              <q-expansion-item v-for="(faq, index) in faqs"
                                :key="index"
                                :label="faq.question"
                                header-class="text-h6 text-weight-medium"
                                expand-icon-class="text-accent"
                                class="faq-item q-mb-md"
                                :aria-expanded="null"
                                role="region"
                                :aria-labelledby="'faq-header-' + index">
                <template v-slot:header>
                  <q-item-section>
                    <q-item-label :id="'faq-header-' + index"
                                  class="text-h6 text-weight-medium">
                      {{ faq.question }}
                    </q-item-label>
                  </q-item-section>
                </template>
                <q-card flat
                        class="bg-white">
                  <q-card-section class="q-pa-lg">
                    <p class="text-body1 text-grey-8">{{ faq.answer }}</p>
                  </q-card-section>
                </q-card>
              </q-expansion-item>
            </q-list>
          </div>
        </div>
      </div>
    </section>

    <!-- Final CTA Section -->
    <section class="final-cta-section q-py-xl bg-gradient-modern text-white">
      <div class="max-ctr q-px-xl">
        <div class="text-center">
          <h2 class="text-h3 text-weight-bold q-mb-md">
            Ready to Find Your Perfect Home with Clarity?
          </h2>
          <p class="text-h5 q-mb-xl max-width-md mx-auto">
            Define your 'North Star,' collaborate effectively, and make your best home-buying decision.
            Start your focused journey today.
          </p>
          <div class="cta-buttons q-gutter-md">
            <q-btn label="Start Free Trial"
                   color="white"
                   text-color="primary"
                   size="xl"
                   unelevated
                   rounded
                   class="q-px-xl q-py-md"
                   aria-label="Start Free Trial of HomesToCompare">
              <q-icon name="rocket_launch"
                      class="q-ml-sm" />
            </q-btn>
            <q-btn label="See a Demo"
                   color="transparent"
                   text-color="white"
                   size="xl"
                   outline
                   rounded
                   class="q-px-xl q-py-md"
                   aria-label="Watch HomesToCompare Demo"
                   @click="playDemo">
              <q-icon name="ondemand_video"
                      class="q-ml-sm" />
            </q-btn>
          </div>
          <div class="cta-features q-mt-xl">
            <div class="row justify-center q-gutter-lg">
              <div class="feature-badge">
                <q-icon name="check"
                        color="accent"
                        size="sm"
                        class="q-mr-xs" />
                <span class="text-body2">No credit card required</span>
              </div>
              <div class="feature-badge">
                <q-icon name="check"
                        color="accent"
                        size="sm"
                        class="q-mr-xs" />
                <span class="text-body2">14-day free trial</span>
              </div>
              <div class="feature-badge">
                <q-icon name="check"
                        color="accent"
                        size="sm"
                        class="q-mr-xs" />
                <span class="text-body2">Cancel anytime</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </q-page>
</template>

<script>
export default {
  name: 'ModernLandingPage',
  data() {
    return {
      features: [
        {
          title: "Your 'North Star' Benchmark",
          description: "Define your ideal property criteria. This 'North Star' becomes the unwavering standard against which all potential homes are measured, ensuring a focused search.",
          icon: 'star_outline',
          color: 'accent'
        },
        {
          title: 'Collaborative Workspace',
          description: "Share properties with family, friends, or agents using a simple access code—no sign-ups needed for them! Gather diverse feedback with attributed notes, photo-specific comments, and ratings.",
          icon: 'groups',
          color: 'primary'
        },
        {
          title: 'AI-Assisted Detail Gathering',
          description: "Our AI extracts key details from photos, descriptions, and market data to build comprehensive property profiles, giving you a head start on your evaluation.",
          icon: 'data_object',
          color: 'secondary'
        }
      ],
      howItWorksSteps: [
        {
          title: "Define Your 'North Star'",
          description: "Detail your perfect home – location, size, features, budget. This becomes your clear benchmark for every comparison.",
          icon: 'flag',
          color: 'primary'
        },
        {
          title: 'Gather & Profile Homes',
          description: "Add properties you're considering. Our AI assists by extracting information from listings to create detailed profiles, laying the groundwork for your analysis.",
          icon: 'add_home_work',
          color: 'accent'
        },
        {
          title: 'Evaluate & Collaborate',
          description: "In your dedicated workspace, take notes, comment on photos, assign ratings, create tasks, and add events to a shared calendar. Invite others to contribute for a well-rounded decision.",
          icon: 'checklist_rtl',
          color: 'secondary'
        }
      ],
      testimonials: [
        {
          name: 'Alex Miller',
          role: 'Home Buyer',
          quote: "The 'North Star' concept completely changed how we searched. Instead of getting overwhelmed, we had a clear focus, and collaborating with my partner was so easy!",
          rating: 5
        },
        {
          name: 'Priya Singh',
          role: 'Relocating Professional',
          quote: "Being able to share access with my family overseas to get their input on photos and features, without them needing an account, was a game-changer.",
          rating: 5
        },
        {
          name: 'Tom Davis',
          role: 'Second-time Buyer',
          quote: "HomesToCompare helped us organize our thoughts and track everything in one place. The AI got the basic data, and we did the real comparing – perfect combo.",
          rating: 4
        }
      ],
      keyBenefits: [
        "Establish a clear 'North Star' to guide your choices.",
        "Collaborate seamlessly with anyone via a simple access code.",
        "Centralize notes, photo-specific comments, and property ratings.",
        "AI extracts key details, saving you initial groundwork.",
        "Organize your search with tasks and a shared calendar.",
        "Make more confident, focused, and well-informed decisions."
      ],
      faqs: [
        {
          question: 'What is HomesToCompare?',
          answer: "HomesToCompare helps you make the best home-buying decision. You define your 'North Star' (ideal property) and use our platform to systematically evaluate comparable homes. AI assists by extracting initial details, while you and your collaborators add notes, comments, and ratings in a shared, organized workspace."
        },
        {
          question: "What is a 'North Star' property?",
          answer: "Your 'North Star' is a benchmark property (either a real listing or an ideal composite of features) that perfectly represents what you're looking for. By comparing potential homes against this clear standard, you can make more focused, objective, and confident decisions."
        },
        {
          question: 'How does collaboration work?',
          answer: "You can share a unique access code for a property workspace with friends, family, or your agent. They can join without needing to register or provide an email. They can then view property details, add their own notes and comments (which are attributed to them), and help rate different aspects of the home."
        },
        {
          question: "What role does AI play?",
          answer: "Our AI helps by processing property listings to extract key information from photos, descriptions, and available market data. This builds a comprehensive initial profile for each property, saving you time on data gathering so you can focus on evaluation and comparison."
        },
        {
          question: 'Can I track tasks and important dates?',
          answer: "Yes! Within each property's workspace, you can create tasks (e.g., 'Schedule a viewing,' 'Check school ratings') and add important events or deadlines to a shared calendar, helping everyone stay organized and on the same page."
        },
        {
          question: 'Is there a free trial?',
          answer: 'Absolutely! We offer a 14-day free trial with full access to all features. No credit card is required to get started.'
        }
      ]
    }
  },
  methods: {
    scrollToSection(sectionClass) {
      const element = document.querySelector(`.${sectionClass}`);
      if (element) {
        const headerOffset = 60; // Adjust based on your header height
        const elementPosition = element.getBoundingClientRect().top + window.scrollY;
        window.scrollTo({
          top: elementPosition - headerOffset,
          behavior: 'smooth'
        });
      } else {
        this.$q.notify({
          message: `Section ${sectionClass} not found`,
          color: 'warning',
          position: 'top'
        });
      }
    },
    async playDemo() {
      // Placeholder for demo modal or redirect
      try {
        // Example: Open a modal with a video
        this.$q.dialog({
          title: 'HomesToCompare Demo',
          message: 'Watch our interactive demo to see HomesToCompare in action!',
          html: true,
          options: {
            type: 'video',
            source: '/assets/videos/demo.mp4' // Replace with actual video path
          },
          cancel: true,
          persistent: true
        }).onOk(() => {
          // Handle OK action if needed
        });
      } catch (error) {
        this.$q.notify({
          message: 'Error loading demo. Please try again later.',
          color: 'negative',
          position: 'top'
        });
      }
    }
  }
}
</script>

<style scoped>
/* Modern Landing Page Styles */
.modern-landing-page {
  overflow-x: hidden;
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  background: linear-gradient(135deg, #1f3393 0%, #9c27b0 50%, #26a69a 100%),
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 2px, transparent 2px);
  background-size: 100% 100%, 60px 60px, 60px 60px;
  opacity: 0.95;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.1;
  color: white;
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 400;
  line-height: 1.6;
}

.min-height-screen {
  min-height: 100vh;
}

/* Animations */
.animate-slide-up {
  animation: slideUp 0.8s ease-out forwards;
  opacity: 0;
}

.animate-fade-in-delayed {
  animation: fadeInDelayed 1s ease-out 0.3s forwards;
  opacity: 0;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDelayed {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
    /* Reduced amplitude for better performance */
  }
}

/* CTA Buttons */
.cta-primary {
  box-shadow: 0 8px 25px rgba(156, 39, 176, 0.3);
  transition: all 0.3s ease;
}

.cta-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(156, 39, 176, 0.4);
}

.cta-secondary {
  transition: all 0.3s ease;
}

.cta-secondary:hover {
  transform: translateY(-2px);
  background-color: rgba(255, 255, 255, 0.1);
}

/* Demo Card */
.demo-card {
  transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
  transition: all 0.3s ease;
}

.demo-card:hover {
  transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
}

/* Feature Cards */
.feature-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(31, 51, 147, 0.1);
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Testimonial Cards */
.testimonial-card {
  transition: all 0.3s ease;
  border: 1px solid rgba(31, 51, 147, 0.1);
}

.testimonial-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* FAQ Section */
.faq-item {
  background: white;
  border-radius: 12px;
  border: 1px solid rgba(31, 51, 147, 0.1);
  transition: all 0.3s ease;
}

.faq-item:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Final CTA */
.bg-gradient-modern {
  background: linear-gradient(135deg, #1f3393 0%, #9c27b0 50%, #26a69a 100%);
}

.feature-badge {
  display: flex;
  align-items: center;
  opacity: 0.9;
}

/* Utility Classes */
.max-width-md {
  max-width: 700px;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.h-full {
  height: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .min-height-screen {
    min-height: auto;
    padding: 4rem 0;
  }

  .hero-section .row.items-center {
    flex-direction: column-reverse;
  }

  .hero-section .col-md-6 {
    text-align: center;
  }

  .hero-section .q-pr-md-mobile {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem;
  }

  .hero-visual {
    margin-top: 2rem;
  }

  .demo-card {
    transform: none;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .hero-cta-buttons .q-btn,
  .final-cta-section .cta-buttons .q-btn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 2rem;
  }

  .feature-badge {
    justify-content: center;
  }
}
</style>