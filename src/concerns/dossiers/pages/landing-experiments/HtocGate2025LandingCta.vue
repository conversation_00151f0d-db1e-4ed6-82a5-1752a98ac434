<template>
  <div class="HtocGateLandingCta col-xs-12 no-flashing-caret">
    <div class="q-mx-md intro-cont q-mt-md">
      <div class="mobile-only">
        <div class="row max-ctr q-pb-lg">
          <div class="col-md-6 col-lg-6 col-xs-12 col-sm-12">
            <div class="hh-hero-content-inner q-mt-xs q-pt-sm">
              <h1 class="hero-title hh-hero-title"
                  style="font-size: 42px; margin-bottom: 18px; margin-top: 8px">
                <span style="font-size: 48px"
                      class="color-bold-blue">House Hunting
                </span>
                <br />
                <span class="color-first">Dossiers</span>
              </h1>
              <p class="h2 hh-hero-content header-black-text"
                 style="font-size: 22px">
                Finding the perfect home is hard. Our insightful dossiers make it much
                easier.
              </p>
              <div class="q-pt-md q-mt-sm">
                <q-btn no-caps
                       style="border-radius: 90px; background: rgb(10, 0, 131); color: white"
                       class="rounded-borders"
                       @click="letsGetStartedForm"
                       padding="md"
                       size="lg">
                  <div class="row items-center no-wrap">
                    <q-icon size="sm"
                            left
                            name="thumb_up" />
                    <div class="text-center">Let's get started</div>
                  </div>
                </q-btn>
              </div>
              <div class="q-mt-md q-pt-md">
                <q-list padding
                        class="justify-center flex items-center"
                        style="max-width: 100vw">
                  <q-item clickable
                          v-ripple>
                    <q-item-section side
                                    style="width: 60px"
                                    class="flex items-center">
                      <q-icon name="done"
                              color="green" />
                    </q-item-section>

                    <q-item-section>
                      <q-item-label class="text-h6"
                                    lines="1"
                                    style="width: 280px; text-align: left">Keep track of properties</q-item-label>
                    </q-item-section>
                  </q-item>
                  <q-item clickable
                          v-ripple>
                    <q-item-section side
                                    style="width: 60px"
                                    class="flex items-center">
                      <q-icon name="done"
                              color="green" />
                    </q-item-section>

                    <q-item-section>
                      <q-item-label class="text-h6"
                                    lines="1"
                                    style="width: 280px; text-align: left">Get feedback from friends</q-item-label>
                    </q-item-section>
                  </q-item>
                  <q-item clickable
                          v-ripple>
                    <q-item-section side
                                    style="width: 60px"
                                    class="flex items-center">
                      <q-icon name="done"
                              color="green" />
                    </q-item-section>

                    <q-item-section>
                      <q-item-label class="text-h6"
                                    lines="1"
                                    style="width: 280px; text-align: left">Get help from our AI</q-item-label>
                    </q-item-section>
                  </q-item>
                </q-list>
              </div>
              <!--  -->
            </div>
          </div>
          <!-- <div class="mob-demo-iframe-ctr col-md-6 col-lg-6 col-xs-12 col-sm-12 q-pb-sm">
            <div
              class="q-pl-md q-pb-sm q-pt-lg text-h3 text-left header-black-text text-bold"
              style="font-size: 34px; color: rgb(10, 0, 131)"
            >
              Check out our demo:
            </div>
            <div
              class="demo-iframe-ctr q-mb-sm rounded-borders q-ma-none q-pb-sm"
              style="border: 2px solid gray"
            >
              <iframe
                width="100%"
                height="550"
                frameborder="0"
                src="https://demo.homestocompare.com/"
                allowfullscreen="allowfullscreen"
                style="border: 0px"
              >
              </iframe>
            </div>
            <div
              class="q-pl-md q-py-none q-my-none text-h3 text-center text-bold"
              style="font-size: 18px; color: rgb(10, 0, 131)"
            >
              <a
                class="color-bold-blue"
                href="https://demo.homestocompare.com/"
                target="_blank"
              >
                https://demo.homestocompare.com
              </a>
            </div>
          </div> -->
        </div>
      </div>
      <div class="mobile-hide">
        <div class="row max-ctr q-pb-lg">
          <div class="col-sm-12">
            <div class="q-pb-xl q-mt-none q-pt-lg q-mr-none">
              <h1 class="hero-title hh-hero-title q-mb-none"
                  style="margin-top: 0px; font-size: 68px">
                <span style="color: rgb(10, 0, 131)"
                      class="hhwyf">House Hunting </span>
                <span class="color-second"> Dossiers</span>
              </h1>
            </div>
          </div>
          <div class="col-xs-12 col-sm-12">
            <div class="hh-hero-content-inner q-mt-none q-pt-lg q-mr-md">
              <!-- -->
              <div class="h2 hh-hero-content q-mt-none q-pt-none">
                <div class="text-left q-px-lg q-ml-lg"
                     style="font-size: 24px; color: rgb(10, 0, 131)">
                  Finding the perfect home is hard. Our insightful dossiers make it much
                  easier.
                </div>
                <div class="q-mt-xl q-pt-xl flex items-center justify-center">
                  <q-list padding
                          class="rounded-borders"
                          style="max-width: max-content">
                    <q-item clickable
                            v-ripple>
                      <q-item-section side>
                        <q-icon name="done"
                                color="green" />
                      </q-item-section>

                      <q-item-section>
                        <q-item-label class="text-h6 text-weight-bolder"
                                      lines="1">Keep track of properties</q-item-label>
                      </q-item-section>
                    </q-item>
                    <q-item clickable
                            v-ripple>
                      <q-item-section side>
                        <q-icon name="done"
                                color="green" />
                      </q-item-section>

                      <q-item-section>
                        <q-item-label class="text-h6 text-weight-bolder"
                                      lines="1">Get feedback from friends</q-item-label>
                      </q-item-section>
                    </q-item>
                    <q-item clickable
                            v-ripple>
                      <!-- <q-item-section avatar top>
                        <q-avatar icon="folder" color="primary" text-color="white" />
                      </q-item-section> -->
                      <q-item-section side>
                        <q-icon name="done"
                                color="green" />
                      </q-item-section>

                      <q-item-section>
                        <q-item-label class="text-h6 text-weight-bolder"
                                      lines="1">Get help from our AI</q-item-label>
                        <!-- <q-item-label caption>February 22nd, 2019</q-item-label> -->
                      </q-item-section>
                    </q-item>
                  </q-list>
                </div>
              </div>
              <div class="q-pt-md q-mt-xl q-px-xl q-mx-lg">
                <q-btn no-caps
                       style="border-radius: 90px; background: rgb(10, 0, 131); color: white"
                       class="rounded-borders w-full dktp-getstarted-btn"
                       @click="letsGetStartedForm"
                       padding="md"
                       size="lg">
                  <div class="row items-center no-wrap">
                    <q-icon size="sm"
                            left
                            name="thumb_up" />
                    <div class="text-center">Let's get started</div>
                  </div>
                </q-btn>
              </div>
            </div>
          </div>
          <!-- <div
            class="dsktp-demo-holder col-md-6 col-lg-8 offset-xl-1 col-xs-12 col-sm-12"
          >
            <q-card>
              <div class="demo-iframe-ctr q-mt-xs rounded-borders q-mx-none" style="">
                <iframe
                  width="100%"
                  height="550"
                  frameborder="0"
                  src="https://demo.homestocompare.com/"
                  allowfullscreen="allowfullscreen"
                  style="border: 0px"
                >
                </iframe>
              </div>
            </q-card>
            <div
              class="q-pl-none q-pb-sm q-pt-sm q-mt-sm text-left header-black-text"
              style="font-size: 24px; color: rgb(10, 0, 131)"
            >
              <span
                >Click here to view demo in a new window: &nbsp;&nbsp;&nbsp;&nbsp;</span
              >
              <a href="https://demo.homestocompare.com/" target="_blank">
                https://demo.homestocompare.com
              </a>
            </div>
          </div> -->
          <div class="col-xs-12 col-sm-12"></div>
        </div>
      </div>
    </div>
    <!--  -->
  </div>
</template>
<script>
import { defineComponent, ref } from "vue"
// import NewAccountEnquiryPrompt from "src/shared/components/customer/NewAccountEnquiryPrompt.vue";
export default defineComponent({
  name: "HtocGateLandingCta",
  components: {
    // NewAccountEnquiryPrompt,
  },
  mounted: function () { },
  setup() {
    // }
  },
  methods: {
    letsGetStartedForm() {
      this.$emit("blurbCta")
    },
    // blurbCtaEnd() {
    // },
  },
  data() {
    return {}
  },
  props: {},
  computed: {},
})
</script>
<style scoped>
.hh-hero-content-inner {
  max-width: 900px;
  margin-right: auto;
  margin-left: auto;
  text-align: center;
}

.hh-hero-content {
  -webkit-text-size-adjust: 100%;
  font-family: Inter, sans-serif;
  box-sizing: border-box;
  letter-spacing: -0.01em;
  /* padding-top: 0px; */
  /* margin: 0px 40px 50px;
  padding-right: 0px;
  padding-left: 0px; */
  font-size: 26px;
  line-height: 1.5em;
  text-align: center;
  /* margin-right: 40px;
  margin-left: 40px; */
  color: #666666;
}

.hh-hero-title {
  -webkit-text-size-adjust: 100%;
  font-family: Inter, sans-serif;
  text-align: center;
  box-sizing: border-box;
  /* margin: 0.67em 0; */
  /* margin-top: 0px;
  margin-bottom: 27px; */
  color: #0c0c0c;
  letter-spacing: -0.01em;
  font-size: 68px;
  line-height: 1.2em;
  margin-left: 0px;
  font-weight: 700;
}
</style>
