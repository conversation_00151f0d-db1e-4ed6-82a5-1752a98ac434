<template>
  <q-page class="bg-grey-1">
    <!-- Hero Section -->
    <div class="hero-section bg-gradient text-white text-center q-pa-xl">
      <div class="max-ctr">
        <section class="hero">
          <div class="hero-overlay"></div>
          <div class="container row">
            <div class="hero-content col-12">
              <div class="justify-center flex">
                <h1 class="animate-pop-in hero-title">
                  Make Smarter Real Estate Choices
                </h1>
              </div>
              <p class="subtitle animate-fade-in text-h4 q-mt-md q-mb-lg">
                Unlock detailed AI-powered insights from property listings and
                compare them effortlessly.
              </p>
              <div class="hero-cta">
                <q-btn label="Get Started"
                       color="yellow"
                       text-color="dark"
                       size="lg"
                       class="q-px-xl q-py-sm"
                       unelevated />
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>

    <!-- How It Works Section -->
    <section id="how-it-works"
             class="section process max-ctr q-py-xl">
      <div class="container">
        <h2 class="text-h2 text-primary q-mb-xl text-center">
          How <span class="text-accent">HomesToCompare</span> Works
        </h2>
        <div class="process-steps">
          <div class="process-step column justify-between items-center">
            <div class="step-number">1</div>
            <div class="step-icon-container">
              <q-icon name="home"
                      color="primary"
                      size="64px"
                      class="q-mb-md" />
            </div>
            <h3 class="text-h4 text-primary q-mb-md">Select Listing</h3>
            <p class="text-h6 text-grey-8">
              Choose an existing property listing from available sales data to
              analyze.
            </p>
          </div>
          <div class="process-step column justify-between items-center">
            <div class="step-number">2</div>
            <div class="step-icon-container">
              <q-icon name="auto_fix_high"
                      color="primary"
                      size="64px"
                      class="q-mb-md" />
            </div>
            <h3 class="text-h4 text-primary q-mb-md">AI Insights</h3>
            <p class="text-h6 text-grey-8">
              Our AI extracts detailed room descriptions and features from
              listing photos and text, creating a comprehensive dossier.
            </p>
          </div>
          <div class="process-step column justify-between items-center">
            <div class="step-number">3</div>
            <div class="step-icon-container">
              <q-icon name="compare"
                      color="primary"
                      size="64px"
                      class="q-mb-md" />
            </div>
            <h3 class="text-h4 text-primary q-mb-md">Compare & Decide</h3>
            <p class="text-h6 text-grey-8">
              Add comparable listings to your dossier and use AI-driven analysis
              to evaluate and compare properties.
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Property Insights Section -->
    <section id="property-insights"
             class="features-section q-py-xl bg-primary text-white">
      <div class="container q-mx-auto text-center q-px-md max-ctr">
        <h2 class="text-h2 q-mb-md">Unlock Property Insights with AI</h2>
        <div class="text-h5 q-mb-xl">
          HomesToCompare transforms listings into actionable insights to guide
          your house hunt.
        </div>
        <div class="row q-col-gutter-xl justify-center">
          <div class="col-12 col-md-6">
            <q-card class="feature-card bg-white text-dark">
              <q-card-section>
                <div class="feature-icon-container">
                  <q-icon name="description"
                          color="primary"
                          size="64px"
                          class="q-mb-md" />
                </div>
                <h3 class="text-h4 text-primary q-mb-md">Detailed Dossiers</h3>
                <q-list class="feature-list">
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="check_circle"
                              color="positive"
                              size="24px" />
                    </q-item-section>
                    <q-item-section>
                      <div class="text-h6">
                        AI-generated room-by-room descriptions from photos
                      </div>
                    </q-item-section>
                  </q-item>
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="check_circle"
                              color="positive"
                              size="24px" />
                    </q-item-section>
                    <q-item-section>
                      <div class="text-h6">
                        Highlight key features and amenities
                      </div>
                    </q-item-section>
                  </q-item>
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="check_circle"
                              color="positive"
                              size="24px" />
                    </q-item-section>
                    <q-item-section>
                      <div class="text-h6">
                        Organized insights for easy reference
                      </div>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-6">
            <q-card class="feature-card bg-white text-dark">
              <q-card-section>
                <div class="feature-icon-container">
                  <q-icon name="compare_arrows"
                          color="primary"
                          size="64px"
                          class="q-mb-md" />
                </div>
                <h3 class="text-h4 text-primary q-mb-md">
                  Comparable Analysis
                </h3>
                <q-list class="feature-list">
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="check_circle"
                              color="positive"
                              size="24px" />
                    </q-item-section>
                    <q-item-section>
                      <div class="text-h6">
                        Add and compare similar listings
                      </div>
                    </q-item-section>
                  </q-item>
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="check_circle"
                              color="positive"
                              size="24px" />
                    </q-item-section>
                    <q-item-section>
                      <div class="text-h6">
                        AI-driven comparison of features and value
                      </div>
                    </q-item-section>
                  </q-item>
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="check_circle"
                              color="positive"
                              size="24px" />
                    </q-item-section>
                    <q-item-section>
                      <div class="text-h6">
                        Make informed decisions with side-by-side insights
                      </div>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </section>

    <!-- Who Benefits Section -->
    <section id="who-benefits"
             class="q-py-xl bg-grey-1">
      <div class="container q-py-xl text-center max-ctr">
        <div class="row q-col-gutter-lg justify-center q-mb-xl">
          <h2 class="text-h2 text-primary">
            What makes <span class="text-accent">HomesToCompare</span> so
            useful?
          </h2>
        </div>
        <div class="row q-col-gutter-xl justify-center">
          <div class="col-xs-12 col-sm-6 col-md-6">
            <q-card class="audience-card bg-white shadow-4 q-hover-shadow-lg rounded-borders transition-all">
              <q-card-section class="text-center column justify-between items-center q-pa-xl">
                <div class="audience-icon-container">
                  <q-icon name="home"
                          color="primary"
                          size="64px"
                          class="q-mb-md" />
                </div>
                <h3 class="text-h4 text-primary q-mb-md">Home Buyers</h3>
                <p class="text-h5 q-mb-lg text-grey-8">
                  Gain clarity with detailed property insights and comparisons.
                </p>
                <ul class="text-left text-body2 text-grey-7 q-px-md q-mb-md no-bullets">
                  <li class="q-mb-md text-h6">
                    <q-icon name="check_circle"
                            color="positive"
                            size="24px"
                            class="q-mr-sm" />
                    Understand property features through AI dossiers
                  </li>
                  <li class="q-mb-md text-h6">
                    <q-icon name="check_circle"
                            color="positive"
                            size="24px"
                            class="q-mr-sm" />
                    Compare listings to find the best match
                  </li>
                  <li class="q-mb-md text-h6">
                    <q-icon name="check_circle"
                            color="positive"
                            size="24px"
                            class="q-mr-sm" />
                    Make confident offers with data-driven insights
                  </li>
                </ul>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-xs-12 col-sm-6 col-md-6">
            <q-card class="audience-card bg-white shadow-4 q-hover-shadow-lg rounded-borders transition-all">
              <q-card-section class="text-center column justify-between items-center q-pa-xl">
                <div class="audience-icon-container">
                  <q-icon name="price_change"
                          color="primary"
                          size="64px"
                          class="q-mb-md" />
                </div>
                <h3 class="text-h4 text-primary q-mb-md">Home Sellers</h3>
                <p class="text-h5 q-mb-lg text-grey-8">
                  Position your property competitively with detailed market
                  comparisons.
                </p>
                <ul class="text-left text-body2 text-grey-7 q-px-md q-mb-md no-bullets">
                  <li class="q-mb-md text-h6">
                    <q-icon name="check_circle"
                            color="positive"
                            size="24px"
                            class="q-mr-sm" />
                    Highlight your property's unique features
                  </li>
                  <li class="q-mb-md text-h6">
                    <q-icon name="check_circle"
                            color="positive"
                            size="24px"
                            class="q-mr-sm" />
                    Compare with similar listings to set the right price
                  </li>
                  <li class="q-mb-md text-h6">
                    <q-icon name="check_circle"
                            color="positive"
                            size="24px"
                            class="q-mr-sm" />
                    Attract buyers with comprehensive insights
                  </li>
                </ul>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq"
             class="faq-section q-py-xl bg-white">
      <div class="container q-mx-auto q-px-md max-ctr">
        <h2 class="text-h2 text-center q-mb-xl text-primary">
          Frequently Asked Questions
        </h2>
        <div class="row justify-center">
          <div class="col-12 col-md-8">
            <q-list bordered
                    separator
                    class="faq-list">
              <q-expansion-item v-for="(faq, index) in faqs"
                                :key="index"
                                :label="faq.question"
                                :caption="faq.answer"
                                header-class="text-h5 text-primary"
                                expand-icon-class="text-primary"
                                class="q-mb-sm">
                <q-card>
                  <q-card-section class="text-h6 text-grey-8"
                                  style="font-weight: 400">
                    {{ faq.answer }}
                  </q-card-section>
                </q-card>
              </q-expansion-item>
            </q-list>
          </div>
        </div>
      </div>
    </section>

    <!-- Call to Action -->
    <div class="cta-section bg-gradient text-white text-center q-pa-xl">
      <div class="max-ctr">
        <h2 class="text-h3 q-mb-md">
          Transform Your House Hunt with AI Insights
        </h2>
        <p class="text-h5 q-mb-lg">
          Join thousands of users making smarter real estate decisions
        </p>
        <q-btn-group unelevated>
          <q-btn label="Get Early Access"
                 color="yellow"
                 text-color="dark"
                 size="lg"
                 class="q-px-xl q-py-sm q-mr-md" />
          <q-btn label="Request a Demo"
                 color="white"
                 text-color="dark"
                 size="lg"
                 class="q-px-xl q-py-sm" />
        </q-btn-group>
      </div>
    </div>
  </q-page>
</template>

<script>
export default {
  data() {
    return {
      faqs: [
        {
          question: 'What is HomesToCompare?',
          answer:
            'HomesToCompare is a platform that uses AI to analyze existing property listings, providing detailed dossiers with room-by-room insights and enabling users to compare listings for informed real estate decisions.',
        },
        {
          question: 'Are the properties real?',
          answer:
            'Yes, HomesToCompare analyzes real, existing property listings from available sales data to provide accurate and relevant insights.',
        },
        {
          question: 'How accurate is the AI-generated data?',
          answer:
            'Our AI uses advanced algorithms to extract and analyze data from listing photos and descriptions, aiming for high accuracy. However, results should be used as a guide alongside professional advice.',
        },
        {
          question: 'Who can use HomesToCompare?',
          answer:
            'HomesToCompare is designed for home buyers, sellers, real estate agents, and anyone seeking detailed insights into property listings and market comparisons.',
        },
        {
          question: 'Is HomesToCompare free to use?',
          answer:
            'Yes, HomesToCompare is free to explore and use. Additional premium features, if introduced in the future, will be clearly marked.',
        },
      ],
    }
  },
}
</script>

<style>
.bg-gradient {
  background: linear-gradient(135deg, #2196f3 0%, #673ab7 100%);
}

.hero-section {
  min-height: 80vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.process-steps {
  display: flex;
  justify-content: space-between;
  gap: 3rem;
  margin-top: 3rem;
}

.process-step {
  flex: 1;
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.process-step:hover {
  transform: translateY(-10px);
}

.step-number {
  font-size: 3.5rem;
  font-weight: bold;
  color: var(--q-primary);
  margin-bottom: 1rem;
  opacity: 0.2;
}

.step-icon-container {
  background: var(--q-primary-light);
  border-radius: 50%;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.feature-card {
  border-radius: 1rem;
  transition: transform 0.3s ease;
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-10px);
}

.feature-icon-container {
  background: var(--q-primary-light);
  border-radius: 50%;
  padding: 1.5rem;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.feature-list {
  text-align: left;
}

.audience-card {
  height: 100%;
  border-radius: 1rem;
}

.audience-icon-container {
  background: var(--q-primary-light);
  border-radius: 50%;
  padding: 1.5rem;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.faq-list {
  border-radius: 1rem;
  overflow: hidden;
}

.cta-section {
  background: linear-gradient(135deg, #2196f3 0%, #673ab7 100%);
}

.animate-pop-in {
  animation: pop-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

@keyframes pop-in {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.no-bullets {
  list-style-type: none;
  padding-left: 0;
}

@media (max-width: 768px) {
  .process-steps {
    flex-direction: column;
    gap: 2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }
}
</style>
