<template>
  <q-layout view="lHh Lpr lFf"
            class="htoc-gate-layout-outer"
            style="min-height: 350px;">
    <q-header elevated
              class="htoc-gate-mht-ctr bg-white">
      <q-toolbar class="htoc-gate-marketing-header-toolbar container max-ctr">
        <q-toolbar-title class="ellipsis inline-flex items-center">
          <div class="toolbar-site-label-main">
            <router-link to="/"
                         class="ignore-link">
              <div>
                <span class="color-second">HOMES</span>
                <span style="color:black;">TO</span>
                <span class="color-first">COMPARE</span>
              </div>
            </router-link>
          </div>
        </q-toolbar-title>
      </q-toolbar>
    </q-header>

    <q-page-container class="main-layout-htoc-2024g-gate max-ctr"
                      style="padding-top: 50px; padding-bottom: 79px;">
      <q-page class="htoc-gate-hp q-mb-lg q-pb-lg max-ctr"
              style="min-height: 221px;">
        <div class="row mt-10">
          <div class="HtocGateLandingCta col-xs-12 no-flashing-caret">
            <div class="q-mx-md intro-cont q-mt-md">
              <!-- Mobile Version -->
              <div class="mobile-only">
                <div class="row max-ctr q-pb-lg">
                  <div class="col-md-6 col-lg-6 col-xs-12 col-sm-12">
                    <div class="hh-hero-content-inner q-mt-xs q-pt-sm">
                      <h1 class="hero-title hh-hero-title"
                          style="font-size:42px;margin-bottom:18px;margin-top:8px;">
                        <span style="font-size:48px;"
                              class="color-bold-blue">House Hunting </span><br>
                        <span class="color-first">Dossiers</span>
                      </h1>
                      <p class="text-h2 hh-hero-content header-black-text"
                         style="font-size:22px;">
                        Finding the perfect home is hard. Our insightful dossiers make it much easier.
                      </p>
                      <div class="q-pt-md q-mt-sm">
                        <q-btn unelevated
                               rounded
                               no-caps
                               style="font-size:20px;padding:16px;min-width:0;min-height:0;border-radius:90px;background:rgb(10, 0, 131);color:white;"
                               @click="getStarted">
                          <q-icon name="thumb_up"
                                  class="on-left"
                                  style="font-size:24px;" />
                          <div class="text-center">Let's get started</div>
                        </q-btn>
                      </div>
                      <div class="q-mt-md q-pt-md">
                        <q-list padding
                                class="justify-center flex items-center"
                                style="max-width:100vw;">
                          <q-item clickable
                                  v-ripple
                                  style="width:auto;">
                            <q-item-section avatar
                                            style="min-width: 40px; padding-right: 10px;">
                              <q-icon color="green"
                                      name="done" />
                            </q-item-section>
                            <q-item-section>
                              <q-item-label class="ellipsis text-h6"
                                            style="width:280px;text-align:left;">Keep track of properties</q-item-label>
                            </q-item-section>
                          </q-item>
                          <q-item clickable
                                  v-ripple
                                  style="width:auto;">
                            <q-item-section avatar
                                            style="min-width: 40px; padding-right: 10px;">
                              <q-icon color="green"
                                      name="done" />
                            </q-item-section>
                            <q-item-section>
                              <q-item-label class="ellipsis text-h6"
                                            style="width:280px;text-align:left;">Get feedback from
                                friends</q-item-label>
                            </q-item-section>
                          </q-item>
                          <q-item clickable
                                  v-ripple
                                  style="width:auto;">
                            <q-item-section avatar
                                            style="min-width: 40px; padding-right: 10px;">
                              <q-icon color="green"
                                      name="done" />
                            </q-item-section>
                            <q-item-section>
                              <q-item-label class="ellipsis text-h6"
                                            style="width:280px;text-align:left;">Get help from our AI</q-item-label>
                            </q-item-section>
                          </q-item>
                        </q-list>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Desktop Version -->
              <div class="mobile-hide">
                <div class="row max-ctr q-pb-lg">
                  <div class="col-sm-12">
                    <div class="q-pb-xl q-mt-none q-pt-lg q-mr-none">
                      <h1 class="hero-title hh-hero-title q-mb-none"
                          style="margin-top:0px;font-size:68px;">
                        <span style="color:rgb(10, 0, 131);"
                              class="hhwyf">House Hunting </span>
                        <span class="color-second"> Dossiers</span>
                      </h1>
                    </div>
                  </div>
                  <div class="col-xs-12 col-sm-12">
                    <div class="hh-hero-content-inner q-mt-none q-pt-lg q-mr-md">
                      <div class="text-h2 hh-hero-content q-mt-none q-pt-none">
                        <div class="text-left q-px-lg q-ml-lg"
                             style="font-size:24px;color:rgb(10, 0, 131);">
                          Finding the perfect home is hard. Our insightful dossiers make it much easier.
                        </div>
                        <div class="q-mt-xl q-pt-xl flex items-center justify-center">
                          <q-list padding
                                  rounded-borders
                                  style="max-width:max-content;">
                            <q-item clickable
                                    v-ripple>
                              <q-item-section avatar>
                                <q-icon color="green"
                                        name="done" />
                              </q-item-section>
                              <q-item-section>
                                <q-item-label class="ellipsis text-h6 text-weight-bolder">Keep track of
                                  properties</q-item-label>
                              </q-item-section>
                            </q-item>
                            <q-item clickable
                                    v-ripple>
                              <q-item-section avatar>
                                <q-icon color="green"
                                        name="done" />
                              </q-item-section>
                              <q-item-section>
                                <q-item-label class="ellipsis text-h6 text-weight-bolder">Get feedback from
                                  friends</q-item-label>
                              </q-item-section>
                            </q-item>
                            <q-item clickable
                                    v-ripple>
                              <q-item-section avatar>
                                <q-icon color="green"
                                        name="done" />
                              </q-item-section>
                              <q-item-section>
                                <q-item-label class="ellipsis text-h6 text-weight-bolder">Get help from our
                                  AI</q-item-label>
                              </q-item-section>
                            </q-item>
                          </q-list>
                        </div>
                      </div>
                      <div class="q-pt-md q-mt-xl q-px-xl q-mx-lg">
                        <q-btn unelevated
                               rounded
                               no-caps
                               class="w-full dktp-getstarted-btn"
                               style="font-size:20px;padding:16px;min-width:0;min-height:0;border-radius:90px;background:rgb(10, 0, 131);color:white;"
                               @click="getStarted">
                          <q-icon name="thumb_up"
                                  class="on-left"
                                  style="font-size:24px;" />
                          <div class="text-center">Let's get started</div>
                        </q-btn>
                      </div>
                    </div>
                  </div>
                  <div class="col-xs-12 col-sm-12"></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="row max-ctr">
          <div class="q-mt-lg col-xs-12 WaysToUseHtocGate-blurb-ctr">
            <div class="w-full ways-to-use-head">
              <div class="mobile-only q-ml-md q-pb-lg text-bold color-bold-blue"
                   style="font-size:24px;">
                Here are some ways we make house hunting easier
              </div>
              <div class="mobile-hide row q-pt-lg">
                <div class="q-pb-none q-pt-lg text-h3 header-black-text text-bold q-pl-md col-md-11 offset-md-1 col-sm-12 offset-sm-0"
                     style="font-size:34px;">
                  Here are some ways HomesToCompare makes house hunting easier
                </div>
              </div>
            </div>

            <!-- Feature 1: Get feedback from friends -->
            <div class="row bg-white pic-with-txt-to-left ways-to-use-ctnt">
              <div class="w-full">
                <div class="col-12 feedback-from-friends">
                  <div class="row bg-white pic-with-txt-to-left q-mb-md">
                    <div class="mobile-hide col-md-5 offset-md-1 col-sm-6 offset-sm-0">
                      <div class="full-height row blurb-content-ctr items-center">
                        <div class="full-width q-px-md">
                          <h4 class="text-bold border-bottom val-prop-item-head color-bold-blue"> Get feedback from
                            friends </h4>
                          <p class="text-h5 val-prop-item-text"> When you add a property to HomesToCompare you get a url
                            that you can share with friends so they can help you evaluate which is best. </p>
                          <p class="text-h5 val-prop-item-text"> They will be able to add notes and comments or suggest
                            other properties you might not yet have seen. </p>
                        </div>
                      </div>
                    </div>
                    <div class="col-xs-12 col-sm-12 col-md-6">
                      <div class="dsk-img-ctr justify-center flex mobile-hide q-pt-lg q-mt-md">
                        <img alt="content"
                             src="/assets/undraw_location_review_dmxd.f660bf52.svg"
                             class="val-prop-img align-middle text-left"
                             style="object-fit:cover;max-width:300px;">
                      </div>
                      <div class="flex justify-center val-prop-img-ctr mobile-only"
                           style="width:100vw;">
                        <img alt="content"
                             src="/assets/undraw_location_review_dmxd.f660bf52.svg"
                             class="val-prop-img align-middle"
                             style="object-fit:cover;max-width:225px;">
                      </div>
                    </div>
                    <div class="col-xs-12 col-sm-12 mobile-only">
                      <div class="full-height row blurb-content-ctr items-center">
                        <div class="q-px-md"
                             style="max-width:90vw;">
                          <h4 style="font-size:24px;"
                              class="text-bold q-mb-sm val-prop-item-head color-bold-blue"> Get feedback from friends
                          </h4>
                          <p class="text-body2 mob-blrb-item-text header-black-text"> When you add a property to
                            HomesToCompare you get a url that you can share with friends so they can help you evaluate
                            which is best. </p>
                          <p class="text-body2 mob-blrb-item-text header-black-text"> They will be able to add notes and
                            comments or suggest other properties you might not yet have seen. </p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row text-center q-my-md q-px-md">
                    <div class="col-xs-12 col-sm-12 col-md-10 offset-md-1">
                      <q-btn unelevated
                             no-caps
                             rounded
                             class="q-mb-none q-pb-none w-full"
                             style="font-size:20px;line-height:1.4;border-radius:90px;background:rgb(10, 0, 131);color:white;"
                             @click="startGettingFeedback">
                        <span class="block">Start Getting Feedback From Your Friends</span>
                      </q-btn>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Feature 2: Keep track of properties -->
            <div class="q-pt-lg">
              <div class="col-12">
                <div class="row bg-white pic-with-txt-to-left q-my-md">
                  <div class="col-xs-12 col-sm-12 col-md-5">
                    <div class="dsk-img-ctr justify-center flex mobile-hide q-pr-xl q-pt-lg">
                      <img alt="content"
                           src="/assets/ud_progress_tracking_re_ulfg.ef9900d4.svg"
                           class="val-prop-img"
                           style="object-fit:cover;max-width:300px;">
                    </div>
                    <div class="flex justify-center val-prop-img-ctr mobile-only"
                         style="width:100vw;">
                      <img alt="content"
                           src="/assets/ud_progress_tracking_re_ulfg.ef9900d4.svg"
                           class="val-prop-img align-middle"
                           style="object-fit:cover;max-width:225px;">
                    </div>
                  </div>
                  <div class="mobile-hide col-md-5">
                    <div class="full-height row blurb-content-ctr items-center">
                      <div class="full-width q-px-md">
                        <h4 class="text-bold border-bottom val-prop-item-head color-bold-blue"> Keep track of properties
                        </h4>
                        <p class="text-h5 val-prop-item-text"> We can track properties you save here and notify you of
                          any interesting changes. </p>
                        <p class="text-h5 val-prop-item-text"> The data you save on HomesToCompare is yours to use as
                          you wish. It will still be there even when the listing it references is gone and you can
                          download it whenever you wish. </p>
                      </div>
                    </div>
                  </div>
                  <div class="col-xs-12 col-sm-12 mobile-only">
                    <div class="full-height row blurb-content-ctr items-center">
                      <div class="q-px-md"
                           style="max-width:90vw;">
                        <h4 style="font-size:24px;"
                            class="text-bold q-mb-sm val-prop-item-head color-bold-blue"> Keep track of properties </h4>
                        <p class="text-body2 mob-blrb-item-text header-black-text"> We can track properties you save
                          here and notify you of any interesting changes. </p>
                        <p class="text-body2 mob-blrb-item-text header-black-text"> The data you save on HomesToCompare
                          is yours to use as you wish. It will still be there even when the listing it references is
                          gone and you can download it whenever you wish. </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row text-center q-my-md q-px-md">
                  <div class="col-xs-12 col-sm-12 col-md-10 offset-md-1">
                    <q-btn unelevated
                           no-caps
                           rounded
                           class="q-mb-none q-pb-none w-full"
                           style="font-size:20px;line-height:1.4;border-radius:90px;background:rgb(10, 0, 131);color:white;"
                           @click="startTrackingProperties">
                      <span class="block">Start Keeping track of properties</span>
                    </q-btn>
                  </div>
                </div>
              </div>
            </div>

            <!-- Feature 3: Get help from our AI -->
            <div class="col-xs-12 blurb-ctr q-py-md">
              <div class="col-12 q-pb-sm q-mb-sm">
                <div class="row bg-white pic-with-txt-to-left q-my-md">
                  <div class="mobile-hide col-md-5 offset-md-1">
                    <div class="full-height row blurb-content-ctr items-center">
                      <div class="full-width q-px-md">
                        <h4 class="text-bold border-bottom val-prop-item-head color-bold-blue"> Get help from our AI
                        </h4>
                        <p class="text-h5 val-prop-item-text"> AI is advancing fast and we have found that it can
                          already help house hunters in many ways. </p>
                        <p class="text-h5 val-prop-item-text"> Currently we use it mainly for labeling and classifying
                          images but we will soon add new features such as market analysis driven by AI but with human
                          oversight. </p>
                      </div>
                    </div>
                  </div>
                  <div class="col-xs-12 col-sm-12 col-md-6">
                    <div class="dsk-img-ctr justify-center flex mobile-hide q-pt-lg q-mt-xl">
                      <img alt="content"
                           src="/assets/undraw_scrum_board_re_wk7v.bdab0d99.svg"
                           class="val-prop-img align-middle text-left"
                           style="object-fit:cover;max-width:300px;">
                    </div>
                    <div class="flex justify-center val-prop-img-ctr mobile-only"
                         style="width:100vw;">
                      <img alt="content"
                           src="/assets/undraw_scrum_board_re_wk7v.bdab0d99.svg"
                           class="val-prop-img align-middle"
                           style="object-fit:cover;max-width:225px;">
                    </div>
                  </div>
                  <div class="col-xs-12 col-sm-12 mobile-only">
                    <div class="full-height row blurb-content-ctr items-center">
                      <div class="q-px-md"
                           style="max-width:90vw;">
                        <h4 style="font-size:24px;"
                            class="text-bold q-mb-sm val-prop-item-head color-bold-blue"> Get help from our AI </h4>
                        <p class="text-body2 mob-blrb-item-text header-black-text"> AI is advancing fast and we have
                          found that it can already help house hunters in many ways. </p>
                        <p class="text-body2 mob-blrb-item-text header-black-text"> Currently we use it mainly for
                          labeling and classifying images but we will soon add new features such as market analysis
                          driven by AI but with human oversight. </p>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row text-center q-my-md q-px-md">
                  <div class="col-xs-12 col-sm-12 col-md-10 offset-md-1">
                    <q-btn unelevated
                           no-caps
                           rounded
                           class="q-mb-none q-pb-none w-full"
                           style="font-size:20px;line-height:1.4;border-radius:90px;background:rgb(10, 0, 131);color:white;"
                           @click="startWithAI">
                      <span class="block">Get Started With Help From Our AI</span>
                    </q-btn>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="q-mb-lg q-pb-lg">
          <!-- Social Sharing Component -->
          <div class="social-sharing-comp">
            <div class="text-subtitle1 q-pa-md text-center">Share this with your friends</div>
            <div class="mt-6 flex justify-center">
              <div class="q-pa-sm ssc-fb">
                <a class="fb-share-link transform border-solid border border-white rounded-full w-12 h-12 flex justify-center items-center"
                   target="_blank"
                   rel="nofollow noreferrer"
                   :href="`//www.facebook.com/sharer/sharer.php?u=${currentUrl}`">
                  <q-icon color="white"
                          style="font-size:2rem;padding:8px;">
                    <svg viewBox="0 0 24 24">
                      <path
                            d="M12 2.04C6.5 2.04 2 6.53 2 12.06C2 17.06 5.66 21.21 10.44 21.96V14.96H7.9V12.06H10.44V9.85C10.44 7.34 11.93 5.96 14.22 5.96C15.31 5.96 16.45 6.15 16.45 6.15V8.62H15.19C13.95 8.62 13.56 9.39 13.56 10.18V12.06H16.34L15.89 14.96H13.56V21.96A10 10 0 0 0 22 12.06C22 6.53 17.5 2.04 12 2.04Z">
                      </path>
                    </svg>
                  </q-icon>
                </a>
              </div>
              <div class="q-pa-sm ssc-tw">
                <a class="tw-share-link hover:scale-125 border-none rounded-full w-12 h-12 flex justify-center items-center"
                   target="_blank"
                   rel="nofollow noreferrer"
                   :href="`https://twitter.com/intent/tweet?text=Check this out: ${currentUrl}`">
                  <q-icon color="white"
                          style="font-size:2rem;padding:8px;">
                    <svg viewBox="0 0 24 24">
                      <path
                            d="M22.46,6C21.69,6.35 20.86,6.58 20,6.69C20.88,6.16 21.56,5.32 21.88,4.31C21.05,4.81 20.13,5.16 19.16,5.36C18.37,4.5 17.26,4 16,4C13.65,4 11.73,5.92 11.73,8.29C11.73,8.63 11.77,8.96 11.84,9.27C8.28,9.09 5.11,7.38 3,4.79C2.63,5.42 2.42,6.16 2.42,6.94C2.42,8.43 3.17,9.75 4.33,10.5C3.62,10.5 2.96,10.3 2.38,10C2.38,10 2.38,10 2.38,10.03C2.38,12.11 3.86,13.85 5.82,14.24C5.46,14.34 5.08,14.39 4.69,14.39C4.42,14.39 4.15,14.36 3.89,14.31C4.43,16 6,17.26 7.89,17.29C6.43,18.45 4.58,19.13 2.56,19.13C2.22,19.13 1.88,19.11 1.54,19.07C3.44,20.29 5.7,21 8.12,21C16,21 20.33,14.46 20.33,8.79C20.33,8.6 20.33,8.42 20.32,8.23C21.16,7.63 21.88,6.87 22.46,6Z">
                      </path>
                    </svg>
                  </q-icon>
                </a>
              </div>
              <div class="q-pa-sm ssc-wap">
                <a class="whatsapp-share-link hover:scale-125 border-none rounded-full flex justify-center items-center"
                   target="_blank"
                   rel="nofollow noreferrer"
                   :href="`https://wa.me/?text=Check this out: ${currentUrl}`">
                  <q-icon color="white"
                          style="font-size:2rem;padding:8px;">
                    <svg viewBox="0 0 24 24">
                      <path
                            d="M12.04 2C6.58 2 2.13 6.45 2.13 11.91C2.13 13.66 2.59 15.36 3.45 16.86L2.05 22L7.3 20.62C8.75 21.41 10.38 21.83 12.04 21.83C17.5 21.83 21.95 17.38 21.95 11.92C21.95 9.27 20.92 6.78 19.05 4.91C17.18 3.03 14.69 2 12.04 2M12.05 3.67C14.25 3.67 16.31 4.53 17.87 6.09C19.42 7.65 20.28 9.72 20.28 11.92C20.28 16.46 16.58 20.15 12.04 20.15C10.56 20.15 9.11 19.76 7.85 19L7.55 18.83L4.43 19.65L5.26 16.61L5.06 16.29C4.24 15 3.8 13.47 3.8 11.91C3.81 7.37 7.5 3.67 12.05 3.67M8.53 7.33C8.37 7.33 8.1 7.39 7.87 7.64C7.65 7.89 7 8.5 7 9.71C7 10.93 7.89 12.1 8 12.27C8.14 12.44 9.76 14.94 12.25 16C12.84 16.27 13.3 16.42 13.66 16.53C14.25 16.72 14.79 16.69 15.22 16.63C15.7 16.56 16.68 16.03 16.89 15.45C17.1 14.87 17.1 14.38 17.04 14.27C16.97 14.17 16.81 14.11 16.56 14C16.31 13.86 15.09 13.26 14.87 13.18C14.64 13.1 14.5 13.06 14.31 13.3C14.15 13.55 13.67 14.11 13.53 14.27C13.38 14.44 13.24 14.46 13 14.34C12.74 14.21 11.94 13.95 11 13.11C10.26 12.45 9.77 11.64 9.62 11.39C9.5 11.15 9.61 11 9.73 10.89C9.84 10.78 10 10.6 10.1 10.45C10.23 10.31 10.27 10.2 10.35 10.04C10.43 9.87 10.39 9.73 10.33 9.61C10.27 9.5 9.77 8.26 9.56 7.77C9.36 7.29 9.16 7.35 9 7.34C8.86 7.34 8.7 7.33 8.53 7.33Z">
                      </path>
                    </svg>
                  </q-icon>
                </a>
              </div>
              <div class="q-pa-sm ssc-ln">
                <a class="linked-in-share-link hover:scale-125 transform border-none rounded-full flex justify-center items-center"
                   target="_blank"
                   rel="nofollow noreferrer"
                   :href="`//www.linkedin.com/shareArticle?mini=true&url=${currentUrl}&title=Check this out:`">
                  <q-icon color="white"
                          style="font-size:2rem;padding:8px;">
                    <svg viewBox="0 0 24 24">
                      <path
                            d="M19 3A2 2 0 0 1 21 5V19A2 2 0 0 1 19 21H5A2 2 0 0 1 3 19V5A2 2 0 0 1 5 3H19M18.5 18.5V13.2A3.26 3.26 0 0 0 15.24 9.94C14.39 9.94 13.4 10.46 12.92 11.24V10.13H10.13V18.5H12.92V13.57C12.92 12.8 13.54 12.17 14.31 12.17A1.4 1.4 0 0 1 15.71 13.57V18.5H18.5M6.88 8.56A1.68 1.68 0 0 0 8.56 6.88C8.56 5.95 7.81 5.19 6.88 5.19A1.69 1.69 0 0 0 5.19 6.88C5.19 7.81 5.95 8.56 6.88 8.56M8.27 18.5V10.13H5.5V18.5H8.27Z">
                      </path>
                    </svg>
                  </q-icon>
                </a>
              </div>
              <div class="q-pa-sm ssc-email">
                <a class="email-share-link hover:scale-125 transform border-none rounded-full flex justify-center items-center"
                   target="_blank"
                   rel="nofollow noreferrer"
                   :href="`mailto:?subject=Check this out:&body=${currentUrl}`">
                  <q-icon color="white"
                          style="font-size:2rem;padding:8px;">
                    <svg viewBox="0 0 24 24">
                      <path
                            d="M5,3A2,2 0 0,0 3,5V19A2,2 0 0,0 5,21H19A2,2 0 0,0 21,19V5A2,2 0 0,0 19,3H5M6.4,6.5H17.6C18.37,6.5 19,7.12 19,7.9V16.1A1.4,1.4 0 0,1 17.6,17.5H6.4C5.63,17.5 5,16.87 5,16.1V7.9C5,7.12 5.62,6.5 6.4,6.5M6,8V10L12,14L18,10V8L12,12L6,8Z">
                      </path>
                    </svg>
                  </q-icon>
                </a>
              </div>
            </div>
          </div>

          <!-- QR Code Section -->
          <div>
            <div class="text-center q-py-md q-my-md">
              <div class="text-subtitle1 q-py-md"> Scan code below to navigate to this page from another device </div>
              <img class="mt-6 mb-2 rounded border"
                   :src="qrCodeImage"
                   alt="QR Code for this page">
            </div>
          </div>
        </div>
      </q-page>
    </q-page-container>

    <q-footer elevated
              class="htoc-gate-foot bg-grey-8 text-white">
      <q-toolbar>
        <q-toolbar-title>
          <div class="text-center q-pa-sm text-body1">
            <router-link to="/i/privacy"
                         class="text-white"> Privacy Policy </router-link> |
            <router-link to="/i/terms-of-service"
                         class="text-white"> Terms Of Service </router-link> |
            <router-link to="/auth/login"
                         class="text-white"> Sign In </router-link> |
            <router-link to="/auth/create-account"
                         class="text-white"> Create An Account </router-link>
          </div>
        </q-toolbar-title>
      </q-toolbar>
      <div class="q-pa-sm">
        <div class="copyright-foot width-full">
          <div class="float-right"> Copyright © 2021 - {{ currentYear }}
            <router-link class="text-white"
                         to="/">HomesToCompare</router-link>
          </div>
        </div>
      </div>
    </q-footer>
  </q-layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router'

// const router = useRouter(); // Uncomment if you need to programmatically navigate

const currentYear = new Date().getFullYear();

const qrCodeImage = ref('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJwAAACcCAYAAACKuMJNAAAAAXNSR0IArs4c6QAACk1JREFUeF7tndGO4zgMBDP//9FzwL3ZBlxbaEpJxr2vlESyWaJkJ5n9+f39/X31XxXYpMBPgdukdN38r0CBKwhbFShwW+WuswJXBrYqUOC2yl1nBa4MbFWgwG2Vu84KXBnYqkCB2yp3nRW4MrBVgQK3Ve4608D9/PxsVS39qPcc7/R6ZzHO65NeND61ry6W1bPAyYqkAFlAC1w73C2iBEiBkz2RdrhsGDhchndZr0cqShwNsPWJj1TrkLKzgNB4a6f4rN12vNUd0MZ/Hk960voFbvEVocAdESxwBY6a0sHeDncChjoK2ZX6/zA49Ufzrf0fQr4d8nHA2YcKK5hdPxV49Z2KCkj5rtbPrk96jx+pJNB0ASnBabstgAWK1t+tH8VjHxoLnCTSFqDALX5oaIc7/uqywD0MON3yw6fW1B910B6pp99Rf1qHSwGQJ+4r9VfgTorbI4IKRgKT3a5P4+2GoY5j/dl8aTzZKT6arzeY/eV9gbsvkS6AfI9IgL+7PgTw1z2l0md7tiAkUGqnDmHjJaALHFSMCmIFtgVMgaL5lJ+N1+ph/U/Hc2kQ33aktsPd/7Grdrh2uIMC9FBCHakdbvjPy9EOtR2OjrzUTgDQ+tMAWv0oPtLb5j/+0GATSBOigqXx0HwrOOWb3qEKHFXsZLeCFTj3UZosx2W4rc/yh4bdCRW4hwGXAkbz7SWaduDT7KRvardXivgOlwZM8wuc62C7O36Bkx8V/XWgaUOn9gJX4FKG1PzlwKlo3jDYHilWMEqJ/JO/dD7F9267vsO9O2DyTwWj91y0PtnJf4EjBUjhD7NTwQvcewvWDrf4ozkLOG2Yb+8Py4GzAqbj6amTAEj92/UJIBuP7V+r148/aVidkBXAvsi1QKweX+AsUTA+BcgWvB3OFdDWx61+Hd0jVf7qbBrodrjFHYsKtroA9gi28ezOz+ZDHYrypflk1x3OtmArCCVs/V8ureEnEbvXW32loPUJIGsvcHCk2g3QDnePYIErcAdCaIPZjha/FqEjko4cauG0fnqkUnzUoSh+KoiNn8aTPxuv9WcBjTscOaQEqMBkt4IWOPczQwKa6t8Od1Lg3R2VNgxtWAKC1qcNSOsXOPnZaIE7ImUBXw4cEb864HRHUvwkoAWUrgTkj+K18VDHo/UoHrLrOxwuKP+g37TgFB/ZKR4qSGqn+GjDEeAFTh6BJLgtGBWA/FGBrd3GbwGnfKk9G1/80EAOe6Qe/3vPAne6I6Z/PYl2DHUIApgKZufTeLJTB7B28kf5T9upXnTloHz0HY46GAVE8wngdD4JQnYLFAFB/mj+tL3Aya8PEbBUYLIXuPsXx6hfj1SS6HQHCb9tknboaeDtiUTjSc3xI9V2GBKQWjwlSPFYAOx6VCDrn9azepD+ZNf+pjscFYQAIkFtgSie1etN50PraQDCjq39FbjjawwroL2004Yj/wUOFCKBbMte3ZGo4LZj2vzJP61H8wn4dAORf32HowUpITufCmz9kaDkjzYI2dP8acMRkOn8OH57pFqHlKBdb1pQio8AtXabb7qhpufH8Re4+zucBaod7h7JHqnw7ZYCl/a043wNnN3BdGR9252JAKTy0JWA5qd2qh/VK42/wEEF0wLRhkoBsvPTfAocvLi0BaFLdjvcmz9LJeKpRVMHsDuS4rEAWv+0/nR85G96A6Xxx0eqTdh2CDs+jccWyPqz41PgU/1SwC562tcitmPZDpaOtwUlQang1p8dT/6pHgVOfr8tFYwKXODuFSJ9SN92uJMCJCh1GCu4HU/+H9/hqIDpHYkEnj6SqcN+ut0CTvWjDUD+xh8aKOACl/2qiwpuNyRt0LRey4/UArcXKOqw1HEKHPww2u7gtCA0/9Ptfx44SpA6oD0ybMHtEZHGQ3pQPLYDkT+7YWk9qufyIzUNMC0wzacCW4CtP9KHgLAFpnwpHrLbeMYfGtIAqYCpnQpQ4KiCR3uBkx/mpwDTfFe+16sd7qRYKkhaIPJPdx6aTzuW5lvAqKOm69F8yofiI72W3+EogAJ3fyQRAAQQ6U9XCrthtb/pD+8pgAJX4NQ36mgHFjjqQQVOAefkvI4mYO369o5B/mm99MhJOzzpk+a3vGHYI5USJjsJQvOnC27Xs+MJYCqw1YP0TeNJN4x+D2cFsJdUu74VMC1Igbv/rJjqV+BOChHABW4zcLZDIPHDfy4qbvnyh9HT+U0DTfGRPzufxusOV+DcMxZtAKtnuh4BMX2nvFyp7EODFYgStAKSILSejSftABSP1TNdj/InfWk+2dvhTgpZAFBgeWUgwB8PHO2I1QJRwSm+9CmaHjJSOwFI8dv8aT0bz/iRSgkVuOOdL9Uj1Zs2aIGzCsFrDlqOjlTa4RYo6oDkjwAhQFM97PrxHY4c2gKQANZO8VHByB8Bk9oLnPyCIxXUAkEAWH/phtgd/2oA0w5P9Vne4SwAFLC1W6CoI9mC23hJL+uf8rf+rP/tDw2U0O4OQUCRPRXcAkgdh/QrcOGRnBaMgCJ7gTsqQMCPdzgCgAKiHUzrW/s0UBQ/5U/x0/o039rTeMlffIcjB5TAuwUl/2n8NJ/0o/hovrWn8ZK/Ahd+O4SASAtI61OBrT2Nl/wVuAJ3YOTjgCOCV9vpqYs6gr3D0UOCjYf8U8HJH+lP861+5C9+aLAOpsdPC0YCF7jsqbTAyb8xXOAK3O2dgzoWHWnUkWn+tP3SIcL3mNMnBOkVdzgqqA2AxlMBbQey/mj8NBC0ns2X9LN2q0eBA8Xo0k6CUweh+QXupEA73D0yBQ70mf4Rjd3BNJ5avj1irD8aTx1pumPafEk/a7d6jB+pqaCrC0YC2Y5NBSJ/dj6Nt/rbfAlwyrfAhVcEAoAKYOfT+AJHioN9+g5E4dgdTwCQPzufxhc4UrzAHRQg4Atc+MNeuhNQh6MC0fr2zkjx0P6i+TYf8pfapwFffoezAtoE0/UL3D2Sth4W8PjrSRQgBUTzyW7XL3AF7vZOU+BoS83aSW/7kPLnj1SSzN7xqJyr72hpPhRfmh/NL3AnBeIdO/wQRRuC7rAEqM03BbbAFTjVlAqc/A2C7QiqGq/r/41FHcaub9ej8e1w8A3caYFox1o7HYEWMBpP8dn5FP/0hv261yJWULvjqaDTBaB87GscWs/Gb8ej//TrSVRQCoDmt8MdFaQNQXpbgOx49F/g9v6FSioI2Qvc6Q5GgpHdCkrjaYdSPNSBaT7dkejIpPmUH50QqX60/vLXIrYAJDgllApG8Ra44/88QxtA9Bx/aCCHZCeALKDUASieAlfgDowQoAXu/n/OSfWjE2j8SKUOkdopIQJqd4eifKnA1MXpSKP1UzvlR/b4SCUHqb3AuSMtBYrmp/UscFJB2gC0nC1o2sGpw1s75Uf2AkcKnewFTgp2Gq6By9x19tMVKHBPJ2Bz/gVus+BPd1fgnk7A5vwL3GbBn+6uwD2dgM35F7jNgj/dXYF7OgGb8y9wmwV/ursC93QCNudf4DYL/nR3Be7pBGzO/z/xzrMOIzFv9wAAAABJRU5ErkJggg=='); // Your base64 QR code

const currentUrl = ref('');

onMounted(() => {
  if (typeof window !== 'undefined') {
    currentUrl.value = window.location.href;
  }
});

// Placeholder methods for button clicks
const getStarted = () => {
  console.log("Let's get started clicked");
  // Example: router.push('/register');
};

const startGettingFeedback = () => {
  console.log("Start Getting Feedback From Your Friends clicked");
  // Example: router.push('/features/feedback');
};

const startTrackingProperties = () => {
  console.log("Start Keeping track of properties clicked");
  // Example: router.push('/features/tracking');
};

const startWithAI = () => {
  console.log("Get Started With Help From Our AI clicked");
  // Example: router.push('/features/ai-assist');
};

</script>

<style lang="scss">
/* IMPORTANT: Many of these styles were originally in global CSS files or inline.
   You'll need to ensure these classes are defined globally in your Quasar project
   (e.g., in src/css/app.scss or a dedicated stylesheet imported there)
   or move relevant styles into this <style> block (potentially scoped).
*/

// Example of how you might define some of these globally or here:
.color-first {
  color: #FF6B6B;
  /* Example color */
}

.color-second {
  color: #4ECDC4;
  /* Example color */
}

.color-bold-blue {
  color: rgb(10, 0, 131);
  font-weight: bold;
}

// This was an inline style
.header-black-text {
  color: #333;
  /* Example color */
}

.container.max-ctr {
  // Assuming .container and .max-ctr are for centering and max-width
  max-width: 1200px; // Example
  margin-left: auto;
  margin-right: auto;
  width: 100%;
}

.toolbar-site-label-main a {
  text-decoration: none;

  div {
    font-size: 1.5rem; // Adjust as needed
    font-weight: bold;
  }
}

.htoc-gate-layout-outer {
  /* styles if any */
}

.htoc-gate-mht-ctr {
  /* styles if any */
}

.htoc-gate-marketing-header-toolbar {
  /* styles if any */
}

.main-layout-htoc-2024g-gate {
  /* styles if any */
}

.htoc-gate-hp {
  /* styles if any */
}

.mt-10 {
  margin-top: 2.5rem;
  /* Example, adjust based on your design system */
}

.HtocGateLandingCta {
  /* styles if any */
}

.no-flashing-caret {
  /* styles if any - might be for input fields not visible here */
}

.intro-cont {
  /* styles if any */
}

.hero-title {
  /* styles if any */
}

.hh-hero-title {
  /* styles if any */
}

.hh-hero-content-inner {
  /* styles if any */
}

.hh-hero-content {
  /* styles if any */
}

.hhwyf {
  /* styles if any */
}


.w-full {
  width: 100%;
}

.dktp-getstarted-btn {
  /* desktop specific styles for the button if any */
}

.WaysToUseHtocGate-blurb-ctr {
  /* styles if any */
}

.ways-to-use-head {
  /* styles if any */
}

.pic-with-txt-to-left {
  /* styles if any */
}

.ways-to-use-ctnt {
  /* styles if any */
}

.feedback-from-friends {
  /* styles if any */
}

.blurb-content-ctr {
  /* styles if any */
}

.val-prop-item-head {
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;

  &.border-bottom {
    // If you want a specific border style
    border-bottom: 2px solid #eee; // Example border
  }
}

.val-prop-item-text {
  /* styles if any */
}

.dsk-img-ctr {
  /* styles if any */
}

.val-prop-img {
  /* styles if any */
}

.align-middle {
  vertical-align: middle;
  /* Or use flex align-items-center */
}

.val-prop-img-ctr {
  /* styles if any */
}

.mob-blrb-item-text {
  /* styles if any */
}

.blurb-ctr {
  /* styles if any */
}


/* Social Sharing Styles */
.social-sharing-comp {
  .ssc-fb a {
    background-color: #3b5998;
  }

  .ssc-tw a {
    background-color: #1da1f2;
  }

  .ssc-wap a {
    background-color: #25d366;
  }

  .ssc-ln a {
    background-color: #0077b5;
  }

  .ssc-email a {
    background-color: #7f7f7f;
  }

  a {
    display: inline-flex; // Already flex in your html structure
    justify-content: center;
    align-items: center;
    width: 48px; // w-12 from Tailwind
    height: 48px; // h-12 from Tailwind
    border-radius: 9999px; // rounded-full
    color: white;
    text-decoration: none;
    transition: transform 0.2s ease-in-out;

    &:hover {
      transform: scale(1.1); // hover:scale-125 (Tailwind might use a different scale factor)
    }
  }
}

.mt-6 {
  margin-top: 1.5rem;
}

// Tailwind mapping
.mb-2 {
  margin-bottom: 0.5rem;
}

// Tailwind mapping
.rounded {
  border-radius: 0.25rem;
}

// Tailwind mapping
.border {
  border: 1px solid #e2e8f0;
}

// Tailwind mapping (example color)


.float-right {
  float: right;
}

.width-full {
  width: 100%;
}

// Likely same as w-full

/* Quasar's responsive classes are preferred over custom .mobile-only/.mobile-hide */
/* Example: Use 'lt-md' for mobile, 'gt-sm' for desktop in class bindings or q-responsive */
/* If you must keep them: */
.mobile-only {
  @media (min-width: $breakpoint-md-min) {
    // $breakpoint-md-min is a Quasar SASS variable
    display: none !important;
  }
}

.mobile-hide {
  @media (max-width: $breakpoint-sm-max) {
    // $breakpoint-sm-max is a Quasar SASS variable
    display: none !important;
  }
}

// Ensure your router links in footer look as intended
.q-footer .text-white {
  color: white !important;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}

// General helper for full height if not using flex
.full-height {
  height: 100%;
}

.full-width {
  width: 100%;
}
</style>