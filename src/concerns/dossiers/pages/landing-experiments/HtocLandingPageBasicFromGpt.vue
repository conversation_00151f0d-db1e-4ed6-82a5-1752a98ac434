<template>
  <q-page class="landing-page once-bggrey2">
    <!-- Hero Section -->
    <div class="hero-section text-white text-center">
      <q-img src="https://via.placeholder.com/1920x800"
             class="full-width"
             style="height: 60vh;"
             ratio="16/9"
             lazy />
      <div class="overlay">
        <h1 class="text-h2 font-bold q-mb-md">AI-Generated Real Estate Insights</h1>
        <p class="text-subtitle1 q-mb-lg">
          Discover properties that reflect the median characteristics of real properties in your area.
        </p>
        <q-btn color="primary"
               size="lg"
               label="Get Started"
               class="q-mt-md" />
      </div>
    </div>

    <!-- Features Section -->
    <div class="features-section q-pa-lg bg-white text-center">
      <h2 class="text-h4 q-mb-lg">Why Choose Our Platform?</h2>
      <div class="row justify-around">
        <q-card flat
                bordered
                class="feature-card q-pa-md col-12 col-md-4">
          <q-icon name="analytics"
                  size="4rem"
                  color="primary"
                  class="q-mb-md" />
          <h3 class="text-h6">AI-Generated Properties</h3>
          <p>
            Explore hypothetical properties tailored to match real market medians in your area.
          </p>
        </q-card>
        <q-card flat
                bordered
                class="feature-card q-pa-md col-12 col-md-4">
          <q-icon name="trending_up"
                  size="4rem"
                  color="primary"
                  class="q-mb-md" />
          <h3 class="text-h6">Localized Insights</h3>
          <p>
            Access hyper-local trends and property data to understand the real estate market better.
          </p>
        </q-card>
        <q-card flat
                bordered
                class="feature-card q-pa-md col-12 col-md-4">
          <q-icon name="edit"
                  size="4rem"
                  color="primary"
                  class="q-mb-md" />
          <h3 class="text-h6">Interactive Customization</h3>
          <p>
            Adjust property features and instantly see how they impact pricing and other metrics.
          </p>
        </q-card>
      </div>
    </div>

    <!-- How It Works Section -->
    <div class="how-it-works-section q-pa-lg bg-grey-1 text-center">
      <h2 class="text-h4 q-mb-lg">How It Works</h2>
      <div class="row justify-around">
        <q-card flat
                bordered
                class="step-card q-pa-md col-12 col-md-3">
          <q-icon name="place"
                  size="3rem"
                  color="secondary"
                  class="q-mb-md" />
          <h3 class="text-h6">1. Select a Location</h3>
          <p>Choose a neighborhood, city, or region you want insights about.</p>
        </q-card>
        <q-card flat
                bordered
                class="step-card q-pa-md col-12 col-md-3">
          <q-icon name="apartment"
                  size="3rem"
                  color="secondary"
                  class="q-mb-md" />
          <h3 class="text-h6">2. Generate Properties</h3>
          <p>
            View AI-generated properties that reflect median characteristics in the area.
          </p>
        </q-card>
        <q-card flat
                bordered
                class="step-card q-pa-md col-12 col-md-3">
          <q-icon name="assessment"
                  size="3rem"
                  color="secondary"
                  class="q-mb-md" />
          <h3 class="text-h6">3. Explore Insights</h3>
          <p>Analyze trends, pricing, and potential value based on customized features.</p>
        </q-card>
      </div>
    </div>

    <!-- Call to Action -->
    <div class="cta-section text-white text-center q-pa-lg bg-primary">
      <h2 class="text-h4 font-bold q-mb-md">Ready to Explore?</h2>
      <p class="q-mb-lg">
        Experience the future of real estate insights with AI-powered property generation.
      </p>
      <q-btn color="white"
             outline
             size="lg"
             label="Sign Up Now" />
    </div>
  </q-page>
</template>

<script>
export default {
  name: "LandingPage",
};
</script>

<style scoped>
.landing-page {
  font-family: Arial, sans-serif;
}

.hero-section {
  position: relative;
}

.hero-section .overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 800px;
}

.features-section .feature-card,
.how-it-works-section .step-card {
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.cta-section {
  background-image: linear-gradient(135deg, #2c7be5, #4ca1ff);
}
</style>
