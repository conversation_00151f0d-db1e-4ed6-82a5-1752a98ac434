<template>
  <q-page class="bg-grey-2">
    <!-- Hero Section -->
    <div class="bg-gradient text-white text-center q-pa-md">
      <div class="max-ctr">
        <section class="hero">
          <div class="hero-overlay"></div>
          <div class="container row">
            <div class="hero-content col-12">
              <div class="justify-center flex">
                <h1 class="animate-pop-in" style="max-width: 1200px">
                  Make Smarter Real Estate Choices
                </h1>
              </div>
              <p class="subtitle animate-fade-in text-h5">
                Unlock detailed AI-powered insights from property listings and
                compare them effortlessly.
              </p>
              <div class="hero-cta">
                <q-btn
                  label="Learn More"
                  color="yellow"
                  text-color="dark"
                  size="lg"
                  class="q-mb-lg"
                />
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>

    <!-- How It Works Section -->
    <section id="how-it-works" class="section process max-ctr">
      <div class="container">
        <h2 class="text-h3 text-primary q-mb-xl q-pb-xl text-center">
          How <span class="text-accent">HomesToCompare</span> Works
        </h2>
        <div class="process-steps">
          <div class="process-step column justify-between items-center">
            <div class="step-number">1</div>
            <h3 class="text-h3">
              Select <br />
              Listing
            </h3>
            <p class="text-h5">
              Choose an existing property listing from available sales data to
              analyze.
            </p>
            <div class="step-illustration">
              <q-icon
                name="home"
                color="purple-500"
                size="56px"
                class="q-mb-md"
              />
            </div>
          </div>
          <div class="process-step column justify-between items-center">
            <div class="step-number">2</div>
            <h3 class="text-h3">
              AI <br />
              Insights
            </h3>
            <p class="text-h5">
              Our AI extracts detailed room descriptions and features from
              listing photos and text, creating a comprehensive dossier.
            </p>
            <div class="step-illustration">
              <q-icon
                name="auto_fix_high"
                color="purple-500"
                size="56px"
                class="q-mb-md"
              />
            </div>
          </div>
          <div class="process-step column justify-between items-center">
            <div class="step-number">3</div>
            <h3 class="text-h3">
              Compare <br />
              & Decide
            </h3>
            <p class="text-h5">
              Add comparable listings to your dossier and use AI-driven analysis
              to evaluate and compare properties.
            </p>
            <div class="step-illustration">
              <q-icon
                name="compare"
                color="purple-500"
                size="56px"
                class="q-mb-md"
              />
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Property Insights Section -->
    <section
      id="property-insights"
      class="features-section q-py-xl bg-grey text-white"
    >
      <div class="container q-mx-auto text-center q-px-md max-ctr">
        <h2 class="text-h3 q-mb-md">Unlock Property Insights with AI</h2>
        <div class="text-h5 q-mb-lg">
          HomesToCompare transforms listings into actionable insights to guide
          your house hunt.
        </div>
        <div class="row q-col-gutter-lg justify-center">
          <div class="col-12 col-md-6">
            <q-card class="bg-green-9 text-white">
              <q-card-section>
                <div class="col-xs-12 col-sm-6">
                  <q-icon
                    name="description"
                    color="purple-500"
                    size="56px"
                    class="q-mb-md"
                  />
                  <h3 class="text-lg font-bold">Detailed Dossiers</h3>
                </div>
                <q-list>
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="remove" />
                    </q-item-section>
                    <q-item-section>
                      <div class="text-h6">
                        AI-generated room-by-room descriptions from photos
                      </div>
                    </q-item-section>
                  </q-item>
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="remove" />
                    </q-item-section>
                    <q-item-section>
                      <div class="text-h6">
                        Highlight key features and amenities
                      </div>
                    </q-item-section>
                  </q-item>
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="remove" />
                    </q-item-section>
                    <q-item-section>
                      <div class="text-h6">
                        Organized insights for easy reference
                      </div>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-6">
            <q-card class="bg-green-9 text-white">
              <q-card-section>
                <div class="col-xs-12 col-sm-6">
                  <q-icon
                    name="compare_arrows"
                    color="blue-500"
                    size="56px"
                    class="q-mb-md"
                  />
                  <h3 class="text-lg font-bold">Comparable Analysis</h3>
                </div>
                <q-list>
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="check" />
                    </q-item-section>
                    <q-item-section>
                      <div class="text-h6">
                        Add and compare similar listings
                      </div>
                    </q-item-section>
                  </q-item>
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="check" />
                    </q-item-section>
                    <q-item-section>
                      <div class="text-h6">
                        AI-driven comparison of features and value
                      </div>
                    </q-item-section>
                  </q-item>
                  <q-item>
                    <q-item-section avatar>
                      <q-icon name="check" />
                    </q-item-section>
                    <q-item-section>
                      <div class="text-h6">
                        Make informed decisions with side-by-side insights
                      </div>
                    </q-item-section>
                  </q-item>
                </q-list>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </section>

    <!-- Who Benefits Section -->
    <section id="who-benefits" class="q-py-lg bg-light">
      <div class="container q-py-xl text-center max-ctr">
        <div class="row q-col-gutter-lg justify-center q-mb-lg">
          <h2 class="text-h3 text-primary q-mb-lg">
            What makes <span class="text-accent">HomesToCompare</span> so
            useful?
          </h2>
        </div>
        <div class="row q-col-gutter-lg justify-center">
          <div class="col-xs-12 col-sm-6 col-md-6">
            <q-card
              class="q-ma-lg bg-white shadow-2 q-hover-shadow-lg rounded-borders transition-all audience-card"
            >
              <q-card-section
                class="text-center column justify-between items-center"
              >
                <q-icon
                  name="home"
                  color="primary"
                  size="56px"
                  class="q-mb-md"
                />
                <h3 class="text-h6 q-mb-md text-primary">Home Buyers</h3>
                <p class="text-h5 q-mb-md text-grey-8">
                  Gain clarity with detailed property insights and comparisons.
                </p>
                <ul
                  class="text-left text-body2 text-grey-7 q-px-md q-mb-md no-bullets"
                >
                  <li class="q-mb-sm text-h6">
                    <q-icon
                      name="check_circle"
                      color="green-6"
                      size="24px"
                      class="q-mr-sm"
                    />
                    Understand property features through AI dossiers.
                  </li>
                  <li class="q-mb-sm text-h6">
                    <q-icon
                      name="check_circle"
                      color="green-6"
                      size="24px"
                      class="q-mr-sm"
                    />
                    Compare listings to find the best match.
                  </li>
                  <li class="q-mb-sm text-h6">
                    <q-icon
                      name="check_circle"
                      color="green-6"
                      size="24px"
                      class="q-mr-sm"
                    />
                    Make confident offers with data-driven insights.
                  </li>
                </ul>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-xs-12 col-sm-6 col-md-6">
            <q-card
              class="q-ma-lg bg-white shadow-2 q-hover-shadow-lg rounded-borders transition-all audience-card"
            >
              <q-card-section
                class="text-center column justify-between items-center"
              >
                <q-icon
                  name="price_change"
                  color="primary"
                  size="56px"
                  class="q-mb-md"
                />
                <h3 class="text-h6 q-mb-md text-primary">Home Sellers</h3>
                <p class="text-h5 q-mb-md text-grey-8">
                  Position your property competitively with detailed market
                  comparisons.
                </p>
                <ul
                  class="text-left text-body2 text-grey-7 q-px-md q-mb-md no-bullets text-h6"
                >
                  <li class="q-mb-sm text-h6">
                    <q-icon
                      name="check_circle"
                      color="green-6"
                      size="24px"
                      class="q-mr-sm"
                    />
                    Highlight your property's unique features.
                  </li>
                  <li class="q-mb-sm text-h6">
                    <q-icon
                      name="check_circle"
                      color="green-6"
                      size="24px"
                      class="q-mr-sm"
                    />
                    Compare with similar listings to set the right price.
                  </li>
                  <li class="q-mb-sm text-h6">
                    <q-icon
                      name="check_circle"
                      color="green-6"
                      size="24px"
                      class="q-mr-sm"
                    />
                    Attract buyers with comprehensive insights.
                  </li>
                </ul>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="faq-section q-pb-xl">
      <div class="container q-mx-auto q-px-md">
        <h2 class="text-h3 text-center q-mb-xl text-primary">
          Frequently Asked Questions
        </h2>
        <div class="row justify-center">
          <div class="col-12 col-md-8">
            <q-list bordered separator>
              <q-expansion-item
                v-for="(faq, index) in faqs"
                :key="index"
                :label="faq.question"
                :caption="faq.answer"
                header-class="text-h5"
                expand-icon-class="text-primary"
              >
                <q-card>
                  <q-card-section class="text-h6" style="font-weight: 400">
                    {{ faq.answer }}
                  </q-card-section>
                </q-card>
              </q-expansion-item>
            </q-list>
          </div>
        </div>
      </div>
    </section>

    <!-- Call to Action -->
    <div class="bg-gradient text-white text-center q-pa-lg">
      <div>
        <h2 class="text-h5 q-mb-md">
          Transform Your House Hunt with AI Insights
        </h2>
        <q-btn-group unelevated>
          <q-btn
            label="Get Early Access"
            color="yellow"
            text-color="dark"
            size="lg"
            class="q-mr-md"
          />
          <q-btn
            label="Request a Demo"
            color="white"
            text-color="dark"
            size="lg"
          />
        </q-btn-group>
      </div>
    </div>
  </q-page>
</template>

<script>
export default {
  data() {
    return {
      faqs: [
        {
          question: 'What is HomesToCompare?',
          answer:
            'HomesToCompare is a platform that uses AI to analyze existing property listings, providing detailed dossiers with room-by-room insights and enabling users to compare listings for informed real estate decisions.',
        },
        {
          question: 'Are the properties real?',
          answer:
            'Yes, HomesToCompare analyzes real, existing property listings from available sales data to provide accurate and relevant insights.',
        },
        {
          question: 'How accurate is the AI-generated data?',
          answer:
            'Our AI uses advanced algorithms to extract and analyze data from listing photos and descriptions, aiming for high accuracy. However, results should be used as a guide alongside professional advice.',
        },
        {
          question: 'Who can use HomesToCompare?',
          answer:
            'HomesToCompare is designed for home buyers, sellers, real estate agents, and anyone seeking detailed insights into property listings and market comparisons.',
        },
        {
          question: 'Is HomesToCompare free to use?',
          answer:
            'Yes, HomesToCompare is free to explore and use. Additional premium features, if introduced in the future, will be clearly marked.',
        },
      ],
    }
  },
}
</script>

<style>
.bg-gradient {
  background: linear-gradient(to right, #2196f3, #673ab7);
}

.process-steps {
  display: flex;
  justify-content: space-between;
  gap: 2rem;
}

.process-step {
  flex: 1;
  text-align: center;
  padding: 2rem;
}

.step-number {
  font-size: 3rem;
  font-weight: bold;
  color: var(--q-primary);
  margin-bottom: 1rem;
}

.animate-pop-in {
  animation: pop-in 0.5s ease-out forwards;
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

@keyframes pop-in {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

.no-bullets {
  list-style-type: none;
  padding-left: 0;
}

.audience-card {
  transition: transform 0.3s ease;
}

.audience-card:hover {
  transform: translateY(-5px);
}
</style>
