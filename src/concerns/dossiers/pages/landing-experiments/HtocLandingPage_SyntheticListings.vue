<template>
  <q-page class="once-bggrey2">
    <!-- Hero Section -->
    <HtocHero></HtocHero>
    <HowHtocWorks></HowHtocWorks>

    <WhyNuneaton></WhyNuneaton>
    <WhoIsHtocFor></WhoIsHtocFor>
    <div class="max-ctr">
      <div>
        <h2 class="text-h3 text-center q-mb-sm text-primary">
          Some outstanding value areas in Nuneaton
        </h2>
      </div>
      <PostcodeClustersList></PostcodeClustersList>
    </div>
    <div class="max-ctr">
      <div>
        <h2 class="text-h3 text-center q-mb-sm text-primary">
          Some example Synthetic Listings
        </h2>
      </div>
      <ReferencePropertiesList></ReferencePropertiesList>
    </div>
    <HtocFaqSection></HtocFaqSection>
    <!-- Call to Action -->
    <div class="bg-gradient text-white text-center q-pa-lg">
      <div>
        <h2 class="text-h5 q-mb-md">
          Join the Revolution in Real Estate Comparables
        </h2>
        <q-btn-group unelevated>
          <q-btn label="Get Early Access"
                 color="yellow"
                 text-color="dark"
                 size="lg"
                 class="q-mr-md" />
          <q-btn label="Request a Demo"
                 color="white"
                 text-color="dark"
                 size="lg" />
        </q-btn-group>
      </div>
    </div>
  </q-page>
</template>

<script>
import PostcodeClustersList from 'src/concerns/postcode-clusters/components/PostcodeClustersList.vue'
import ReferencePropertiesList from 'src/concerns/reference-properties/components/ReferencePropertiesList.vue'
// import ReferencePropertiesList from 'src/concerns/reference-properties/pages/ReferencePropertiesList.vue'
import HtocFaqSection from 'src/concerns/marketing/components/final/HtocFaqSection.vue'
import HtocHero from 'src/concerns/marketing/components/final/HtocHero.vue'
import HowHtocWorks from 'src/concerns/marketing/components/final/HowHtocWorks.vue'
import WhoIsHtocFor from 'src/concerns/marketing/components/final/WhoIsHtocFor.vue'
import WhyNuneaton from 'src/concerns/marketing/components/final/WhyNuneaton.vue'
export default {
  components: {
    PostcodeClustersList,
    ReferencePropertiesList,
    HtocFaqSection,
    HowHtocWorks,
    HtocHero,
    WhoIsHtocFor,
    WhyNuneaton,
  },
  methods: {
    goToPage(page) {
      this.$router.push(`/${page}`)
    },
  },
}
</script>

<style>
.bg-gradient {
  background: linear-gradient(to right, #2196f3, #673ab7);
}
</style>
