<template>
  <div class="resume-section"
       id="epc"
       style="padding-top: 105px;">
    <div style="height: 100%;">
      <div class="resume-section-inner epc-floorplan-wrapper flex-warp">
        <div class="epc-text">
          The EPC report provides performance data to help you understand the property’s energy efficiency, and can also
          provide useful additional data regarding power outlets, glazing, hot water and heating throughout the
          property.
        </div>
        <div class="top-info-group epc-report-image">
          <div class="section-header epchead text-medium">EPC</div>
          <div class="border-right">
            <div>
              <div class="epc-box">
                <div class="epc-rating-box">
                  <p class="epc-rating-title">Energy rating</p>
                  <p class="epc-rating-result">{{ currentRatingLetter }}</p>
                </div>
                <p class="epc-address">{{ epcData.address }}</p>
                <p class="epc-extra-box epc-extra-box1">Valid until {{ epcData.validUntil }}</p>
              </div>
            </div>
            <svg width="100%"
                 viewBox="0 0 615 376"
                 xmlns="http://www.w3.org/2000/svg"
                 style="max-width: 100%;"
                 aria-labelledby="svg-title"
                 role="img"
                 class="br-top">
              <title id="svg-title">Your energy rating is {{ currentRatingLetter }}({{ epcData.currentRating }}) and
                your potential is {{ potentialRatingLetter }}({{ epcData.potentialRating }})</title>
              <line x1="72"
                    y1="0"
                    x2="72"
                    y2="376"
                    style="stroke: #b1b4b6; stroke-width: 1;"></line>
              <line x1="410"
                    y1="0"
                    x2="410"
                    y2="376"
                    style="stroke: #b1b4b6; stroke-width: 1;"></line>
              <line x1="510"
                    y1="0"
                    x2="510"
                    y2="376"
                    style="stroke: #b1b4b6; stroke-width: 1;"></line>
              <line x1="0"
                    y1="25"
                    x2="615"
                    y2="25"
                    style="stroke: #b1b4b6; stroke-width: 1;"></line>
              <line x1="0"
                    y1="0"
                    x2="615"
                    y2="0"
                    style="stroke: #b1b4b6; stroke-width: 1;"></line>
              <line x1="0"
                    y1="0"
                    x2="615"
                    y2="0"
                    style="stroke: #b1b4b6; stroke-width: 1;"></line>
              <line x1="0"
                    y1="0"
                    x2="0"
                    y2="376"
                    style="stroke: #b1b4b6; stroke-width: 1;"></line>
              <line x1="615"
                    y1="376"
                    x2="615"
                    y2="0"
                    style="stroke: #b1b4b6; stroke-width: 1;"></line>
              <line x1="615"
                    y1="376"
                    x2="0"
                    y2="376"
                    style="stroke: #b1b4b6; stroke-width: 1;"></line>
              <rect width="78"
                    height="50"
                    x="72"
                    y="25"
                    class="band-A"></rect>
              <rect width="118"
                    height="50"
                    x="72"
                    y="75"
                    class="band-B"></rect>
              <rect width="158"
                    height="50"
                    x="72"
                    y="125"
                    class="band-C"></rect>
              <rect width="198"
                    height="50"
                    x="72"
                    y="175"
                    class="band-D"></rect>
              <rect width="238"
                    height="50"
                    x="72"
                    y="225"
                    class="band-E"></rect>
              <rect width="278"
                    height="50"
                    x="72"
                    y="275"
                    class="band-F"></rect>
              <rect width="318"
                    height="50"
                    x="72"
                    y="325"
                    class="band-G"></rect>
              <rect width="72"
                    height="50"
                    x="0"
                    y="25"
                    class="band-A-score"></rect>
              <rect width="72"
                    height="50"
                    x="0"
                    y="75"
                    class="band-B-score"></rect>
              <rect width="72"
                    height="50"
                    x="0"
                    y="125"
                    class="band-C-score"></rect>
              <rect width="72"
                    height="50"
                    x="0"
                    y="175"
                    class="band-D-score"></rect>
              <rect width="72"
                    height="50"
                    x="0"
                    y="225"
                    class="band-E-score"></rect>
              <rect width="72"
                    height="50"
                    x="0"
                    y="275"
                    class="band-F-score"></rect>
              <rect width="72"
                    height="50"
                    x="0"
                    y="325"
                    class="band-G-score"></rect>
              <text x="0"
                    y="0"
                    class="letter">
                <tspan x="107"
                       y="64">A</tspan>
                <tspan x="147"
                       y="114">B</tspan>
                <tspan x="187"
                       y="164">C</tspan>
                <tspan x="227"
                       y="214">D</tspan>
                <tspan x="267"
                       y="264">E</tspan>
                <tspan x="307"
                       y="314">F</tspan>
                <tspan x="347"
                       y="364">G</tspan>
              </text>
              <text x="0"
                    y="0"
                    class="small">
                <tspan x="8"
                       y="55">92+</tspan>
                <tspan x="8"
                       y="105">81-91</tspan>
                <tspan x="8"
                       y="155">69-80</tspan>
                <tspan x="8"
                       y="205">55-68</tspan>
                <tspan x="8"
                       y="255">39-54</tspan>
                <tspan x="8"
                       y="305">21-38</tspan>
                <tspan x="8"
                       y="355">1-20</tspan>
              </text>
              <text x="8"
                    y="14"
                    class="small small-caption"
                    dominant-baseline="middle">Score</text>
              <text x="85"
                    y="14"
                    class="small small-caption"
                    dominant-baseline="middle">Energy rating</text>
              <text x="460"
                    y="14"
                    class="small small-caption"
                    text-anchor="middle"
                    dominant-baseline="middle">Current</text>
              <text x="565"
                    y="14"
                    class="small small-caption"
                    text-anchor="middle"
                    dominant-baseline="middle">Potential</text>
              <svg :x="currentX"
                   :y="currentY"
                   width="90"
                   height="50">
                <rect width="75"
                      height="50"
                      :class="currentBandClass"
                      x="25"></rect>
                <polygon points="0,25 25,50 25,0 0,25"
                         :class="currentBandClass"></polygon>
                <text x="18"
                      y="32"
                      class="current-potential-number">{{ epcData.currentRating }} |</text>
                <text x="67"
                      y="32"
                      class="small-letter">{{ currentRatingLetter }}</text>
              </svg>
              <svg :x="potentialX"
                   :y="potentialY"
                   width="90"
                   height="50">
                <rect width="75"
                      height="50"
                      :class="potentialBandClass"
                      x="25"></rect>
                <polygon points="0,25 25,50 25,0 0,25"
                         :class="potentialBandClass"></polygon>
                <text x="18"
                      y="32"
                      class="current-potential-number">{{ epcData.potentialRating }} |</text>
                <text x="67"
                      y="32"
                      class="small-letter">{{ potentialRatingLetter }}</text>
              </svg>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  mounted() {
    const urlParams = new URLSearchParams(window.location.search);
    const currentRating = parseInt(urlParams.get('currentRating'), 10);
    const potentialRating = parseInt(urlParams.get('potentialRating'), 10);

    if (!isNaN(currentRating)) {
      this.epcData.currentRating = currentRating;
    }
    if (!isNaN(potentialRating)) {
      this.epcData.potentialRating = potentialRating;
    }
  },
  data() {
    return {
      epcData: {
        currentRating: 44,
        potentialRating: 64,
        address: 'Taylor Court, Carrville, DH1',
        validUntil: '01.07.2028',
      },
      bandPositions: {
        A: 25,
        B: 75,
        C: 125,
        D: 175,
        E: 225,
        F: 275,
        G: 325,
      },
      ratingBands: {
        A: { min: 92 },
        B: { min: 81, max: 91 },
        C: { min: 69, max: 80 },
        D: { min: 55, max: 68 },
        E: { min: 39, max: 54 },
        F: { min: 21, max: 38 },
        G: { min: 1, max: 20 },
      }
    };
  },
  computed: {
    currentRatingBand() {
      return this.getRatingBand(this.epcData.currentRating);
    },
    potentialRatingBand() {
      return this.getRatingBand(this.epcData.potentialRating);
    },
    currentRatingLetter() {
      return this.currentRatingBand ? this.currentRatingBand : '';
    },
    potentialRatingLetter() {
      return this.potentialRatingBand ? this.potentialRatingBand : '';
    },
    currentY() {
      return this.currentRatingBand ? this.bandPositions[this.currentRatingBand] : 0;
    },
    potentialY() {
      return this.potentialRatingBand ? this.bandPositions[this.potentialRatingBand] : 0;
    },
    currentX() {
      return 415; // Fixed X position for current rating
    },
    potentialX() {
      return 515; // Fixed X position for potential rating
    },
    currentBandClass() {
      return this.currentRatingBand ? `band-${this.currentRatingBand}` : '';
    },
    potentialBandClass() {
      return this.potentialRatingBand ? `band-${this.potentialRatingBand}` : '';
    }
  },
  methods: {
    getRatingBand(rating) {
      for (const band in this.ratingBands) {
        const bandRange = this.ratingBands[band];
        if (bandRange.min <= rating && (bandRange.max === undefined || rating <= bandRange.max)) {
          return band;
        }
      }
      return 'G'; // Default to lowest band if rating is out of range (e.g., 0 or negative - assuming G is lowest)
    }
  },
};
</script>
<style>
.epchead {
  margin-bottom: 20px;
  padding-bottom: 10px;
}

.additionaldata {
  border-collapse: collapse;
}

.additionaldata tr td {
  font-weight: 300;
  font-size: 14px;
  text-align: left;
  padding: 8px 8px;
  border-bottom: 1px solid #ccc;
}

.additionaldata tr td.text-medium {
  font-weight: 500;
}

.br-top {
  border-top: 1px solid #ccc;
  padding: 10px 0px 10px 0px;
}

.epc-text {
  flex: 0 0 100%;
  padding: 0 10px 10px;
}

.flex-warp {
  flex-wrap: wrap;
}

.epc-report-image {
  padding: 10px;
  width: 100%;
}

.epc-report-image1 {
  padding: 10px;
  width: 100%;
}

.epc-floorplan-wrapper {
  display: block !important;
}

@media (min-width: 900px) {
  .epc-floorplan-wrapper {
    display: flex !important;
  }

  .epc-report-image {
    padding: 10px;
    width: 40%;
  }

  .epc-report-image1 {
    padding: 10px;
    width: 60%;
  }
}

.epc-box {
  color: #fff;
  background: #1d70b8;
  padding: 0;
  overflow: hidden;
  font-family: "Avenir";
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin-bottom: 15px;
}

.epc-box p {
  color: #fff;
  font-weight: 400;
  line-height: 1.25;
  margin-top: 0;
  margin-bottom: 15px;
}

.epc-box .epc-address {
  padding-left: 20px;
  padding-top: 20px;
}

.epc-rating-box {
  width: 120px;
  background: #1d70b8;
  text-align: center;
  float: right;
  border: 2px solid #fff;
  margin: 10px;
}

.epc-rating-box .epc-rating-title {
  line-height: 30px;
}

.epc-box .epc-rating-box .epc-rating-result {
  line-height: 30px;
  font-size: 55px;
  font-weight: bold;
}

.epc-box .epc-extra-box {
  border-top: 2px solid #fff;
  clear: both;
  margin: 0;
  padding: 10px;
  text-align: center;
  float: left;
  width: 100%;
  font-weight: bold;
}

.letter {
  font-size: 40px;
  font-family: sans-serif;
  fill: #0b0c0c;
  font-weight: bold;
}

.current-potential-number {
  font-size: 22px;
  font-family: sans-serif;
  fill: #000;
  line-height: 50px;
  margin-top: 100px;
}

.small-letter {
  font-size: 19px;
  font-family: sans-serif;
  fill: #0b0c0c;
  font-weight: bold;
  text-align: center;
}

.small {
  font-size: 20px;
  font-family: sans-serif;
  fill: #0b0c0c;
  line-height: 50px;
  margin-top: 100px;
  font-weight: bold;
}

.small.small-caption {
  font-size: 16px;
}

.band-A {
  fill: #008054;
}

.band-B {
  fill: #19b459;
}

.band-C {
  fill: #8dce46;
}

.band-D {
  fill: #ffd500;
}

.band-E {
  fill: #fcaa65;
}

.band-F {
  fill: #ef8023;
}

.band-G {
  fill: #e9153b;
}

.band-A-score {
  fill: #67A687;
}

.band-B-score {
  fill: #72CA8B;
}

.band-C-score {
  fill: #b4df86;
}

.band-D-score {
  fill: #ffe666;
}

.band-E-score {
  fill: #fdc79b;
}

.band-F-score {
  fill: #f4ac71;
}

.band-G-score {
  fill: #f2738a;
}
</style>