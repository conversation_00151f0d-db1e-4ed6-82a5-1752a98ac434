<template>
  <div class="q-mx-xs q-py-none recent-sales">
    <q-card>
      <q-card-section>
        <div class="text-h6">Most Useful Comparable Recent Sale:</div>
      </q-card-section>
      <q-card-section>
        <p>{{ mostSimilarRecentSale.st_address }}</p>
        <p>{{ mostSimilarRecentSale.st_epc_postcode }}</p>
        <p>Sold date: {{ mostSimilarRecentSale.sold_transaction_date }}</p>
        <p>Floor area: {{ mostSimilarRecentSale.total_floor_area }}</p>
        <p>Sold price: {{ mostSimilarRecentSale.formatted_sold_price }}</p>
        <div v-if="mostSimilarRecentSale.st_epc_uprn">
          <a :href="`https://www.zoopla.co.uk/property/uprn/${mostSimilarRecentSale.st_epc_uprn}`">
            {{ `https://www.zoopla.co.uk/property/uprn/${mostSimilarRecentSale.st_epc_uprn}` }}
          </a>
        </div>
      </q-card-section>
    </q-card>

    <q-card-section>
      <h5>Other Recent Sales</h5>
      <q-list dense
              bordered
              separator>
        <q-expansion-item :default-opened="true"
                          v-for="(otherRecentSale, index) in otherRecentSales"
                          :key="index"
                          class="text-subtitle2"
                          :label="`${otherRecentSale.st_address} : ${otherRecentSale.formatted_sold_price}`">
          <q-card>
            <q-card-section class="text-body2">
              <p>{{ otherRecentSale.st_epc_postcode }}</p>
              <p>Sold date: {{ otherRecentSale.sold_transaction_date }}</p>
              <p>Floor area: {{ otherRecentSale.total_floor_area }}</p>
              <p>Sold price: {{ otherRecentSale.formatted_sold_price }}</p>
              <div v-if="otherRecentSale.st_epc_uprn">
                <a :href="`https://www.zoopla.co.uk/property/uprn/${otherRecentSale.st_epc_uprn}`">
                  {{ `https://www.zoopla.co.uk/property/uprn/${otherRecentSale.st_epc_uprn}` }}
                </a>
              </div>
            </q-card-section>
          </q-card>
        </q-expansion-item>
      </q-list>
    </q-card-section>
  </div>
</template>

<script>
export default {
  props: {
    // llmFeedback: {
    //   type: Object,
    //   default: () => { },
    // },
    mostSimilarRecentSale: {
      type: Object,
      default: () => { },
    },
    otherRecentSales: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
  }
}
</script>

<style scoped>
/* Add any specific styles for recent sales here */
</style>