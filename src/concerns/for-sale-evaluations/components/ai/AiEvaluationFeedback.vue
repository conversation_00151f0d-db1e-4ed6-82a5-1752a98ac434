<template>
  <div v-if="llmFeedback"
       class="q-mx-xs q-py-none ai-evaluation-feedback">
    <q-card-section>
      <!-- <h5 class="q-mt-md">Feedback from our AI</h5> -->
      <div class="row q-gutter-sm">
        <q-chip color="green"
                text-color="white"
                class="q-mr-sm">
          Competitive: {{ llmFeedback.is_asking_price_competitive_or_overpriced }}
        </q-chip>
        <q-chip color="blue"
                text-color="white">
          Estimated Fair Value: £{{ llmFeedback.estimated_fair_value_price }}
        </q-chip>
      </div>
    </q-card-section>
    <q-card-section>
      <div v-for="(content, reason) in llmFeedback.reasoning_content"
           :key="reason">
        <h6 class="text-weight-bold q-mb-none q-mt-lg reasoning-content-item">
          {{ capitalizeFirstLetter(reason) }}
        </h6>
        <q-list v-if="['weak_points_about_the_property', 'strong_points_about_the_property', 'questions_to_ask_the_seller'].includes(reason)"
                dense>
          <q-item v-for="(item, index) in content"
                  :key="index"
                  class="text-body1"
                  clickable>
            <q-item-section avatar>
              <q-icon :name="sectionIcon(reason)"></q-icon>
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ item }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
        <p v-else
           class="text-body1">{{ content }}</p>
      </div>
    </q-card-section>
  </div>
</template>

<script>
export default {
  props: {
    llmFeedback: {
      type: Object,
      default: () => { },
    },
    // showMainFeedback: {
    //   type: Boolean,
    //   default: false,
    // },
  },
  methods: {
    sectionIcon(feedbackTitle) {
      if (feedbackTitle === "strong_points_about_the_property") {
        return 'thumb_up'
      } else if (feedbackTitle === "weak_points_about_the_property") {
        return 'thumb_down'
      } else if (feedbackTitle === "questions_to_ask_the_seller") {
        return 'radio_button_checked'
      }
      return 'radio_button_checked'
    },
    capitalizeFirstLetter(feedbackTitle) {
      return feedbackTitle.charAt(0).toUpperCase() + feedbackTitle.slice(1).replace(/_/g, ' ');
    }
  }
}
</script>

<style scoped>
/* Add any specific styles for main feedback here */
</style>