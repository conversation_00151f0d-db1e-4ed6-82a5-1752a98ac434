<template>
  <div class="q-mx-xs q-py-none ListingDetailsBlockMain">
    <div class="q-py-sm row">
      <!-- <div>
        <div class="text-h6 col-xs-12 q-pb-md">{{ evaluationDetails.catchy_title }}</div>
        <div class="col-xs-12 col-md-6">
          <div class="text-subtitle2">{{ evaluationDetails.street_name }}</div>
          <div class="text-subtitle2">{{ evaluationDetails.street_address }}</div>
          <div class="text-subtitle2">{{ evaluationDetails.postal_code }}</div>
        </div>
      </div> -->
      <div v-if="showDossierHeader"
           class="col-xs-12 col-md-6">
        <ListingPriceAndRooms :evaluationDetails="evaluationDetails"></ListingPriceAndRooms>
      </div>
      <div class="ldbm-carousel col-xs-12">
        <ListingCarousel :carouselSlides="carouselSlides"> </ListingCarousel>
      </div>
      <div class="text-body1 q-pt-lg col-xs-12 ldb-exp-ctr">
        <q-expansion-item :default-opened="true"
                          aria-expanded="true"
                          dense
                          header-class="bg-primary text-white"
                          class="asset-part-group q-mb-md"
                          icon="location_city"
                          label="Property Description">
          <template v-slot:header>
            <div class="text-h6 q-pa-none text-capitalize full-width">

              <div class="q-py-sm"
                   style="display: flex;align-items: left;">
                <q-icon class="q-pr-sm"
                        name="dashboard" />
                <q-item-label class="q-pb-sm"
                              style="margin-top: -2px;">Property Description</q-item-label>
              </div>
              <div class="text-caption"
                   style="font-size: 0.65rem;margin-top:-6px">Click to expand/collapse</div>
            </div>
          </template>
          <div class="q-pa-md prop-desc-ctr">
            <div v-html="evaluationDetails.description"></div>
          </div>
        </q-expansion-item>
      </div>

      <q-card-section class="q-pa-none">
        <q-list class="q-mb-lg q-mt-none dense">
          <q-item style="min-height: 22px;"
                  class="text-subtitle2 q-pa-none q-ma-none"
                  v-for="(point, index) in evaluationDetails.description_bullet_points"
                  :key="index">
            <q-item-section>
              <q-item-label>{{ point }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>


      <div v-if="processedAssetParts.length"
           class="col-xs-12 q-py-sm">
        <q-expansion-item v-for="group in processedAssetParts"
                          :key="group.key"
                          :id="'group-' + group.key"
                          :label="group.name || group.key.replaceAll('_', ' ')"
                          icon="category"
                          dense
                          header-class="bg-primary text-white"
                          class="asset-part-group q-mb-md"
                          :default-opened="false"
                          aria-expanded="false">
          <template v-slot:header>
            <div class="text-h6 q-pa-none text-capitalize full-width">
              {{ group.name || group.key.replaceAll('_', ' ') }}
              <!-- <div class="text-caption"
                   style="font-size: 0.65rem;margin-top:-6px">Click to expand/collapse</div> -->
            </div>

          </template>
          <div v-for="part in group.parts"
               :key="part.id"
               :id="part.asset_part_slug"
               class="asset-part-section-outer q-pb-none q-pt-lg">
            <SingleAssetPart :assetPart="part"></SingleAssetPart>
          </div>
        </q-expansion-item>
      </div>

      <q-separator inset />

      <!-- <div id="main-dossier-map"
           class="col-xs-12 no-wrap items-center justify-around">
        <div class="q-my-lg shc-map-ctr"
             v-if="evaluationDetails">
          <ListingDetailsGoogleMap :relevantSoldTransactions="relevantSoldTransactions"
                                   :evaluationDetails="evaluationDetails" />
        </div>
      </div> -->
    </div>
  </div>
</template>

<script>
import SingleAssetPart from "src/concerns/dossiers/components/SingleAssetPart.vue"
// import ListingDetailsGoogleMap from "src/concerns/maps/components/ListingDetailsGoogleMap.vue"
import ListingPriceAndRooms from "src/concerns/for-sale-evaluations/components/listing-blocks/ListingPriceAndRooms.vue"
import ListingCarousel from "src/components/pics/ListingCarousel.vue"
import { scroll } from 'quasar'
const { getScrollTarget, setVerticalScrollPosition } = scroll

export default {
  components: {
    // ListingDetailsGoogleMap,
    ListingCarousel,
    ListingPriceAndRooms,
    SingleAssetPart
  },
  props: {
    evaluationDetails: {
      type: Object,
      default: () => { },
    },
    showDossierHeader: {
      type: Boolean,
      default: false,
    },
    relevantSoldTransactions: {
      type: Array,
      default: () => [],
    },
    realtyDossier: {
      type: Object,
      required: false,
    },
  },
  computed: {
    carouselSlides() {
      let carouselSlides = []
      let picsColl = []
      let picsOrder = []
      if (this.evaluationDetails && this.evaluationDetails.sale_listing_pics) {
        picsColl = this.evaluationDetails.sale_listing_pics
        picsOrder = this.evaluationDetails.evaluation_pics_order || []
      }

      picsColl.forEach(function (picObject, index) {
        let imageUrl = picObject.image_details.url
        if (!picObject.flag_is_hidden) {
          let sortOrder = picsOrder[picObject.uuid]
          carouselSlides.push({
            thumb: imageUrl,
            src: imageUrl,
            altText: picObject.photo_title,
            sortOrder: sortOrder,
          })
        }
      })
      return carouselSlides.sort((a, b) => a.sortOrder - b.sortOrder)
    },
    assetParts() {
      // this.realtyDossier?.primary_dossier_asset?.detailed_asset_parts || {}
      // 20 may 2025 - prefer below to above
      return this.realtyDossier?.dossier_sale_listing?.detailed_asset_parts || {}
    },
    processedAssetParts() {
      if (!this.assetParts) return [];

      // Configuration for asset part types
      const assetPartConfig = {
        bedroom: { order: 1, name: "Bedrooms", display: true },
        bathroom: { order: 6, name: "Bathrooms", display: true },
        garage: { order: 35, name: "Garage", display: true },
        kitchen: { order: 2, name: "Kitchen", display: true },
        living_room: { order: 3, name: "Living Room", display: true },
        dining_room: { order: 4, name: "Dining Room", display: true },
        hallway: { order: 5, name: "Hallway", display: true },
        default: { order: 99, name: null, display: true },
        other: { order: 99, name: "Other", display: false },
      };

      // Process asset parts
      const processed = Object.entries(this.assetParts)
        .map(([key, parts]) => {
          // Use config if key exists, otherwise default to 'default'
          const config = assetPartConfig[key] || assetPartConfig.default;
          return {
            key, // Original key for uniqueness
            name: config.name, // Display name
            order: config.order, // Order for sorting
            display: config.display, // Whether to display
            parts: Array.isArray(parts) ? parts : [parts], // Ensure parts is an array
          };
        })
        .filter(group => group.display) // Only include groups where display is true
        .sort((a, b) => a.order - b.order); // Sort by order

      return processed;
    },
  },
  methods: {
    scrollToSection(hash) {
      if (!hash) return
      const element = document.getElementById(hash.slice(1))
      if (element) {
        const target = getScrollTarget(element)
        const offset = element.offsetTop - 80 // Adjust offset for header
        setVerticalScrollPosition(target, offset, 300) // 300ms animation
      }
    }
  },
  watch: {
    '$route.hash': {
      immediate: true,
      handler(newHash) {
        this.$nextTick(() => {
          this.scrollToSection(newHash)
        })
      }
    }
  }
}
</script>

<style scoped>
.asset-part-section {
  padding: 16px 0;
}

.asset-part-group {
  margin-bottom: 24px;
}
</style>