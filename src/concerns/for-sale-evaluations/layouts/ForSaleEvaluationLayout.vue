<template>
  <q-page>
    <div class="single-bex-edit-layout-ctr">
      <div v-if="isGuestRoute">
        <q-breadcrumbs class="q-py-md q-px-sm text-blue">
          <template v-slot:separator>
            <!-- <q-icon size="1.5em" name="chevron_right" color="primary" /> -->
          </template>
          <q-breadcrumbs-el :to="{ name: 'rMyPurchaseEvaluations' }"
                            label="Back To Your Listings"
                            icon="widgets" />
          <q-icon size="1.5em"
                  name="chevron_left"
                  color="primary" />
          <!-- <q-breadcrumbs-el label="" icon="chevron_left" /> -->
        </q-breadcrumbs>
      </div>
      <div v-if="true"
           class="q-mt-lg SingleSubdomainListingLayout-tabs">
        <q-tabs v-model="activeTab"
                dense
                mobile-arrows
                class="text-grey"
                active-color="primary"
                indicator-color="primary"
                align="justify"
                narrow-indicator
                outside-arrows>
          <q-route-tab name="overview"
                       :to="viewRouteDetails"
                       label="Preview" />
          <q-route-tab name="edit-panel"
                       label="Edit"
                       :to="editEvaluationRouteDetails"
                       :key="$route.fullPath"
                       :exact="false" />
          <q-route-tab name="viewInNewWin-panel"
                       label="Open In New Window"
                       @click="viewInNewWin"
                       :key="$route.fullPath"
                       :exact="true" />
        </q-tabs>

        <q-separator />

        <q-tab-panels transition-next="fade"
                      transition-duration="1000"
                      transition-prev="slide-left"
                      :infinite="false"
                      v-model="activeTab"
                      animated>
          <q-tab-panel class="q-px-xs"
                       name="overview">
            <router-view :saleListing="saleListing" />
          </q-tab-panel>
          <q-tab-panel class="q-px-none"
                       name="edit-panel">
            <router-view :saleListing="saleListing" />
          </q-tab-panel>
        </q-tab-panels>
      </div>
      <div v-else>
        <router-view :saleListing="saleListing" />
      </div>
    </div>
  </q-page>
</template>
<script>
import useSaleEvaluations from "src/compose/useSaleEvaluations.js"
export default {
  components: {},
  methods: {
    viewInNewWin(navEvent) {
    },
    // myTweak(offset) {
    //   offset = 50
    //   return { minHeight: offset ? `calc(100vh - ${offset}px)` : "100vh" }
    // },
  },
  setup(props) {
    const { getSaleEvaluation } = useSaleEvaluations()
    // const { makeEditCall } = useEditHelper()
    return {
      getSaleEvaluation,
      // makeEditCall,
    }
  },
  props: {
    // rootDashSvt: {
    //   type: String,
    //   default: "",
    // },
  },
  mounted() {
    let retrievalObject = {
      // svt: this.rootDashSvt,
      saleListingUuid: this.$route.params.saleListingUuid
    }
    this.getSaleEvaluation(retrievalObject).then((responseObject) => {
      this.saleListing = responseObject.data.sale_listing
    })
  },
  computed: {
    isGuestRoute() {
      return true
    },
    editEvaluationRouteDetails() {
      let routeName = "rSingleEvaluationEdit"
      let editEvaluationRouteDetails = {
        name: routeName,
        params: {
          // editToken: this.editToken,
        },
      }
      return editEvaluationRouteDetails
    },
    viewRouteDetails() {
      let routeName = "rForSaleEvalDetails"
      let viewRouteDetails = {
        name: routeName,
        params: {},
      }
      return viewRouteDetails
    },
  },
  data() {
    return {
      editToken: null,
      activeTab: null,
      saleListing: null,
    }
  },
}
</script>
<style></style>
