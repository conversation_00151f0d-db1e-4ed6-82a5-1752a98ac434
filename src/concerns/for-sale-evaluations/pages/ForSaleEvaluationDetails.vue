<template>
  <q-page class="q-pa-md ForSaleEvaluationDetails">
    <!-- 23 mar 2025 - can remove this page as it is no longer useful -->
    <div v-if="saleListing"
         flat
         bordered
         class="my-card">
      <!---->

      <ListingDetailsBlockMain :showDescription="false"
                               :evaluationDetails="saleListing"></ListingDetailsBlockMain>

      <!-- <LlmEvaluationFeedback :llmFeedback="saleListing.llm_feedback"></LlmEvaluationFeedback> -->

      <div>
        <ListingDetailsChartDisplay :chartName="targetChartName"
                                    @passChartSeries="passChartSeries"
                                    @openChartModal="openChartModal" />

      </div>
    </div>
  </q-page>
</template>

<script>
import ListingDetailsChartDisplay from "src/concerns/charts/components/ListingDetailsChartDisplay.vue"
import ListingDetailsBlockMain from "src/concerns/for-sale-evaluations/components/listing-blocks/ListingDetailsBlockMain.vue"
// // import LlmEvaluationFeedback from "src/concerns/for-sale-evaluations/components/LlmEvaluationFeedback.vue"
export default {
  name: 'ForSaleEvaluationDetails',
  components: {
    ListingDetailsBlockMain,
    // LlmEvaluationFeedback,
    ListingDetailsChartDisplay
  },
  data() {
    return {}
  },
  props: {
    saleListing: {
      type: Object,
      required: false
    }
  },
  computed: {
    targetChartName() {
      return this.$route.params.targetChartName || 'price_by_floor_area'
    },
  },
  methods: {
    capitalizeFirstLetter(string) {
      return string.charAt(0).toUpperCase() + string.slice(1).replace(/_/g, ' ')
    },
    modalStateChanged(newVal) {
      this.modalOpen = newVal;
    },
    openChartModal(selectedPoint) {
      this.selectedPoint = selectedPoint
      this.modalOpen = true;
      const queryString = new URLSearchParams({ actionId: selectedPoint.SoldTransactionId }).toString();
      window.history.pushState(null, '', `?${queryString}`);
      // window.dispatchEvent(new Event('popstate'))
    },
    passChartSeries(chartSeries) {
      this.soldDataPoints = chartSeries[0].data
      // transactionLatitude = chartSeries[0].data[0].Latitude
    },
  }
};
</script>

<style scoped></style>
