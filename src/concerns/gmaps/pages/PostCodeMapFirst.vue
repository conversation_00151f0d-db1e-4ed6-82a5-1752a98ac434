<template>
  <q-page>
    <div id="map"
         style="width: 100%; height: 500px;"></div>
  </q-page>
</template>

<script>
export default {
  name: 'PostcodeGeoJsonMap',
  data() {
    return {
      postcodeAreaGeoJson: {
        primary: {
          type: 'Feature',
          geometry: {
            type: 'Polygon',
            coordinates: [
              [
                [-1.444005704102988, 52.52749642932371],
                [-1.444025859688696, 52.527396642988556],
                [-1.444335667380188, 52.52739809708167],
                [-1.444376523154933, 52.52744890635708],
                [-1.444419539387614, 52.52746816305576],
                [-1.444478302833591, 52.52750911277576],
                [-1.444565090538625, 52.527505871127076],
                [-1.444573869915274, 52.527503138877506],
                [-1.444861698137238, 52.52746243257897],
                [-1.444923190209864, 52.52750715062198],
                [-1.445066104764079, 52.52753987247718],
                [-1.445147542641271, 52.527597885670886],
                [-1.445146682086967, 52.52766620559266],
                [-1.445126768036576, 52.527726846326736],
                [-1.445098019807874, 52.52775637990246],
                [-1.445094293234513, 52.52776856591636],
                [-1.445075867173201, 52.52779712768742],
                [-1.445076871691872, 52.527817038809125],
                [-1.444991173987425, 52.527919594035104],
                [-1.44504857932245, 52.52817888672168],
                [-1.444842044323905, 52.52818285722652],
                [-1.444731737417348, 52.52817291087064],
                [-1.444661079842969, 52.52818538726227],
                [-1.444553543385526, 52.528190461330595],
                [-1.444303319979697, 52.5281545408959],
                [-1.444272639914998, 52.52814390767053],
                [-1.444115332349705, 52.528149163098085],
                [-1.444027834795638, 52.5281385180555],
                [-1.443909822234509, 52.52814561501045],
                [-1.44381623938448, 52.5281366647064],
                [-1.443786090061415, 52.52812855597415],
                [-1.443556400439257, 52.528118132565545],
                [-1.443467059760602, 52.52809950223946],
                [-1.443507871578804, 52.52790477452655],
                [-1.443469825733709, 52.527829854923105],
                [-1.443551195360876, 52.52773730117841],
                [-1.443793449000822, 52.52774582998744],
                [-1.443862993681957, 52.527768376713844],
                [-1.44386622082238, 52.52751276333243],
                [-1.444005704102988, 52.52749642932371],
              ],
            ],
          },
          properties: {
            postcodes: 'CV11 6FA',
            mapit_code: 'CV116FA',
          },
        },
      },
    };
  },
  mounted() {
    this.loadGoogleMapsScript().then(() => {
      const map = new google.maps.Map(document.getElementById('map'), {
        center: { lat: 52.52749642932371, lng: -1.444005704102988 },
        zoom: 16,
      });

      const polygonCoords = this.postcodeAreaGeoJson.primary.geometry.coordinates[0].map(
        ([lng, lat]) => ({ lat, lng })
      );

      const postcodePolygon = new google.maps.Polygon({
        paths: polygonCoords,
        strokeColor: '#007bff',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#007bff',
        fillOpacity: 0.35,
        map,
      });

      // Create an InfoWindow to display information
      const infoWindow = new google.maps.InfoWindow();

      // Event listeners for hover effects
      google.maps.event.addListener(postcodePolygon, 'mouseover', () => {
        const contentString = `
          <div>
            <h4>Postcode Information</h4>
            <p>Postcodes: ${this.postcodeAreaGeoJson.primary.properties.postcodes}</p>
            <p>Mapit Code: ${this.postcodeAreaGeoJson.primary.properties.mapit_code}</p>
          </div>
        `;
        infoWindow.setContent(contentString);
        infoWindow.setPosition(this.getCenterOfPolygon(postcodePolygon));
        infoWindow.open(map);
      });

      google.maps.event.addListener(postcodePolygon, 'mouseout', () => {
        infoWindow.close();
      });
    });
  },
  methods: {
    loadGoogleMapsScript() {
      return new Promise((resolve, reject) => {
        if (window.google && window.google.maps) {
          resolve();
        } else {
          const script = document.createElement('script');
          console.log(`GMAPS_API_KEY2 is ${process.env.GMAPS_API_KEY}`)
          script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.GMAPS_API_KEY}`;
          script.async = true;
          script.onload = resolve;
          script.onerror = reject;
          document.head.appendChild(script);
        }
      });
    },
    // Method to get the center of the polygon
    getCenterOfPolygon(polygon) {
      const bounds = new google.maps.LatLngBounds();
      polygon.getPath().forEach((path) => bounds.extend(path));
      return bounds.getCenter();
    },
  },
};
</script>