<template>
  <q-page>
    <div id="map"
         style="width: 100%; height: 500px;"></div>
  </q-page>
</template>

<script>
import useAreaClusters from "src/compose/useAreaClusters.js"
export default {
  name: 'PostcodeGeoJsonMap',
  data() {
    return {
      postcodeAreaGeoJson: null,
    };
  },
  setup(props) {
    const { getPostcodeAreas } = useAreaClusters()
    return {
      getPostcodeAreas,
    }
  },
  mounted() {
    this.loadGoogleMapsScript().then(() => {
      this.getPostcodeAreas().then((result) => {
        let postcodeAreas = result.data
        // let incomingGeoJson = result.data[0]?.postcode_area_geojson || {}
        // let secondGeoJ = result.data[1]?.postcode_area_geojson.primary || {}
        // this.postcodeAreaGeoJson = incomingGeoJson
        const currentMap = new google.maps.Map(document.getElementById('map'), {
          center: { lat: 52.52749642932371, lng: -1.444005704102988 },
          zoom: 16,
        });

        if (postcodeAreas) {
          postcodeAreas.forEach((postcodeArea) => {
            if (postcodeArea) {
              this.setBB(currentMap, postcodeArea)
            }
          });
          // this.setBB(currentMap, this.postcodeAreaGeoJson.primary);
          // this.setBB(currentMap, secondGeoJ);
        }
      });
    });
  },
  methods: {
    instantiatePolygon(map, polygonCoords, geoJsonDetails, postcodeAreaDetails) {
      const postcodePolygon = new google.maps.Polygon({
        paths: polygonCoords,
        strokeColor: '#007bff',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#007bff',
        fillOpacity: 0.35,
        map,
      });

      // Create an InfoWindow to display information
      const infoWindow = new google.maps.InfoWindow();

      // Event listeners for hover effects
      google.maps.event.addListener(postcodePolygon, 'mouseover', () => {
        const contentString = `
              <div>
                <h5>${geoJsonDetails.properties.postcodes}</h5>
                <p>Average property price: ${postcodeAreaDetails.postcode_average_property_price}</p>
                <p>Mapit Code: ${geoJsonDetails.properties.mapit_code}</p>
              </div>
            `;
        infoWindow.setContent(contentString);
        infoWindow.setPosition(this.getCenterOfPolygon(postcodePolygon));
        infoWindow.open(map);
      });

      google.maps.event.addListener(postcodePolygon, 'mouseout', () => {
        infoWindow.close();
      });
    },
    setBB(map, postcodeAreaDetails) {
      let geoJsonDetails = postcodeAreaDetails.postcode_area_geojson?.primary
      if (geoJsonDetails) {

        if (geoJsonDetails.geometry.type === "Polygon") {
          let polygonCoords = geoJsonDetails.geometry.coordinates[0].map(
            ([lng, lat]) => {
              if (typeof lat === 'number' && typeof lng === 'number') {
                return { lat, lng };
              } else {
                console.log(`Invalid coordinates: lat=${lat}, lng=${lng}`);
                // console.error(`Invalid coordinates: lat=${lat}, lng=${lng}`);
                return null;
              }
            }
          ).filter(coord => coord !== null);
          this.instantiatePolygon(map, polygonCoords, geoJsonDetails, postcodeAreaDetails)
        }
        else if (geoJsonDetails.geometry.type === "MultiPolygon") {
          geoJsonDetails.geometry.coordinates.forEach(polyCoords => {
            let polygonCoords = polyCoords[0].map(
              ([lng, lat]) => ({ lat, lng })
            );
            this.instantiatePolygon(map, polygonCoords, geoJsonDetails, postcodeAreaDetails)

          });

          // map.data.addGeoJson(geoJsonDetails)
        }
      }
    },
    loadGoogleMapsScript() {
      return new Promise((resolve, reject) => {
        if (window.google && window.google.maps) {
          resolve();
        } else {
          const script = document.createElement('script');
          console.log(`GMAPS_API_KEY2 is ${process.env.GMAPS_API_KEY}`)
          script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.GMAPS_API_KEY}`;
          script.async = true;
          script.onload = resolve;
          script.onerror = reject;
          document.head.appendChild(script);
        }
      });
    },
    // fetchPostcodeData() {
    //   return fetch('http://damp-violet.lvh.me:3333/api/v1/sold_transactions/synthetic')
    //     .then(response => {
    //       if (!response.ok) {
    //         throw new Error('Network response was not ok');
    //       }
    //       return response.json();
    //     })
    //     .then(data => {
    //       this.postcodeAreaGeoJson = data; // Assuming the response directly corresponds to postcodeAreaGeoJson structure
    //     })
    //     .catch(error => {
    //       console.error('Error fetching postcode data:', error);
    //     });
    // },
    // Method to get the center of the polygon
    getCenterOfPolygon(polygon) {
      const bounds = new google.maps.LatLngBounds();
      polygon.getPath().forEach((path) => bounds.extend(path));
      return bounds.getCenter();
    },
  },
};
</script>