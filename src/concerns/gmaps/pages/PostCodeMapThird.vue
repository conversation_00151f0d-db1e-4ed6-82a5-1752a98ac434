<template>
  <q-page>
    <div id="map"
         style="width: 100vw; height: 500px;"></div>
  </q-page>
</template>

<script>
import useReferenceProperties from "src/compose/useReferenceProperties.js"
export default {
  name: 'PostcodeGeoJsonMap',
  data() {
    return {
      postcodeAreaGeoJson: null,
    };
  },
  setup(props) {
    const { getPostcodeCluster } = useReferenceProperties()
    return {
      getPostcodeCluster,
    }
  },
  mounted() {
    this.loadGoogleMapsScript().then(() => {
      let clusterUuiddd = 'cf02d8cf-07a4-44aa-8c26-a21f0cd3ba09'
      this.getPostcodeCluster(clusterUuiddd).then((result) => {
        let postcodeAreas = result.data.postcode_areas
        let centerLatitude = parseFloat(result.data.center_latitude)
        let centerLongitude = parseFloat(result.data.center_longitude)

        const currentMap = new google.maps.Map(document.getElementById('map'), {
          center: { lat: centerLatitude, lng: centerLongitude },
          // center: { lat: 52.52749642932371, lng: -1.444005704102988 },
          zoom: 16,
        });

        let lastPolygonCoords = null
        if (postcodeAreas) {
          postcodeAreas.forEach((postcodeArea) => {
            if (postcodeArea) {
              lastPolygonCoords = this.setBB(currentMap, postcodeArea)
            }
          });
          // this.setBB(currentMap, this.postcodeAreaGeoJson.primary);
          // this.setBB(currentMap, secondGeoJ);
        }
        // dec 2024 - Below is not perfect but works well if 
        // centerLatitude and centerLng are not available
        var bounds = new google.maps.LatLngBounds();
        var i;
        for (i = 0; i < lastPolygonCoords.length; i++) {
          bounds.extend(lastPolygonCoords[i]);
        }
        currentMap.setCenter(bounds.getCenter())
        // console.log(bounds.getCenter());
        // // Calculate the centroid
        // const centroid = google.maps.geometry.spherical.computeCentroid(postcodePolygon.getPath());
        // // Now you can use the centroid
        // console.log(`Centroid: Latitude: ${centroid.lat()}, Longitude: ${centroid.lng()}`);

      });
    });
  },
  methods: {
    instantiatePolygon(map, polygonCoords, geoJsonDetails, postcodeAreaDetails) {
      const postcodePolygon = new google.maps.Polygon({
        paths: polygonCoords,
        strokeColor: '#007bff',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#007bff',
        fillOpacity: 0.35,
        map,
      });

      // Create an InfoWindow to display information
      const infoWindow = new google.maps.InfoWindow();

      // Event listeners for hover effects
      google.maps.event.addListener(postcodePolygon, 'mouseover', () => {
        const contentString = `
              <div>
                <h5>${geoJsonDetails.properties.postcodes}</h5>
                <p>Average property price: ${postcodeAreaDetails.postcode_average_property_price}</p>
                <p>Mapit Code: ${geoJsonDetails.properties.mapit_code}</p>
              </div>
            `;
        infoWindow.setContent(contentString);
        infoWindow.setPosition(this.getCenterOfPolygon(postcodePolygon));
        infoWindow.open(map);
      });

      google.maps.event.addListener(postcodePolygon, 'mouseout', () => {
        infoWindow.close();
      });
      return postcodePolygon
    },
    setBB(map, postcodeAreaDetails) {
      let lastPolygonCoords = null
      let geoJsonDetails = postcodeAreaDetails.postcode_area_geojson?.primary
      if (geoJsonDetails) {

        if (geoJsonDetails.geometry.type === "Polygon") {
          let polygonCoords = geoJsonDetails.geometry.coordinates[0].map(
            ([lng, lat]) => {
              if (typeof lat === 'number' && typeof lng === 'number') {
                return { lat, lng };
              } else {
                console.log(`Invalid coordinates: lat=${lat}, lng=${lng}`);
                // console.error(`Invalid coordinates: lat=${lat}, lng=${lng}`);
                return null;
              }
            }
          ).filter(coord => coord !== null);
          lastPolygonCoords = polygonCoords
          this.instantiatePolygon(map, polygonCoords, geoJsonDetails, postcodeAreaDetails)
        }
        else if (geoJsonDetails.geometry.type === "MultiPolygon") {
          geoJsonDetails.geometry.coordinates.forEach(polyCoords => {
            let polygonCoords = polyCoords[0].map(
              ([lng, lat]) => ({ lat, lng })
            );
            lastPolygonCoords = polygonCoords
            this.instantiatePolygon(map, polygonCoords, geoJsonDetails, postcodeAreaDetails)

          });

          // map.data.addGeoJson(geoJsonDetails)
        }
      }
      return lastPolygonCoords
    },
    loadGoogleMapsScript() {
      return new Promise((resolve, reject) => {
        if (window.google && window.google.maps) {
          resolve();
        } else {
          const script = document.createElement('script');
          console.log(`GMAPS_API_KEY2 is ${process.env.GMAPS_API_KEY}`)
          script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.GMAPS_API_KEY}`;
          script.async = true;
          script.onload = resolve;
          script.onerror = reject;
          document.head.appendChild(script);
        }
      });
    },
    // Method to get the center of the polygon
    getCenterOfPolygon(polygon) {
      const bounds = new google.maps.LatLngBounds();
      polygon.getPath().forEach((path) => bounds.extend(path));
      return bounds.getCenter();
    },
  },
};
</script>