<template>
  <section id="faq"
           class="faq-section q-py-xl">
    <div class="container q-mx-auto q-px-md">
      <h2 class="text-h3 text-center q-mb-xl">Frequently Asked Questions</h2>

      <div class="row justify-center">
        <div class="col-12 col-md-8">
          <q-list bordered
                  separator>
            <q-expansion-item v-for="(item, index) in faqItems"
                              :key="index"
                              :label="item.question"
                              header-class="text-h6">
              <q-card>
                <q-card-section>
                  <div v-html="item.answer"></div>
                </q-card-section>
              </q-card>
            </q-expansion-item>
          </q-list>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const faqItems = [
  {
    question: 'What do I get exactly?',
    answer: 'You get lifetime access to the CodeFast course, which includes step-by-step lessons, real-world projects (SaaS), and guidance to build and launch your own online business, even if you\'re a complete beginner.'
  },
  {
    question: 'Who is this course for?',
    answer: 'This course is for anyone who wants to learn to code and build their own apps, whether you\'re a beginner or someone tired of long, theory-heavy courses made for the pre-AI world.'
  },
  {
    question: 'Is there a refund policy?',
    answer: 'Yes, you can get a refund if you\'ve completed less than 10% of the course. Just contact us within 7 days of purchase.'
  },
  {
    question: 'What tech stack will I learn?',
    answer: 'Frontend: React, Next.js, Tailwind CSS<br>Backend: Next.js API, MongoDB, Stripe/LemonSqueezy for payments'
  }
]
</script>