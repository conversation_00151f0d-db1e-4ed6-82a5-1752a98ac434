<template>
  <section class="features-section q-py-xl bg-dark text-white">
    <div class="container q-mx-auto text-center q-px-md">
      <h2 class="text-h3 q-mb-xl">
        Coding courses are designed for
        <span class="text-red-300 italic">software engineers</span>, not
        <span class="text-green-300 italic">entrepreneurs</span>
      </h2>

      <div class="row q-col-gutter-lg justify-center">
        <div class="col-12 col-md-5">
          <q-card class="bg-red-9 text-white">
            <q-card-section>
              <div class="text-h6 flex items-center justify-between">
                Coding as an employee
                <q-icon name="close"
                        size="md"
                        class="text-red-400" />
              </div>
              <q-list>
                <q-item v-for="(item, index) in employeeFeatures"
                        :key="index">
                  <q-item-section avatar>
                    <q-icon name="remove" />
                  </q-item-section>
                  <q-item-section>{{ item }}</q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-5">
          <q-card class="bg-green-9 text-white">
            <q-card-section>
              <div class="text-h6 flex items-center justify-between">
                Coding as an entrepreneur
                <q-icon name="check_circle"
                        size="md"
                        class="text-green-400" />
              </div>
              <q-list>
                <q-item v-for="(item, index) in entrepreneurFeatures"
                        :key="index">
                  <q-item-section avatar>
                    <q-icon name="check" />
                  </q-item-section>
                  <q-item-section>{{ item }}</q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const employeeFeatures = [
  'Invert binary trees',
  'Master 47 sorting algorithms you\'ll never implement',
  'Memorize Big O notation to impress your interviewer',
  'Read documentation longer than The Lord of the Rings',
  'Write complex code when a simple AI prompt would do'
]

const entrepreneurFeatures = [
  'Learn only the fundamentals',
  'Use AI to code for you',
  'Keep learning on the fly'
]
</script>