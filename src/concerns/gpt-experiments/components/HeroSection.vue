<template>
  <section class="hero-section q-py-xl">
    <div class="container q-mx-auto text-center">
      <div class="row justify-center q-mb-lg">
        <div class="col-12 col-md-8">
          <div class="flex items-center justify-center q-mb-md">
            <q-avatar size="32px">
              <!-- <img src="~assets/avatar.jpg" alt="Marc"> -->
            </q-avatar>
            <span class="q-ml-sm text-weight-medium">By <PERSON></span>
          </div>

          <h1 class="text-h2 text-weight-black q-mb-md">
            Learn to code
            <span class="highlight-text">in weeks,</span>
            not months
          </h1>

          <p class="text-h6 text-grey-7 q-mb-xl">
            Everything you need to build your SaaS or any online business—even as a complete beginner.
          </p>

          <div class="q-gutter-y-md">
            <q-btn color="primary"
                   label="Get instant access"
                   size="lg"
                   href="#pricing"
                   class="full-width"
                   style="max-width: 300px" />
            <p class="text-grey-7">
              <span class="text-weight-bold text-dark">277</span>
              entrepreneurs love the course
            </p>
          </div>
        </div>
      </div>

      <timeline-steps />
    </div>
  </section>
</template>

<script setup>
import TimelineSteps from './TimelineSteps.vue'
</script>

<style lang="scss" scoped>
.hero-section {
  background: #fff;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.highlight-text {
  position: relative;
  display: inline-block;
  z-index: 1;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -8px;
    right: -8px;
    height: 24px;
    background: rgba($primary, 0.2);
    z-index: -1;
  }
}
</style>