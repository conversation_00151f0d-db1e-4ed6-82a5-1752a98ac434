<template>
  <q-card :class="{ 'pricing-card-featured': featured }">
    <q-card-section>
      <div class="text-h5">{{ title }}</div>
      <div class="text-subtitle2 text-grey-7">{{ subtitle }}</div>

      <div class="pricing q-my-lg">
        <div class="row items-end">
          <div class="text-h3">${{ price }}</div>
          <div class="q-ml-sm text-grey-7 text-strike">${{ originalPrice }}</div>
        </div>
        <div class="text-caption text-grey-7">USD</div>
      </div>

      <q-list>
        <q-item v-for="(feature, index) in features" :key="index">
          <q-item-section avatar>
            <q-icon name="check" color="positive" />
          </q-item-section>
          <q-item-section>{{ feature }}</q-item-section>
        </q-item>
      </q-list>

      <q-btn
        :color="featured ? 'primary' : 'black'"
        :label="buttonText"
        class="full-width q-mt-lg"
        size="lg"
      />
    </q-card-section>
  </q-card>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  },
  subtitle: {
    type: String,
    required: true
  },
  price: {
    type: Number,
    required: true
  },
  originalPrice: {
    type: Number,
    required: true
  },
  features: {
    type: Array,
    required: true
  },
  buttonText: {
    type: String,
    required: true
  },
  featured: {
    type: Boolean,
    default: false
  }
})
</script>

<style lang="scss" scoped>
.pricing-card-featured {
  border: 2px solid $primary;
  transform: scale(1.05);
}
</style>