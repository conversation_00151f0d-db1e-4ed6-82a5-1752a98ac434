<template>
  <section id="pricing" class="pricing-section q-py-xl">
    <div class="container q-mx-auto q-px-md">
      <div class="text-center q-mb-xl">
        <div class="text-h3 q-mb-md">Code your idea fast, build your freedom</div>
        <p class="text-h6 text-grey-7">Everything you need to turn your idea into an online business, fast.</p>
      </div>

      <div class="row q-col-gutter-xl justify-center">
        <div class="col-12 col-md-5">
          <pricing-card
            title="CodeFast Course"
            subtitle="Learn to build your ideas, fast"
            :price="149"
            :original-price="299"
            :features="basicFeatures"
            button-text="Get instant access"
          />
        </div>

        <div class="col-12 col-md-5">
          <pricing-card
            title="CodeFast Course + ShipFast"
            subtitle="Ship your ideas even faster"
            :price="299"
            :original-price="648"
            :features="bundleFeatures"
            button-text="Get CodeFast + ShipFast"
            featured
          />
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import PricingCard from './PricingCard.vue'

const basicFeatures = [
  '12 hours of content (211 videos)',
  '3 modules: The Mindset 💡, The Fundamentals 🏠, Your First SaaS 🏰',
  'Private Discord community',
  'Lifetime updates'
]

const bundleFeatures = [
  'Everything in the course',
  'ShipFast codebase ($349 value)',
  'Used by 5,000+ developers',
  'Ship startups in days, not weeks'
]
</script>