<template>
  <q-card :class="`bg-${testimonial.background}`">
    <q-card-section>
      <div class="row items-center q-mb-md">
        <q-avatar size="56px">
          <q-img :src="testimonial.image" :alt="testimonial.name" />
        </q-avatar>
        <div class="q-ml-md">
          <div class="text-h6">{{ testimonial.name }}</div>
          <div class="text-subtitle2">
            Built
            <a :href="`https://${testimonial.project}`" target="_blank" class="text-white">
              {{ testimonial.project }}
            </a>
          </div>
        </div>
      </div>
      <q-separator dark />
      <div class="text-h6 q-pt-md">
        "{{ testimonial.quote }}"
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup>
defineProps({
  testimonial: {
    type: Object,
    required: true
  }
})
</script>