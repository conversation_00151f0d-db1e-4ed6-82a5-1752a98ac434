<template>
  <section class="testimonials-section q-py-xl">
    <div class="container q-mx-auto">
      <h2 class="text-h3 text-center q-mb-xl">
        {{ totalStudents }} students learned to code, fast
      </h2>

      <div class="row q-col-gutter-lg">
        <div v-for="testimonial in testimonials" :key="testimonial.id" class="col-12 col-md-6">
          <testimonial-card :testimonial="testimonial" />
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import TestimonialCard from './TestimonialCard.vue'

const totalStudents = 277

const testimonials = [
  {
    id: 1,
    name: '<PERSON><PERSON>',
    image: 'testimonials/jainil.jpg',
    quote: 'First sale is definitely special, especially when it\'s your first self coded web-app.',
    project: 'confettisaas.pro',
    background: 'accent-orange'
  },
  {
    id: 2,
    name: '<PERSON><PERSON>',
    image: 'testimonials/adsy.jpg',
    quote: 'I\'ve never finished Udemy courses... <PERSON> cuts the BS and teaches you only the most important parts.',
    project: 'indielaunch.ch',
    background: 'accent-green'
  }
]
</script>