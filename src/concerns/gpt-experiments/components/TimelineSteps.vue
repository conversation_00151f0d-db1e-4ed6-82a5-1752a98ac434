<template>
  <div class="timeline-steps q-mt-xl">
    <div class="row q-col-gutter-lg">
      <div v-for="(step, index) in steps" :key="index" class="col-12 col-sm-6 col-md-3">
        <div class="text-center">
          <q-img
            :src="step.icon"
            :ratio="1"
            class="timeline-icon q-mb-md"
            style="max-width: 120px; margin: 0 auto"
          />
          <h6 class="text-weight-bold q-mb-sm">{{ step.day }}</h6>
          <p class="text-grey-7">{{ step.description }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const steps = [
  {
    icon: 'icons/gear.png',
    day: 'Day 1',
    description: 'Learn the fundamentals of coding'
  },
  {
    icon: 'icons/server.png',
    day: 'Day 4',
    description: 'Log in users and save in database'
  },
  {
    icon: 'icons/credit-card.png',
    day: 'Day 9',
    description: 'Set up subscription payments'
  },
  {
    icon: 'icons/rocket.png',
    day: 'Day 14',
    description: 'Launch your idea!'
  }
]
</script>

<style lang="scss" scoped>
.timeline-steps {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    top: 100px;
    left: 25%;
    right: 25%;
    height: 2px;
    background: $primary;
    display: none;
    @media (min-width: 1024px) {
      display: block;
    }
  }
}

.timeline-icon {
  position: relative;
  background: white;
  border-radius: 50%;
  padding: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
</style>