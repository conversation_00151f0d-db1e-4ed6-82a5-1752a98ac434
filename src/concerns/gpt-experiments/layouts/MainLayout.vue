<template>
  <q-layout view="lHh Lpr lFf">
    <q-header elevated
              class="bg-white text-dark">
      <q-toolbar>
        <q-toolbar-title>
          <q-avatar>
            <!-- <img src="~assets/logo.png"
                 alt="Logo"> -->
          </q-avatar>
          <span class="text-weight-bold q-ml-sm">CodeFast</span>
        </q-toolbar-title>

        <div class="gt-sm">
          <q-btn flat
                 label="Reviews"
                 href="#testimonials" />
          <q-btn flat
                 label="Pricing"
                 href="#pricing" />
          <q-btn flat
                 label="FAQ"
                 href="#faq" />
          <q-btn outline
                 label="Sign in"
                 class="q-ml-md" />
          <q-btn color="primary"
                 label="Get CodeFast"
                 class="q-ml-sm"
                 href="#pricing" />
        </div>

        <q-btn flat
               round
               dense
               icon="menu"
               class="lt-md"
               @click="toggleDrawer" />
      </q-toolbar>
    </q-header>

    <q-drawer v-model="drawer"
              side="right"
              bordered
              class="lt-md">
      <q-list>
        <q-item clickable
                href="#testimonials">
          <q-item-section>Reviews</q-item-section>
        </q-item>
        <q-item clickable
                href="#pricing">
          <q-item-section>Pricing</q-item-section>
        </q-item>
        <q-item clickable
                href="#faq">
          <q-item-section>FAQ</q-item-section>
        </q-item>
        <q-separator />
        <q-item>
          <q-item-section>
            <q-btn outline
                   label="Sign in"
                   class="full-width q-mb-sm" />
            <q-btn color="primary"
                   label="Get CodeFast"
                   class="full-width"
                   href="#pricing" />
          </q-item-section>
        </q-item>
      </q-list>
    </q-drawer>

    <q-page-container>
      <router-view />
    </q-page-container>

    <!-- Footer Section -->
    <!-- <q-footer class="footer-section q-pa-md bg-grey-9 text-white">
      <div class="row justify-around">
        <div class="col-12 col-md-4">
          <h4 class="text-h6">Synthetic Comparables</h4>
          <p>Revolutionizing real estate with AI-powered insights.</p>
        </div>
        <div class="col-12 col-md-4">
          <h4 class="text-h6">Quick Links</h4>
          <q-list bordered>
            <q-item v-for="link in ['Features', 'Pricing', 'API Docs', 'Contact']"
                    :key="link">
              <q-item-section>{{ link }}</q-item-section>
            </q-item>
          </q-list>
        </div>
        <div class="col-12 col-md-4">
          <h4 class="text-h6">Contact Us</h4>
          <p>Email: <EMAIL></p>
          <p>Phone: ****** 567 890</p>
        </div>
      </div>
    </q-footer> -->
    <!-- Footer -->
    <q-footer class="bg-grey-9 text-white q-pa-md text-center">
      <p>&copy; 2024 PropertyAI. All rights reserved. Revolutionizing real estate with AI-powered insights.</p>
      <!-- <div class="col-12 col-md-4">
        <h4 class="text-h6">Synthetic Comparables</h4>
        <p>Revolutionizing real estate with AI-powered insights.</p>
      </div> -->
      <div>
        <q-btn flat
               label="Privacy Policy"
               class="q-mr-md text-white" />
        <q-btn flat
               label="Terms of Service"
               class="q-mr-md text-white" />
        <q-btn flat
               label="Contact"
               class="text-white" />
      </div>
    </q-footer>

    <!-- <q-footer class="bg-dark text-white">
      <div class="q-pa-lg">
        <div class="row q-col-gutter-md">
          <div class="col-12 col-md-4">
            <div class="text-h6 q-mb-md">
              <q-avatar size="24px">
                <img src="~assets/logo.png" alt="Logo">
              </q-avatar>
              CodeFast
            </div>
            <p class="text-body2">Learn to code in weeks, not months.</p>
            <p class="text-caption">Made with ☕️ and 🥐 by Marc</p>
          </div>

          <div class="col-12 col-md-4">
            <div class="text-subtitle1 q-mb-md">Links</div>
            <q-list dense>
              <q-item clickable>
                <q-item-section>Support</q-item-section>
              </q-item>
              <q-item clickable>
                <q-item-section>Pricing</q-item-section>
              </q-item>
              <q-item clickable>
                <q-item-section>Course</q-item-section>
              </q-item>
            </q-list>
          </div>

          <div class="col-12 col-md-4">
            <div class="text-subtitle1 q-mb-md">Legal</div>
            <q-list dense>
              <q-item clickable>
                <q-item-section>Terms of Service</q-item-section>
              </q-item>
              <q-item clickable>
                <q-item-section>Privacy Policy</q-item-section>
              </q-item>
            </q-list>
          </div>
        </div>
      </div>
    </q-footer> -->
  </q-layout>
</template>

<script setup>
import { ref } from 'vue'

const drawer = ref(false)
const toggleDrawer = () => {
  drawer.value = !drawer.value
}
</script>