<template>
  <q-page>
    <!-- Hero Section -->
    <hero-section />

    <!-- Features Section -->
    <features-section />

    <!-- Testimonials Section -->
    <testimonials-section />

    <!-- Pricing Section -->
    <pricing-section />

    <!-- FAQ Section -->
    <faq-section />
  </q-page>
</template>

<script setup>
import HeroSection from 'src/concerns/gpt-experiments/components/HeroSection.vue'
import FeaturesSection from 'src/concerns/gpt-experiments/components/FeaturesSection.vue'
import TestimonialsSection from 'src/concerns/gpt-experiments/components/TestimonialsSection.vue'
import PricingSection from 'src/concerns/gpt-experiments/components/PricingSection.vue'
import FaqSection from 'src/concerns/gpt-experiments/components/FaqSection.vue'
</script>