<template>
  <q-page class="bg-grey-1">
    <!-- Header Section -->
    <div class="hero-section text-center q-pa-md bg-primary text-white">
      <q-toolbar>
        <q-toolbar-title>Synthetic Comparables</q-toolbar-title>
        <q-btn flat
               label="Features" />
        <q-btn flat
               label="Pricing" />
        <q-btn flat
               label="API Docs" />
        <q-btn flat
               label="Contact" />
        <q-btn color="white"
               flat
               label="Sign Up" />
      </q-toolbar>
      <div class="q-mt-lg">
        <h1 class="text-h2">Revolutionize Property Comparisons with AI</h1>
        <p class="text-subtitle2 q-mt-md">
          Generate realistic, privacy-preserving synthetic property data to
          enhance your real estate insights.
        </p>

        <h1 class="text-h2 font-bold q-mb-md">
          Unlock Real Estate Insights with AI-Powered Synthetic Properties
        </h1>
        <p class="text-subtitle2 q-mb-lg">
          Generate hyper-realistic property data while protecting individual privacy.
          Discover market trends without compromising personal information.
        </p>
        <q-btn glossy
               color="secondary"
               label="Get Started"
               class="q-mt-md" />
        <q-btn outline
               color="white"
               label="Learn More"
               class="q-mt-md" />
      </div>
    </div>

    <!-- Features Section -->
    <div class="features-section q-pa-lg">
      <h2 class="text-center text-h4 q-mb-md">Why Choose Synthetic Comparables?</h2>
      <div class="row justify-around">
        <q-card class="col-12 col-md-3 q-pa-md">
          <q-icon name="security"
                  color="primary"
                  size="3rem" />
          <h3 class="text-h6 q-mt-sm">Privacy-First Design</h3>
          <p>
            Generate synthetic properties without exposing sensitive real-world
            data.
          </p>
        </q-card>
        <q-card class="col-12 col-md-3 q-pa-md">
          <q-icon name="insights"
                  color="primary"
                  size="3rem" />
          <h3 class="text-h6 q-mt-sm">Accurate Insights</h3>
          <p>
            Gain detailed market insights with statistically accurate synthetic
            data.
          </p>
        </q-card>
        <q-card class="col-12 col-md-3 q-pa-md">
          <q-icon name="map"
                  color="primary"
                  size="3rem" />
          <h3 class="text-h6 q-mt-sm">Geographic Flexibility</h3>
          <p>
            Analyze properties by zip code, county, or metropolitan area.
          </p>
        </q-card>
      </div>
    </div>

    <!-- How It Works Section -->
    <div class="how-it-works-section q-pa-lg bg-grey-3">
      <h2 class="text-center text-h4 q-mb-md">How It Works</h2>
      <div class="row items-center q-mt-md">
        <div class="col-12 col-md-6">
          <q-img src="https://via.placeholder.com/600x400"
                 alt="Workflow Diagram" />
        </div>
        <div class="col-12 col-md-6">
          <q-timeline>
            <q-timeline-entry title="Aggregate Market Data"
                              icon="cloud_download">
              We collect and anonymize real-world data to generate insights.
            </q-timeline-entry>
            <q-timeline-entry title="Analyze Trends"
                              icon="analytics"
                              color="secondary">
              Statistical models evaluate trends within selected geographic
              boundaries.
            </q-timeline-entry>
            <q-timeline-entry title="Generate Synthetic Properties"
                              icon="build"
                              color="green">
              AI generates synthetic, anonymized properties based on market
              characteristics.
            </q-timeline-entry>
            <q-timeline-entry title="Deliver Actionable Insights"
                              icon="insights"
                              color="teal">
              Get property comparisons with detailed reports and export
              options.
            </q-timeline-entry>
          </q-timeline>
        </div>
      </div>
    </div>

    <!-- Call to Action Section -->
    <div class="cta-section text-center q-pa-lg bg-primary text-white">
      <h3 class="text-h5">Ready to Transform Real Estate Insights?</h3>
      <p>Sign up today and get started with synthetic property data.</p>
      <q-btn glossy
             color="white"
             label="Get Started for Free" />
    </div>

  </q-page>
</template>

<script>
export default {
  name: "LandingPage",
};
</script>

<style>
.hero-section {
  min-height: 50vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.features-section .q-card {
  max-width: 300px;
  text-align: center;
}

.how-it-works-section .q-timeline {
  max-width: 400px;
  margin: auto;
}

.cta-section {
  padding: 2rem 1rem;
}
</style>
