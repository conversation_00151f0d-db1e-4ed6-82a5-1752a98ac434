<template>
  <q-page class="bg-grey-1">
    <!-- Hero Section -->
    <div class="bg-gradient-to-br from-blue-6 to-purple-7 text-white text-center q-pa-lg">
      <h1 class="text-h2 font-bold q-mb-md">
        Unlock Real Estate Insights with AI-Powered Synthetic Properties
      </h1>
      <p class="text-subtitle2 q-mb-lg">
        Generate hyper-realistic property data while protecting individual privacy.
        Discover market trends without compromising personal information.
      </p>
      <!-- Email Signup -->
      <q-form @submit="handleEmailSubmit"
              class="q-mx-auto q-pb-md">
        <q-input v-model="email"
                 type="email"
                 placeholder="Enter your email for early access"
                 dense
                 outlined />
        <q-btn color="yellow-5"
               label="Get Started"
               class="q-mt-md"
               type="submit" />
      </q-form>
      <!-- Scroll Down Indicator -->
      <q-icon name="keyboard_arrow_down"
              size="2.5rem"
              class="q-mt-lg animate-bounce" />
    </div>

    <!-- How It Works Section -->
    <div class="q-pa-lg"
         id="how-it-works">
      <h2 class="text-h4 font-bold text-center q-mb-lg">How PropertyAI Works</h2>
      <div class="row justify-around">
        <q-card v-for="(step, index) in howItWorksSteps"
                :key="index"
                class="col-12 col-md-3 q-pa-md text-center"
                @click="toggleStep(index)">
          <q-icon :name="step.icon"
                  size="3rem"
                  :color="step.color" />
          <h3 class="text-h6 q-mt-md">{{ step.title }}</h3>
          <q-slide-transition>
            <p v-if="activeStep === index"
               class="text-body2 text-grey-7">
              {{ step.description }}
            </p>
          </q-slide-transition>
        </q-card>
      </div>
    </div>

    <!-- Key Features Section -->
    <div class="bg-grey-3 q-pa-lg">
      <h2 class="text-h4 font-bold text-center q-mb-lg">Why Choose PropertyAI</h2>
      <div class="row justify-around">
        <q-card class="col-12 col-md-3 q-pa-md text-center">
          <q-icon name="security"
                  size="3rem"
                  color="green" />
          <h3 class="text-h6 q-mt-md">Privacy First</h3>
          <p>100% synthetic data that protects individual property information.</p>
        </q-card>
        <q-card class="col-12 col-md-3 q-pa-md text-center">
          <q-icon name="place"
                  size="3rem"
                  color="blue" />
          <h3 class="text-h6 q-mt-md">Precise Insights</h3>
          <p>Statistically accurate market intelligence at your fingertips.</p>
        </q-card>
        <q-card class="col-12 col-md-3 q-pa-md text-center">
          <q-icon name="trending_up"
                  size="3rem"
                  color="purple" />
          <h3 class="text-h6 q-mt-md">Advanced AI</h3>
          <p>Cutting-edge machine learning models for unparalleled market analysis.</p>
        </q-card>
      </div>
    </div>

    <!-- Call to Action Section -->
    <div class="bg-gradient-to-r from-blue-6 to-purple-7 text-white q-pa-lg text-center">
      <h2 class="text-h4 font-bold q-mb-md">
        Ready to Transform Your Real Estate Intelligence?
      </h2>
      <p class="text-body1 q-mb-lg">
        Join our early access program and be at the forefront of AI-powered real estate market insights.
      </p>
      <q-btn label="Join Waitlist"
             color="white"
             text-color="blue-6" />
    </div>
  </q-page>
</template>

<script>
export default {
  name: "PropertyAILandingPage",
  data() {
    return {
      email: "",
      activeStep: null,
      howItWorksSteps: [
        {
          title: "Data Collection",
          description:
            "We aggregate anonymized statistical data from public sources.",
          icon: "storage",
          color: "blue",
        },
        {
          title: "AI Synthesis",
          description:
            "Our advanced AI models generate statistically accurate synthetic properties.",
          icon: "security",
          color: "green",
        },
        {
          title: "Market Insights",
          description:
            "Generate comprehensive market intelligence without compromising individual privacy.",
          icon: "trending_up",
          color: "purple",
        },
      ],
    };
  },
  methods: {
    handleEmailSubmit() {
      alert(
        `Thank you for your interest, ${this.email}! We'll be in touch soon.`
      );
      this.email = "";
    },
    toggleStep(index) {
      this.activeStep = this.activeStep === index ? null : index;
    },
  },
};
</script>

<style>
.hero-section .animate-bounce {
  animation: bounce 2s infinite;
}

@keyframes bounce {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}
</style>
