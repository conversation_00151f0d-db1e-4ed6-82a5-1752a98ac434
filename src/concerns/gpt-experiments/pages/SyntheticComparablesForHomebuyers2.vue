<template>
  <q-page class="bg-grey-1">
    <!-- Hero Section -->
    <div class="bg-gradient-to-r from-green-6 to-blue-8 text-white text-center q-pa-lg">
      <h1 class="text-h2 font-bold q-mb-md">
        Unlock Real Estate Insights Without Compromising Privacy
      </h1>
      <p class="text-subtitle2 q-mb-lg">
        The Property Data Revolution You've Been Waiting For
      </p>
      <p class="text-body1 q-mb-lg">
        Analyze real estate markets without exposing individual property details.
      </p>
      <q-btn glossy
             label="Get Early Access"
             color="yellow-4"
             text-color="blue-8"
             size="lg"
             class="q-mt-md" />
    </div>

    <!-- Problem Section -->
    <div class="q-pa-lg">
      <h2 class="text-h4 font-bold text-center q-mb-lg">
        The Problem Real Estate Professionals Face
      </h2>
      <p class="text-body1 text-center q-mb-lg">
        Traditional property comparison tools expose sensitive information, compromise privacy, and limit comprehensive
        market analysis. Professionals struggle to:
      </p>
      <div class="row justify-around">
        <q-card class="col-12 col-md-3 q-pa-md text-center">
          <q-icon name="shield"
                  size="3rem"
                  color="red" />
          <h3 class="text-h6 q-mt-md">Protect Privacy</h3>
          <p>Individual property details are often at risk of exposure.</p>
        </q-card>
        <q-card class="col-12 col-md-3 q-pa-md text-center">
          <q-icon name="visibility_off"
                  size="3rem"
                  color="orange" />
          <h3 class="text-h6 q-mt-md">Access Insights</h3>
          <p>Accessing full market views remains a challenge.</p>
        </q-card>
        <q-card class="col-12 col-md-3 q-pa-md text-center">
          <q-icon name="dataset"
                  size="3rem"
                  color="yellow" />
          <h3 class="text-h6 q-mt-md">Generate Comparables</h3>
          <p>Generating realistic, comprehensive comparables is limited.</p>
        </q-card>
      </div>
    </div>

    <!-- Solution Section -->
    <div class="bg-grey-3 q-pa-lg">
      <h2 class="text-h4 font-bold text-center q-mb-lg">
        Our Breakthrough Solution
      </h2>
      <p class="text-body1 text-center q-mb-lg">
        Introducing the AI Synthetic Property Comparables Platform:
      </p>
      <div class="row justify-around">
        <q-card class="col-12 col-md-3 q-pa-md text-center">
          <q-icon name="sync"
                  size="3rem"
                  color="green" />
          <h3 class="text-h6 q-mt-md">Privacy-Preserving</h3>
          <p>100% synthetic, no real property information exposed.</p>
        </q-card>
        <q-card class="col-12 col-md-3 q-pa-md text-center">
          <q-icon name="insights"
                  size="3rem"
                  color="blue" />
          <h3 class="text-h6 q-mt-md">Hyper-Local Insights</h3>
          <p>Generate geographically accurate property representations.</p>
        </q-card>
        <q-card class="col-12 col-md-3 q-pa-md text-center">
          <q-icon name="assessment"
                  size="3rem"
                  color="purple" />
          <h3 class="text-h6 q-mt-md">Risk-Free Analysis</h3>
          <p>Explore markets with zero privacy risks.</p>
        </q-card>
      </div>
    </div>

    <!-- How It Works Section -->
    <div class="q-pa-lg">
      <h2 class="text-h4 font-bold text-center q-mb-lg">How It Works</h2>
      <div class="row justify-around">
        <q-card class="col-12 col-md-3 q-pa-md text-center">
          <q-icon name="location_on"
                  size="3rem"
                  color="blue-6" />
          <h3 class="text-h6 q-mt-md">Geographically Intelligent</h3>
          <p>Generate properties by zip code, county, or metro area.</p>
        </q-card>
        <q-card class="col-12 col-md-3 q-pa-md text-center">
          <q-icon name="model_training"
                  size="3rem"
                  color="green-6" />
          <h3 class="text-h6 q-mt-md">Statistically Accurate</h3>
          <p>Advanced machine learning creates realistic profiles.</p>
        </q-card>
        <q-card class="col-12 col-md-3 q-pa-md text-center">
          <q-icon name="lock"
                  size="3rem"
                  color="purple-6" />
          <h3 class="text-h6 q-mt-md">Privacy-First Design</h3>
          <p>Zero risk of exposing real property details.</p>
        </q-card>
      </div>
    </div>

    <!-- Features Section -->
    <div class="bg-grey-3 q-pa-lg">
      <h2 class="text-h4 font-bold text-center q-mb-lg">Powerful Features, Endless Possibilities</h2>
      <div class="row justify-around">
        <q-card class="col-12 col-md-4 q-pa-md text-center">
          <q-icon name="storage"
                  size="3rem"
                  color="teal" />
          <h3 class="text-h6 q-mt-md">Comprehensive Data</h3>
          <p>Compare properties, export reports, and explore detailed insights.</p>
        </q-card>
        <q-card class="col-12 col-md-4 q-pa-md text-center">
          <q-icon name="security"
                  size="3rem"
                  color="red-6" />
          <h3 class="text-h6 q-mt-md">Uncompromising Privacy</h3>
          <p>100% synthetic data with robust privacy protections.</p>
        </q-card>
      </div>
    </div>

    <!-- CTA Section -->
    <div class="bg-gradient-to-br from-purple-7 to-blue-6 text-white text-center q-pa-lg">
      <h2 class="text-h4 font-bold q-mb-lg">
        Ready to Revolutionize Your Property Analysis?
      </h2>
      <q-btn glossy
             label="Get Early Access"
             color="yellow-4"
             text-color="blue-8"
             size="lg"
             class="q-mt-md q-mr-md" />
      <q-btn glossy
             label="Request a Demo"
             color="white"
             text-color="blue-8"
             size="lg" />
    </div>

    <!-- Footer -->
    <q-footer class="bg-grey-9 text-white q-pa-md text-center">
      <p>&copy; 2024 Synthetic Properties. All rights reserved.</p>
      <div>
        <q-btn flat
               label="Privacy Policy"
               class="text-white q-mr-md" />
        <q-btn flat
               label="Terms of Service"
               class="text-white q-mr-md" />
        <q-btn flat
               label="Contact Us"
               class="text-white" />
      </div>
    </q-footer>
  </q-page>
</template>

<script>
export default {
  name: "SyntheticPropertyLandingPage",
};
</script>

<style>
.bg-gradient-to-r {
  background: linear-gradient(to right, var(--q-color-green-6), var(--q-color-blue-8));
}

.bg-gradient-to-br {
  background: linear-gradient(to bottom right, var(--q-color-purple-7), var(--q-color-blue-6));
}
</style>
