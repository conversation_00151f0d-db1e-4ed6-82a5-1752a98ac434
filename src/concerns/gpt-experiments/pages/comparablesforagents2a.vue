<template>
  <q-page padding>
    <!-- Hero Section -->
    <div class="text-center q-mb-lg">
      <q-banner class="bg-primary text-white"
                rounded>
        <template v-slot:avatar>
          <q-icon name="business_center"
                  size="40px" />
        </template>
        <div class="text-h5">HomesToCompare for Estate Agents</div>
        <div class="text-subtitle2">
          Simplify Property Comparisons, Attract More Clients
        </div>
      </q-banner>
    </div>

    <!-- Intro Section -->
    <div class="row q-gutter-lg">
      <div class="col-12 col-md-6 flex flex-center">
        <div>
          <h1 class="text-primary">Unlock Smarter Real Estate Insights</h1>
          <p class="text-body1 q-my-md">
            HomesToCompare empowers estate agents to offer accurate property
            insights without invading privacy. Effortlessly generate synthetic
            property comparables tailored to your clients' needs and attract more
            buyers and sellers with precision data.
          </p>
          <q-btn label="Get Started"
                 color="primary"
                 push />
          <q-btn label="Learn More"
                 flat />
        </div>
      </div>
      <div class="col-12 col-md-6">
        <q-card flat
                bordered
                class="shadow-2">
          <q-img src="https://via.placeholder.com/600x400"
                 alt="Empowering Estate Agents" />
        </q-card>
      </div>
    </div>

    <!-- How It Works Section -->
    <div class="text-center q-my-xl">
      <h2 class="text-h4 text-primary">How It Works</h2>
      <p class="text-body2 q-my-sm">
        Provide unparalleled property insights to your clients in 5 simple steps.
      </p>
    </div>
    <div class="row q-gutter-lg">
      <q-timeline class="col-12 col-md-10 offset-md-1">
        <q-timeline-entry color="primary"
                          icon="location_on"
                          title="Select Location">
          Choose the postcode or neighborhood to analyze.
        </q-timeline-entry>
        <q-timeline-entry color="primary"
                          icon="list"
                          title="Browse Sold Properties">
          View recently sold properties relevant to your target market.
        </q-timeline-entry>
        <q-timeline-entry color="primary"
                          icon="compare_arrows"
                          title="Refine Comparables">
          Select properties that closely resemble your client’s ideal listing or their property.
        </q-timeline-entry>
        <q-timeline-entry color="primary"
                          icon="sync"
                          title="Generate Synthetic Listings">
          Create synthetic properties that mirror the market without exposing real data.
        </q-timeline-entry>
        <q-timeline-entry color="primary"
                          icon="bar_chart"
                          title="Use in Client Proposals">
          Impress clients with accurate, privacy-compliant insights to make confident decisions.
        </q-timeline-entry>
      </q-timeline>
    </div>

    <!-- Success Story Section -->
    <div class="q-my-xl text-center">
      <h2 class="text-h4 text-primary">Success with HomesToCompare</h2>
    </div>
    <div class="row q-gutter-lg">
      <q-card flat
              bordered
              class="col-12 col-md-10 offset-md-1">
        <q-img src="https://via.placeholder.com/800x400"
               alt="User Success Story" />
        <div class="q-pa-md">
          <h3 class="text-primary">Sarah’s Story: Closing Deals Faster</h3>
          <p class="text-body2">
            Sarah, an estate agent, used HomesToCompare to help her clients
            quickly understand the market. By showing them synthetic comparables
            generated from real sales data, she gained their trust and provided
            insights without exposing private property details. This led to faster
            decisions and more closed deals.
          </p>
        </div>
      </q-card>
    </div>

    <!-- Benefits Section -->
    <div class="q-my-xl text-center">
      <h2 class="text-h4 text-primary">Why Estate Agents Choose HomesToCompare</h2>
    </div>
    <div class="row q-gutter-lg">
      <q-card class="col-12 col-md-4 text-center"
              flat
              bordered>
        <q-icon name="privacy_tip"
                size="56px"
                color="primary" />
        <h3 class="text-h6 q-my-sm">Preserve Privacy</h3>
        <p class="text-body2">
          Synthetic comparables ensure no sensitive property details are disclosed.
        </p>
      </q-card>
      <q-card class="col-12 col-md-4 text-center"
              flat
              bordered>
        <q-icon name="speed"
                size="56px"
                color="primary" />
        <h3 class="text-h6 q-my-sm">Save Time</h3>
        <p class="text-body2">
          Generate detailed property data quickly for client proposals.
        </p>
      </q-card>
      <q-card class="col-12 col-md-4 text-center"
              flat
              bordered>
        <q-icon name="group_add"
                size="56px"
                color="primary" />
        <h3 class="text-h6 q-my-sm">Attract More Clients</h3>
        <p class="text-body2">
          Impress buyers and sellers with comprehensive, easy-to-understand insights.
        </p>
      </q-card>
    </div>

    <!-- Call to Action Section -->
    <div class="q-my-xl text-center">
      <h2 class="text-h4 text-primary">Supercharge Your Real Estate Business</h2>
      <p class="text-body2 q-my-md">
        Join top-performing estate agents who’ve transformed their client
        interactions with HomesToCompare.
      </p>
      <q-btn label="Get Started"
             color="primary"
             size="lg"
             push />
      <q-btn label="Request a Demo"
             flat
             size="lg" />
    </div>
  </q-page>
</template>

<script>
export default {
  name: "LandingPageForAgents",
};
</script>

<style scoped>
.shadow-2 {
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}
</style>
