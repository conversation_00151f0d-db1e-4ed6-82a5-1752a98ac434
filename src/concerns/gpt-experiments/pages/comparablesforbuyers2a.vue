<template>
  <q-page padding>
    <!-- Hero Section -->
    <div class="text-center q-mb-lg">
      <q-banner class="bg-primary text-white"
                rounded>
        <template v-slot:avatar>
          <q-icon name="search"
                  size="40px" />
        </template>
        <div class="text-h5">HomesToCompare for Buyers</div>
        <div class="text-subtitle2">
          Find Your Dream Home Without Overloading Your Search
        </div>
      </q-banner>
    </div>

    <div class="row q-gutter-lg">
      <div class="col-12 col-md-6 flex flex-center">
        <div>
          <h1 class="text-primary">
            Simplify Your Search for the Perfect Home
          </h1>
          <p class="text-body1 q-my-md">
            HomesToCompare empowers homebuyers by using synthetic property
            comparables to narrow down their search. No need to visit dozens of
            listings or rely on vague descriptions. Instead, create your dream
            home blueprint using actual recently sold properties, tailored to
            your needs.
          </p>
          <q-btn label="Find Your Home"
                 color="primary"
                 push />
          <q-btn label="Learn More"
                 flat />
        </div>
      </div>
      <div class="col-12 col-md-6">
        <q-card flat
                bordered
                class="shadow-2">
          <q-img src="https://via.placeholder.com/600x400"
                 alt="Streamlined Buying Process" />
        </q-card>
      </div>
    </div>

    <!-- How It Works Section -->
    <div class="text-center q-my-xl">
      <h2 class="text-h4 text-primary">How It Works</h2>
      <p class="text-body2 q-my-sm">
        Transform your search experience in 5 simple steps.
      </p>
    </div>
    <div class="row q-gutter-lg">
      <q-timeline class="col-12 col-md-10 offset-md-1">
        <q-timeline-entry color="primary"
                          icon="location_on"
                          title="Choose Your Location">
          Enter your preferred postcode or neighborhood.
        </q-timeline-entry>
        <q-timeline-entry color="primary"
                          icon="list"
                          title="Browse Sold Properties">
          Explore a curated list of recently sold homes in the area.
        </q-timeline-entry>
        <q-timeline-entry color="primary"
                          icon="check_circle"
                          title="Select Matches">
          Pick up to 3 homes that resemble your dream property.
        </q-timeline-entry>
        <q-timeline-entry color="primary"
                          icon="sync"
                          title="Create Your Dream Listing">
          We generate a synthetic property listing based on your selections.
        </q-timeline-entry>
        <q-timeline-entry color="primary"
                          icon="bar_chart"
                          title="Start Comparing">
          Use the synthetic listing to refine your search for homes currently on the market.
        </q-timeline-entry>
      </q-timeline>
    </div>

    <!-- Success Story Section -->
    <div class="q-my-xl text-center">
      <h2 class="text-h4 text-primary">Real Success with HomesToCompare</h2>
    </div>
    <div class="row q-gutter-lg">
      <q-card flat
              bordered
              class="col-12 col-md-10 offset-md-1">
        <q-img src="https://via.placeholder.com/800x400"
               alt="User Success Story" />
        <div class="q-pa-md">
          <h3 class="text-primary">Mark and Susan's Journey</h3>
          <p class="text-body2">
            Mark and Susan were overwhelmed by the number of homes on the market
            that didn’t quite fit their needs. Using HomesToCompare, they
            selected three recently sold properties that were close to their
            dream home. The platform generated a synthetic property listing that
            encapsulated their ideal layout, size, and features.
          </p>
          <p class="text-body2">
            With this as a reference, they focused their search on properties
            that matched the synthetic listing. Within weeks, they found the
            perfect home—saving time and energy while avoiding unnecessary
            compromises.
          </p>
        </div>
      </q-card>
    </div>

    <!-- Benefits Section -->
    <div class="q-my-xl text-center">
      <h2 class="text-h4 text-primary">Why Buyers Love HomesToCompare</h2>
    </div>
    <div class="row q-gutter-lg">
      <q-card class="col-12 col-md-4 text-center"
              flat
              bordered>
        <q-icon name="privacy_tip"
                size="56px"
                color="primary" />
        <h3 class="text-h6 q-my-sm">Privacy Protected</h3>
        <p class="text-body2">
          No need to share personal details or create an account to get started.
        </p>
      </q-card>
      <q-card class="col-12 col-md-4 text-center"
              flat
              bordered>
        <q-icon name="speed"
                size="56px"
                color="primary" />
        <h3 class="text-h6 q-my-sm">Efficient Search</h3>
        <p class="text-body2">
          Instantly narrow down options and save time in your property hunt.
        </p>
      </q-card>
      <q-card class="col-12 col-md-4 text-center"
              flat
              bordered>
        <q-icon name="insights"
                size="56px"
                color="primary" />
        <h3 class="text-h6 q-my-sm">Data-Driven Decisions</h3>
        <p class="text-body2">
          Make confident choices using synthetic comparables tailored to your
          preferences.
        </p>
      </q-card>
    </div>

    <!-- Call to Action Section -->
    <div class="q-my-xl text-center">
      <h2 class="text-h4 text-primary">Find Your Dream Home Today</h2>
      <p class="text-body2 q-my-md">
        Join homebuyers who’ve transformed their search process with
        HomesToCompare.
      </p>
      <q-btn label="Get Started"
             color="primary"
             size="lg"
             push />
      <q-btn label="Schedule a Demo"
             flat
             size="lg" />
    </div>
  </q-page>
</template>

<script>
export default {
  name: "LandingPage",
};
</script>

<style scoped>
.shadow-2 {
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}
</style>
