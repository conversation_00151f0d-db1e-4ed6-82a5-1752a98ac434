<template>
  <q-page padding>
    <!-- Accessibility and SEO improvements -->
    <header class="text-center q-mb-lg">
      <q-banner class="bg-primary text-white"
                rounded
                aria-labelledby="page-title">
        <template v-slot:avatar>
          <q-icon name="search"
                  size="40px"
                  aria-hidden="true" />
        </template>
        <h1 id="page-title"
            class="text-h5">HomesToCompare for Buyers</h1>
        <p class="text-subtitle2">
          Find Your Dream Home Without Overloading Your Search
        </p>
      </q-banner>
    </header>

    <!-- Hero Section with Improved Responsiveness -->
    <section class="row q-col-gutter-lg items-center">
      <div class="col-12 col-md-6 flex flex-center">
        <div class="text-content">
          <h2 class="text-primary text-h4">
            Simplify Your Search for the Perfect Home
          </h2>
          <p class="text-body1 q-my-md">
            HomesToCompare empowers homebuyers by using synthetic property
            comparables to narrow down their search. No need to visit dozens of
            listings or rely on vague descriptions. Instead, create your dream
            home blueprint using actual recently sold properties, tailored to
            your needs.
          </p>
          <div class="action-buttons q-gutter-sm">
            <q-btn label="Find Your Home"
                   color="primary"
                   push
                   to="/search"
                   aria-label="Start home search" />
            <q-btn label="Learn More"
                   flat
                   to="/about"
                   aria-label="Learn more about HomesToCompare" />
          </div>
        </div>
      </div>
      <div class="col-12 col-md-6">
        <q-card flat
                bordered
                class="home-image-card shadow-2"
                aria-label="Home search visualization">
          <!-- Replace placeholder with optimized image -->
          <q-img src="/images/home-search-hero.webp"
                 alt="Streamlined Home Buying Process"
                 loading="lazy"
                 placeholder-src="/images/home-search-placeholder.jpg" />
        </q-card>
      </div>
    </section>

    <!-- How It Works Section -->
    <section class="q-my-xl text-center">
      <h2 class="text-h4 text-primary">How It Works</h2>
      <p class="text-body2 q-my-sm">
        Transform your search experience in 5 simple steps.
      </p>

      <div class="row justify-center">
        <q-timeline class="col-12 col-md-10"
                    color="primary">
          <q-timeline-entry icon="location_on"
                            title="Choose Your Location"
                            subtitle="Enter your preferred postcode or neighborhood." />
          <q-timeline-entry icon="list"
                            title="Browse Sold Properties"
                            subtitle="Explore a curated list of recently sold homes in the area." />
          <q-timeline-entry icon="check_circle"
                            title="Select Matches"
                            subtitle="Pick up to 3 homes that resemble your dream property." />
          <q-timeline-entry icon="sync"
                            title="Create Your Dream Listing"
                            subtitle="We generate a synthetic property listing based on your selections." />
          <q-timeline-entry icon="bar_chart"
                            title="Start Comparing"
                            subtitle="Use the synthetic listing to refine your search for homes currently on the market." />
        </q-timeline>
      </div>
    </section>

    <!-- Success Story Section -->
    <section class="q-my-xl text-center">
      <h2 class="text-h4 text-primary">Real Success with HomesToCompare</h2>

      <q-card flat
              bordered
              class="success-story-card col-12 col-md-10 mx-auto">
        <q-img src="/images/success-story.webp"
               alt="Mark and Susan's Home Buying Success"
               loading="lazy"
               placeholder-src="/images/success-story-placeholder.jpg" />
        <div class="q-pa-md">
          <h3 class="text-primary text-h5">Mark and Susan's Journey</h3>
          <div class="text-body2">
            <p class="q-mb-md">
              Mark and Susan were overwhelmed by the number of homes on the market
              that didn't quite fit their needs. Using HomesToCompare, they
              selected three recently sold properties that were close to their
              dream home. The platform generated a synthetic property listing that
              encapsulated their ideal layout, size, and features.
            </p>
            <p>
              With this as a reference, they focused their search on properties
              that matched the synthetic listing. Within weeks, they found the
              perfect home—saving time and energy while avoiding unnecessary
              compromises.
            </p>
          </div>
        </div>
      </q-card>
    </section>

    <!-- Benefits Section -->
    <section class="q-my-xl text-center">
      <h2 class="text-h4 text-primary">Why Buyers Love HomesToCompare</h2>

      <div class="row q-col-gutter-lg justify-center">
        <div v-for="(benefit, index) in benefits"
             :key="index"
             class="col-12 col-md-4">
          <q-card flat
                  bordered
                  class="benefit-card full-height column items-center justify-center">
            <q-icon :name="benefit.icon"
                    size="56px"
                    color="primary"
                    class="q-mb-sm" />
            <h3 class="text-h6 q-my-sm">{{ benefit.title }}</h3>
            <p class="text-body2 text-center">
              {{ benefit.description }}
            </p>
          </q-card>
        </div>
      </div>
    </section>

    <!-- Call to Action Section -->
    <section class="q-my-xl text-center">
      <h2 class="text-h4 text-primary">Find Your Dream Home Today</h2>
      <p class="text-body2 q-my-md">
        Join homebuyers who've transformed their search process with
        HomesToCompare.
      </p>
      <div class="action-buttons q-gutter-sm">
        <q-btn label="Get Started"
               color="primary"
               size="lg"
               push
               to="/search"
               aria-label="Start home search" />
        <q-btn label="Schedule a Demo"
               flat
               size="lg"
               to="/demo"
               aria-label="Schedule a HomesToCompare demo" />
      </div>
    </section>
  </q-page>
</template>

<script>
export default {
  name: 'LandingPage',
  data() {
    return {
      benefits: [
        {
          icon: 'privacy_tip',
          title: 'Privacy Protected',
          description: 'No need to share personal details or create an account to get started.'
        },
        {
          icon: 'speed',
          title: 'Efficient Search',
          description: 'Instantly narrow down options and save time in your property hunt.'
        },
        {
          icon: 'insights',
          title: 'Data-Driven Decisions',
          description: 'Make confident choices using synthetic comparables tailored to your preferences.'
        }
      ]
    }
  },
  // Optional: Add methods for tracking CTA clicks
  methods: {
    trackGetStarted() {
      // Implement analytics tracking
      this.$gtag.event('cta_click', {
        'event_category': 'engagement',
        'event_label': 'Get Started'
      })
    },
    trackScheduleDemo() {
      // Implement analytics tracking
      this.$gtag.event('cta_click', {
        'event_category': 'engagement',
        'event_label': 'Schedule Demo'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.shadow-2 {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.home-image-card,
.success-story-card,
.benefit-card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;

  @media (max-width: 600px) {
    flex-direction: column;

    .q-btn {
      width: 100%;
      margin-bottom: 10px;
    }
  }
}
</style>