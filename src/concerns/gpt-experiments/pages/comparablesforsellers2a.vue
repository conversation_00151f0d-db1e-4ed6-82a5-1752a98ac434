<template>
  <q-page padding>
    <!-- Hero Section -->
    <div class="text-center q-mb-lg">
      <q-banner class="bg-primary text-white"
                rounded>
        <template v-slot:avatar>
          <q-icon name="home"
                  size="40px" />
        </template>
        <div class="text-h5">HomesToCompare for Sellers</div>
        <div class="text-subtitle2">
          Sell Smarter Without Sharing Every Detail About Your Property
        </div>
      </q-banner>
    </div>

    <div class="row q-gutter-lg">
      <div class="col-12 col-md-6">
        <q-card flat
                bordered
                class="shadow-2">
          <q-img src="https://via.placeholder.com/600x400"
                 alt="Streamlined Selling Process" />
        </q-card>
      </div>
      <div class="col-12 col-md-6 flex flex-center">
        <div>
          <h1 class="text-primary">
            Skip the Hassle of Revealing Full Property Details
          </h1>
          <p class="text-body1 q-my-md">
            HomesToCompare revolutionizes home selling. Forget about sharing
            every detail of your home with third-party platforms or filling out
            long, intrusive forms. Instead, select nearby sold properties that
            closely match your home, and we’ll handle the rest.
          </p>
          <q-btn label="Get Started"
                 color="primary"
                 push />
          <q-btn label="Learn More"
                 flat />
        </div>
      </div>
    </div>

    <!-- Success Story Section -->
    <div class="q-my-xl text-center">
      <h2 class="text-h4 text-primary">Real Success with HomesToCompare</h2>
    </div>
    <div class="row q-gutter-lg">
      <q-card flat
              bordered
              class="col-12 col-md-10 offset-md-1">
        <q-img src="https://via.placeholder.com/800x400"
               alt="User Success Story" />
        <div class="q-pa-md">
          <h3 class="text-primary">
            Emily's Success: Selling Without Oversharing
          </h3>
          <p class="text-body2">
            Emily wanted to sell her three-bedroom family home in a bustling
            suburb but was hesitant to share full details about her property
            online. Using HomesToCompare, she simply selected three recently
            sold properties in her postcode area that resembled her own. In
            minutes, the platform generated a synthetic comparable listing,
            helping her understand the market value of her home.
          </p>
          <p class="text-body2">
            Armed with this data, Emily confidently listed her home at the
            right price and received multiple offers within days. All without
            compromising her privacy.
          </p>
        </div>
      </q-card>
    </div>

    <!-- How It Works Section -->
    <div class="text-center q-my-xl">
      <h2 class="text-h4 text-primary">How It Works</h2>
      <p class="text-body2 q-my-sm">
        Sell your property in 5 simple steps, stress-free and private.
      </p>
    </div>
    <div class="row q-gutter-lg">
      <q-timeline class="col-12 col-md-10 offset-md-1">
        <q-timeline-entry color="primary"
                          icon="location_on"
                          title="Enter Your Location">
          Start by entering your postcode or city.
        </q-timeline-entry>
        <q-timeline-entry color="primary"
                          icon="list"
                          title="Browse Sold Properties">
          Get a curated list of recently sold properties in your area.
        </q-timeline-entry>
        <q-timeline-entry color="primary"
                          icon="check_circle"
                          title="Select Matches">
          Choose up to 3 properties most similar to your own.
        </q-timeline-entry>
        <q-timeline-entry color="primary"
                          icon="sync"
                          title="Generate Comparables">
          Receive synthetic comparable listings tailored to your home.
        </q-timeline-entry>
        <q-timeline-entry color="primary"
                          icon="bar_chart"
                          title="Analyze and Adjust">
          Review and tweak results for the perfect price range insights.
        </q-timeline-entry>
      </q-timeline>
    </div>

    <!-- Benefits Section -->
    <div class="q-my-xl text-center">
      <h2 class="text-h4 text-primary">Why Sellers Love HomesToCompare</h2>
    </div>
    <div class="row q-gutter-lg">
      <q-card class="col-12 col-md-4 text-center"
              flat
              bordered>
        <q-icon name="shield"
                size="56px"
                color="primary" />
        <h3 class="text-h6 q-my-sm">Privacy First</h3>
        <p class="text-body2">
          Select similar sold properties—no need to disclose the full details
          of your home.
        </p>
      </q-card>
      <q-card class="col-12 col-md-4 text-center"
              flat
              bordered>
        <q-icon name="timer"
                size="56px"
                color="primary" />
        <h3 class="text-h6 q-my-sm">Time-Saving</h3>
        <p class="text-body2">
          Get synthetic comparable listings in minutes—no lengthy forms or
          intrusive questions.
        </p>
      </q-card>
      <q-card class="col-12 col-md-4 text-center"
              flat
              bordered>
        <q-icon name="bar_chart"
                size="56px"
                color="primary" />
        <h3 class="text-h6 q-my-sm">Actionable Insights</h3>
        <p class="text-body2">
          Accurately estimate your home's value and list confidently.
        </p>
      </q-card>
    </div>

    <!-- Call to Action Section -->
    <div class="q-my-xl text-center">
      <h2 class="text-h4 text-primary">Start Selling Smarter Today</h2>
      <p class="text-body2 q-my-md">
        Join hundreds of home sellers who’ve streamlined their sales process
        with HomesToCompare.
      </p>
      <q-btn label="Sign Up Now"
             color="primary"
             size="lg"
             push />
      <q-btn label="Schedule a Demo"
             flat
             size="lg" />
    </div>
  </q-page>
</template>

<script>
export default {
  name: "LandingPage",
};
</script>

<style scoped>
.shadow-2 {
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
}
</style>
