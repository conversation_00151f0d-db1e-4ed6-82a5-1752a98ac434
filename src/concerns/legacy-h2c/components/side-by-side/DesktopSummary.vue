<template>
  <div class="flex flex-center desktop-summary" style="">
    <div style="width: 100%">
      <ComparisonImages2
        :leftColImage="leftColImage"
        :rightColImage="rightColImage"
        :leftColTeaser="leftColTeaser"
        :rightColTeaser="rightColTeaser"
        :leftColPriceInCents="leftColPriceInCents"
        :rightColPriceInCents="rightColPriceInCents"
        :compUuid="compUuid"
        :comparisonsHeight="comparisonsHeight"
        :routeShowSideBySide="routeShowSideBySide"
        :listItemUuid="listItemUuid"
      ></ComparisonImages2>
    </div>
  </div>
</template>
<script>
// import ComparisonImages from "./ComparisonImages.vue"
import ComparisonImages2 from "./ComparisonImages2.vue"
export default {
  components: {
    // ComparisonImages,
    ComparisonImages2,
  },
  data() {
    return {
      // leftCarouselSlideModel: "1",
      // rightCarouselSlideModel: "1",
    }
  },
  computed: {
    listItemUuid() {
      return this.comparisonDetails.curated_list_item_uuid
    },
    comparisonsHeight() {
      if (this.$q.platform.is.mobile) {
        return 250
      } else {
        return 400
      }
    },
    leftColPriceInCents() {
      if (
        this.leftColItem.summary_listing &&
        this.leftColItem.summary_listing.price_sale_current_cents
      ) {
        return this.leftColItem.summary_listing.price_sale_current_cents
      } else {
        return 0
      }
    },
    rightColPriceInCents() {
      if (
        this.rightColItem.summary_listing &&
        this.rightColItem.summary_listing.price_sale_current_cents
      ) {
        return this.rightColItem.summary_listing.price_sale_current_cents
      } else {
        return 0
      }
    },
    leftColTeaser() {
      return (
        this.comparisonDetails.teaser_title_top ||
        this.comparisonDetails.top_listing_title
      )
    },
    compUuid() {
      return this.comparisonDetails.uuid
    },
    rightColTeaser() {
      return (
        this.comparisonDetails.teaser_title_bottom ||
        this.comparisonDetails.bottom_listing_title
      )
    },
    leftColImage() {
      if (this.orderedLeftViewPics[0]) {
        return `${this.orderedLeftViewPics[0].image_details.url}`
      } else {
        return "https://media.rightmove.co.uk/49k/48904/123245402/48904_CLV211517_IMG_01_0000.jpeg"
      }
    },
    rightColImage() {
      // var picsColl = this.comparisonDetails.bottom_listing_visible_pics || []
      //  this.rightColItem.summary_listing.view_pics || []
      if (this.orderedRightViewPics[0]) {
        return `${this.orderedRightViewPics[0].image_details.url}`
      } else {
        return "https://media.rightmove.co.uk/49k/48904/123245402/48904_CLV211517_IMG_01_0000.jpeg"
      }
    },
    orderedLeftViewPics() {
      // var picsColl = this.leftColItem.listing.view_pics || []
      var picsColl = this.comparisonDetails.top_listing_visible_pics || []
      let picsOrder = []
      if (this.comparisonDetails.pic_comparisons_order) {
        picsOrder = this.comparisonDetails.pic_comparisons_order["left"] || []
      }
      picsColl.forEach((picItem) => {
        let order = picsOrder[picItem.uuid]
        picItem.order = order
      })
      // debugger
      return picsColl.sort((a, b) => a.order - b.order)
    },
    orderedRightViewPics() {
      var picsColl = this.comparisonDetails.bottom_listing_visible_pics || []
      //  this.rightColItem.listing.view_pics || []
      let picsOrder = []
      if (this.comparisonDetails.pic_comparisons_order) {
        picsOrder = this.comparisonDetails.pic_comparisons_order["right"] || []
      }
      picsColl.forEach((picItem) => {
        let order = picsOrder[picItem.uuid]
        picItem.order = order
      })
      return picsColl.sort((a, b) => a.order - b.order)
    },
  },
  watch: {},
  props: {
    routeShowSideBySide: {
      type: Object,
      default: () => {},
    },
    rightColItem: {
      type: Object,
      default: () => {
        title: ""
      },
    },
    leftColItem: {
      type: Object,
      default: () => {
        title: ""
      },
    },
    comparisonDetails: {
      type: Object,
      default: () => {},
    },
  },
}
</script>
