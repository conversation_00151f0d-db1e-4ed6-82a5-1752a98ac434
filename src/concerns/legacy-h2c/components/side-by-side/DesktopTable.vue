<template>
  <q-markup-table v-if="tableReady" separator="cell" flat bordered>
    <thead>
      <tr>
        <th colspan="2" class="text-center" style="width: 50%; padding: 0px">
          <q-responsive
            class="col"
            :ratio="16 / 9"
            style="max-width: 80vw; min-height: 30vh; margin-bottom: 25px"
          >
            <ReactiveCarousel
              :currentCarouselSlides="leftCarouselSlides"
            ></ReactiveCarousel>
            <div v-if="isDetailedPublicView">
              <div
                class="q-pa-sm"
                style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis"
              >
                {{ leftColItem.listing.title }}
              </div>
            </div>
            <!-- <router-link v-else :to="{ name: 'rHuntListingEdit' }">
              <div
                class="q-pa-sm"
                style="
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                "
              >
                {{ leftColItem.listing.title }}
              </div>
            </router-link> -->
          </q-responsive>
        </th>
        <th class="text-center" style="padding: 0px">
          <q-responsive
            class="col"
            :ratio="16 / 9"
            style="max-width: 80vw; min-height: 30vh; margin-bottom: 25px"
          >
            <ReactiveCarousel
              :currentCarouselSlides="rightCarouselSlides"
            ></ReactiveCarousel>
            <div v-if="isDetailedPublicView">
              <div
                class="q-pa-sm"
                style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis"
              >
                {{ rightColItem.listing.title }}
              </div>
            </div>
          </q-responsive>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td class="text-left" style="border-right: none">Price</td>
        <td class="text-right" style="border-left: none">
          <ConvertableCurrencyDisplay
            :priceInCents="leftColItem.listing.price_sale_current_cents"
            :originalCurrency="leftColItem.listing.currency"
          ></ConvertableCurrencyDisplay>
          <!-- {{ leftColItem.listing.formatted_display_price }} -->
        </td>
        <td class="text-left">
          <ConvertableCurrencyDisplay
            :priceInCents="rightColItem.listing.price_sale_current_cents"
            :originalCurrency="rightColItem.listing.currency"
          ></ConvertableCurrencyDisplay>
          <!-- {{ rightColItem.listing.formatted_display_price }} -->
        </td>
      </tr>
      <tr>
        <td class="text-left" style="border-right: none">Bedrooms</td>
        <td class="text-right" style="border-left: none">
          {{ leftColItem.listing.count_bedrooms }}
        </td>
        <td class="text-left">
          {{ rightColItem.listing.count_bedrooms }}
        </td>
      </tr>
      <tr>
        <td class="text-left" style="border-right: none">Bathrooms</td>
        <td class="text-right" style="border-left: none">
          {{ leftColItem.listing.count_bathrooms }}
        </td>
        <td class="text-left">
          {{ rightColItem.listing.count_bathrooms }}
        </td>
      </tr>
      <tr>
        <td class="text-left" style="border-right: none">Area</td>
        <td class="text-right" style="border-left: none">
          {{ leftColItem.listing.formatted_constructed_area }}
        </td>
        <td class="text-left">
          {{ rightColItem.listing.formatted_constructed_area }}
        </td>
      </tr>
      <tr>
        <td class="text-left" style="border-right: none">Overall Rating</td>
        <td class="text-right" style="border-left: none">
          <q-rating
            readonly
            :model-value="parseInt(leftColItem.item.rating_overall || 0)"
            size="1.5em"
            color="black"
            icon="star_border"
            icon-selected="star"
          />
        </td>
        <td class="text-left">
          <q-rating
            readonly
            :model-value="rightColItem.item.rating_overall || 0"
            size="1.5em"
            color="black"
            icon="star_border"
            icon-selected="star"
          />
        </td>
      </tr>
      <tr v-for="ratingCol in ratingCols" :key="ratingCol.name">
        <td class="text-left" style="border-right: none">
          Rating: {{ ratingCol.label }}
        </td>
        <td class="text-right" style="border-left: none">
          <q-rating
            readonly
            :model-value="leftColItem.item.ratings_breakdown[ratingCol.field]"
            size="1.5em"
            color="black"
            icon="star_border"
            icon-selected="star"
          />
        </td>
        <td class="text-left">
          <q-rating
            readonly
            :model-value="rightColItem.item.ratings_breakdown[ratingCol.field]"
            size="1.5em"
            color="black"
            icon="star_border"
            icon-selected="star"
          />
        </td>
      </tr>
      <tr v-for="checklistCol in checklistCols" :key="checklistCol.name">
        <td class="text-left" style="border-right: none">
          {{ checklistCol.label }}
        </td>
        <td class="text-right" style="border-left: none">
          <q-checkbox
            keep-color
            true-value="yes"
            false-value="no"
            readonly
            :model-value="
              leftColItem.item.checklist_values_for_features[checklistCol.field]
            "
            label=""
            color="cyan"
          />
        </td>
        <td class="text-left">
          <q-checkbox
            keep-color
            true-value="yes"
            false-value="no"
            readonly
            :model-value="
              rightColItem.item.checklist_values_for_features[checklistCol.field]
            "
            label=""
            color="cyan"
          />
        </td>
      </tr>
    </tbody>
  </q-markup-table>
</template>
<script>
import ReactiveCarousel from "src/h2c/components/ReactiveCarousel.vue"
import ConvertableCurrencyDisplay from "components/widgets/ConvertableCurrencyDisplay.vue"
export default {
  components: {
    ConvertableCurrencyDisplay,
    ReactiveCarousel,
  },
  data() {
    return {}
  },
  computed: {
    tableReady() {
      return true
      // return this.leftColItem.item.ratings_breakdown
    },
  },
  watch: {},
  props: {
    isDetailedPublicView: {
      type: Boolean,
      default: true,
    },
    rightColItem: {
      type: Object,
      default: () => {
        title: ""
      },
    },
    leftColItem: {
      type: Object,
      default: () => {
        title: ""
      },
    },
    leftCarouselSlides: {
      type: Array,
      default: () => [{}],
    },
    rightCarouselSlides: {
      type: Array,
      default: () => [{}],
    },
    checklistCols: {
      type: Object,
      default: () => {},
    },
    ratingCols: {
      type: Object,
      default: () => {},
    },
    // rightCarouselSlideModel: {
    //   type: String,
    // },
  },
}
</script>
