<template>
  <div class="q-my-md">
    <div>
      <ComparisonImages2 :leftColImage="leftColImage"
                         :rightColImage="rightColImage"
                         :compUuid="comparisonDetails.uuid"
                         :leftColPriceInCents="leftColPriceInCents"
                         :rightColPriceInCents="rightColPriceInCents"
                         :comparisonsHeight="comparisonsHeight"></ComparisonImages2>
    </div>
    <div v-if="isRegularComparison">
      <SimpleRegularComparisonTable :leftColItem="leftColItem"
                                    :rightColItem="rightColItem"
                                    :comparisonDetails="comparisonDetails"></SimpleRegularComparisonTable>
    </div>
    <div v-else>
      <SimpleComparisonTable :leftColItem="leftColItem"
                             :rightColItem="rightColItem"
                             :comparisonDetails="comparisonDetails"></SimpleComparisonTable>
    </div>
    <div>
      <div class="photos-editable row q-col-gutter-sm">
        <div class="col-xs-6">
          <div v-if="leftListingHasTitle">
            <ShowListingColPhotos listingPosition="left"
                                  :listingItem="leftColItem"
                                  :viewPics="orderedLeftViewPics"
                                  :comparisonDetails="comparisonDetails"
                                  :showingSideBySide="true"></ShowListingColPhotos>
            <div class="q-py-md"></div>
          </div>
        </div>
        <div class="col-xs-6">
          <div v-if="rightListingHasTitle">
            <ShowListingColPhotos listingPosition="right"
                                  :listingItem="rightColItem"
                                  :viewPics="orderedRightViewPics"
                                  :comparisonDetails="comparisonDetails"
                                  :showingSideBySide="true"></ShowListingColPhotos>
            <div class="q-py-md"></div>
          </div>
        </div>
        <div class="col-xs-6">
          <div v-if="priceCanBeShown()">
            <div class="q-py-md q-pl-md q-mb-lg text-subtitle1"
                 style="overflow: hidden; text-overflow: ellipsis">
              <div>Original Listing:</div>
              <a target=""
                 :href="comparisonSummary.import_url.top">{{
                  comparisonSummary.import_url.top
                }}</a>
            </div>
          </div>
        </div>
        <div class="col-xs-6">
          <div v-if="priceCanBeShown()">
            <div class="q-pl-sm q-py-md q-mb-lg text-subtitle1"
                 style="overflow: hidden; text-overflow: ellipsis">
              <div>Original Listing:</div>
              <a target=""
                 :href="comparisonSummary.import_url.bottom">{{
                  comparisonSummary.import_url.bottom
                }}</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import SimpleRegularComparisonTable from "src/h2c/components/side-by-side/SimpleRegularComparisonTable.vue"
import SimpleComparisonTable from "src/h2c/components/side-by-side/SimpleComparisonTable.vue"
import ShowListingColPhotos from "src/h2c/components/show/ShowListingColPhotos.vue"
import ComparisonImages2 from "src/h2c/components/side-by-side/ComparisonImages2.vue"

export default {
  components: {
    SimpleRegularComparisonTable,
    SimpleComparisonTable,
    ShowListingColPhotos,
    ComparisonImages2,
  },
  inject: ["configAndLocalData"],
  methods: {
    priceCanBeShown() {
      let awaitingGuess = this.configAndLocalData.checkIfAwaitingGuess(
        this.comparisonDetails.curated_list_item_uuid
      )
      return !!!awaitingGuess
    },
  },
  // setup(props) {
  // },
  computed: {
    comparisonSummary() {
      return this.comparisonDetails.comparison_summary || {}
    },
    comparisonsHeight() {
      if (this.$q.platform.is.mobile) {
        return 250
      } else {
        return 600
      }
    },
    leftColPriceInCents() {
      if (this.leftColItem.listing && this.leftColItem.listing.price_sale_current_cents) {
        return this.leftColItem.listing.price_sale_current_cents
      } else {
        return 0
      }
    },
    rightColPriceInCents() {
      if (
        this.rightColItem.listing &&
        this.rightColItem.listing.price_sale_current_cents
      ) {
        return this.rightColItem.listing.price_sale_current_cents
      } else {
        return 0
      }
    },
    leftColImage() {
      if (this.orderedLeftViewPics[0] && this.orderedLeftViewPics[0].image_details.url) {
        return this.orderedLeftViewPics[0].image_details.url
      } else {
        return ""
      }
    },
    rightColImage() {
      if (
        this.orderedRightViewPics[0] &&
        this.orderedRightViewPics[0].image_details.url
      ) {
        return this.orderedRightViewPics[0].image_details.url
      } else {
        return ""
      }
    },
    orderedLeftViewPics() {
      // var picsColl = this.leftColItem.listing.view_pics || []
      var picsColl = this.comparisonDetails.top_listing_visible_pics || []
      let picsOrder = this.comparisonDetails.pic_comparisons_order["left"] || []

      picsColl.forEach((picItem) => {
        let order = picsOrder[picItem.uuid]
        picItem.order = order
      })
      return picsColl.sort((a, b) => a.order - b.order)
    },
    orderedRightViewPics() {
      var picsColl = this.comparisonDetails.bottom_listing_visible_pics || []
      //  this.rightColItem.listing.view_pics || []
      let picsOrder = this.comparisonDetails.pic_comparisons_order["right"] || []
      picsColl.forEach((picItem) => {
        let order = picsOrder[picItem.uuid]
        picItem.order = order
      })
      return picsColl.sort((a, b) => a.order - b.order)
    },
    leftListingHasTitle() {
      return true
      return this.leftColItem.listing.title
    },
    rightListingHasTitle() {
      return true
      return this.rightColItem.listing.title
    },
  },
  data() {
    return {
      splitterModel: 50,
    }
  },
  props: {
    isRegularComparison: {
      type: Boolean,
      default: false,
    },
    comparisonDetails: {
      type: Object,
      default: () => { },
    },
    leftColItem: {
      type: Object,
      default: () => { },
    },
    rightColItem: {
      type: Object,
      default: () => {
        title: ""
      },
    },
  },
}
</script>
