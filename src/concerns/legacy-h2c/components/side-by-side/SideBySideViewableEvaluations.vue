<template>
  <div class="q-my-md sbs-viewable-evaluations">
    <div class="comp-images2-row row">
      <ComparisonImages2
        :leftColImage="leftColImage"
        :rightColImage="rightColImage"
        :compUuid="comparisonDetails.uuid"
        :leftColPriceInCents="leftColPriceInCents"
        :rightColPriceInCents="rightColPriceInCents"
        :comparisonsHeight="comparisonsHeight"
      ></ComparisonImages2>
    </div>
    <div>
      <SimpleEvaluationComparisonTable
        :leftColItem="leftColItem"
        :rightColItem="rightColItem"
        :comparisonDetails="comparisonDetails"
      ></SimpleEvaluationComparisonTable>
    </div>
    <div class="row q-mt-sm" style="padding: 1px">
      <SbsComparisonSections
        :extraComparisonCols="extraComparisonCols"
        v-if="true"
        :comparisonDetails="comparisonDetails"
      >
      </SbsComparisonSections>
      <SbsNotes
        v-if="configAndLocalData.siteConfig.enableSbsNotes"
        :comparisonDetails="comparisonDetails"
      >
      </SbsNotes>
      <SbsChecklist
        v-if="configAndLocalData.siteConfig.enableSbsChecklist"
        :comparisonDetails="comparisonDetails"
      >
      </SbsChecklist>
      <SbsPhotos :comparisonDetails="comparisonDetails"> </SbsPhotos>
      <SbsMortgageCalculator
        v-if="configAndLocalData.siteConfig.enableMortgageCalc"
        :leftColItem="leftColItem"
        :rightColItem="rightColItem"
        :comparisonDetails="comparisonDetails"
      >
      </SbsMortgageCalculator>
      <SbsMaps
        v-if="configAndLocalData.siteConfig.enableSbsMaps"
        :comparisonDetails="comparisonDetails"
      >
      </SbsMaps>

      <SbsAdjustments
        v-if="configAndLocalData.siteConfig.enableSbsAdjustments"
        :comparisonDetails="comparisonDetails"
      >
      </SbsAdjustments>
      <!--  -->
      <div class="col-xs-12">
        <div class="col-xs-6">
          <div v-if="priceCanBeShown()">
            <div
              class="q-py-md q-pl-md q-mb-lg text-subtitle1"
              style="overflow: hidden; text-overflow: ellipsis"
            >
              <div>Original Listing:</div>
              <a target="" :href="comparisonSummary.import_url.top">{{
                comparisonSummary.import_url.top
              }}</a>
            </div>
          </div>
        </div>
        <div class="col-xs-6">
          <div v-if="priceCanBeShown()">
            <div
              class="q-pl-sm q-py-md q-mb-lg text-subtitle1"
              style="overflow: hidden; text-overflow: ellipsis"
            >
              <div>Original Listing:</div>
              <a target="" :href="comparisonSummary.import_url.bottom">{{
                comparisonSummary.import_url.bottom
              }}</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class=""></div>
    <div></div>
  </div>
</template>
<script>
import SbsComparisonSections from "src/h2c/components/side-by-side-rows/SbsComparisonSections.vue"
import SbsChecklist from "src/h2c/components/side-by-side-rows/SbsChecklist.vue"
import SbsPhotos from "src/h2c/components/side-by-side-rows/SbsPhotos.vue"
import SbsAdjustments from "src/h2c/components/side-by-side-rows/SbsAdjustments.vue"
import SbsNotes from "src/h2c/components/side-by-side-rows/SbsNotes.vue"
import SbsMaps from "src/h2c/components/side-by-side-rows/SbsMaps.vue"
import SbsMortgageCalculator from "src/h2c/components/side-by-side-rows/SbsMortgageCalculator.vue"
import SimpleEvaluationComparisonTable from "src/h2c/components/side-by-side/SimpleEvaluationComparisonTable.vue"
// import ShowListingColPhotos from "src/h2c/components/show/ShowListingColPhotos.vue"
import ComparisonImages2 from "src/h2c/components/side-by-side/ComparisonImages2.vue"
export default {
  components: {
    SbsComparisonSections,
    SbsChecklist,
    SimpleEvaluationComparisonTable,
    SbsMortgageCalculator,
    ComparisonImages2,
    SbsPhotos,
    SbsAdjustments,
    SbsNotes,
    SbsMaps,
  },
  inject: ["configAndLocalData"],
  methods: {
    priceCanBeShown() {
      let awaitingGuess = this.configAndLocalData.checkIfAwaitingGuess(
        this.comparisonDetails.curated_list_item_uuid
      )
      return !!!awaitingGuess
    },
  },
  // setup(props) {
  // },
  computed: {
    picsExpCaption() {
      if (this.picsRowExpanded) {
        return "Click here to hide"
      } else {
        return "Click here to show"
      }
    },
    comparisonSummary() {
      return this.comparisonDetails.comparison_summary || {}
    },
    comparisonsHeight() {
      if (this.$q.platform.is.mobile) {
        return 250
      } else {
        return 600
      }
    },
    leftColPriceInCents() {
      if (this.leftColItem.listing && this.leftColItem.listing.price_sale_current_cents) {
        return this.leftColItem.listing.price_sale_current_cents
      } else {
        return 0
      }
    },
    rightColPriceInCents() {
      if (
        this.rightColItem.listing &&
        this.rightColItem.listing.price_sale_current_cents
      ) {
        return this.rightColItem.listing.price_sale_current_cents
      } else {
        return 0
      }
    },
    leftColImage() {
      if (this.orderedLeftViewPics[0] && this.orderedLeftViewPics[0].image_details.url) {
        return this.orderedLeftViewPics[0].image_details.url
      } else {
        return ""
      }
    },
    rightColImage() {
      if (
        this.orderedRightViewPics[0] &&
        this.orderedRightViewPics[0].image_details.url
      ) {
        return this.orderedRightViewPics[0].image_details.url
      } else {
        return ""
      }
    },
    orderedLeftViewPics() {
      // var picsColl = this.leftColItem.listing.view_pics || []
      var picsColl = this.comparisonDetails.top_evaluation_visible_pics || []
      let picsOrder = this.comparisonDetails.pic_comparisons_order["left"] || []

      picsColl.forEach((picItem) => {
        let order = picsOrder[picItem.uuid]
        picItem.order = order
      })
      return picsColl.sort((a, b) => a.order - b.order)
    },
    orderedRightViewPics() {
      var picsColl = this.comparisonDetails.bottom_evaluation_visible_pics || []
      //  this.rightColItem.listing.view_pics || []
      let picsOrder = this.comparisonDetails.pic_comparisons_order["right"] || []
      picsColl.forEach((picItem) => {
        let order = picsOrder[picItem.uuid]
        picItem.order = order
      })
      return picsColl.sort((a, b) => a.order - b.order)
    },
    // leftListingHasTitle() {
    //   return true
    //   return this.leftColItem.listing.title
    // },
    // rightListingHasTitle() {
    //   return true
    //   return this.rightColItem.listing.title
    // },
  },
  data() {
    return {
      picsRowExpanded: false,
      splitterModel: 50,
    }
  },
  props: {
    extraComparisonCols: {
      type: Object,
      default: () => {},
    },
    isRegularComparison: {
      type: Boolean,
      default: false,
    },
    comparisonDetails: {
      type: Object,
      default: () => {},
    },
    leftColItem: {
      type: Object,
      default: () => {},
    },
    rightColItem: {
      type: Object,
      default: () => {
        title: ""
      },
    },
  },
}
</script>
