<template>
  <q-markup-table class="simp-comp-tbl no-flashing-caret fixed-markup-table"
                  separator="cell"
                  flat
                  bordered>
    <thead>
      <tr>
        <th WIDTH="50%"
            colspan="2"
            class="text-center text-h5"
            style="width: 50%; padding: 0px">
          <div class="q-pa-sm"
               style="max-width: 50vw; font-size: medium">
            <!-- Property A (<BtnComparisonFromExisting :comparisonDetails="comparisonDetails"
                                       selectedListingIncoming="rightListing"></BtnComparisonFromExisting>) -->
          </div>
        </th>
        <th class="text-center"
            style="padding: 0px">
          <div class="q-pa-sm"
               style="max-width: 50vw; font-size: medium">
            <!-- Property B (<BtnComparisonFromExisting :comparisonDetails="comparisonDetails"
                                       selectedListingIncoming="leftListing"></BtnComparisonFromExisting>) -->
          </div>
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td colspan="2"
            class="text-center text-h5"
            style="
            max-width: 50vw;
            word-break: break-word;
            white-space: pre-wrap;
            padding: 5px;
          ">
          <div class="text-center text-body2 comp-lsting-title text-weight-regular">
            {{ comparisonDetails.top_listing_title }}
          </div>
        </td>
        <td class="text-center text-h5"
            style="
            max-width: 50vw;
            word-break: break-word;
            white-space: pre-wrap;
            padding: 5px;
          ">
          <div class="text-center text-body2 comp-lsting-title text-weight-regular">
            {{ comparisonDetails.bottom_listing_title }}
          </div>
        </td>
      </tr>
      <!-- <tr style="display: none">
        <td
          colspan="2"
          class="text-center text-h5"
          style="
            max-width: 50vw;
            word-break: break-word;
            white-space: pre-wrap;
            font-size: large;
          "
        >
          <q-expansion-item
            expand-separator
            icon=""
            :label="comparisonDetails.top_listing_title"
            caption="."
            v-model="descExpanded"
            class="height-100-pc"
            style="height: 100%"
          >
            <q-card>
              <q-card-section>
                <div v-html="comparisonDetails.top_listing_description"></div>
              </q-card-section>
            </q-card>
          </q-expansion-item>
        </td>
        <td
          class="text-center text-h5"
          style="
            max-width: 50vw;
            word-break: break-word;
            white-space: pre-wrap;
            font-size: large;
            padding: 0px;
          "
        >
          <q-expansion-item
            expand-separator
            icon=""
            :label="rightColItem.listing.title"
            caption="."
            v-model="descExpanded"
            class="height-100-pc"
            style="height: 100%"
          >
            <q-card>
              <q-card-section>
                <div>
                  <div v-html="comparisonDetails.bottom_listing_description"></div>
                </div>
              </q-card-section>
            </q-card>
          </q-expansion-item>
        </td>
      </tr> -->
      <tr v-if="showPriceCol"
          class="simple-comp-price-row"
          @click="startChangeCurrLeft">
        <td class="text-left"
            style="border-right: none; border-bottom: none">
          Price
          <!-- <div
            class="chg-curr-lbl q-pt-sm q-px-none q-ma-none cursor-pointer"
            style="color: #2196f3; font-size: smaller"
          >
            Click to change currency
          </div> -->
        </td>
        <td class="text-right text-h5"
            style="
            overflow: auto;
            padding-left: 0px;
            border-bottom: none;
            border-left: none;
            font-size: large;
          ">
          <!-- <ConvertableCurrencyDisplay @endChangeCurr="endChangeCurrLeft"
                                      @startChangeCurr="startChangeCurrLeft"
                                      :showCurrOptionsModal="showCurrOptionsModalLeft"
                                      :priceInCents="summaryFieldValue('top', 'price_sale_current_cents') || 0"
                                      :originalCurrency="summaryFieldValue('top', 'listing_currency')">
          </ConvertableCurrencyDisplay> -->
        </td>
        <td class="overflow: auto;text-left text-h5"
            style="font-size: large; border-bottom: none">
          <!-- <ConvertableCurrencyDisplay @endChangeCurr="endChangeCurrRight"
                                      @startChangeCurr="startChangeCurrRight"
                                      :showCurrOptionsModal="showCurrOptionsModalRight"
                                      :priceInCents="summaryFieldValue('bottom', 'price_sale_current_cents')"
                                      :originalCurrency="summaryFieldValue('bottom', 'listing_currency')"
                                      :currencyConversionEnabled="currencyConversionEnabled">
          </ConvertableCurrencyDisplay> -->
        </td>
      </tr>
      <tr v-if="showPriceCol"
          class="price-comp-prompt-row q-pa-none q-ma-none"
          @click="startChangeCurrLeft">
        <td colspan="2"
            class="text-left q-pa-none q-ma-none"
            style="border-right: none; border-top: none">
          <div class="chg-curr-lbl q-pa-none q-ma-none cursor-pointer"
               style="color: #2196f3; font-size: smaller">
            Click to change currency..
          </div>
        </td>
        <td class="q-pa-none q-ma-none"
            style="border-right: none; border-top: none"></td>
      </tr>
      <tr v-else
          class="price-comp-prompt-row q-pa-none q-ma-none">
        <td colspan="3"
            style="height: 0px; padding: 0px">
          <q-separator />
        </td>
      </tr>
      <tr>
        <td class="text-left"
            style="border-right: none">Bedrooms</td>
        <td class="large-font text-right"
            style="border-left: none">
          {{ summaryFieldValue("top", "count_bedrooms") }}
          <!-- {{ leftColItem.realty_asset.count_bedrooms }} -->
        </td>
        <td class="large-font text-left">
          {{ summaryFieldValue("bottom", "count_bedrooms") }}
          <!-- {{ rightColItem.realty_asset.count_bedrooms }} -->
        </td>
      </tr>
      <tr>
        <td class="text-left"
            style="border-right: none">Bathrooms</td>
        <td class="large-font text-right"
            style="border-left: none">
          {{ summaryFieldValue("top", "count_bathrooms") }}
          <!-- {{ leftColItem.realty_asset.count_bathrooms }} -->
        </td>
        <td class="large-font text-left">
          {{ summaryFieldValue("bottom", "count_bathrooms") }}
          <!-- {{ rightColItem.realty_asset.count_bathrooms }} -->
        </td>
      </tr>
      <tr v-if="areaShouldBeShown">
        <td class="text-left"
            style="border-right: none">Area</td>
        <td class="large-font text-right"
            style="border-left: none">
          {{ summaryFieldValue("top", "constructed_area") }}
          <!-- {{ leftColItem.realty_asset.formatted_constructed_area }} -->
        </td>
        <td class="large-font text-left">
          {{ summaryFieldValue("bottom", "constructed_area") }}
          <!-- {{ rightColItem.realty_asset.formatted_constructed_area }} -->
        </td>
      </tr>
      <tr>
        <td colspan="2"
            class="text-body2"
            style="
            max-width: 50vw;
            word-break: break-word;
            white-space: pre-wrap;
            font-size: large;
            padding-left: 0px;
          ">
          <q-expansion-item expand-separator
                            icon=""
                            label="Description"
                            :caption="descCaption"
                            v-model="descExpanded"
                            class="height-100-pc"
                            style="height: 100%">
            <q-card>
              <q-card-section>
                <div class="comp-lsting-desc-html text-body2">
                  <div v-html="comparisonDetails.top_listing_description"></div>
                </div>
              </q-card-section>
            </q-card>
          </q-expansion-item>
        </td>
        <td class="text-body2"
            style="
            max-width: 50vw;
            word-break: break-word;
            white-space: pre-wrap;
            font-size: large;
            padding: 0px;
          ">
          <q-expansion-item expand-separator
                            icon=""
                            label="Description"
                            :caption="descCaption"
                            v-model="descExpanded"
                            class="height-100-pc"
                            style="height: 100%">
            <q-card>
              <q-card-section>
                <div class="comp-lsting-desc-html text-body2">
                  <div v-html="comparisonDetails.bottom_listing_description"></div>
                </div>
              </q-card-section>
            </q-card>
          </q-expansion-item>
        </td>
      </tr>
      <!-- <tr>
        <td class="text-left" style="border-right: none">Overall Rating</td>
        <td class="text-right" style="border-left: none">
          <q-rating
            readonly
            :model-value="parseInt(leftColItem.item.rating_overall || 0)"
            size="1.5em"
            color="black"
            icon="star_border"
            icon-selected="star"
          />
        </td>
        <td class="text-left">
          <q-rating
            readonly
            :model-value="rightColItem.item.rating_overall || 0"
            size="1.5em"
            color="black"
            icon="star_border"
            icon-selected="star"
          />
        </td>
      </tr> -->
    </tbody>
  </q-markup-table>
</template>
<script>
// import { siteConfig } from "src/h2c/data/site-config"
// import useLocalData from "src/compose/useLocalData.js"
// import BtnComparisonFromExisting from "src/h2c/components/forms/BtnComparisonFromExisting.vue"
// import ConvertableCurrencyDisplay from "components/widgets/ConvertableCurrencyDisplay.vue"
export default {
  components: {
    // BtnComparisonFromExisting,
    // ConvertableCurrencyDisplay,
  },
  created() {
    // this.siteConfig = siteConfig
  },
  data() {
    return {
      awaitingGuess: true,
      descExpanded: false,
      showCurrOptionsModalLeft: false,
      showCurrOptionsModalRight: false,
    }
  },
  // setup() {
  //   // const { getGuessFromLocal } = useLocalData()
  //   return {
  //     getGuessFromLocal,
  //   }
  // },
  // inject: ["configAndLocalData", "currentUserProvider"],

  // mounted: function () {
  //   let awaitingGuess = this.configAndLocalData.checkIfAwaitingGuess(
  //     this.comparisonDetails.curated_list_item_uuid
  //   )
  //   this.awaitingGuess = awaitingGuess
  // },
  methods: {
    endChangeCurrLeft() {
      this.showCurrOptionsModalLeft = false
    },
    startChangeCurrLeft() {
      if (this.currencyConversionEnabled) {
        this.showCurrOptionsModalLeft = true
      }
    },
    endChangeCurrRight() {
      this.showCurrOptionsModalRight = false
    },
    startChangeCurrRight() {
      this.showCurrOptionsModalRight = true
    },
    summaryFieldValue(topOrBottom, fieldName) {
      let sf = this.comparisonDetails.comparison_summary[fieldName] || {}
      let sfv = sf[topOrBottom]
      return sfv
    },
  },
  computed: {
    // currencyConversionEnabled() {
    //   const currencyRates = this.currentUserProvider.state.currencyRates
    //   return (
    //     Object.keys(currencyRates).length > 1 &&
    //     Object.keys(currencyRates.rates).length > 0
    //   )
    // },
    showPriceCol() {
      return false
      // if (this.siteConfig.enablePriceGuesses) {
      //   if (this.awaitingGuess) {
      //     return false
      //   } else {
      //     return true
      //   }
      // } else {
      //   return true
      // }
    },
    areaShouldBeShown() {
      // TODO - compute this
      return false
    },
    descCaption() {
      if (this.descExpanded) {
        return ""
      } else {
        return "click to view..."
      }
    },
  },
  watch: {},
  props: {
    rightColItem: {
      type: Object,
      default: () => {
        title: ""
      },
    },
    leftColItem: {
      type: Object,
      default: () => {
        title: ""
      },
    },
    comparisonDetails: {
      type: Object,
      default: () => { },
    },
    // checklistCols: {
    //   type: Object,
    //   default: () => {},
    // },
    // ratingCols: {
    //   type: Object,
    //   default: () => {},
    // },
  },
}
</script>
<style>
.height-100-pc {
  height: 100%;
}

.fixed-markup-table table {
  table-layout: fixed;
}

.q-item__label--caption {
  color: #2196f3;
}
</style>
