<template>
  <q-card class="property-card q-ma-md">
    <q-carousel v-model="slide"
                animated
                arrows
                navigation
                infinite
                height="300px"
                class="bg-grey-1">
      <q-carousel-slide v-for="(img, index) in property.summary_listing_details.images"
                        :key="index"
                        :name="index">
        <q-img :src="img"
               class="full-height"
               fit="cover" />
      </q-carousel-slide>
    </q-carousel>

    <q-card-section>
      <div class="row items-center no-wrap">
        <div class="col">
          <div class="text-h6">{{ property.summary_listing_details.title }}</div>
          <div class="text-subtitle1">{{ property.summary_listing_long_address }}</div>
        </div>
        <div class="col-auto">
          <q-badge color="primary"
                   class="text-bold text-h6 q-pa-sm">
            £{{ formatPrice(property.summary_listing_price) }}
          </q-badge>
        </div>
      </div>
    </q-card-section>

    <q-separator />

    <q-card-section>
      <div class="row q-col-gutter-md">
        <div class="col-12 col-sm-6">
          <div class="row q-gutter-sm">
            <q-item class="bg-blue-1 rounded-borders">
              <q-item-section avatar>
                <q-icon name="bed"
                        color="primary"
                        size="md" />
              </q-item-section>
              <q-item-section>
                <q-item-label class="text-bold">{{ property.summary_listing_bedrooms_count }}</q-item-label>
                <q-item-label caption>Bedrooms</q-item-label>
              </q-item-section>
            </q-item>

            <q-item class="bg-blue-1 rounded-borders">
              <q-item-section avatar>
                <q-icon name="bathroom"
                        color="primary"
                        size="md" />
              </q-item-section>
              <q-item-section>
                <q-item-label class="text-bold">{{ property.summary_listing_bathrooms_count }}</q-item-label>
                <q-item-label caption>Bathrooms</q-item-label>
              </q-item-section>
            </q-item>

            <q-item class="bg-blue-1 rounded-borders">
              <q-item-section avatar>
                <q-icon name="home"
                        color="primary"
                        size="md" />
              </q-item-section>
              <q-item-section>
                <q-item-label class="text-bold">Detached</q-item-label>
                <q-item-label caption>Property Type</q-item-label>
              </q-item-section>
            </q-item>
          </div>
        </div>

        <div class="col-12 col-sm-6">
          <q-list>
            <q-item>
              <q-item-section avatar>
                <q-icon name="place"
                        color="deep-orange" />
              </q-item-section>
              <q-item-section>
                <q-item-label caption>Postcode</q-item-label>
                <q-item-label>{{ property.summary_listing_postcode }}</q-item-label>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section avatar>
                <q-icon name="numbers"
                        color="deep-orange" />
              </q-item-section>
              <q-item-section>
                <q-item-label caption>Reference</q-item-label>
                <q-item-label>{{ property.summary_listing_details.reference }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>
    </q-card-section>

    <q-separator />

    <q-card-section>
      <div class="text-h6">Agent Details</div>
      <q-item class="q-pa-none">
        <q-item-section avatar
                        v-if="property.realty_agent_details['display-logo']">
          <q-img :src="property.realty_agent_details['display-logo'].url"
                 width="65px"
                 height="65px" />
        </q-item-section>
        <q-item-section>
          <q-item-label class="text-bold">{{ property.realty_agent_details.name }}</q-item-label>
          <q-item-label caption>
            <q-icon name="phone"
                    size="xs" /> {{ property.realty_agent_details.telephone }}
          </q-item-label>
        </q-item-section>
      </q-item>
    </q-card-section>

    <q-card-actions align="between">
      <q-btn flat
             color="primary"
             :href="property.full_listing_url"
             target="_blank">
        View Full Details
        <q-icon name="open_in_new"
                class="q-ml-xs" />
      </q-btn>
      <q-btn flat
             color="secondary"
             :href="'https://www.onthemarket.com' + property.realty_agent_details['contact-url']"
             target="_blank">
        Contact Agent
        <q-icon name="mail"
                class="q-ml-xs" />
      </q-btn>
    </q-card-actions>

    <q-card-section class="q-pa-xs">
      <q-expansion-item icon="map"
                        label="View Map"
                        caption="Property location"
                        header-class="text-primary">
        <q-card>
          <q-card-section>
            <div id="property-map"
                 class="map-container">
              <q-skeleton type="rect"
                          height="200px"
                          v-if="!mapReady" />
              <!-- Map would be initialized here in a real component -->
              <div v-else
                   class="bg-grey-4 full-height flex flex-center">
                <div>Map at {{ property.summary_listing_latitude }}, {{ property.summary_listing_longitude }}</div>
              </div>
            </div>
          </q-card-section>
        </q-card>
      </q-expansion-item>
    </q-card-section>
  </q-card>
</template>

<script>
export default {
  name: 'PropertyCard',

  data() {
    return {
      slide: 0,
      mapReady: true,
      property: {
        "id": 3,
        "uuid": "3b96b13f-036a-4672-9d3b-15b188cfb7a5",
        "realty_search_query_uuid": "cdc87440-f355-4060-afea-ce335297d20c",
        "summary_listing_flags": 0,
        "realty_agent_details": {
          "id": 51387,
          "name": "Fraser Stretton - Leicester",
          "rank": 2,
          "enhanced?": false,
          "telephone": "0116 484 9432",
          "contact-url": "/agents/contact/16542200/?form-name=details-contact",
          "details-url": "/agents/branch/fraser-stretton-leicester/",
          "development?": false,
          "display-logo": {
            "url": "https://media.onthemarket.com/agents/companies/9443/230503134346840/logo-100x65.png",
            "width": 65,
            "height": 65,
            "resized?": true
          },
          "base-contact-url": "/agents/contact/16542200/"
        },
        "agency_tenant_uuid": "4a28f0a0-d5b6-4cce-809b-4b121aba71ae",
        "full_listing_uuid": null,
        "scrape_item_uuid": "b9d8774b-ecde-46ff-890d-2042d58017d4",
        "realty_asset_uuid": null,
        "full_listing_url": "https://www.onthemarket.com/details/16542200/",
        "summary_listing_import_url": "https://www.onthemarket.com/async/search/properties/?search-type=for-sale&location-id=cv11&min-price=250000&max-price=350000&min-bedrooms=2",
        "translations": {},
        "summary_listing_aasm_state": null,
        "discarded_at": null,
        "summary_listing_uprn": null,
        "summary_listing_long_address": "Conwy Close, Attleborough, Nuneaton",
        "summary_listing_postcode": "CV11",
        "summary_listing_price": "350000",
        "summary_listing_bedrooms_count": 4,
        "summary_listing_bathrooms_count": 2,
        "summary_listing_latitude": 52.519998,
        "summary_listing_longitude": -1.456825,
        "summary_listing_source_site": 1,
        "summary_listing_details": {
          "title": "4 bedroom detached house for sale",
          "images": [
            "https://media.onthemarket.com/properties/16542200/1531556701/image-0-480x320.jpg",
            "https://media.onthemarket.com/properties/16542200/1531556701/image-1-480x320.jpg",
            "https://media.onthemarket.com/properties/16542200/1531556701/image-2-480x320.jpg",
            "https://media.onthemarket.com/properties/16542200/1531556701/image-3-480x320.jpg",
            "https://media.onthemarket.com/properties/16542200/1531556701/image-4-480x320.jpg"
          ],
          "reference": "16542200"
        },
        "created_at": "2025-03-07T14:09:32.609Z",
        "updated_at": "2025-03-07T16:15:01.675Z"
      }
    }
  },

  methods: {
    formatPrice(price) {
      return new Intl.NumberFormat('en-GB').format(price);
    }
  },

  mounted() {
    // In a real implementation, you would initialize a map here
    // using the property's coordinates
    // this.initializeMap();
  }
}
</script>

<style scoped>
.property-card {
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
}

.map-container {
  height: 200px;
  width: 100%;
}

.bg-blue-1 {
  background-color: #e3f2fd;
}
</style>