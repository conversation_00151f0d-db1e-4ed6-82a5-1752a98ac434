<template>
  <div class="col-12 BaseGoogleMap">
    <div id="map"
         style="width: 100%; height: 500px;"></div>
  </div>
</template>

<script>
import {
  loadGoogleMapsScript,
  setupMapForPostcodeCluster,
  setupSinglePoint,
  setupMultiplePoints
} from 'src/concerns/postcode-clusters/utils/mapUtils';

export default {
  name: 'BaseGoogleMap',
  components: {
  },
  data() {
    return {
      postcodeAreaGeoJson: null,
      showDialog: false,
      dialogData: {},
      gmapInstance: null,
    };
  },
  watch: {
    selectedPoint: {
      immediate: false,
      handler(newVal) {
        let infWinToBeShown = true
        setupSinglePoint(this.gmapInstance, {
          mapLat: this.selectedPoint.transactionLatitude,
          mapLng: this.selectedPoint.transactionLongitude,
          markerText: this.selectedPoint.dataItemLabel,
        }, infWinToBeShown)
      },
    },
  },
  mounted() {
    loadGoogleMapsScript().then(() => {
      let gmapInstance = new google.maps.Map(document.getElementById('map'), {
        center: { lat: 52.517, lng: -1.455 },
        zoom: 18,
        // maxZoom: 18,
        mapId: 'DEMO_MAP_ID',
        // mapId is needed for advanced marker
        // When it is set, styles will be ignored - needs to be set via console
        // styles: [
        //   {
        //     featureType: 'poi',
        //     elementType: 'all',
        //     stylers: [{ visibility: 'off' }],
        //   },
        // ],
      })
      if (this.clusterDetails) {
        setupMapForPostcodeCluster(
          this.clusterDetails, this.setClusterLabel, this.setBB, this
        );
      }
      if (this.selectedPoint) {
        let infWinToBeShown = true
        setupSinglePoint(gmapInstance, {
          mapLat: this.selectedPoint.transactionLatitude,
          mapLng: this.selectedPoint.transactionLongitude,
          dataItemLabel: this.selectedPoint.dataItemLabel,
          soldPriceShort: this.selectedPoint.soldPriceShort,
          SoldTransactionId: this.selectedPoint.SoldTransactionId,
          markerText: this.selectedPoint.soldPriceShort,
        }, infWinToBeShown);
        setupMultiplePoints(gmapInstance, this.soldDataPoints, this.markerClickCallback)
        this.gmapInstance = gmapInstance
      }
    });
  },
  props: {
    selectedPoint: Object,
    clusterDetails: {
      type: Object,
      required: false,
    },
    soldDataPoints: {
      type: Array,
      required: false,
    },
  },
  methods: {
    markerClickCallback(mapPoint) {
      this.$emit("passMapPoint", mapPoint)
    },
    setClusterLabel(currentMap, centerLatitude, centerLongitude) {
      if (this.clusterDetails.formatted_average_property_price) {

        let centroid = new google.maps.LatLng(centerLatitude, centerLongitude)
        let labelMarker = new google.maps.Marker({
          position: centroid,
          map: currentMap,
          label: {
            text: this.clusterDetails.formatted_average_property_price,
            color: "white",
            fontSize: "26px",
            fontWeight: "bold",
          },
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            scale: 0, // Hide the default marker icon
          },
        });
        // Set the visibility of the label based on zoom level
        google.maps.event.addListener(currentMap, 'zoom_changed', function () {
          const zoomLevel = currentMap.getZoom();
          if (zoomLevel < 15 || zoomLevel > 18) { // Adjust the zoom level threshold as needed
            labelMarker.setVisible(false);
          } else {
            labelMarker.setVisible(true);
          }
        });
      }
    },
    setBB(map, postcodeAreaDetails) {
      let lastPolygonCoords = null;
      let geoJsonDetails = postcodeAreaDetails.postcode_area_geojson?.primary;

      if (geoJsonDetails) {
        if (geoJsonDetails.geometry.type === "Polygon") {
          let polygonCoords = geoJsonDetails.geometry.coordinates[0].map(
            ([lng, lat]) => ({
              lat,
              lng
            })
          );
          lastPolygonCoords = polygonCoords;
          this.instantiatePolygon(map, polygonCoords, geoJsonDetails, postcodeAreaDetails);
        } else if (geoJsonDetails.geometry.type === "MultiPolygon") {
          geoJsonDetails.geometry.coordinates.forEach(polyCoords => {
            let polygonCoords = polyCoords[0].map(
              ([lng, lat]) => ({
                lat,
                lng
              })
            );
            lastPolygonCoords = polygonCoords;
            this.instantiatePolygon(map, polygonCoords, geoJsonDetails, postcodeAreaDetails);
          });
        }
      }

      return lastPolygonCoords;
    },
    instantiatePolygon(map, polygonCoords, geoJsonDetails, postcodeAreaDetails) {
      console.log(`Instantiating for ${postcodeAreaDetails.postal_code}`);
      const postcodePolygon = new google.maps.Polygon({
        paths: polygonCoords,
        strokeColor: '#007bff',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#007bff',
        fillOpacity: 0.35,
        map,
      });

      // Create an InfoWindow to display information
      const infoWindow = new google.maps.InfoWindow();

      // Event listeners for hover effects
      postcodePolygon.addListener('click', () => {
        const contentString = `
          <div>
            <h6>${geoJsonDetails.properties.postcodes}</h6>
          </div>
        `;
        infoWindow.setContent(contentString);
        infoWindow.setPosition(this.getCenterOfPolygon(postcodePolygon));
        infoWindow.open(map);
      });

      // postcodePolygon.addListener('mouseout', () => {
      //   infoWindow.close();
      // });

      return postcodePolygon;
    },
    // Method to get the center of the polygon
    getCenterOfPolygon(polygon) {
      const bounds = new google.maps.LatLngBounds();
      polygon.getPath().forEach((path) => bounds.extend(path));
      return bounds.getCenter();
    },
  },
};
</script>