<script>
import { defineComponent, onMounted, onBeforeUnmount, ref } from 'vue';
import "leaflet/dist/leaflet.css";
// import L from 'leaflet';

function initMap(element) {
  const map = L.map(element, {
    // options
  }).setView([51.505, -0.09], 13);
  <PERSON><PERSON>tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
    maxZoom: 19,
    attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
  }).addTo(map);
  return map;
};

export default defineComponent({
  emits: ['created', 'removed'],
  setup(props, { emit }) {
    const mapElement = ref(null);
    let map;
    let observer;

    function removeMap() {
      if (map) {
        map.remove();
        map = undefined;
        emit('removed');
      }
    }

    onMounted(() => {
      if (mapElement.value) {
        // observer = new IntersectionObserver((entries) => {
        //   if (entries[0].isIntersecting && mapElement.value) {
        //     // Element shown - insert map
        //     removeMap();
        //     map = initMap(mapElement.value);
        //     emit('created', map);
        //   } else {
        //     // Not shown - remove map
        //     removeMap();
        //   }
        // }, { threshold: [1] });

        // // Observe visibility of map container
        // observer.observe(mapElement.value);
      } else {
        console.error('Map element is not available');
      }
    });

    onBeforeUnmount(() => {
      if (observer && mapElement.value) {
        observer.unobserve(mapElement.value);
      }
      removeMap();
    });

    return {
      mapElement,
    };
  }
});
</script>

<template>
  <div class="full-height"
       ref="mapElement"
       v-once><!-- Using v-once to make sure Vue will not update --></div>
</template>