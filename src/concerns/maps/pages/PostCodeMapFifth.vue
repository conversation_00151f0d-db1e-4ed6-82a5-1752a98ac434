<template>
  <form>
    <label for="zoom">Zoom:</label>
    <input type="number"
           id="zoom"
           v-model="zoom" />
  </form>

  <ol-map style="height: 400px">
    <ol-view ref="view"
             :center="center"
             :rotation="rotation"
             :zoom="zoom"
             :projection="projection"
             @change:center="centerChanged"
             @change:resolution="resolutionChanged"
             @change:rotation="rotationChanged" />

    <ol-tile-layer>
      <ol-source-osm />
    </ol-tile-layer>

    <ol-vector-layer>
      <ol-source-vector>
        <!-- Display Polygon for the postcode boundary -->
        <ol-feature>
          <ol-geom-polygon :coordinates="[boundaryCoordinates]">
            <ol-style>
              <ol-style-stroke color="blue"
                               :width="2" />
              <ol-style-fill color="rgba(0, 0, 255, 0.2)" />
            </ol-style>
          </ol-geom-polygon>
        </ol-feature>
      </ol-source-vector>
    </ol-vector-layer>

    <ol-rotate-control></ol-rotate-control>
    <ol-interaction-link />
  </ol-map>

  <ul>
    <li>center: {{ currentCenter }}</li>
    <li>resolution: {{ currentResolution }}</li>
    <li>zoom: {{ currentZoom }}</li>
    <li>rotation: {{ currentRotation }}</li>
  </ul>
</template>

<script setup>
import { ref } from "vue";
import { fromLonLat } from "ol/proj";

// Default center for the map
const center = ref(fromLonLat([-1.87243905894199, 52.4448816189598]));
const projection = ref("EPSG:3857"); // Web Mercator projection
const zoom = ref(16);
const rotation = ref(0);

const currentCenter = ref(center.value);
const currentZoom = ref(zoom.value);
const currentRotation = ref(rotation.value);
const currentResolution = ref(0);

// Postcode boundary coordinates in LonLat
const rawBoundaryCoordinates = [
  [-1.87243905894199, 52.4448816189598],
  [-1.87232690081235, 52.4450314966672],
  [-1.87228681910246, 52.4450485345336],
  [-1.87222794271074, 52.4450628461408],
  [-1.87174776245476, 52.4450002247115],
  [-1.8717146610062, 52.4444919473145],
  [-1.87174310721478, 52.4443878197515],
  [-1.87177169503818, 52.4443566643016],
  [-1.8721889300139, 52.444250879743],
  [-1.87240064054676, 52.4446585083485],
  [-1.87243905894199, 52.4448816189598], // Close the polygon
];

// Convert boundary coordinates to map projection (EPSG:3857)
const boundaryCoordinates = rawBoundaryCoordinates.map((coord) =>
  fromLonLat(coord)
);

function resolutionChanged(event) {
  currentResolution.value = event.target.getResolution();
  currentZoom.value = event.target.getZoom();
}
function centerChanged(event) {
  currentCenter.value = event.target.getCenter();
}
function rotationChanged(event) {
  currentRotation.value = event.target.getRotation();
}
</script>

<style scoped>
.ol-map {
  position: relative;
}

.ol-map-loading:after {
  content: "";
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80px;
  height: 80px;
  margin-top: -40px;
  margin-left: -40px;
  border-radius: 50%;
  border: 5px solid rgba(180, 180, 180, 0.6);
  border-top-color: var(--vp-c-brand-1);
  animation: spinner 0.6s linear infinite;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}
</style>
