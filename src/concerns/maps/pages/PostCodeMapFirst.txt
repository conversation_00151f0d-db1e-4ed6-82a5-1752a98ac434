<template>
  <div style="height:600px; width:800px">
    <q-no-ssr>
      <l-map ref="map"
             :use-global-leaflet="false"
             v-model:zoom="zoom"
             :center="[52.525, -1.45]">
        <l-tile-layer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                      layer-type="base"
                      name="OpenStreetMap"></l-tile-layer>
      </l-map>
    </q-no-ssr>
  </div>
</template>

<script>
import "leaflet/dist/leaflet.css";

// get this error on dokku server so commenting out this component:
// Cannot find module '@vue-leaflet/vue-leaflet

import { LMap, LTileLayer } from "@vue-leaflet/vue-leaflet";
// import L from "leaflet";
// import * as L from 'leaflet'

export default {
  components: {
    LMap,
    LTileLayer,
  },
  data() {
    return {
      zoom: 12, // Set zoom appropriate for the CV11 area
    };
  },
  mounted() {
    // this.addPostcodeBoundaries();
  },
  methods: {
    async addPostcodeBoundaries() {
      try {
        // Fetch GeoJSON for CV11 postcode boundaries
        const response = await fetch("https://api.example.com/cv11-boundaries"); // Replace with your GeoJSON API URL
        const geojson = await response.json();

        // Access the map instance from the ref
        const map = this.$refs.map.mapObject;

        // Add the GeoJSON layer to the map
        const geoJsonLayer = L.geoJSON(geojson, {
          style: {
            color: "#0074D9",
            weight: 2,
            fillColor: "#7FDBFF",
            fillOpacity: 0.5,
          },
        });
        geoJsonLayer.addTo(map);

        // Zoom to the bounds of the GeoJSON layer
        map.fitBounds(geoJsonLayer.getBounds());
      } catch (error) {
        console.error("Error loading postcode boundaries:", error);
      }
    },
  },
};
</script>

<style>
/* Optional: Style adjustments */
</style>
