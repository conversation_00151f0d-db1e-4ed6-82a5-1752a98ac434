<template>
  <form>
    <label for="zoom">Zoom:</label>
    <input type="number"
           id="zoom"
           v-model="zoom" />
  </form>

  <ol-map style="height: 400px">
    <ol-view ref="view"
             :center="center"
             :rotation="rotation"
             :zoom="zoom"
             :projection="projection"
             @change:center="centerChanged"
             @change:resolution="resolutionChanged"
             @change:rotation="rotationChanged" />

    <ol-tile-layer>
      <ol-source-osm />
    </ol-tile-layer>

    <ol-vector-layer v-if="postcodeData">
      <ol-source-vector>
        <!-- Point for the postcode -->
        <ol-feature>
          <ol-geom-point :coordinates="center">
            <ol-style>
              <ol-style-icon src="https://cdn.jsdelivr.net/npm/ol/examples/data/icon.png"
                             :scale="0.5" />
            </ol-style>
          </ol-geom-point>
        </ol-feature>

        <!-- Circle around the postcode -->
        <ol-feature>
          <ol-geom-circle :center="center"
                          :radius="2000">
            <ol-style>
              <ol-style-stroke color="red"
                               :width="2" />
              <ol-style-fill color="rgba(255, 0, 0, 0.2)" />
            </ol-style>
          </ol-geom-circle>
        </ol-feature>
      </ol-source-vector>
    </ol-vector-layer>

    <ol-rotate-control></ol-rotate-control>
    <ol-interaction-link />
  </ol-map>

  <ul>
    <li>center: {{ currentCenter }}</li>
    <li>resolution: {{ currentResolution }}</li>
    <li>zoom: {{ currentZoom }}</li>
    <li>rotation: {{ currentRotation }}</li>
  </ul>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { fromLonLat } from "ol/proj";

const center = ref(fromLonLat([-1.4673, 52.5236])); // Default center
const projection = ref("EPSG:3857"); // Web Mercator projection
const zoom = ref(8);
const rotation = ref(0);

const currentCenter = ref(center.value);
const currentZoom = ref(zoom.value);
const currentRotation = ref(rotation.value);
const currentResolution = ref(0);

const postcodeData = ref(null);

function resolutionChanged(event) {
  currentResolution.value = event.target.getResolution();
  currentZoom.value = event.target.getZoom();
}
function centerChanged(event) {
  currentCenter.value = event.target.getCenter();
}
function rotationChanged(event) {
  currentRotation.value = event.target.getRotation();
}

// Fetch postcode data and update map
onMounted(async () => {
  try {
    const response = await fetch("https://api.postcodes.io/outcodes/CV11");
    const data = await response.json();

    if (data.status === 200 && data.result) {
      postcodeData.value = data.result;

      // Update center to the postcode coordinates
      const newCenter = fromLonLat([data.result.longitude, data.result.latitude]);
      center.value = newCenter;
      currentCenter.value = newCenter; // Update display center
    }
  } catch (error) {
    console.error("Error fetching postcode data:", error);
  }
});
</script>

<style scoped>
.ol-map {
  position: relative;
}

.ol-map-loading:after {
  content: "";
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 80px;
  height: 80px;
  margin-top: -40px;
  margin-left: -40px;
  border-radius: 50%;
  border: 5px solid rgba(180, 180, 180, 0.6);
  border-top-color: var(--vp-c-brand-1);
  animation: spinner 0.6s linear infinite;
}

@keyframes spinner {
  to {
    transform: rotate(360deg);
  }
}
</style>
