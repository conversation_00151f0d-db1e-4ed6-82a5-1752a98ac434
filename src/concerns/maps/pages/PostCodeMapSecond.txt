<template>
  <div style="height:600px; width:800px">
    <q-no-ssr>
      <l-map ref="map"
             :use-global-leaflet="false"
             v-model:zoom="zoom"
             :center="[47.41322, -1.219482]">
        <l-tile-layer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                      layer-type="base"
                      name="OpenStreetMap"></l-tile-layer>
      </l-map>
    </q-no-ssr>
  </div>
</template>

<script>
import "leaflet/dist/leaflet.css";
import { LMap, LTileLayer } from "@vue-leaflet/vue-leaflet";
// get this error on dokku server so commenting out this component:
// Cannot find module '@vue-leaflet/vue-leaflet

export default {
  components: {
    LMap,
    LTileLayer,
  },
  data() {
    return {
      zoom: 2,
    };
  },
  mounted() {
    console.log(this.$refs.map);
  }
};
</script>

<style></style>