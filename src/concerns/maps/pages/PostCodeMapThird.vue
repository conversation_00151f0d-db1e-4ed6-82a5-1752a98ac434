<template>
  <div>
    <h1>Explore the Map</h1>
    <BaseLeafletMap @created="onMapCreated"
                    @removed="onMapRemoved" />
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import BaseLeafletMap from "../components/BaseLeafletMap.vue"
// import BaseLeafletMap from "@/components/BaseLeafletMap.vue";

export default defineComponent({
  name: "MapViewer",
  components: {
    BaseLeafletMap,
  },
  methods: {
    // onMapCreated(map) {
    //   console.log("Map instance created", map);
    //   // Example: Add a marker
    //   const marker = L.marker([51.505, -0.09]).addTo(map);
    //   marker.bindPopup("<b>Hello world!</b><br>I am a popup.").openPopup();
    // },
    // onMapRemoved() {
    //   console.log("Map instance removed");
    // },
  },
});
</script>

<style>
/* Optional styles */
</style>
