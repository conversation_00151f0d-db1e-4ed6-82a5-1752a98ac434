<template>
  <q-page class="flex">
    <q-no-ssr>
      <div style="height: 400px; width: 100%">
        <ol-map :loadTilesWhileAnimating="true"
                :loadTilesWhileInteracting="true"
                style="height: 100%; width: 100%"
                :view="view">
          <ol-tile-layer>
            <ol-source-osm />
          </ol-tile-layer>

          <ol-vector-layer v-if="postcodeData">
            <ol-source-vector>
              <ol-feature>
                <ol-geom-point :coordinates="centerCoordinates">
                  <ol-style>
                    <ol-style-icon src="https://cdn.jsdelivr.net/npm/ol/examples/data/icon.png"
                                   :scale="0.5" />
                  </ol-style>
                </ol-geom-point>
              </ol-feature>
              <ol-feature>
                <ol-geom-circle :center="centerCoordinates"
                                :radius="2000">
                  <ol-style>
                    <ol-style-stroke color="red"
                                     :width="2" />
                    <ol-style-fill color="rgba(255,0,0,0.2)" />
                  </ol-style>
                </ol-geom-circle>
              </ol-feature>
            </ol-source-vector>
          </ol-vector-layer>
        </ol-map>
      </div>
    </q-no-ssr>
  </q-page>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue'
import { fromLonLat } from 'ol/proj'
import 'ol/ol.css'
// import { Map, Layers, Sources } from "vue3-openlayers";

// import Map from 'ol/Map'
import View from 'ol/View'
// import TileLayer from 'ol/layer/Tile'
// import OSM from 'ol/source/OSM'
// import VectorLayer from 'ol/layer/Vector'
// import VectorSource from 'ol/source/Vector'

export default defineComponent({
  name: 'PostcodeCV11Map',
  setup() {
    const postcodeData = ref(null)
    const centerCoordinates = ref(fromLonLat([-1.4673, 52.5236]))


    // const view = ref({
    //   center: centerCoordinates.value,
    //   zoom: 13
    // })

    const view = ref(
      new View({
        // center: [0, 0],
        center: centerCoordinates.value,
        zoom: 13,
      })
    );

    onMounted(async () => {
      try {
        const response = await fetch('https://api.postcodes.io/outcodes/CV11')
        const data = await response.json()
        if (data.status === 200 && data.result) {
          postcodeData.value = data.result
          centerCoordinates.value = fromLonLat([data.result.longitude, data.result.latitude])
          view.value.center = centerCoordinates.value
        }
      } catch (error) {
        console.error('Error fetching postcode data:', error)
      }
    })

    return {
      view,
      postcodeData,
      centerCoordinates
    }
  }
})
</script>
