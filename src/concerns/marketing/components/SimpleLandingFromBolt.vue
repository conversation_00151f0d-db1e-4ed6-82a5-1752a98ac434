<template>
  <q-page>
    <!-- Hero Section -->
    <section class="hero-section bg-gradient text-white text-center q-pa-xl">
      <div class="container">
        <h1 class="text-h2 q-mb-md">Make better decisions when buying or selling a home</h1>
        <p class="text-h6 q-mb-xl">
          The future of real estate insights for buyers, sellers, and agents.
          Privacy-first synthetic property comparisons tailored to your needs.
        </p>
        <div class="row justify-center q-gutter-md">
          <q-btn size="lg"
                 color="yellow"
                 text-color="dark"
                 label="Get Started" />
          <q-btn size="lg"
                 outline
                 color="white"
                 label="Learn More" />
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="q-pa-xl bg-white">
      <div class="container">
        <div class="row q-col-gutter-xl">
          <div class="col-12 col-md-4 text-center">
            <q-icon name="home"
                    size="4rem"
                    color="primary"
                    class="q-mb-md" />
            <h3 class="text-h5 q-mb-md">For Buyers</h3>
            <p>Create your dream home blueprint from actual recently sold properties. Compare and assess potential
              options with confidence.</p>
          </div>
          <div class="col-12 col-md-4 text-center">
            <q-icon name="sell"
                    size="4rem"
                    color="secondary"
                    class="q-mb-md" />
            <h3 class="text-h5 q-mb-md">For Sellers</h3>
            <p>Price your property strategically using AI-generated synthetic comparables—no need to disclose full
              property details.</p>
          </div>
          <div class="col-12 col-md-4 text-center">
            <q-icon name="groups"
                    size="4rem"
                    color="accent"
                    class="q-mb-md" />
            <h3 class="text-h5 q-mb-md">For Agents</h3>
            <p>Save time and build trust with clients using privacy-focused market insights and streamlined property
              valuations.</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="once-bggrey2 q-pa-xl">
      <div class="container">
        <h2 class="text-h4 text-center q-mb-xl">Why Choose HomesToCompare?</h2>
        <div class="row q-col-gutter-xl">
          <div class="col-12 col-md-6">
            <q-card flat
                    bordered>
              <q-card-section>
                <div class="text-h6"><q-icon name="security"
                          class="q-mr-sm" />Privacy First</div>
                <p class="q-mt-md">Our synthetic data approach ensures your property details stay private while
                  providing powerful insights.</p>
              </q-card-section>
            </q-card>
          </div>
          <div class="col-12 col-md-6">
            <q-card flat
                    bordered>
              <q-card-section>
                <div class="text-h6"><q-icon name="psychology"
                          class="q-mr-sm" />AI-Powered</div>
                <p class="q-mt-md">Advanced algorithms create accurate property comparisons based on real market data.
                </p>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="bg-primary text-white q-pa-xl text-center">
      <div class="container">
        <h2 class="text-h4 q-mb-md">Ready to get started?</h2>
        <p class="text-h6 q-mb-xl">Join the revolution in real estate comparables today.</p>
        <div class="row justify-center q-gutter-md">
          <q-btn size="lg"
                 color="yellow"
                 text-color="dark"
                 label="Get Early Access" />
          <q-btn size="lg"
                 outline
                 color="white"
                 label="Request Demo" />
        </div>
      </div>
    </section>
  </q-page>
</template>

<script>
export default {
  name: 'IndexPage'
}
</script>

<style lang="scss">
.hero-section {
  min-height: 80vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--q-primary) 0%, var(--q-secondary) 100%);
}

.bg-gradient {
  background: linear-gradient(135deg, var(--q-primary) 0%, var(--q-secondary) 100%);
}
</style>