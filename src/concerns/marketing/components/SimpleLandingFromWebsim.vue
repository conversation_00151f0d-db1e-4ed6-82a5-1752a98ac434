<template>
  <q-page>


    <section class="hero">
      <div class="container">
        <h1>Make better decisions when buying or selling a home</h1>
        <p class="subtitle">The future of real estate insights for buyers, sellers, and agents. Privacy-first synthetic
          property comparisons tailored to your needs.</p>
      </div>
    </section>

    <section class="section">
      <div class="container">
        <div class="grid">
          <div class="card">
            <h3>For Buyers</h3>
            <p>Create your dream home blueprint from actual recently sold properties. You can then use this to
              confidently
              compare and assess potential options going forward.</p>
          </div>
          <div class="card">
            <h3>For Sellers</h3>
            <p>Price your property like a pro. Select similar sold properties and let our AI create accurate synthetic
              comparables for strategic pricing—no need to disclose full property details.</p>
          </div>
        </div>
      </div>
    </section>

    <section class="section">
      <div class="container">
        <h2 class="section-title">Starting with Nuneaton</h2>
        <p class="subtitle">At HomesToCompare, we believe in starting small and perfecting our platform before scaling
          up
        </p>

        <div class="features">
          <div class="feature-card">
            <h3>Why Start Small?</h3>
            <ul>
              <li>Test and refine the platform with real user feedback</li>
              <li>Build a deep understanding of local market nuances</li>
              <li>Ensure a privacy-focused and high-quality experience</li>
              <li>Lay the groundwork for sustainable national growth</li>
            </ul>
          </div>

          <div class="feature-card">
            <h3>Why CV11?</h3>
            <ul>
              <li>Dynamic housing market with diverse property types</li>
              <li>Strategic commuter location near Coventry and Birmingham</li>
              <li>Appealing price ranges for various buyer profiles</li>
              <li>Strong community and development opportunities</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <section class="section">
      <div class="container">
        <h2 class="section-title">Why Choose HomesToCompare?</h2>
        <div class="features">
          <div class="feature-card">
            <h3>Privacy-First Comparables</h3>
            <p>No need to share sensitive property details to gain insights. AI synthesizes data for safe and powerful
              analysis.</p>
          </div>
          <div class="feature-card">
            <h3>AI-Powered Insights</h3>
            <p>Hyper-local, statistically accurate synthetic property data for smarter decision-making.</p>
          </div>
          <div class="feature-card">
            <h3>Tailored for Everyone</h3>
            <p>Whether you're buying, selling, or advising clients, HomesToCompare meets your unique needs.</p>
          </div>
        </div>
      </div>
    </section>

    <section class="section">
      <div class="container">
        <h2 class="section-title">How It Works</h2>
        <div class="steps">
          <div class="step">
            <p>Browse recently sold homes and pick the ones that most resemble what you're buying, selling, or
              analyzing.
            </p>
          </div>
          <div class="step">
            <p>Our platform will create synthetic property listing (including AI generated images) based on this. You
              can
              tweak this till it is perfect for you.</p>
          </div>
          <div class="step">
            <p>You can then use this as your "northstar" to compare and make informed decisions about the properties
              currently on the market.</p>
          </div>
        </div>
      </div>
    </section>

    <section class="section">
      <div class="container">
        <h2 class="section-title">Who Can Benefit?</h2>
        <div class="grid">
          <div class="card">
            <h3>Homebuyers</h3>
            <p>Compare current listings with synthetic properties that reflect your dream home.</p>
          </div>
          <div class="card">
            <h3>Home Sellers</h3>
            <p>Avoid lengthy property descriptions. Choose and compare similar properties.</p>
          </div>
          <div class="card">
            <h3>Estate Agents</h3>
            <p>Provide clients with precise, privacy-safe property comparisons.</p>
          </div>
        </div>
      </div>
    </section>

    <section class="cta">
      <div class="container">
        <h2>Join the Revolution in Real Estate Comparables</h2>
        <p class="subtitle">Get started today and make smarter property decisions</p>
        <a href="https://homestocompare.com/signup"
           class="btn">Get Started</a>
      </div>
    </section>
  </q-page>
</template>

<script>
export default {
  name: "HomesToCompareLandingPage",
  mounted: function () {

    // Add smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
          behavior: 'smooth'
        });
      });
    });

    // Add intersection observer for fade-in animations
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = 1;
          entry.target.style.transform = 'translateY(0)';
        }
      });
    });

    document.querySelectorAll('.feature-card, .card, .step').forEach((el) => {
      el.style.opacity = 0;
      el.style.transform = 'translateY(20px)';
      el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
      observer.observe(el);
    });

  },

};
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Inter', sans-serif;
}

:root {
  --primary: #2D3748;
  --secondary: #4A5568;
  --accent: #4299E1;
  --background: #F7FAFC;
}

body {
  line-height: 1.6;
  background: var(--background);
  color: var(--primary);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

header {
  background: white;
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 100;
}

.hero {
  padding: 8rem 0 4rem;
  text-align: center;
  background: linear-gradient(135deg, #EBF8FF 0%, #F7FAFC 100%);
}

h1 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  color: var(--primary);
}

.subtitle {
  font-size: 1.25rem;
  color: var(--secondary);
  max-width: 800px;
  margin: 0 auto 2rem;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  padding: 4rem 0;
}

.feature-card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 1rem;
}

.section {
  padding: 4rem 0;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.card {
  background: white;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  counter-reset: step;
}

.step {
  position: relative;
  padding: 2rem;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.step::before {
  counter-increment: step;
  content: counter(step);
  position: absolute;
  top: -15px;
  left: -15px;
  width: 40px;
  height: 40px;
  background: var(--accent);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.cta {
  text-align: center;
  padding: 4rem 0;
  background: linear-gradient(135deg, #4299E1 0%, #2B6CB0 100%);
  color: white;
}

.btn {
  display: inline-block;
  padding: 1rem 2rem;
  background: white;
  color: var(--accent);
  text-decoration: none;
  border-radius: 30px;
  font-weight: bold;
  transition: transform 0.3s ease;
}

.btn:hover {
  transform: translateY(-3px);
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-10px);
  }

  100% {
    transform: translateY(0px);
  }
}

.floating {
  animation: float 6s ease-in-out infinite;
}
</style>
