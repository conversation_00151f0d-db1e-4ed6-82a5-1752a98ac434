<template>
  <q-page class="landing-page">
    <section class="hero">
      <div class="hero-overlay"></div>
      <div class="container">
        <div class="hero-content">
          <h1 class="animate-pop-in">Revolutionize Your Real Estate Decisions</h1>
          <p class="subtitle animate-fade-in">Unlock AI-powered property insights without compromising privacy. Your
            smart companion in real estate strategy.</p>
          <div class="hero-cta">
            <a href="#how-it-works"
               class="btn btn-primary">Discover How It Works</a>
            <a href="https://homestocompare.com/signup"
               class="btn btn-secondary">Get Started Free</a>
          </div>
        </div>
      </div>
    </section>

    <section id="benefits"
             class="section benefits">
      <div class="container">
        <h2 class="section-title">Empowering Real Estate Decisions</h2>
        <div class="benefits-grid">
          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-shield-alt"></i>
            </div>
            <h3>Privacy-First Approach</h3>
            <p>Advanced AI generates insights without exposing sensitive property details.</p>
          </div>
          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-chart-line"></i>
            </div>
            <h3>Hyper-Local Intelligence</h3>
            <p>Statistically accurate synthetic data tailored to your local market.</p>
          </div>
          <div class="benefit-card">
            <div class="benefit-icon">
              <i class="fas fa-users"></i>
            </div>
            <h3>Universal Solutions</h3>
            <p>Designed for buyers, sellers, and real estate professionals alike.</p>
          </div>
        </div>
      </div>
    </section>

    <section id="how-it-works"
             class="section process">
      <div class="container">
        <h2 class="section-title">How HomesToCompare Works</h2>
        <div class="process-steps">
          <div class="process-step">
            <div class="step-number">1</div>
            <h3>Browse & Select</h3>
            <p>Explore recently sold homes that match your ideal property profile.</p>
            <div class="step-illustration">
              <i class="fas fa-home"></i>
            </div>
          </div>
          <div class="process-step">
            <div class="step-number">2</div>
            <h3>AI Synthesis</h3>
            <p>Our AI generates a synthetic property listing with precision-crafted details.</p>
            <div class="step-illustration">
              <i class="fas fa-robot"></i>
            </div>
          </div>
          <div class="process-step">
            <div class="step-number">3</div>
            <h3>Strategic Comparison</h3>
            <p>Use your AI-generated benchmark to evaluate current market offerings.</p>
            <div class="step-illustration">
              <i class="fas fa-search"></i>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section id="who-benefits"
             class="section audience">
      <div class="container">
        <h2 class="section-title">Who Thrives with HomesToCompare?</h2>
        <div class="audience-grid">
          <div class="audience-card">
            <div class="audience-icon">
              <i class="fas fa-user-tie"></i>
            </div>
            <h3>Home Buyers</h3>
            <p>Create your dream home blueprint with data-driven confidence.</p>
          </div>
          <div class="audience-card">
            <div class="audience-icon">
              <i class="fas fa-tags"></i>
            </div>
            <h3>Home Sellers</h3>
            <p>Price strategically with AI-generated comparative insights.</p>
          </div>
          <div class="audience-card">
            <div class="audience-icon">
              <i class="fas fa-briefcase"></i>
            </div>
            <h3>Estate Agents</h3>
            <p>Provide clients with precise, privacy-preserving property analysis.</p>
          </div>
        </div>
      </div>
    </section>

    <section class="cta-final">
      <div class="container">
        <div class="cta-content">
          <h2>Transform Your Real Estate Strategy Today</h2>
          <p>Join hundreds of smart property professionals leveraging AI-powered insights.</p>
          <div class="cta-buttons">
            <a href="https://homestocompare.com/signup"
               class="btn btn-primary btn-large">Start Your Free Trial</a>
            <a href="#how-it-works"
               class="btn btn-secondary btn-large">Learn More</a>
          </div>
        </div>
      </div>
    </section>
  </q-page>
</template>

<script>
export default {
  name: "HomesToCompareLandingPage",
  mounted() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        document.querySelector(this.getAttribute('href')).scrollIntoView({
          behavior: 'smooth'
        });
      });
    });

    // Intersection Observer for animations
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('is-visible');
          observer.unobserve(entry.target);
        }
      }, {
        threshold: 0.1
      });
    });

    // Observe elements for animations
    document.querySelectorAll('.benefit-card, .process-step, .audience-card, .section-title')
      .forEach(el => {
        el.classList.add('animate-hidden');
        observer.observe(el);
      });
  }
};
</script>

<style>
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css');

:root {
  --primary-color: #2D6BED;
  --secondary-color: #4A5568;
  --background-light: #F7FAFC;
  --text-dark: #1A202C;
  --accent-color: #48BB78;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background: var(--background-light);
  color: var(--text-dark);
  line-height: 1.6;
}

.landing-page {
  overflow-x: hidden;
}

.hero {
  position: relative;
  padding: 10rem 0 6rem;
  background: linear-gradient(135deg, var(--primary-color) 0%, #2C5282 100%);
  color: white;
  text-align: center;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.2);
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
}

.hero h1 {
  font-size: 3.5rem;
  margin-bottom: 1.5rem;
  font-weight: 800;
}

.hero .subtitle {
  font-size: 1.4rem;
  max-width: 700px;
  margin: 0 auto 2.5rem;
  color: rgba(255, 255, 255, 0.9);
}

.hero-cta {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
}

.btn {
  display: inline-block;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary {
  background: white;
  color: var(--primary-color);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.section {
  padding: 6rem 0;
  background: var(--background-light);
}

.section-title {
  text-align: center;
  margin-bottom: 4rem;
  font-size: 2.5rem;
  color: var(--text-dark);
}

.benefits-grid,
.audience-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.benefit-card,
.audience-card {
  background: white;
  padding: 2.5rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.benefit-card:hover,
.audience-card:hover {
  transform: translateY(-10px);
}

.benefit-icon,
.audience-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.process-step {
  background: white;
  padding: 2.5rem;
  border-radius: 12px;
  text-align: center;
  position: relative;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
}

.step-number {
  position: absolute;
  top: -15px;
  left: -15px;
  background: var(--accent-color);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.step-illustration {
  font-size: 3rem;
  color: var(--primary-color);
  margin-top: 1.5rem;
}

.cta-final {
  background: linear-gradient(135deg, var(--primary-color) 0%, #2C5282 100%);
  color: white;
  text-align: center;
  padding: 6rem 0;
}

.cta-content h2 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 2.5rem;
}

.btn-large {
  padding: 1.2rem 2.5rem;
  font-size: 1.1rem;
}

/* Animation Styles */
.animate-hidden {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.animate-hidden.is-visible {
  opacity: 1;
  transform: translateY(0);
}

.animate-pop-in {
  animation: pop-in 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
  opacity: 0;
}

.animate-fade-in {
  animation: fade-in 1s ease-out forwards;
  opacity: 0;
}

@keyframes pop-in {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 1024px) {

  .benefits-grid,
  .audience-grid,
  .process-steps {
    grid-template-columns: 1fr;
  }

  .hero h1 {
    font-size: 2.5rem;
  }

  .hero .subtitle {
    font-size: 1.2rem;
  }

  .hero-cta {
    flex-direction: column;
    gap: 1rem;
  }
}
</style>