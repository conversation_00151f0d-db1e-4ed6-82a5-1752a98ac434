<template>
  <section id="how-it-works"
           class="section process max-ctr">
    <div class="container">
      <!-- <h2 class="section-title text-h3">How HomesToCompare Works</h2> -->
      <h2 class="text-h3 text-primary q-mb-xl q-pb-xl text-center">
        How <span class="text-accent">HomesToCompare</span> Works
      </h2>
      <div class="process-steps">
        <div class="process-step column justify-between items-center">
          <div class="step-number">1</div>
          <h3 class="text-h3">Browse <br> & Select</h3>
          <p class="text-h5">
            Browse recently sold homes and pick the ones that most resemble what you're buying, selling, or analyzing.
          </p>
          <div class="step-illustration">
            <q-icon name="psychology"
                    size="56px"
                    class="text-purple-500 q-mb-md" />
            <!-- <i class="fas fa-home"></i> -->
          </div>
        </div>
        <div class="process-step column justify-between items-center">
          <div class="step-number">2</div>
          <h3 class="text-h3">AI <br> Synthesis</h3>
          <p class="text-h5">
            Our platform will create a synthetic property listing along with AI generated images based on this. You can
            tweak this till it is perfect for you. </p>
          <!-- <h3>AI Synthesis</h3>
          <p>Our AI generates a synthetic property listing with precision-crafted details.</p> -->
          <div class="step-illustration">
            <!-- <i class="fas fa-robot"></i> -->
            <q-icon name="auto_fix_high"
                    size="56px"
                    class="text-purple-500 q-mb-md" />

          </div>
        </div>
        <div class="process-step column justify-between items-center">
          <div class="step-number">3</div>
          <h3 class="text-h3">Informed <br> Comparisons</h3>
          <p class="text-h5">
            You can then use this as your "northstar" to compare and make informed decisions about the properties
            currently on the market.
          </p>
          <div class="step-illustration">
            <q-icon name="insights"
                    size="56px"
                    class="text-purple-500 q-mb-md" />

          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
</script>
<style>
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css');

:root {
  --primary-color: #2D6BED;
  --secondary-color: #4A5568;
  --background-light: #F7FAFC;
  --text-dark: #1A202C;
  --accent-color: #48BB78;
}

/* 


* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background: var(--background-light);
  color: var(--text-dark);
  line-height: 1.6;
}

.landing-page {
  overflow-x: hidden;
}
 */

.btn {
  display: inline-block;
  padding: 1rem 2rem;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary {
  background: white;
  color: var(--primary-color);
}

.btn-secondary {
  background: transparent;
  color: white;
  border: 2px solid white;
}

.btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.section {
  padding: 6rem 0;
  background: var(--background-light);
}

.section-title {
  text-align: center;
  margin-bottom: 4rem;
  font-size: 2.5rem;
  color: var(--text-dark);
}

.benefits-grid,
.audience-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.benefit-card,
.audience-card {
  background: white;
  padding: 2.5rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.benefit-card:hover,
.audience-card:hover {
  transform: translateY(-10px);
}

.benefit-icon,
.audience-icon {
  font-size: 3rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.process-step {
  background: white;
  padding: 2.5rem;
  border-radius: 12px;
  text-align: center;
  position: relative;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
}

.step-number {
  position: absolute;
  top: -15px;
  left: -15px;
  background: var(--accent-color);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.step-illustration {
  font-size: 3rem;
  color: var(--primary-color);
  margin-top: 1.5rem;
}

.cta-final {
  background: linear-gradient(135deg, var(--primary-color) 0%, #2C5282 100%);
  color: white;
  text-align: center;
  padding: 6rem 0;
}

.cta-content h2 {
  font-size: 3rem;
  margin-bottom: 1.5rem;
}

.cta-buttons {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 2.5rem;
}

.btn-large {
  padding: 1.2rem 2.5rem;
  font-size: 1.1rem;
}

/* Animation Styles */
.animate-hidden {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.animate-hidden.is-visible {
  opacity: 1;
  transform: translateY(0);
}

.animate-pop-in {
  animation: pop-in 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
  opacity: 0;
}

.animate-fade-in {
  animation: fade-in 1s ease-out forwards;
  opacity: 0;
}

@keyframes pop-in {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 1024px) {

  .benefits-grid,
  .audience-grid,
  .process-steps {
    grid-template-columns: 1fr;
  }

}
</style>