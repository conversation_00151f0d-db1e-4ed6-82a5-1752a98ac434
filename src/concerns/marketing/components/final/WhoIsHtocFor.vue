<template>
  <section id="who-benefits"
           class="q-py-lg bg-light">
    <div class="container q-py-xl text-center max-ctr">
      <div class="row q-col-gutter-lg justify-center  q-mb-lg">
        <h2 class="text-h3 text-primary q-mb-lg">
          What makes <span class="text-accent">HomesToCompare</span> so useful?
        </h2>
      </div>
      <div class="row q-col-gutter-lg justify-center">
        <div class="col-xs-12 col-sm-6 col-md-6 ">
          <q-card class="q-ma-lg bg-white shadow-2 q-hover-shadow-lg rounded-borders transition-all audience-card">
            <q-card-section class="text-center column justify-between items-center">
              <q-icon name="home"
                      size="56px"
                      class="text-primary q-mb-md" />
              <h3 class="text-h6 q-mb-md text-primary">Home Buyers</h3>
              <p class="text-h5 q-mb-md text-grey-8">
                Navigate the home-buying journey with unprecedented clarity and confidence.
              </p>
              <ul class="text-left text-body2 text-grey-7 q-px-md q-mb-md no-bullets">
                <li class="q-mb-sm text-h6">
                  <q-icon name="check_circle"
                          size="sm"
                          class="q-mr-sm text-green-6" />
                  Discover ideal neighborhoods based on your preferences.
                </li>
                <li class="q-mb-sm text-h6">
                  <q-icon name="check_circle"
                          size="sm"
                          class="q-mr-sm text-green-6" />
                  Compare properties side-by-side to find the perfect fit.
                </li>
                <li class="q-mb-sm text-h6">
                  <q-icon name="check_circle"
                          size="sm"
                          class="q-mr-sm text-green-6" />
                  Utilize our mortgage calculator to plan your budget effectively.
                </li>
              </ul>
              <!-- <q-btn color="primary"
                     outline
                     no-caps
                     class="full-width">
                Buyer's Guide
              </q-btn> -->
            </q-card-section>
          </q-card>
        </div>
        <div class="col-xs-12 col-sm-6 col-md-6 ">
          <q-card
                  class="col-xs-12 col-sm-6 col-md-6 q-ma-lg bg-white shadow-2 q-hover-shadow-lg rounded-borders transition-all audience-card">
            <q-card-section class="text-center column justify-between items-center">
              <q-icon name="price_change"
                      size="56px"
                      class="text-primary q-mb-md" />
              <h3 class="text-h6 q-mb-md text-primary">Home Sellers</h3>
              <p class="text-h5 q-mb-md text-grey-8">
                Maximize your property's value with data-driven strategic pricing.
              </p>
              <ul class="text-left text-body2 text-grey-7 q-px-md q-mb-md no-bullets text-h6">
                <li class="q-mb-sm text-h6">
                  <q-icon name="check_circle"
                          size="sm"
                          class="q-mr-sm text-green-6" />
                  Get useful property valuations using our advanced algorithms.
                </li>
                <li class="q-mb-sm text-h6">
                  <q-icon name="check_circle"
                          size="sm"
                          class="q-mr-sm text-green-6" />
                  Identify optimal listing prices to attract qualified buyers.
                </li>
                <li class="q-mb-sm text-h6">
                  <q-icon name="check_circle"
                          size="sm"
                          class="q-mr-sm text-green-6" /> Track market trends to time your sale perfectly.
                </li>
              </ul>
              <!-- <q-btn color="primary"
                     outline
                     no-caps
                     class="full-width">
                Seller's Toolkit
              </q-btn> -->
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
  </section>
</template>
<script>
export default {
  name: "WhoThrivesSection",
};
</script>
<style scoped>
.no-bullets {
  list-style-type: none;
  padding-left: 0;
  /* Optional: aligns content perfectly with no additional padding */
}

.bg-light {
  background-color: #f7fafc;
}

.text-primary {
  color: #2d3748;
}

.text-accent {
  color: #4299e1;
}

.rounded-borders {
  border-radius: 12px;
}

.audience-card:hover {
  transform: translateY(-5px);
  transition: transform 0.3s ease-in-out;
}
</style>