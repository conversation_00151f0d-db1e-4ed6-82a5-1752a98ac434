<template>
  <section class="features-section q-py-xl bg-grey text-white">
    <div class="container q-mx-auto text-center q-px-md max-ctr">
      <h2 class="text-h3 q-mb-md">
        Starting with Nuneaton
      </h2>
      <div class="text-h5 q-mb-lg">At HomesToCompare, we believe in starting small and perfecting our platform before
        scaling up</div>

      <div class="row q-col-gutter-lg justify-center">
        <div class="col-12 col-md-6">
          <q-card class="bg-green-9 text-white">
            <q-card-section>
              <div class="col-xs-12 col-sm-6">
                <q-icon name="flag"
                        size="56px"
                        class="text-purple-500 q-mb-md" />
                <h3 class="text-lg font-bold">Why Start Small?</h3>
              </div>
              <q-list>
                <q-item v-for="(item, index) in employeeFeatures"
                        :key="index">
                  <q-item-section avatar>
                    <q-icon name="remove" />
                  </q-item-section>
                  <q-item-section class="text-h6">{{ item }}</q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>

        <div class="col-12 col-md-6">
          <q-card class="bg-green-9 text-white">
            <q-card-section>
              <div class="col-xs-12 col-sm-6">
                <q-icon name="place"
                        size="56px"
                        class="text-blue-500 q-mb-md" />
                <h3 class="text-lg font-bold">Why CV11?</h3>
              </div>
              <q-list>
                <q-item v-for="(item, index) in entrepreneurFeatures"
                        :key="index">
                  <q-item-section avatar>
                    <q-icon name="check" />
                  </q-item-section>
                  <q-item-section class="text-h6">{{ item }}</q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
const employeeFeatures = [
  "Test and refine the platform with real user feedback",
  "Build a deep understanding of local market nuances",
  "Ensure a privacy-focused and high-quality experience",
  "Lay the groundwork for sustainable national growth"
]

const entrepreneurFeatures = [
  "Dynamic housing market with diverse property types",
  "Strategic commuter location near Coventry and Birmingham",
  "Appealing price ranges for various buyer profiles",
  "Strong community and development opportunities"]
</script>