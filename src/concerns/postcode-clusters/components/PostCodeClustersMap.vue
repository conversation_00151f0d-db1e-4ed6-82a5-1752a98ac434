<template>
  <div class="col-12">
    <div id="map"
         style="width: 100%; height: 500px;"></div>
  </div>
</template>

<script>
import useAreaClusters from "src/compose/useAreaClusters.js";

export default {
  name: 'PostCodeClustersMap',
  data() {
    return {
      postcodeAreaGeoJson: null,
    };
  },
  setup(props) {
    const { getPostcodeCluster } = useAreaClusters();
    return {
      getPostcodeCluster,
    };
  },
  mounted() {
    this.loadGoogleMapsScript().then(() => {
      let centerLatitude = parseFloat(this.clusterDetails.center_latitude);
      let centerLongitude = parseFloat(this.clusterDetails.center_longitude);

      const currentMap = new google.maps.Map(document.getElementById('map'), {
        center: { lat: centerLatitude, lng: centerLongitude },
        zoom: 15,
      });

      // Create a LatLngBounds object to store the bounds of all polygons
      const bounds = new google.maps.LatLngBounds();

      this.incomingClusters.forEach(clusterDetails => {
        this.sortoutData(currentMap, clusterDetails, bounds);
      });

      // Adjust the map to fit all polygons
      currentMap.fitBounds(bounds);
      this.setClusterLabel(currentMap, centerLatitude, centerLongitude)
    });
  },
  computed: {
    clusterDetails() {
      return this.incomingClusters[0];
    },
  },
  props: {
    incomingClusters: {
      type: Array,
      required: false,
    },
  },
  methods: {
    instantiatePolygon(map, polygonCoords, clusterDetails, bounds) {
      const postcodePolygon = new google.maps.Polygon({
        paths: polygonCoords,
        strokeColor: '#007bff',
        strokeOpacity: 0.8,
        strokeWeight: 2,
        fillColor: '#007bff',
        fillOpacity: 0.35,
        map,
      });

      // Extend the bounds with the polygon coordinates
      polygonCoords.forEach(coord => bounds.extend(coord));

      // Create an InfoWindow to display information
      const infoWindow = new google.maps.InfoWindow();

      // Event listeners for hover effects
      postcodePolygon.addListener('mouseover', () => {
        const contentString = `
          <div>
            <h5>${clusterDetails.cluster_name}</h5>
            <p>Average property price: ${clusterDetails.formatted_average_property_price}</p>
          </div>
        `;
        infoWindow.setContent(contentString);
        infoWindow.setPosition(this.getCenterOfPolygon(postcodePolygon));
        infoWindow.open(map);
      });

      postcodePolygon.addListener('mouseout', () => {
        infoWindow.close();
      });

      return postcodePolygon;
    },
    setClusterLabel(currentMap, centerLatitude, centerLongitude) {
      if (this.clusterDetails.formatted_average_property_price) {

        let centroid = new google.maps.LatLng(centerLatitude, centerLongitude)
        let labelMarker = new google.maps.Marker({
          position: centroid,
          map: currentMap,
          label: {
            text: this.clusterDetails.formatted_average_property_price,
            color: "white",
            fontSize: "26px",
            fontWeight: "bold",
          },
          icon: {
            path: google.maps.SymbolPath.CIRCLE,
            scale: 0, // Hide the default marker icon
          },
        });
        // Set the visibility of the label based on zoom level
        google.maps.event.addListener(currentMap, 'zoom_changed', function () {
          const zoomLevel = currentMap.getZoom();
          if (zoomLevel < 15 || zoomLevel > 18) { // Adjust the zoom level threshold as needed
            labelMarker.setVisible(false);
          } else {
            labelMarker.setVisible(true);
          }
        });
      }
    },
    sortoutData(currentMap, clusterDetails, bounds) {
      // let postcodeAreas = clusterDetails.postcode_areas;
      // let postcodeArea = postcodeAreas[0]
      if (clusterDetails.cluster_geojsons) {
        this.setBbForCluster(currentMap, bounds, clusterDetails)
      }
    },
    setBbForCluster(map, bounds, clusterDetails) {
      let geoJsonDetails = clusterDetails.cluster_geojsons
      // let geoJsonDetails = postcodeAreaDetails.postcode_area_geojson?.primary;
      if (geoJsonDetails?.geometry) {
        if (geoJsonDetails.geometry.type === "Polygon") {
          let polygonCoords = geoJsonDetails.geometry.coordinates[0].map(
            ([lng, lat]) => ({ lat, lng })
          );
          this.instantiatePolygon(map, polygonCoords, clusterDetails, bounds);
        } else if (geoJsonDetails.geometry.type === "MultiPolygon") {
          geoJsonDetails.geometry.coordinates.forEach(polyCoords => {
            let polygonCoords = polyCoords[0].map(
              ([lng, lat]) => ({ lat, lng })
            );
            this.instantiatePolygon(map, polygonCoords, clusterDetails, bounds);
          });
        }
      }
    },
    loadGoogleMapsScript() {
      return new Promise((resolve, reject) => {
        if (window.google && window.google.maps) {
          resolve();
        } else {
          const script = document.createElement('script');
          console.log(`GMAPS_API_KEY2 is ${process.env.GMAPS_API_KEY}`);
          script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.GMAPS_API_KEY}&libraries=marker`;
          script.async = true;
          script.onload = resolve;
          script.onerror = reject;
          document.head.appendChild(script);
        }
      });
    },
    // Method to get the center of the polygon
    getCenterOfPolygon(polygon) {
      const bounds = new google.maps.LatLngBounds();
      polygon.getPath().forEach((path) => bounds.extend(path));
      return bounds.getCenter();
    },
  },
};
</script>