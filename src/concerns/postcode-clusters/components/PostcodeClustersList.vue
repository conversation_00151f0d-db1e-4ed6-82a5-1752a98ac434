<template>
  <div class="reference-properties q-pa-md">
    <div v-if="pageIsLoading"
         class="full-width flex justify-center items-center"
         style="height: 80vh">
      <q-circular-progress indeterminate
                           rounded
                           size="50px"
                           color="primary"
                           class="q-ma-md" />
    </div>
    <div v-else
         class="full-width">
      <div v-if="noClusters"
           class="text-center q-pa-xl">
      </div>
      <div v-else>
        <div class="row items-center q-mb-lg">
        </div>

        <div class="row q-col-gutter-md">
          <div class="col-12 col-sm-6 col-md-6"
               v-for="(referenceProperty, index) in clustersToUse"
               :key="referenceProperty.uuid || index">
            <q-card>
              <q-card-section>

                <h4>{{ referenceProperty.cluster_name }}</h4>
                <h5>Average price {{ referenceProperty.formatted_average_property_price }}</h5>
                <div>
                  <router-link :to="{
                    name: 'rAreaCluster',
                    params: { areaClusterUuid: referenceProperty.uuid },
                  }">
                    <h5>{{ referenceProperty.cluster_description }}</h5>
                    <h5>{{ referenceProperty.uuid }}</h5>
                  </router-link>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
        <q-space />
      </div>
    </div>
  </div>
</template>
<script>
import useAreaClusters from "src/compose/useAreaClusters.js"
export default {
  components: {
  },
  setup(props) {
    const { getPostcodeClusters } = useAreaClusters()
    return {
      getPostcodeClusters,
    }
  },
  props: {
    incomingClusters: {
      type: Array,
      // default() {
      //   return []
      // },
    }
  },
  mounted() {
    if (this.clustersToUse.length < 1) {
      this.getPostcodeClusters()
        .then((response) => {
          this.localClusters = response.data || []
        })
        .catch((error) => { })
    }
  },
  data() {
    return {
      pageIsLoading: false,
      localClusters: [],
    }
  },
  methods: {},
  computed: {
    clustersToUse() {
      return this.incomingClusters || this.localClusters
    },
    noClusters() {
      return this.clustersToUse.length < 1
    },
  },
  methods: {},
  watch: {},
}
</script>

<style scoped>
.q-card {
  transition: all 0.3s ease;
}

.q-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 25px 0 rgba(0, 0, 0, .1);
}
</style>
