<template>
  <q-dialog v-model="localShowDialog">
    <q-card class="transaction-card">
      <q-card-section>
        <div class="text-h6">{{ dialogData.street_address }}</div>
        <div class="text-subtitle2">{{ dialogData.postal_code }}</div>
      </q-card-section>

      <q-card-section>
        <div class="row items-center q-gutter-sm">
          <q-chip color="purple"
                  text-color="white">
            {{ dialogData.sold_price }}
          </q-chip>
          <q-chip outline>
            Sold: {{ dialogData.sold_date }}
          </q-chip>
        </div>
      </q-card-section>

      <q-card-section>
        <div class="row q-gutter-y-sm">
          <div class="col-12">
            <div class="text-caption text-grey">EPC Details</div>
            <div class="row q-gutter-x-md">
              <div>
                <q-icon name="square_foot"
                        size="xs" />
                {{ dialogData.total_floor_area }} m²
              </div>
              <div>
                <q-icon name="calendar_today"
                        size="xs" />
                EPC Date: {{ dialogData.epc_date }}
              </div>
            </div>
          </div>

          <div v-if="dialogData.latitude && dialogData.longitude"
               class="col-12">
            <div class="text-caption text-grey">Location</div>
            <div class="row q-gutter-x-md">
              <div>
                <q-icon name="location_on"
                        size="xs" />
                {{ dialogData.latitude }},
                {{ dialogData.longitude }}
              </div>
            </div>
          </div>
        </div>
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat
               color="primary"
               label="View Details"
               @click="$emit('view-details', dialogData)" />
        <q-btn flat
               label="Close"
               v-close-popup />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script>
export default {
  name: 'SoldTransactionDialog',
  props: {
    showDialog: {
      type: Boolean,
      required: false
    },
    dialogData: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      localShowDialog: this.showDialog
    };
  },
  watch: {
    showDialog(newVal) {
      this.localShowDialog = newVal;
    },
    localShowDialog(newVal) {
      this.$emit('update:showDialog', newVal);
    }
  }
};
</script>
