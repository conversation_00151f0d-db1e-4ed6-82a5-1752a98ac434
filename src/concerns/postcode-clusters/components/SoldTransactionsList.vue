<template>
  <div class="col-12">
    <div class="row q-col-gutter-md">
      <div v-if="!saleTransactions?.confirmed_st_epcs?.length"
           class="col-12 text-center">
        <q-card class="q-pa-md">
          <q-card-section>
            <div class="text-h6">No transactions found</div>
          </q-card-section>
        </q-card>
      </div>

      <div v-for="transaction in sortedTransactions"
           :key="transaction.reference"
           class="col-12 col-md-6 col-lg-4">
        <q-card class="transaction-card">
          <q-card-section>
            <div class="text-h6">{{ transaction.street_address }}</div>
            <div class="text-subtitle2">
              {{ transaction.postal_code }}
            </div>
          </q-card-section>

          <q-card-section v-for="soldTransaction in transaction.sold_transactions"
                          :key="soldTransaction.sold_date">
            <div class="row items-center q-gutter-sm">
              <q-chip color="purple"
                      text-color="white">
                {{ soldTransaction.formatted_sold_price }}
              </q-chip>
              <q-chip outline>
                Sold: {{ formatDate(soldTransaction.sold_date) }}
              </q-chip>
            </div>
          </q-card-section>

          <q-card-section v-for="epcDetail in transaction.epc_details"
                          :key="epcDetail.inspection_date">
            <div class="row q-gutter-y-sm">
              <div class="col-12">
                <div class="text-caption text-grey">EPC Details</div>
                <div class="row q-gutter-x-md">
                  <div>
                    <q-icon name="square_foot"
                            size="xs" />
                    {{ epcDetail.total_floor_area }} m²
                  </div>
                  <div>
                    <q-icon name="calendar_today"
                            size="xs" />
                    EPC Date: {{ formatDate(epcDetail.inspection_date) }}
                  </div>
                </div>
              </div>

              <div v-if="transaction.latitude && transaction.longitude"
                   class="col-12">
                <div class="text-caption text-grey">Location</div>
                <div class="row q-gutter-x-md">
                  <div>
                    <q-icon name="location_on"
                            size="xs" />
                    {{ formatCoordinate(transaction.latitude) }},
                    {{ formatCoordinate(transaction.longitude) }}
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>

          <q-card-actions align="right">
            <q-btn flat
                   color="primary"
                   label="View Details"
                   @click="$emit('view-details', transaction)" />
          </q-card-actions>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SoldTransactionsList',

  props: {
    saleTransactions: {
      type: Object,
      required: false,
      default: () => ({})
    },
  },

  computed: {
    sortedTransactions() {
      if (!this.saleTransactions?.confirmed_st_epcs) return [];
      return [...this.saleTransactions.confirmed_st_epcs].sort((a, b) => {
        // Handle cases where sold_transactions[0] is missing
        const dateA = a.sold_transactions.length > 0 ? new Date(a.sold_transactions[0].sold_date) : new Date(0);
        const dateB = b.sold_transactions.length > 0 ? new Date(b.sold_transactions[0].sold_date) : new Date(0);

        // Sort by sold date, most recent first
        return dateB - dateA;
      });
    }
  },

  methods: {
    formatDate(dateString) {
      if (!dateString) return 'N/A';
      return new Date(dateString).toLocaleDateString('en-GB', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      });
    },

    formatCoordinate(coord) {
      if (!coord) return 'N/A';
      return coord.toFixed(6);
    },

    // latestEpcDetail(epcDetails) {
    //   if (!epcDetails || !epcDetails.length) return {};
    //   return epcDetails.reduce((latest, current) => {
    //     return new Date(latest.inspection_date) > new Date(current.inspection_date) ? latest : current;
    //   });
    // }
  }
};
</script>

<style scoped>
.transaction-card {
  height: 100%;
  transition: all 0.2s ease-in-out;
}

.transaction-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
</style>