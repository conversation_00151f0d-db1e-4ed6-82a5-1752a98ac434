<template>
  <div class="col-12">
    <div id="map"
         style="width: 100%; height: 500px;"></div>
  </div>
  <SoldTransactionDialog v-model="showDialog"
                         :dialogData="dialogData" />
</template>

<script>
import SoldTransactionDialog from './SoldTransactionDialog.vue';
import { setupSaleTransactionsMap, loadGoogleMapsScript } from '../utils/mapUtils';

export default {
  name: 'SoldTransactionsMap',
  components: {
    SoldTransactionDialog
  },
  data() {
    return {
      showDialog: false,
      dialogData: {}
    };
  },
  props: {
    saleTransactions: {
      type: Object,
      required: false,
    },
  },
  mounted() {
    loadGoogleMapsScript().then(() => {
      setupSaleTransactionsMap(this.saleTransactions, this);
    });
  },
  methods: {
    setClusterLabel(currentMap, centerLatitude, centerLongitude) {
      // Implementation same as before
    },
    // Helper method to debounce a function
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },
    latestEpcDetail(epcDetails) {
      if (!epcDetails || !epcDetails.length) return {};
      return epcDetails.reduce((latest, current) => {
        return new Date(latest.inspection_date) > new Date(current.inspection_date) ? latest : current;
      });
    }
  },
};
</script>