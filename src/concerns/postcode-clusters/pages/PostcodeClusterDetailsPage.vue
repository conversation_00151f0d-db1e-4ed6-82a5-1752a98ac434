<template>
  <div class="PostcodeClusterDetailsPage q-pa-md">
    <div>
      <q-breadcrumbs class="q-py-md q-px-sm">
        <template v-slot:separator>
          <!-- <q-icon size="1.5em" name="chevron_right" color="primary" /> -->
        </template>
        <q-breadcrumbs-el :to="{ name: 'rCuratedNeighbourhoods' }"
                          label="Back To Neighbourhoods"
                          icon="widgets" />
        <q-icon size="1.5em"
                name="chevron_left"
                color="primary" />
        <!-- <q-breadcrumbs-el label="" icon="chevron_left" /> -->
      </q-breadcrumbs>
    </div>
    <div v-if="pageIsLoading"
         class="full-width flex justify-center items-center"
         style="height: 80vh">
      <q-circular-progress indeterminate
                           rounded
                           size="50px"
                           color="primary"
                           class="q-ma-md" />
    </div>
    <div v-else
         class="full-width">
      <div class="q-mb-lg">
        <q-card class="q-pa-md q-mb-lg">
          <q-card-section>
            <q-avatar icon="mdi-map-marker"
                      color="primary"
                      text-color="white"
                      size="xl"
                      class="q-mr-md" />
            <div>
              <h5 class="q-mb-none">{{ clusterDetails.cluster_name }}</h5>
              <q-separator />
              <p>{{ clusterDetails.cluster_description }}</p>
            </div>
          </q-card-section>
        </q-card>

        <div v-if="clusterDetails.uuid"
             class="row q-col-gutter-md">
          <PostCodeClusterMap :clusterDetails="clusterDetails" />
        </div>

        <div v-if="clusterDetails.benchmark_property"
             class="q-mt-lg q-mb-lg">
          <q-card class="q-pa-md q-mb-lg">
            <q-card-section>
              <q-avatar icon="mdi-home"
                        color="secondary"
                        text-color="white"
                        size="xl"
                        class="q-mr-md" />
              <div>
                <h5 class="q-mb-sm">Representative Property in this Neighborhood</h5>
                <p>Note: This is a synthetic property representing the average in this neighborhood.</p>
                <q-separator class="q-mt-md q-mb-md" />
                <h6 class="q-mb-md">{{ clusterDetails.benchmark_property.generic_property_title }}</h6>
                <div class="text-body1 q-mb-md">
                  Average price {{ clusterDetails.cluster_average_property_price_cents }}
                </div>

                <div class="text-body1 q-mb-md">
                  {{ clusterDetails.benchmark_property.generic_property_description }}
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import PostCodeClusterMap from "src/concerns/postcode-clusters/components/PostCodeClusterMap.vue"
import useAreaClusters from "src/compose/useAreaClusters.js"
export default {
  components: {
    PostCodeClusterMap,
  },
  setup(props) {
    const { getPostcodeCluster } = useAreaClusters()
    return {
      getPostcodeCluster,
    }
  },
  props: {
  },
  mounted() {
    let clusterUuiddd = this.$route.params.areaClusterUuid
    //  'cf02d8cf-07a4-44aa-8c26-a21f0cd3ba09'
    this.getPostcodeCluster(clusterUuiddd)
      .then((response) => {
        this.clusterDetails = response.data || {}
      })
      .catch((error) => { })
  },
  data() {
    return {
      pageIsLoading: false,
      clusterDetails: {},
    }
  },
  methods: {
    // addNewProperty() {
    //   this.$q.notify({
    //     message: 'Property creation coming soon',
    //     color: 'info'
    //   })
    // }
  },
  computed: {
    // noReferenceProperties() {
    //   return this.referenceProperties.length < 1
    // },
  },
  methods: {},
  watch: {},
}
</script>


<style scoped>
/* .PostcodeClusterDetailsPage {
  max-width: 900px;
  margin: 0 auto;
} */

.q-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.q-avatar {
  float: left;
  margin-right: 16px;
}

.q-card-section {
  display: flex;
  align-items: center;
}

h5,
h6 {
  font-weight: bold;
}

p {
  margin: 0;
}
</style>