<template>
  <div class="reference-properties q-pa-md">
    <div v-if="localClusters.length > 0"
         class="max-ctr">
      <div>
        <h2 class="text-h3 text-center q-mb-sm  text-primary">
          Great neighbourhoods for house hunting in Nuneaton
        </h2>
        <!-- <h2 class="text-h3 text-center q-mb-sm  text-primary">
          Some outstanding value areas in Nuneaton
        </h2> -->
      </div>
      <PostcodeClustersList :incomingClusters="localClusters"></PostcodeClustersList>
      <div>
        <div v-if="clusterDetails.uuid"
             class="row q-col-gutter-md">
          <PostCodeClustersMap :incomingClusters="localClusters"></PostCodeClustersMap>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import PostcodeClustersList from "src/concerns/postcode-clusters/components/PostcodeClustersList.vue"
import PostCodeClustersMap from "src/concerns/postcode-clusters/components/PostCodeClustersMap.vue"
import useAreaClusters from "src/compose/useAreaClusters.js"
export default {
  components: {
    PostcodeClustersList,
    PostCodeClustersMap
  },
  setup(props) {
    const { getPostcodeClusters } = useAreaClusters()
    return {
      getPostcodeClusters,
    }
  },
  props: {
  },
  mounted() {

  },
  data() {
    return {
      pageIsLoading: false,
      // clusterDetails: {},
      localClusters: [],
    }
  },
  mounted() {
    this.getPostcodeClusters()
      .then((response) => {
        this.localClusters = response.data || []
      })
      .catch((error) => { })
  },
  methods: {},
  computed: {
    clusterDetails() {
      return this.localClusters[0]
      // .length < 1
    },
    // noReferenceProperties() {
    //   return this.referenceProperties.length < 1
    // },
  },
  methods: {},
  watch: {},
}
</script>

<style scoped>
/* .q-card {
  transition: all 0.3s ease;
}

.q-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 25px 0 rgba(0, 0, 0, .1);
} */
</style>
