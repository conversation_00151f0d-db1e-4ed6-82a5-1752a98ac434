<template>
  <div class="sold-properties-page q-pa-md">
    <div v-if="saleTransactions.confirmed_st_epcs.length > 0"
         class="max-ctr">
      <div>
        <h2 class="text-h3 text-center q-mb-sm  text-primary">
          Recently sold in Nuneaton
        </h2>
        <div>
          <SoldTransactionsMap :saleTransactions="saleTransactions"></SoldTransactionsMap>
        </div>
        <div>
          <SoldTransactionsList :saleTransactions="saleTransactions"></SoldTransactionsList>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import SoldTransactionsMap from "src/concerns/postcode-clusters/components/SoldTransactionsMap.vue"
import SoldTransactionsList from "src/concerns/postcode-clusters/components/SoldTransactionsList.vue"
import useSaleTransactions from "src/compose/useSaleTransactions.js"
export default {
  components: {
    SoldTransactionsMap,
    SoldTransactionsList
  },
  setup(props) {
    const { getSaleTransactions } = useSaleTransactions()
    return {
      getSaleTransactions,
    }
  },
  // props: {
  // },
  data() {
    return {
      pageIsLoading: false,
      saleTransactions: {
        confirmed_st_epcs: []
      }
    }
  },
  mounted() {
    this.getSaleTransactions()
      .then((response) => {
        this.saleTransactions = response.data || []
      })
      .catch((error) => { })
  },
  methods: {},
  computed: {
    // clusterDetails() {
    //   return this.localClusters[0]
    // },
  },
  methods: {},
  watch: {},
}
</script>

<style scoped>
/* .q-card {
  transition: all 0.3s ease;
}

.q-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 25px 0 rgba(0, 0, 0, .1);
} */
</style>
