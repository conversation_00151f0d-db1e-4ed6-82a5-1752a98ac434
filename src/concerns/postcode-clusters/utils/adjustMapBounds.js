export function adjustMapBounds(currentMap, bounds) {
  console.log(`prezoom is ${currentMap.getZoom()}`)
  console.log(`postzoom is ${currentMap.getZoom()}`)
  // currentMap.setZoom(18)
  var offset = 0.0109 // Increase offset if necessary
  if (!bounds || bounds.isEmpty()) {
    console.error('Bounds are empty or undefined')
  } else {
    var center = bounds.getCenter()
    const newBounds = new google.maps.LatLngBounds()

    newBounds.extend(
      new google.maps.LatLng(center.lat() + offset, center.lng() + offset)
    )
    newBounds.extend(
      new google.maps.LatLng(center.lat() - offset, center.lng() - offset)
    )

    currentMap.fitBounds(newBounds)
  }
}
