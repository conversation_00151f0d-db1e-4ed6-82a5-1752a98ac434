export function createMarkerElement(location) {
  const markerElement = document.createElement('div')
  markerElement.style.backgroundColor = 'purple'
  markerElement.style.color = 'white'
  markerElement.style.minWidth = '40px'
  markerElement.style.height = '30px'
  markerElement.style.display = 'flex'
  markerElement.style.alignItems = 'center'
  markerElement.style.justifyContent = 'center'
  markerElement.style.borderRadius = '10px'
  markerElement.style.fontSize = '12px'
  markerElement.style.padding = '0px 10px'
  markerElement.style.fontWeight = 'bold'
  if (location.sold_transactions) {
    markerElement.textContent =
      location.sold_transactions[0]?.short_formatted_sold_price || 'N/A'
  }
  if (location.markerText) {
    markerElement.textContent = location.markerText
  }
  return markerElement
}

export function createMarker(position, markerElement, currentMap) {
  return new google.maps.marker.AdvancedMarkerElement({
    position: position,
    map: currentMap,
    content: markerElement,
    gmpClickable: true,
  })
}

export function createRegularMarker(position, currentMap, labelText) {
  return new google.maps.Marker({
    position: position,
    map: currentMap,
    label: {
      text: labelText,
      color: 'white',
      fontSize: '26px',
      fontWeight: 'bold',
    },
    icon: {
      path: google.maps.SymbolPath.CIRCLE,
      scale: 0, // Hide the default marker icon
    },
  })
}
