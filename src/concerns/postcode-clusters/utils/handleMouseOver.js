export function handleMouseOver(
  location,
  context,
  stInfWin,
  position,
  currentMap,
  openInfoWindow
) {
  const latestEpc = location.epc_details[0] // context.latestEpcDetail(location.epc_details)
  const contentString = `
    <div>
      <h6>${location.street_address}</h6>
      <p>Sold for: ${
        location.sold_transactions[0]?.formatted_sold_price || 'N/A'
      }</p>
      <p>on: ${location.sold_transactions[0]?.sold_date || 'N/A'}</p>
      <p>Total floor area: ${latestEpc?.total_floor_area || 'N/A'}</p>
      <p>from EPC on: ${latestEpc?.inspection_date || 'N/A'}</p>
      <button id="more-info-button">More</button>
    </div>
  `
  stInfWin.setContent(contentString)
  stInfWin.setPosition(position)

  if (openInfoWindow) {
    openInfoWindow.close()
  }
  stInfWin.open(currentMap)

  google.maps.event.addListenerOnce(stInfWin, 'domready', () => {
    document
      .getElementById('more-info-button')
      .addEventListener('click', () => {
        context.dialogData = {
          street_address: location.street_address,
          sold_price:
            location.sold_transactions[0]?.formatted_sold_price || 'N/A',
          sold_date: location.sold_transactions[0]?.sold_date || 'N/A',
          total_floor_area: latestEpc?.total_floor_area || 'N/A',
          epc_date: latestEpc?.inspection_date || 'N/A',
        }
        context.showDialog = true
      })
  })
}
