import { initializeMap } from './initializeMap'
import {
  createMarkerElement,
  createMarker,
  createRegularMarker,
} from './createMarker'
import { handleMouseOver } from './handleMouseOver'
import { adjustMapBounds } from './adjustMapBounds'

function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}
export function setupSaleTransactionsMap(saleTransactions, context) {
  const currentMap = initializeMap()
  const bounds = new google.maps.LatLngBounds()
  let openInfoWindow = null

  if (saleTransactions.confirmed_st_epcs) {
    saleTransactions.confirmed_st_epcs.forEach((location) => {
      if (location.latitude === null || location.longitude === null) {
        console.log(
          `Skipping location with null lat/long: Loc: ${location.street_address}`
        )
        return
      }
      const position = { lat: location.latitude, lng: location.longitude }
      const markerElement = createMarkerElement(location)
      const marker = createMarker(position, markerElement, currentMap)

      //without below mouseover will not trigger!!!
      marker.addListener('click', ({ domEvent, latLng }) => {
        console.log('m')
      })

      const stInfWin = new google.maps.InfoWindow()
      let markerClosed = true

      const debouncedMouseover = context.debounce((event) => {
        if (markerClosed) {
          handleMouseOver(
            location,
            context,
            stInfWin,
            position,
            currentMap,
            openInfoWindow
          )
          openInfoWindow = stInfWin
          markerClosed = false
        }
      }, 200)

      marker.content.addEventListener('mouseover', debouncedMouseover)

      const debouncedMouseout = context.debounce((event) => {
        stInfWin.close()
        markerClosed = true
      }, 8000)

      marker.content.addEventListener('mouseout', debouncedMouseout)

      bounds.extend(position)
    })
  }
  adjustMapBounds(currentMap, bounds)
}

export function loadGoogleMapsScript() {
  return new Promise((resolve, reject) => {
    if (window.google && window.google.maps) {
      resolve()
    } else {
      const script = document.createElement('script')
      console.log(`GMAPS_API_KEY2 is ${process.env.GMAPS_API_KEY}`)
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.GMAPS_API_KEY}&libraries=marker`
      script.async = true
      script.onload = resolve
      script.onerror = reject
      document.head.appendChild(script)
    }
  })
}

export function setupMapForPostcodeCluster(
  clusterDetails,
  setClusterLabel,
  setBB,
  context
) {
  let postcodeAreas = clusterDetails.postcode_areas
  let centerLatitude = parseFloat(clusterDetails.center_latitude)
  let centerLongitude = parseFloat(clusterDetails.center_longitude)

  const currentMap = new google.maps.Map(document.getElementById('map'), {
    center: { lat: centerLatitude, lng: centerLongitude },
    zoom: 16,
    mapId: 'DEMO_MAP_ID',
    styles: [
      {
        featureType: 'poi',
        elementType: 'all',
        stylers: [{ visibility: 'off' }],
      },
    ],
  })

  const bounds = new google.maps.LatLngBounds()
  let openInfoWindow = null

  if (postcodeAreas) {
    postcodeAreas.forEach((postcodeArea) => {
      if (postcodeArea) {
        const lastPolygonCoords = setBB(currentMap, postcodeArea)
        if (lastPolygonCoords) {
          lastPolygonCoords.forEach((coord) => bounds.extend(coord))
        }
        if (postcodeArea.realty_assets) {
          postcodeArea.realty_assets.forEach((location) => {
            if (location.latitude === null || location.longitude === null) {
              console.log(
                `Skipping location with null lat/long: Loc: ${location.street_address}`
              )
              return
            }
            const position = { lat: location.latitude, lng: location.longitude }
            const markerElement = createMarkerElement(location)
            const marker = createMarker(position, markerElement, currentMap)

            //without below mouseover will not trigger!!!
            marker.addListener('click', ({ domEvent, latLng }) => {
              console.log('m')
            })

            const stInfWin = new google.maps.InfoWindow()
            let markerClosed = true

            const debouncedMouseover = debounce((event) => {
              if (markerClosed) {
                handleMouseOver(
                  location,
                  context,
                  stInfWin,
                  position,
                  currentMap,
                  openInfoWindow
                )
                openInfoWindow = stInfWin
                markerClosed = false
              }
            }, 200)

            marker.content.addEventListener('mouseover', debouncedMouseover)

            const debouncedMouseout = debounce((event) => {
              stInfWin.close()
              markerClosed = true
            }, 8000)

            marker.content.addEventListener('mouseout', debouncedMouseout)

            bounds.extend(position)
          })
        }
      }
    })
  }

  if (!clusterDetails.center_latitude || !clusterDetails.center_longitude) {
    currentMap.fitBounds(bounds)
  } else {
    setClusterLabel(currentMap, centerLatitude, centerLongitude)
  }

  // adjustMapBounds(currentMap, bounds)
}

export function setupMultiplePoints(
  gmapInstance,
  soldDataPoints,
  markerClickCallback
) {
  if (soldDataPoints && soldDataPoints.length > 0) {
    const bounds = new google.maps.LatLngBounds()
    let infWinToBeShown = false
    soldDataPoints.forEach((singlePoint) => {
      setupSinglePoint(
        gmapInstance,
        {
          mapLat: singlePoint.transactionLatitude,
          mapLng: singlePoint.transactionLongitude,
          dataItemLabel: singlePoint.dataItemLabel,
          soldPriceShort: singlePoint.soldPriceShort,
          SoldTransactionId: singlePoint.SoldTransactionId,
          markerText: singlePoint.soldPriceShort,
        },
        infWinToBeShown,
        markerClickCallback
      )

      bounds.extend({
        lat: singlePoint.transactionLatitude,
        lng: singlePoint.transactionLongitude,
      })
    })
    gmapInstance.fitBounds(bounds)
    // adjustMapBounds(gmapInstance, bounds)
  }
}
export function setupSinglePoint(
  gmapInstance,
  singlePoint = null,
  infWinToBeShown = false,
  markerClickCallback = null
) {
  // const gmapInstance = initializeMap()
  // const bounds = new google.maps.LatLngBounds()
  // Add a marker for the single point if provided
  if (
    singlePoint &&
    singlePoint.mapLat !== undefined &&
    singlePoint.mapLng !== undefined
  ) {
    const positionSingle = { lat: singlePoint.mapLat, lng: singlePoint.mapLng }

    const markerElement = createMarkerElement(singlePoint)
    const marker = createMarker(positionSingle, markerElement, gmapInstance)
    // const marker = createRegularMarker(
    //   positionSingle,
    //   gmapInstance,
    //   singlePoint.markerText
    // )

    if (infWinToBeShown) {
      const infWin = createInfoWindow(singlePoint, positionSingle, gmapInstance)
    }
    //without below mouseover will not trigger!!!
    marker.addListener('click', ({ domEvent, latLng }) => {
      if (markerClickCallback) {
        markerClickCallback(singlePoint)
      }
      // console.log('m')
    })

    gmapInstance.setCenter(positionSingle)
  }
}

function createInfoWindow(singlePoint, positionSingle, currentMap) {
  const contentString = `
  <div style="width: 150px; height: 60px; overflow: auto;">
    <div>${singlePoint.dataItemLabel}</div>
    <div>${singlePoint.markerText}</div>
  </div>
`
  let stInfWin = new google.maps.InfoWindow()
  stInfWin.setContent(contentString)
  stInfWin.setPosition(positionSingle)
  stInfWin.open(currentMap)
}
