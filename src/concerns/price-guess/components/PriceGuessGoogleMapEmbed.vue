<template>
  <div class="q-ma-md">
    <iframe loading="lazy"
            width="100%"
            height="480"
            frameborder="0"
            scrolling="no"
            marginheight="0"
            marginwidth="0"
            :src="mapSrc"></iframe>
  </div>
</template>

<script>
export default {
  name: 'GoogleMapsEmbed',
  props: {
    embedLocation: {
      type: String,
      required: true,
      default: 'Costa Del Sol'
    },
    apiKey: {
      type: String,
      required: false,
      default: `${process.env.GMAPS_API_KEY}`
    }
  },
  computed: {
    mapSrc() {
      return `https://maps.google.it/maps?q=${encodeURIComponent(this.embedLocation)}&output=embed&key=${this.apiKey}`;
    }
  },
  mounted() {
    // Dynamically load Google Maps API script
    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${this.apiKey}`;
    script.async = true;
    document.head.appendChild(script);
  }
};
</script>

<style scoped>
.q-ma-md {
  margin: 16px;
}
</style>