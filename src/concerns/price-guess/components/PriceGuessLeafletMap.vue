<template>
  <div class="q-pa-none">
    <!-- q-no-ssr is crucial for Leaflet in a Quasar/SSR environment -->
    <q-no-ssr>
      <q-card class="property-map-card">
        <q-card-section class="q-pa-none">
          <!-- Show a loading spinner while Leaflet and map data are being prepared -->
          <div v-if="!isMapReady"
               class="map-container flex flex-center">
            <q-spinner-dots color="primary"
                            size="40px" />
          </div>

          <!-- The map component is only rendered on the client and shown when ready -->
          <component :is="LMap"
                     v-show="isMapReady"
                     v-if="LMap"
                     ref="map"
                     v-model:zoom="zoom"
                     v-model:center="center"
                     :use-global-leaflet="false"
                     class="map-container"
                     @ready="onMapReady">
            <!-- Standard OpenStreetMap tile layer -->
            <component :is="LTileLayer"
                       :url="tileLayerUrl"
                       :attribution="attribution"
                       layer-type="base"
                       name="OpenStreetMap" />

            <!-- A single loop renders all markers, making the template clean -->
            <component :is="LMarker"
                       v-for="property in allProperties"
                       :key="property.id"
                       :lat-lng="[property.lat, property.lng]"
                       :icon="property.icon"
                       @click="onMarkerClick(property)">
              <!-- Each marker gets a popup with custom content -->
              <component :is="LPopup"
                         :content="getPopupContent(property)" />
            </component>
          </component>
        </q-card-section>
      </q-card>
    </q-no-ssr>
  </div>
</template>

<script setup>
// Use <script setup> for a more concise and modern Vue 3 syntax
import { ref, shallowRef, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { QCard, QCardSection, QSpinnerDots } from 'quasar';

// --- PROPS ---
const props = defineProps({
  currentProperty: {
    type: Object,
    default: () => ({}),
  },
  relevantSoldTransactions: {
    type: Array,
    default: () => [],
  },
  dossierAssetsComparisons: {
    type: Array,
    default: () => [],
  },
  propIdToZoomTo: {
    type: String,
    default: null,
  },
});

// --- STATE ---
const $router = useRouter();
const map = ref(null);
const zoom = ref(15);
const center = ref([52.3676, 4.9041]); // Default to Amsterdam
const isMapReady = ref(false);

// --- LEAFLET DYNAMIC IMPORTS & CONFIG ---
// Use shallowRef for complex, non-reactive objects like component definitions
// and external library instances to avoid performance overhead.
const LMap = shallowRef(null);
const LTileLayer = shallowRef(null);
const LMarker = shallowRef(null);
const LPopup = shallowRef(null);
const L = shallowRef(null); // To hold the main Leaflet library object

const icons = shallowRef({ main: null, comparison: null, sale: null });
const tileLayerUrl = 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';
const attribution = '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors';

// --- HELPER FUNCTIONS ---
const isValidCoordinate = (lat, lng) =>
  typeof lat === 'number' && typeof lng === 'number' && isFinite(lat) && isFinite(lng) &&
  lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180;

// Centralized data mapping to keep the logic DRY (Don't Repeat Yourself)
const mapPropertyData = (property, type) => {
  if (!property) return null;

  const dataMap = {
    main: {
      lat: property.latitude,
      lng: property.longitude,
      id: `main_${property.uuid || 'default'}`,
      title: property.title || 'Main Property',
      address: property.street_address || 'N/A',
      price: property.formatted_display_price || 'N/A',
      bgImage: property.sale_listing_pics?.[0]?.image_details?.url,
    },
    comparison: {
      lat: property.right_side_property?.latitude,
      lng: property.right_side_property?.longitude,
      id: `comp_${property.right_side_property?.uuid}`,
      title: property.right_side_property?.title || 'Comparison Property',
      address: property.right_side_property?.street_address || 'N/A',
      price: property.right_side_property?.formatted_display_price || 'N/A',
      bgImage: property.right_side_property?.sale_listing_pics?.[0]?.image_details?.url,
    },
    sale: {
      lat: property.st_epc_latitude,
      lng: property.st_epc_longitude,
      id: `sale_${property.st_uuid}`,
      title: 'Recent Sale',
      address: property.st_address || 'N/A',
      price: property.formatted_sold_price || 'N/A',
      bgImage: null, // Sales often don't have images
    },
  };

  const mapped = dataMap[type];
  if (!isValidCoordinate(mapped.lat, mapped.lng)) return null;

  return {
    ...mapped,
    type,
    icon: icons.value[type],
    bgImage: mapped.bgImage || `https://dummyimage.com/200x100/cccccc/ffffff.png&text=No+Image`,
  };
};

// --- COMPUTED PROPERTIES ---
const allProperties = computed(() => {
  // Don't compute properties until Leaflet and icons are loaded to prevent errors
  if (!L.value || !icons.value.main) return [];

  const main = mapPropertyData(props.currentProperty, 'main');
  const comparisons = props.dossierAssetsComparisons.map(p => mapPropertyData(p, 'comparison'));
  const sales = props.relevantSoldTransactions.map(p => mapPropertyData(p, 'sale'));

  // Filter out any entries that had invalid coordinates or were otherwise null
  return [main, ...comparisons, ...sales].filter(Boolean);
});

// --- LIFECYCLE HOOKS ---
onMounted(async () => {
  // Ensure we are on the client side before trying to load Leaflet
  if (typeof window !== 'undefined') {
    await loadLeaflet();
  }
});

// --- METHODS ---
const loadLeaflet = async () => {
  try {
    const leaflet = await import('leaflet');
    L.value = leaflet;

    const vueLeaflet = await import('@vue-leaflet/vue-leaflet');
    LMap.value = vueLeaflet.LMap;
    LTileLayer.value = vueLeaflet.LTileLayer;
    LMarker.value = vueLeaflet.LMarker;
    LPopup.value = vueLeaflet.LPopup;

    await import('leaflet/dist/leaflet.css');

    // Create icons once Leaflet is loaded.
    // We must replace the entire .value of a shallowRef to trigger reactivity.
    icons.value = {
      main: createCustomIcon('#4CAF50', 'home'),        // Green for main
      comparison: createCustomIcon('#2196F3', 'compare_arrows'), // Blue for comparisons
      sale: createCustomIcon('#FF9800', 'sell'),       // Orange for sales
    };
  } catch (error) {
    console.error('Failed to load Leaflet:', error);
  }
};

const createCustomIcon = (color, iconName) => {
  if (!L.value) return null;
  // Use L.divIcon to create custom HTML-based markers
  return L.value.divIcon({
    html: `<div style="background-color: ${color}; width: 32px; height: 32px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 6px rgba(0,0,0,0.4); display: flex; align-items: center; justify-content: center; color: white;">
             <i class="material-icons" style="font-size: 18px;">${iconName}</i>
           </div>`,
    className: 'custom-div-icon', // This class is useful for unscoped CSS targeting
    iconSize: [32, 32],
    iconAnchor: [16, 16],
    popupAnchor: [0, -16],
  });
};

const onMapReady = () => {
  // This is called by the LMap component when it's initialized
  updateMapView();
};

const updateMapView = () => {
  const mapInstance = map.value?.leafletObject;
  if (!mapInstance) return;

  // If there are no properties, just show the map at its default location
  if (allProperties.value.length === 0) {
    isMapReady.value = true;
    return;
  }

  const propertyToZoomTo = allProperties.value.find(p => p.id === props.propIdToZoomTo);

  if (propertyToZoomTo) {
    // Priority 1: A specific property is requested via prop
    panToLocation(propertyToZoomTo.lat, propertyToZoomTo.lng, 17);
  } else if (allProperties.value.length > 1) {
    // Priority 2: More than one property, fit them all in view
    const bounds = L.value.latLngBounds(allProperties.value.map(p => [p.lat, p.lng]));
    if (bounds.isValid()) {
      mapInstance.fitBounds(bounds, { padding: [50, 50] });
    }
  } else {
    // Priority 3: Only one property, center on it
    const singleProperty = allProperties.value[0];
    panToLocation(singleProperty.lat, singleProperty.lng, 15);
  }

  // Now that the view is set, show the map
  isMapReady.value = true;
};

const panToLocation = (lat, lng, newZoom = 17) => {
  if (isValidCoordinate(lat, lng)) {
    center.value = [lat, lng];
    zoom.value = newZoom;
  }
};

const onMarkerClick = (property) => {
  panToLocation(property.lat, property.lng);
  $router.push({ query: { listing: property.id } });
};

const getPopupContent = (property) => {
  return `<div class="map-popup-content" style="background-image: url('${property.bgImage}');">
            <div class="popup-text-scrim">
              <h4 class="popup-title">${property.title}</h4>
              <p class="popup-address">${property.address}</p>
              ${property.price !== 'N/A' ? `<p class="popup-price">${property.price}</p>` : ''}
            </div>
          </div>`;
};

// --- WATCHERS ---
// Watch for external changes (e.g., from a URL query) to pan the map
watch(() => props.propIdToZoomTo, (newId) => {
  if (newId && isMapReady.value) {
    const property = allProperties.value.find(p => p.id === newId);
    if (property) {
      panToLocation(property.lat, property.lng);
    }
  }
});

// If the property data loads after the map is already ready, update the view
watch(allProperties, (newProperties, oldProperties) => {
  if (newProperties.length > 0 && oldProperties.length === 0 && isMapReady.value) {
    updateMapView();
  }
});
</script>

<style scoped>
.property-map-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
}

.map-container {
  height: 500px;
  /* You can adjust this height */
  width: 100%;
  background-color: #e0e0e0;
  /* Placeholder background for loading state */
  z-index: 1;
}

/* Deep selectors are needed to style Leaflet's dynamically generated HTML */
:deep(.custom-div-icon) {
  background: transparent;
  border: none;
}

:deep(.leaflet-popup-content-wrapper) {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.leaflet-popup-content) {
  margin: 0;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.leaflet-popup-tip) {
  background: transparent;
}

/* Custom styles for the popup's inner content */
:deep(.map-popup-content) {
  width: 200px;
  min-height: 100px;
  background-size: cover;
  background-position: center;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

:deep(.popup-text-scrim) {
  padding: 10px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.85) 0%, rgba(0, 0, 0, 0) 100%);
}

:deep(.popup-title) {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: bold;
  line-height: 1.2;
}

:deep(.popup-address),
:deep(.popup-price) {
  margin: 2px 0 0 0;
  font-size: 12px;
  line-height: 1.2;
}
</style>