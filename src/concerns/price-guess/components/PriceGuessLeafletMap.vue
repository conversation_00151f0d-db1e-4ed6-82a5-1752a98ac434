<template>
  <div class="q-pa-none">
    <q-card class="property-map-card">
      <q-card-section class="q-pa-none">
        <div ref="map"
             class="map-container" />
      </q-card-section>
    </q-card>

    <!-- Property Summary List -->
    <!-- <q-list bordered
            separator
            class="q-mt-md property-summary-list">
      <q-item v-for="property in allProperties"
              :key="property.id"
              clickable
              v-ripple
              @click="panAndZoomToLocation(property)"
              :ref="el => setItemRef(el, property.id)"
              class="property-summary-item">
        <q-item-section avatar>
          <q-avatar :color="property.type === 'main' ? 'green' : (property.type === 'comparison' ? 'red' : 'blue')"
                    text-color="white"
                    size="sm">
            {{ property.type.charAt(0).toUpperCase() }}
          </q-avatar>
        </q-item-section>
        <q-item-section>
          <q-item-label>{{ property.title }}</q-item-label>
          <q-item-label caption
                        lines="1">{{ property.address }}</q-item-label>
        </q-item-section>
        <q-item-section side
                        top>
          <q-item-label caption>{{ property.price }}</q-item-label>
        </q-item-section>
      </q-item>
    </q-list> -->
  </div>
</template>

<script>
import { defineComponent, ref, onMounted, computed, nextTick } from 'vue';
import { Loader } from '@googlemaps/js-api-loader';
import { QCard, QCardSection, QList, QItem, QItemSection, QItemLabel, QAvatar } from 'quasar';
import { useRouter } from 'vue-router'

export default defineComponent({
  name: 'PriceGuessGoogleMap',
  components: {
    QCard,
    QCardSection,
    // QList,
    // QItem,
    // QItemSection,
    // QItemLabel,
    // QAvatar,
  },
  props: {
    currentProperty: {
      type: Object,
      required: false,
      default: () => ({}),
    },
    relevantSoldTransactions: {
      type: Array,
      default: () => [],
    },
    dossierAssetsComparisons: {
      type: Array,
      default: () => [],
    },
    // initialProperty: { // New prop for the property to zoom to on load
    //   type: Object,
    //   default: null,
    // },
    propIdToZoomTo: {
      type: String,
      default: null,
    },
  },
  setup(props) {
    const $router = useRouter()
    const map = ref(null);
    const googleMapInstance = ref(null);
    const markers = ref({});
    const itemRefs = ref({});

    const allProperties = computed(() => {
      const properties = [];

      if (props.currentProperty?.latitude && props.currentProperty?.longitude) {
        properties.push({
          bgImage: props.currentProperty.sale_listing_pics[0]?.image_details?.url || 'https://dummyimage.com/200x100?text=.',
          id: `main_${props.currentProperty.uuid || 'default'}`,
          type: 'main',
          title: props.currentProperty.title || 'Main Property',
          address: props.currentProperty.street_address || 'N/A',
          price: props.currentProperty.formatted_display_price || 'N/A',
          lat: props.currentProperty.latitude,
          lng: props.currentProperty.longitude,
          icon: 'http://maps.google.com/mapfiles/ms/icons/green-dot.png',
        });
      }

      props.dossierAssetsComparisons.forEach((comparison, index) => {
        if (comparison.right_side_property?.latitude && comparison.right_side_property?.longitude) {
          properties.push({
            bgImage: comparison.right_side_property.sale_listing_pics[0]?.image_details?.url || 'https://dummyimage.com/200x100?text=.',
            id: `comp_${comparison.right_side_property.uuid || index}`,
            type: 'comparison',
            title: comparison.right_side_property.title || `Comparison ${index + 1}`,
            address: comparison.right_side_property.street_address || 'N/A',
            price: comparison.right_side_property.formatted_display_price || 'N/A',
            lat: comparison.right_side_property.latitude,
            lng: comparison.right_side_property.longitude,
            icon: 'http://maps.google.com/mapfiles/ms/icons/blue-dot.png',
          });
        }
      });

      props.relevantSoldTransactions.forEach((sale, index) => {
        if (sale.st_epc_latitude && sale.st_epc_longitude) {
          properties.push({
            id: `sale_${sale.st_uuid || index}`,
            type: 'sale',
            title: `Recent Sale ${index + 1}`,
            address: sale.st_address || 'N/A',
            price: sale.formatted_sold_price || 'N/A',
            lat: sale.st_epc_latitude,
            lng: sale.st_epc_longitude,
            icon: 'http://maps.google.com/mapfiles/ms/icons/blue-dot.png',
          });
        }
      });

      return properties;
    });

    const initMap = async () => {
      const mapOptions = {
        mapId: process.env.GMAPS_MAP_ID || 'H2C_MAP_ID',
        mapTypeId: 'roadmap',
        zoomControl: true,
      };

      googleMapInstance.value = new google.maps.Map(map.value, mapOptions);
      const googleMap = googleMapInstance.value;
      const bounds = new google.maps.LatLngBounds();

      const { AdvancedMarkerElement, PinElement } = await google.maps.importLibrary("marker");

      allProperties.value.forEach((property) => {
        if (property.lat && property.lng) {
          const position = { lat: property.lat, lng: property.lng };

          const marker = new google.maps.Marker({
            position: position,
            map: googleMap,
            icon: {
              url: property.icon,
              scaledSize: new google.maps.Size(32, 32),
            },
            title: property.title,
          });

          markers.value[property.id] = marker;

          const infoWindow = new google.maps.InfoWindow({
            content: `
              <div style="
                max-width: 200px;
                background-image: url('${property.bgImage}');
                background-size: cover;
                background-position: center;
                padding: 10px;
                border-radius: 5px;
                color: white;
                text-shadow: 1px 1px 2px black;
              ">
                <h4>${property.title}</h4>
                <p><strong>Address:</strong> ${property.address}</p>
              </div>
            `,
            pixelOffset: new google.maps.Size(0, -30)
          });

          marker.infoWindow = infoWindow;

          marker.addListener('click', () => {
            infoWindow.open(googleMap, marker);
          });
          marker.addListener('mouseover', () => {
            infoWindow.open(googleMap, marker);
          });

          bounds.extend(position);
        }
      });

      let propertyToZoomTo = allProperties.value.find(
        point => point.id === props.propIdToZoomTo)

      // Check if initialProperty is provided and has valid coordinates
      if (propertyToZoomTo) {
        googleMap.setCenter({ lat: propertyToZoomTo.lat, lng: propertyToZoomTo.lng });
        googleMap.setZoom(17);
        // Optionally open info window for the initial property
        const marker = markers.value[propertyToZoomTo.id];
        if (marker?.infoWindow) {
          marker.infoWindow.open(googleMap, marker);
        }
      } else if (!bounds.isEmpty()) {
        // Fallback to original bounds fitting logic
        const distinctPoints = allProperties.value.filter((p) => p.lat && p.lng).length > 1;
        if (!distinctPoints || bounds.getNorthEast().equals(bounds.getSouthWest())) {
          googleMap.setCenter(bounds.getCenter());
          googleMap.setZoom(15);
        } else {
          googleMap.fitBounds(bounds);
        }
      }
    };

    const panAndZoomToLocation = (property) => {
      if (googleMapInstance.value && property.lat && property.lng) {
        googleMapInstance.value.setCenter({ lat: property.lat, lng: property.lng });
        googleMapInstance.value.setZoom(17);
        const marker = markers.value[property.id];
        if (marker?.infoWindow) {
          marker.infoWindow.open(googleMapInstance.value, marker);
        }
      }
      // debugger
      $router.push({
        query: {
          // ...this.$route.query, // Preserve existing query params
          ...{ listing: property.id }, // Add or update new query params
        },
      });

    };

    const scrollToSummary = async (propertyId) => {
      await nextTick();
      const element = itemRefs.value[propertyId];
      if (element && element.scrollIntoView) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    };

    const setItemRef = (el, id) => {
      if (el) {
        itemRefs.value[id] = el;
      }
    };

    onMounted(() => {
      const loader = new Loader({
        apiKey: process.env.GMAPS_API_KEY || '',
        version: 'weekly',
        libraries: ['places', 'marker'],
      });
      loader.load().then(() => {
        initMap();
      }).catch((error) => {
        console.error('Failed to load Google Maps:', error);
      });
      itemRefs.value = {};
    });

    return {
      map,
      googleMapInstance,
      markers,
      allProperties,
      panAndZoomToLocation,
      scrollToSummary,
      setItemRef,
    };
  },
});
</script>

<style scoped>
.map-container {
  height: 500px;
  width: 100%;
}

.property-map-card {
  margin: 0 auto;
}

.property-summary-list {
  max-height: 400px;
  overflow-y: auto;
}

.property-summary-item {
  cursor: pointer;
}

.property-summary-item:hover {
  background-color: #f5f5f5;
}
</style>