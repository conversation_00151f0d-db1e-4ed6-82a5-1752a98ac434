<template>
  <div class="q-pa-none">
    <!-- q-no-ssr prevents this component from rendering on the server -->
    <q-no-ssr>
      <q-card class="property-map-card">
        <q-card-section class="q-pa-none">
          <!-- A placeholder while the map loads to improve user experience -->
          <div v-if="!isMapReady"
               class="map-container flex flex-center">
            <q-spinner-dots color="primary"
                            size="40px" />
          </div>

          <!-- The :is="LMap" pattern is great for client-side only components -->
          <component :is="LMap"
                     v-show="isMapReady"
                     v-if="LMap"
                     ref="map"
                     v-model:zoom="zoom"
                     v-model:center="center"
                     :use-global-leaflet="false"
                     class="map-container"
                     @ready="onMapReady">
            <component :is="LTileLayer"
                       :url="tileLayerUrl"
                       :attribution="attribution"
                       layer-type="base"
                       name="OpenStreetMap" />

            <!-- Single v-for loop for all property markers -->
            <component :is="LMarker"
                       v-for="property in allProperties"
                       :key="property.id"
                       :lat-lng="[property.lat, property.lng]"
                       :icon="property.icon"
                       @click="onMarkerClick(property)">
              <component :is="LPopup"
                         :content="getPopupContent(property)" />
            </component>
          </component>
        </q-card-section>
      </q-card>
    </q-no-ssr>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { QCard, QCardSection, QSpinnerDots } from 'quasar';

// --- PROPS ---
const props = defineProps({
  currentProperty: {
    type: Object,
    default: () => ({}),
  },
  relevantSoldTransactions: {
    type: Array,
    default: () => [],
  },
  dossierAssetsComparisons: {
    type: Array,
    default: () => [],
  },
  propIdToZoomTo: {
    type: String,
    default: null,
  },
});

// --- STATE ---
const $router = useRouter();
const map = ref(null);
const zoom = ref(15);
const center = ref([51.505, -0.09]); // Default to London
const isMapReady = ref(false); // Used to show a loading spinner

// --- LEAFLET DYNAMIC IMPORTS ---
// Use refs for components to make them available in the template
const LMap = ref(null);
const LTileLayer = ref(null);
const LMarker = ref(null);
const LPopup = ref(null);

// Store Leaflet instance and icons in reactive refs
const L = ref(null);
const icons = ref({
  main: null,
  comparison: null,
  sale: null,
});

const tileLayerUrl = 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';
const attribution = '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors';

// --- HELPER FUNCTIONS ---
const isValidCoordinate = (lat, lng) =>
  typeof lat === 'number' &&
  typeof lng === 'number' &&
  isFinite(lat) && isFinite(lng) &&
  lat >= -90 && lat <= 90 &&
  lng >= -180 && lng <= 180;

// Centralized data mapping to avoid repetition
const mapPropertyData = (property, type) => {
  if (!property) return null;

  const dataMap = {
    main: {
      lat: property.latitude,
      lng: property.longitude,
      id: `main_${property.uuid || 'default'}`,
      title: property.title || 'Main Property',
      address: property.street_address || 'N/A',
      price: property.formatted_display_price || 'N/A',
      bgImage: property.sale_listing_pics?.[0]?.image_details?.url,
    },
    comparison: {
      lat: property.right_side_property?.latitude,
      lng: property.right_side_property?.longitude,
      id: `comp_${property.right_side_property?.uuid}`,
      title: property.right_side_property?.title || 'Comparison Property',
      address: property.right_side_property?.street_address || 'N/A',
      price: property.right_side_property?.formatted_display_price || 'N/A',
      bgImage: property.right_side_property?.sale_listing_pics?.[0]?.image_details?.url,
    },
    sale: {
      lat: property.st_epc_latitude,
      lng: property.st_epc_longitude,
      id: `sale_${property.st_uuid}`,
      title: 'Recent Sale',
      address: property.st_address || 'N/A',
      price: property.formatted_sold_price || 'N/A',
      bgImage: null, // Sales don't have images in this data structure
    },
  };

  const mapped = dataMap[type];
  if (!isValidCoordinate(mapped.lat, mapped.lng)) return null;

  return {
    ...mapped,
    type,
    icon: icons.value[type],
    bgImage: mapped.bgImage || `https://dummyimage.com/200x100/cccccc/ffffff.png&text=${mapped.title}`,
  };
};

// --- COMPUTED PROPERTIES ---
const allProperties = computed(() => {
  if (!L.value) return []; // Don't compute properties until Leaflet and icons are loaded

  const main = mapPropertyData(props.currentProperty, 'main');
  const comparisons = props.dossierAssetsComparisons.map(p => mapPropertyData(p, 'comparison'));
  const sales = props.relevantSoldTransactions.map(p => mapPropertyData(p, 'sale'));

  // Filter out any null/invalid entries
  return [main, ...comparisons, ...sales].filter(Boolean);
});

// --- LIFECYCLE HOOKS ---
onMounted(async () => {
  if (typeof window !== 'undefined') {
    await loadLeaflet();
  }
});

// --- METHODS ---
const loadLeaflet = async () => {
  try {
    const leaflet = await import('leaflet');
    L.value = leaflet; // Store Leaflet instance

    const vueLeaflet = await import('@vue-leaflet/vue-leaflet');
    LMap.value = vueLeaflet.LMap;
    LTileLayer.value = vueLeaflet.LTileLayer;
    LMarker.value = vueLeaflet.LMarker;
    LPopup.value = vueLeaflet.LPopup;

    await import('leaflet/dist/leaflet.css');

    // Create icons once Leaflet is loaded
    icons.value.main = createCustomIcon('#4CAF50', 'home');
    icons.value.comparison = createCustomIcon('#2196F3', 'compare_arrows');
    icons.value.sale = createCustomIcon('#FF9800', 'sell');
  } catch (error) {
    console.error('Failed to load Leaflet:', error);
  }
};

const createCustomIcon = (color, iconName = 'home') => {
  if (!L.value) return null;
  return L.value.divIcon({
    html: `<div style="background-color: ${color}; width: 30px; height: 30px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 6px rgba(0,0,0,0.3); display: flex; align-items: center; justify-content: center; color: white;">
             <i class="material-icons" style="font-size: 16px;">${iconName}</i>
           </div>`,
    className: 'custom-div-icon', // Keep this for potential unscoped CSS targeting
    iconSize: [30, 30],
    iconAnchor: [15, 15],
    popupAnchor: [0, -15],
  });
};

const updateMapView = () => {
  const mapInstance = map.value?.leafletObject;
  if (!mapInstance || allProperties.value.length === 0) {
    isMapReady.value = true; // Still show map even if empty
    return;
  }

  const propertyToZoomTo = allProperties.value.find(p => p.id === props.propIdToZoomTo);

  if (propertyToZoomTo) {
    // Priority 1: Zoom to a specific property from props
    panToLocation(propertyToZoomTo.lat, propertyToZoomTo.lng, 17);
  } else if (allProperties.value.length > 1) {
    // Priority 2: Fit all properties on the map
    const bounds = L.value.latLngBounds(allProperties.value.map(p => [p.lat, p.lng]));
    if (bounds.isValid()) {
      mapInstance.fitBounds(bounds, { padding: [50, 50] });
    }
  } else {
    // Priority 3: Center on the single available property
    const singleProperty = allProperties.value[0];
    panToLocation(singleProperty.lat, singleProperty.lng, 15);
  }
  isMapReady.value = true;
};

const onMapReady = () => {
  updateMapView();
};

const panToLocation = (lat, lng, newZoom = 17) => {
  if (isValidCoordinate(lat, lng)) {
    center.value = [lat, lng];
    zoom.value = newZoom;
  }
};

const onMarkerClick = (property) => {
  panToLocation(property.lat, property.lng);
  $router.push({ query: { listing: property.id } });
};

const getPopupContent = (property) => {
  return `<div class="map-popup-content" style="background-image: url('${property.bgImage}');">
            <div class="popup-text-scrim">
              <h4 class="popup-title">${property.title}</h4>
              <p class="popup-address">${property.address}</p>
              ${property.price !== 'N/A' ? `<p class="popup-price">${property.price}</p>` : ''}
            </div>
          </div>`;
};

// Watch for external changes to propIdToZoomTo
watch(() => props.propIdToZoomTo, (newId) => {
  if (newId && isMapReady.value) {
    const property = allProperties.value.find(p => p.id === newId);
    if (property) {
      panToLocation(property.lat, property.lng);
    }
  }
});

// Watch for the property data to become available after Leaflet loads
watch(allProperties, (newProperties) => {
  if (newProperties.length > 0 && map.value?.leafletObject) {
    updateMapView();
  }
}, { immediate: true });
</script>

<style scoped>
.property-map-card {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
}

.map-container {
  height: 400px;
  width: 100%;
  background-color: #f0f0f0;
  /* Placeholder background */
  z-index: 1;
}

/* Deep selectors to style Leaflet's generated HTML */
:deep(.custom-div-icon) {
  background: transparent;
  border: none;
}

:deep(.leaflet-popup-content-wrapper) {
  padding: 0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

:deep(.leaflet-popup-content) {
  margin: 0;
  border-radius: 8px;
  overflow: hidden;
}

:deep(.leaflet-popup-tip) {
  background: transparent;
  /* Tip can be hidden if design allows */
}

/* Custom styles for the popup content */
:deep(.map-popup-content) {
  width: 200px;
  min-height: 100px;
  background-size: cover;
  background-position: center;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

:deep(.popup-text-scrim) {
  padding: 8px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0) 100%);
}

:deep(.popup-title) {
  margin: 0;
  font-size: 14px;
  font-weight: bold;
}

:deep(.popup-address),
:deep(.popup-price) {
  margin: 2px 0 0 0;
  font-size: 12px;
}
</style>