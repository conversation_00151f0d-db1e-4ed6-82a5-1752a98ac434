import { ref, computed } from 'vue'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export function usePriceGuess() {
  // Reactive state
  const priceGuessData = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const currentPropertyIndex = ref(0)
  const userGuesses = ref([])
  const gameResults = ref([])

  // API function to fetch price guess data
  const fetchPriceGuessData = async (dossierUuid) => {
    isLoading.value = true
    error.value = null
    
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/dossiers/price_guess/${dossierUuid}`
      const response = await axios.get(apiUrl)
      
      priceGuessData.value = response.data
      currentPropertyIndex.value = 0
      userGuesses.value = []
      gameResults.value = []
      
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to fetch price guess data'
      console.error('Error fetching price guess data:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Computed properties
  const properties = computed(() => {
    return priceGuessData.value?.dossier?.property_details || []
  })

  const currentProperty = computed(() => {
    return properties.value[currentPropertyIndex.value] || null
  })

  const totalProperties = computed(() => {
    return properties.value.length
  })

  const isGameComplete = computed(() => {
    return currentPropertyIndex.value >= totalProperties.value
  })

  const totalScore = computed(() => {
    return gameResults.value.reduce((sum, result) => sum + result.score, 0)
  })

  const maxPossibleScore = computed(() => {
    return totalProperties.value * 100
  })

  // Helper functions
  const formatPrice = (priceInCents, currency = 'GBP') => {
    if (!priceInCents) return 'Price not available'
    
    const amount = priceInCents / 100
    try {
      return new Intl.NumberFormat('en-UK', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount)
    } catch (e) {
      return `${currency} ${amount.toFixed(0)}`
    }
  }

  const getPropertyImages = (property) => {
    return property?.sale_listing_pics || []
  }

  const getMainImage = (property) => {
    const images = getPropertyImages(property)
    return images.length > 0 ? images[0] : null
  }

  const validateGuess = (guess, actualPrice) => {
    const errors = []
    
    if (!guess || guess <= 0) {
      errors.push('Guess must be a positive number')
      return { isValid: false, errors }
    }

    const maxAllowed = actualPrice * 3 // 200% too high
    const minAllowed = actualPrice * 0.1 // 90% too low

    if (guess > maxAllowed) {
      errors.push('Guess is more than 200% too high')
    } else if (guess < minAllowed) {
      errors.push('Guess is less than 90% of actual price')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  const calculateScore = (guess, actualPrice) => {
    const difference = Math.abs((guess - actualPrice) / actualPrice) * 100
    
    if (difference <= 5) return 100
    if (difference <= 10) return 90
    if (difference <= 15) return 80
    if (difference <= 20) return 70
    if (difference <= 25) return 60
    if (difference <= 35) return 50
    if (difference <= 50) return 40
    if (difference <= 75) return 30
    if (difference <= 100) return 20
    return 10
  }

  const getFeedbackMessage = (score, difference) => {
    if (score >= 90) return 'Excellent! You have a great eye for property values.'
    if (score >= 80) return 'Very good! You were very close to the actual price.'
    if (score >= 70) return 'Good guess! You have a decent understanding of the market.'
    if (score >= 60) return 'Not bad! You were reasonably close.'
    if (score >= 50) return 'Fair attempt, but there\'s room for improvement.'
    if (score >= 40) return 'Getting warmer, but still quite a bit off.'
    if (score >= 30) return 'Quite far off, but keep practicing!'
    return 'Very far from the actual price. Study the market more!'
  }

  const submitGuess = (guess) => {
    if (!currentProperty.value) return null

    const actualPrice = currentProperty.value.price_sale_current_cents / 100
    const guessAmount = parseFloat(guess)
    
    const validation = validateGuess(guessAmount, actualPrice)
    if (!validation.isValid) {
      return { success: false, errors: validation.errors }
    }

    const difference = ((guessAmount - actualPrice) / actualPrice) * 100
    const score = calculateScore(guessAmount, actualPrice)
    const feedback = getFeedbackMessage(score, Math.abs(difference))

    const result = {
      property: currentProperty.value,
      guess: guessAmount,
      actualPrice: actualPrice,
      difference: difference,
      score: score,
      feedback: feedback,
      propertyIndex: currentPropertyIndex.value
    }

    gameResults.value.push(result)
    userGuesses.value.push(guessAmount)

    return { success: true, result }
  }

  const nextProperty = () => {
    if (currentPropertyIndex.value < totalProperties.value - 1) {
      currentPropertyIndex.value++
      return true
    }
    return false
  }

  const resetGame = () => {
    currentPropertyIndex.value = 0
    userGuesses.value = []
    gameResults.value = []
  }

  const getScoreColor = (score) => {
    if (score >= 90) return 'positive'
    if (score >= 70) return 'warning'
    return 'negative'
  }

  const getPerformanceRating = (totalScore, maxScore) => {
    const percentage = (totalScore / maxScore) * 100
    
    if (percentage >= 90) return { rating: 'Expert', color: 'positive', icon: 'star' }
    if (percentage >= 80) return { rating: 'Advanced', color: 'positive', icon: 'trending_up' }
    if (percentage >= 70) return { rating: 'Good', color: 'warning', icon: 'thumb_up' }
    if (percentage >= 60) return { rating: 'Fair', color: 'warning', icon: 'thumbs_up_down' }
    if (percentage >= 50) return { rating: 'Beginner', color: 'negative', icon: 'school' }
    return { rating: 'Novice', color: 'negative', icon: 'help' }
  }

  return {
    // State
    priceGuessData,
    isLoading,
    error,
    currentPropertyIndex,
    userGuesses,
    gameResults,

    // Computed
    properties,
    currentProperty,
    totalProperties,
    isGameComplete,
    totalScore,
    maxPossibleScore,

    // Methods
    fetchPriceGuessData,
    formatPrice,
    getPropertyImages,
    getMainImage,
    validateGuess,
    submitGuess,
    nextProperty,
    resetGame,
    getScoreColor,
    getPerformanceRating
  }
}
