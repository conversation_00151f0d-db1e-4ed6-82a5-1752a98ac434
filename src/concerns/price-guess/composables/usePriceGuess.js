import { ref, computed } from 'vue'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export function usePriceGuess() {
  // Reactive state
  const priceGuessData = ref(null)
  const isLoading = ref(false)
  const error = ref(null)
  const currentPropertyIndex = ref(0)
  const userGuesses = ref([])
  const gameResults = ref([])

  // API function to fetch price guess data
  const fetchPriceGuessData = async (dossierUuid) => {
    isLoading.value = true
    error.value = null

    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/dossiers/price_guess/${dossierUuid}`
      const response = await axios.get(apiUrl)

      priceGuessData.value = response.data
      currentPropertyIndex.value = 0
      userGuesses.value = []
      gameResults.value = []

      return response.data
    } catch (err) {
      error.value =
        err.response?.data?.message || 'Failed to fetch price guess data'
      console.error('Error fetching price guess data:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // API function to save price estimate
  const savePriceEstimate = async (estimateData) => {
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/price_estimates`
      const response = await axios.post(apiUrl, {
        price_estimate: estimateData,
      })
      return response.data
    } catch (err) {
      console.error('Error saving price estimate:', err)
      throw err
    }
  }

  // API function to fetch price estimate comparisons
  const fetchPriceEstimateComparisons = async (listingUuid) => {
    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/price_estimates/comparisons/${listingUuid}`
      const response = await axios.get(apiUrl)
      return response.data
    } catch (err) {
      console.error('Error fetching price estimate comparisons:', err)
      throw err
    }
  }

  // Computed properties
  const properties = computed(() => {
    return priceGuessData.value?.dossier?.property_details || []
  })

  const currentProperty = computed(() => {
    return properties.value[currentPropertyIndex.value] || null
  })

  const totalProperties = computed(() => {
    return properties.value.length
  })

  const isGameComplete = computed(() => {
    return currentPropertyIndex.value >= totalProperties.value
  })

  const totalScore = computed(() => {
    return gameResults.value.reduce((sum, result) => sum + result.score, 0)
  })

  const maxPossibleScore = computed(() => {
    return totalProperties.value * 100
  })

  // Helper functions
  const formatPrice = (priceInCents, currency = 'GBP') => {
    if (!priceInCents) return 'Price not available'

    const amount = priceInCents / 100
    try {
      return new Intl.NumberFormat('en-UK', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(amount)
    } catch (e) {
      return `${currency} ${amount.toFixed(0)}`
    }
  }

  const getPropertyImages = (property) => {
    return property?.sale_listing_pics || []
  }

  const getMainImage = (property) => {
    const images = getPropertyImages(property)
    return images.length > 0 ? images[0] : null
  }

  const validateGuess = (guess, actualPrice) => {
    const errors = []

    if (!guess || guess <= 0) {
      errors.push('Guess must be a positive number')
      return { isValid: false, errors }
    }

    const maxAllowed = actualPrice * 3 // 200% too high
    const minAllowed = actualPrice * 0.1 // 90% too low

    if (guess > maxAllowed) {
      errors.push('Guess is more than 200% too high')
    } else if (guess < minAllowed) {
      errors.push('Guess is less than 90% of actual price')
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  const calculateScore = (guess, actualPrice) => {
    const difference = Math.abs((guess - actualPrice) / actualPrice) * 100

    if (difference <= 5) return 100
    if (difference <= 10) return 90
    if (difference <= 15) return 80
    if (difference <= 20) return 70
    if (difference <= 25) return 60
    if (difference <= 35) return 50
    if (difference <= 50) return 40
    if (difference <= 75) return 30
    if (difference <= 100) return 20
    return 10
  }

  const getFeedbackMessage = (score) => {
    if (score >= 90)
      return 'Excellent! You have a great eye for property values.'
    if (score >= 80)
      return 'Very good! You were very close to the actual price.'
    if (score >= 70)
      return 'Good guess! You have a decent understanding of the market.'
    if (score >= 60) return 'Not bad! You were reasonably close.'
    if (score >= 50) return "Fair attempt, but there's room for improvement."
    if (score >= 40) return 'Getting warmer, but still quite a bit off.'
    if (score >= 30) return 'Quite far off, but keep practicing!'
    return 'Very far from the actual price. Study the market more!'
  }

  const submitGuess = async (guess, userInfo = {}) => {
    if (!currentProperty.value) return null

    const actualPrice = currentProperty.value.price_sale_current_cents / 100
    const guessAmount = parseFloat(guess)

    const validation = validateGuess(guessAmount, actualPrice)
    if (!validation.isValid) {
      return { success: false, errors: validation.errors }
    }

    const difference = ((guessAmount - actualPrice) / actualPrice) * 100
    const score = calculateScore(guessAmount, actualPrice)
    const feedback = getFeedbackMessage(score)

    // Prepare estimate data for backend
    const estimateData = {
      estimated_price_cents: Math.round(guessAmount * 100),
      price_at_time_of_estimate_cents:
        currentProperty.value.price_sale_current_cents,
      estimate_currency: currentProperty.value.currency || 'GBP',
      estimate_title: `Price Guess Game - ${currentProperty.value.street_address}`,
      estimate_text: `User guess: ${formatPrice(
        guessAmount * 100,
        currentProperty.value.currency
      )}`,
      estimate_vicinity: currentProperty.value.city,
      estimate_postal_code: currentProperty.value.postal_code,
      estimate_latitude_center: currentProperty.value.latitude,
      estimate_longitude_center: currentProperty.value.longitude,
      estimator_name: userInfo.name || 'Anonymous Player',
      is_ai_estimate: false,
      is_for_sale_listing: true,
      is_for_rental_listing: false,
      is_protected: false,
      percentage_above_or_below: difference,
      listing_uuid: currentProperty.value.uuid,
      user_uuid: userInfo.userUuid || null,
      scoot_uuid: userInfo.scootUuid || null,
      estimate_details: {
        game_score: score,
        property_index: currentPropertyIndex.value,
        game_session_id: userInfo.sessionId || null,
        feedback_message: feedback,
      },
    }

    const result = {
      property: currentProperty.value,
      guess: guessAmount,
      actualPrice: actualPrice,
      difference: difference,
      score: score,
      feedback: feedback,
      propertyIndex: currentPropertyIndex.value,
      estimateData: estimateData,
    }

    try {
      // Save estimate to backend
      const savedEstimate = await savePriceEstimate(estimateData)
      result.savedEstimate = savedEstimate

      gameResults.value.push(result)
      userGuesses.value.push(guessAmount)

      return { success: true, result }
    } catch (error) {
      console.error('Failed to save estimate:', error)
      // Still add to local results even if save fails
      gameResults.value.push(result)
      userGuesses.value.push(guessAmount)

      return {
        success: true,
        result,
        saveError: 'Failed to save estimate to server',
      }
    }
  }

  const nextProperty = () => {
    if (currentPropertyIndex.value < totalProperties.value - 1) {
      currentPropertyIndex.value++
      return true
    }
    return false
  }

  const resetGame = () => {
    currentPropertyIndex.value = 0
    userGuesses.value = []
    gameResults.value = []
  }

  const getScoreColor = (score) => {
    if (score >= 90) return 'positive'
    if (score >= 70) return 'warning'
    return 'negative'
  }

  const getPerformanceRating = (totalScore, maxScore) => {
    const percentage = (totalScore / maxScore) * 100

    if (percentage >= 90)
      return { rating: 'Expert', color: 'positive', icon: 'star' }
    if (percentage >= 80)
      return { rating: 'Advanced', color: 'positive', icon: 'trending_up' }
    if (percentage >= 70)
      return { rating: 'Good', color: 'warning', icon: 'thumb_up' }
    if (percentage >= 60)
      return { rating: 'Fair', color: 'warning', icon: 'thumbs_up_down' }
    if (percentage >= 50)
      return { rating: 'Beginner', color: 'negative', icon: 'school' }
    return { rating: 'Novice', color: 'negative', icon: 'help' }
  }

  // Fetch all estimates for comparison summary
  const fetchAllEstimatesForProperties = async () => {
    const allEstimates = []

    for (const property of properties.value) {
      try {
        const estimates = await fetchPriceEstimateComparisons(property.uuid)
        allEstimates.push({
          property: property,
          estimates: estimates,
        })
      } catch (error) {
        console.error(
          `Failed to fetch estimates for property ${property.uuid}:`,
          error
        )
        allEstimates.push({
          property: property,
          estimates: [],
          error: error.message,
        })
      }
    }

    return allEstimates
  }

  return {
    // State
    priceGuessData,
    isLoading,
    error,
    currentPropertyIndex,
    userGuesses,
    gameResults,

    // Computed
    properties,
    currentProperty,
    totalProperties,
    isGameComplete,
    totalScore,
    maxPossibleScore,

    // Methods
    fetchPriceGuessData,
    savePriceEstimate,
    fetchPriceEstimateComparisons,
    fetchAllEstimatesForProperties,
    formatPrice,
    getPropertyImages,
    getMainImage,
    validateGuess,
    submitGuess,
    nextProperty,
    resetGame,
    getScoreColor,
    getPerformanceRating,
  }
}
