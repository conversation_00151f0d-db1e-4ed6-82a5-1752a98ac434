import { ref, computed } from 'vue'
import axios from 'axios'
import { pwbFlexConfig } from 'boot/pwb-flex-conf'

export function usePriceGuessResults() {
  // Reactive state
  const isLoading = ref(false)
  const error = ref(null)
  const gameResults = ref([])
  const comparisonData = ref([])
  const isLoadingComparisons = ref(false)
  const sessionDate = ref(null)

  // API function to load game results by session ID
  const loadGameResults = async (gameSessionId) => {
    isLoading.value = true
    error.value = null

    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/price_estimates/game_session/${gameSessionId}`
      const response = await axios.get(apiUrl)

      gameResults.value = response.data.estimates || []
      sessionDate.value = response.data.session_date || new Date()

      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || 'Failed to load game results'
      console.error('Error loading game results:', err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // API function to load comparison data for the session
  const loadComparisonData = async (gameSessionId) => {
    isLoadingComparisons.value = true

    try {
      const apiUrl = `${pwbFlexConfig.dataApiBase}/api_public/v4/price_estimates/game_session/${gameSessionId}/comparisons`
      const response = await axios.get(apiUrl)
      comparisonData.value = response.data.comparisons || []

      return response.data
    } catch (err) {
      console.error('Error loading comparison data:', err)
      // Don't throw error for comparisons - it's optional
      comparisonData.value = []
    } finally {
      isLoadingComparisons.value = false
    }
  }

  // Computed properties
  const totalScore = computed(() => {
    return gameResults.value.reduce((sum, result) => {
      return sum + (result.estimate_details?.game_score || 0)
    }, 0)
  })

  const maxPossibleScore = computed(() => {
    return gameResults.value.length * 100
  })

  const performanceRating = computed(() => {
    if (maxPossibleScore.value === 0)
      return { rating: 'No Data', color: 'grey', icon: 'help' }

    const percentage = (totalScore.value / maxPossibleScore.value) * 100

    if (percentage >= 90)
      return { rating: 'Expert', color: 'positive', icon: 'star' }
    if (percentage >= 80)
      return { rating: 'Advanced', color: 'positive', icon: 'trending_up' }
    if (percentage >= 70)
      return { rating: 'Good', color: 'warning', icon: 'thumb_up' }
    if (percentage >= 60)
      return { rating: 'Fair', color: 'warning', icon: 'thumbs_up_down' }
    if (percentage >= 50)
      return { rating: 'Beginner', color: 'negative', icon: 'school' }
    return { rating: 'Novice', color: 'negative', icon: 'help' }
  })

  // Helper functions
  const formatPrice = (cents, currency = 'GBP') => {
    if (!cents) return '£0'

    const amount = cents / 100
    const formatter = new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    })

    return formatter.format(amount)
  }

  const getScoreColor = (score) => {
    if (score >= 90) return 'positive'
    if (score >= 70) return 'warning'
    return 'negative'
  }

  const formatDate = (date) => {
    if (!date) return ''

    const dateObj = new Date(date)
    return dateObj.toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // Calculate user ranking for a property
  const calculateUserRanking = (userGuess, allEstimates, actualPrice) => {
    const userAccuracy = Math.abs((userGuess - actualPrice) / actualPrice)

    const betterCount = allEstimates.filter((est) => {
      const estAccuracy = Math.abs(
        (est.estimated_price_cents - actualPrice) / actualPrice
      )
      return estAccuracy < userAccuracy
    }).length

    return betterCount + 1
  }

  const getRankingColor = (ranking, total) => {
    const percentile = (total - ranking) / total

    if (percentile >= 0.8) return 'positive'
    if (percentile >= 0.6) return 'warning'
    return 'negative'
  }

  const getPerformanceText = (ranking, total) => {
    const percentile = Math.round(((total - ranking) / total) * 100)

    if (percentile >= 90) return `Excellent! You're in the top 10% of players.`
    if (percentile >= 75) return `Great job! You're in the top 25% of players.`
    if (percentile >= 50) return `Good work! You're above average.`
    if (percentile >= 25) return `Not bad, but there's room for improvement.`
    return `Keep practicing! You'll get better with experience.`
  }

  // Process comparison data to add calculated fields
  const processComparisonData = () => {
    return comparisonData.value.map((property) => {
      const userResult = gameResults.value.find(
        (result) => result.listing_uuid === property.uuid
      )
      if (!userResult || !property.price_estimates_summary) return property

      const userGuess = userResult.estimated_price_cents
      const actualPrice = userResult.price_at_time_of_estimate_cents
      const allEstimates = property.price_estimates_summary
      // Calculate average guess
      const averageGuess =
        allEstimates.length > 0
          ? allEstimates.reduce(
              (sum, est) => sum + est.estimated_price_cents,
              0
            ) / allEstimates.length
          : 0

      // Calculate ranking
      const ranking = calculateUserRanking(userGuess, allEstimates, actualPrice)
      const totalPlayers = allEstimates.length // + 1 // +1 for user

      return {
        ...property,
        property_title: userResult.estimate_title,
        property_vicinity: userResult.estimate_vicinity,
        currency: userResult.estimate_currency,
        your_guess: userGuess,
        average_guess: averageGuess,
        actual_price: actualPrice,
        ranking: ranking,
        total_players: totalPlayers,
        ranking_color: getRankingColor(ranking, totalPlayers),
        performance_text: getPerformanceText(ranking, totalPlayers),
      }
    })
  }

  const processedComparisonData = computed(() => {
    return processComparisonData()
  })

  return {
    // State
    isLoading,
    error,
    gameResults,
    comparisonData: processedComparisonData,
    isLoadingComparisons,
    sessionDate,

    // Computed
    totalScore,
    maxPossibleScore,
    performanceRating,

    // Methods
    loadGameResults,
    loadComparisonData,
    formatPrice,
    getScoreColor,
    formatDate,
    calculateUserRanking,
    getRankingColor,
    getPerformanceText,
  }
}
