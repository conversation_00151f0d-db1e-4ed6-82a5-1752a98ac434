<template>
  <div class="price-guess-page">
    <!-- Loading State -->
    <div v-if="isLoading"
         class="loading-container">
      <div class="max-ctr q-pa-xl text-center">
        <q-spinner color="primary"
                   size="3em" />
        <div class="q-mt-md text-h6 text-grey-7">Loading property data...</div>
        <div class="text-body2 text-grey-6">Preparing your price guessing challenge</div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error"
         class="error-container">
      <div class="max-ctr q-pa-xl text-center">
        <q-icon name="error_outline"
                color="negative"
                size="4em" />
        <div class="q-mt-md text-h5 text-negative">Oops! Something went wrong</div>
        <div class="q-mt-sm text-body1 text-grey-7">{{ error }}</div>
        <q-btn color="primary"
               label="Try Again"
               class="q-mt-lg"
               @click="initializeGame" />
      </div>
    </div>

    <!-- Game Content -->
    <div v-else-if="!isGameComplete"
         class="game-container">
      <div class="max-ctr q-pa-lg">
        <!-- Header -->
        <div class="game-header q-mb-xl">
          <div class="row items-center justify-between">
            <div>
              <h1 class="text-h4 text-weight-bold text-primary q-mb-sm">Property Price Challenge</h1>
              <p class="text-body1 text-grey-7">Test your property valuation skills</p>
            </div>
            <div class="progress-info text-right">
              <div class="text-h6 text-primary">Property {{ currentPropertyIndex + 1 }} of {{ totalProperties }}</div>
              <q-linear-progress :value="(currentPropertyIndex + 1) / totalProperties"
                                 color="primary"
                                 size="8px"
                                 rounded
                                 class="q-mt-sm"
                                 style="width: 200px;" />
            </div>
          </div>
        </div>

        <!-- Property Card -->
        <q-card class="property-card"
                flat
                bordered>
          <!-- Property Images -->
          <div class="property-images"
               v-if="propertyImages.length > 0">
            <q-carousel v-model="currentImageIndex"
                        thumbnails
                        infinite
                        :autoplay="false"
                        arrows
                        prev-icon="arrow_left"
                        next-icon="arrow_right"
                        class="property-carousel">
              <q-carousel-slide v-for="(image, index) in propertyImages"
                                :key="index"
                                :name="index"
                                :img-src="image.image_details?.url"
                                class="carousel-slide">
                <div class="absolute-bottom custom-caption">
                  <div class="text-subtitle2">{{ image.photo_title || `Image ${index + 1}` }}</div>
                </div>
              </q-carousel-slide>
            </q-carousel>
          </div>

          <!-- Property Details -->
          <q-card-section class="property-details q-pa-lg">
            <div class="row q-col-gutter-lg">
              <div class="col-12 col-md-8">
                <div class="property-info">
                  <h2 class="text-h5 text-weight-medium q-mb-sm">{{ currentProperty.street_address }}</h2>
                  <div class="text-body1 text-grey-7 q-mb-md">{{ currentProperty.title }}</div>

                  <div class="property-features">
                    <div class="row q-col-gutter-md">
                      <div class="col-auto"
                           v-if="currentProperty.count_bedrooms">
                        <div class="feature-item">
                          <q-icon name="bed"
                                  color="primary"
                                  size="sm" />
                          <span class="q-ml-sm">{{ currentProperty.count_bedrooms }} bed{{
                            currentProperty.count_bedrooms !== 1 ? 's' : '' }}</span>
                        </div>
                      </div>
                      <div class="col-auto"
                           v-if="currentProperty.count_bathrooms">
                        <div class="feature-item">
                          <q-icon name="bathtub"
                                  color="primary"
                                  size="sm" />
                          <span class="q-ml-sm">{{ currentProperty.count_bathrooms }} bath{{
                            currentProperty.count_bathrooms !== 1 ? 's' : '' }}</span>
                        </div>
                      </div>
                      <div class="col-auto"
                           v-if="currentProperty.count_garages">
                        <div class="feature-item">
                          <q-icon name="garage"
                                  color="primary"
                                  size="sm" />
                          <span class="q-ml-sm">{{ currentProperty.count_garages }} garage{{
                            currentProperty.count_garages !== 1 ? 's' : '' }}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="location-info q-mt-md">
                    <div class="row items-center">
                      <q-icon name="place"
                              color="grey-6"
                              size="sm" />
                      <span class="q-ml-sm text-body2 text-grey-7">
                        {{ currentProperty.city }}, {{ currentProperty.country }}
                        <span v-if="currentProperty.postal_code"> • {{ currentProperty.postal_code }}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-12 col-md-4">
                <div class="guess-section">
                  <div class="guess-form">
                    <div class="text-h6 q-mb-md">What's your price guess?</div>

                    <q-input v-model.number="userGuess"
                             type="number"
                             label="Enter your price guess"
                             prefix="£"
                             outlined
                             dense
                             :error="!!validationErrors.length"
                             :error-message="validationErrors.join(', ')"
                             @update:model-value="validateCurrentGuess"
                             class="q-mb-md">
                      <template v-slot:append>
                        <q-icon name="help_outline"
                                color="grey-6">
                          <q-tooltip class="bg-primary">
                            Enter your best guess for this property's sale price
                          </q-tooltip>
                        </q-icon>
                      </template>
                    </q-input>

                    <q-btn color="primary"
                           label="Submit Guess"
                           icon="send"
                           :disable="!userGuess || validationErrors.length > 0"
                           @click="handleSubmitGuess"
                           class="full-width"
                           unelevated
                           rounded />
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Feedback Dialog -->
        <q-dialog v-model="showFeedback"
                  persistent>
          <q-card class="feedback-card"
                  style="min-width: 400px;">
            <q-card-section class="feedback-header text-white"
                            :class="`bg-${getScoreColor(currentResult?.score || 0)}`">
              <div class="row items-center">
                <q-icon :name="currentResult?.score >= 80 ? 'celebration' : currentResult?.score >= 60 ? 'thumb_up' : 'info'"
                        size="md"
                        class="q-mr-md" />
                <div>
                  <div class="text-h6">Guess Result</div>
                  <div class="text-subtitle2">Score: {{ currentResult?.score || 0 }}/100</div>
                </div>
              </div>
            </q-card-section>

            <q-card-section class="q-pa-lg">
              <div class="result-details">
                <div class="result-row q-mb-md">
                  <div class="result-label">Your Guess:</div>
                  <div class="result-value text-weight-bold">{{ formatPrice((currentResult?.guess || 0) * 100,
                    currentProperty?.currency) }}</div>
                </div>

                <div class="result-row q-mb-md">
                  <div class="result-label">Actual Price:</div>
                  <div class="result-value text-weight-bold">{{ formatPrice(currentProperty?.price_sale_current_cents,
                    currentProperty?.currency) }}</div>
                </div>

                <div class="result-row q-mb-md">
                  <div class="result-label">Difference:</div>
                  <div class="result-value"
                       :class="currentResult?.difference > 0 ? 'text-negative' : 'text-positive'">
                    {{ currentResult?.difference > 0 ? '+' : '' }}{{ currentResult?.difference?.toFixed(1) || 0 }}%
                  </div>
                </div>

                <q-separator class="q-my-md" />

                <div class="feedback-message text-center q-pa-md">
                  <div class="text-body1">{{ currentResult?.feedback }}</div>
                </div>
              </div>
            </q-card-section>

            <q-card-actions align="right"
                            class="q-pa-lg">
              <q-btn flat
                     label="Next Property"
                     color="primary"
                     icon="arrow_forward"
                     @click="handleNextProperty"
                     :disable="isGameComplete" />
            </q-card-actions>
          </q-card>
        </q-dialog>
      </div>
    </div>

    <!-- Game Complete -->
    <div v-else-if="isGameComplete"
         class="results-container">
      <div class="max-ctr q-pa-lg">
        <!-- Results Header -->
        <div class="results-header q-mb-xl text-center">
          <div class="performance-badge q-mb-lg">
            <q-icon :name="performanceRating.icon"
                    :color="performanceRating.color"
                    size="4em" />
            <div class="text-h4 text-weight-bold q-mt-md"
                 :class="`text-${performanceRating.color}`">
              {{ performanceRating.rating }}
            </div>
            <div class="text-h6 text-grey-7">{{ totalScore }} / {{ maxPossibleScore }} points</div>
          </div>

          <h1 class="text-h4 text-weight-bold text-primary q-mb-sm">Challenge Complete!</h1>
          <p class="text-body1 text-grey-7">Here's how you performed on each property</p>
        </div>

        <!-- Results Table -->
        <q-card class="results-card q-mb-lg"
                flat
                bordered>
          <q-card-section class="q-pa-lg">
            <q-table :rows="gameResults"
                     :columns="resultsColumns"
                     row-key="propertyIndex"
                     flat
                     :pagination="{ rowsPerPage: 0 }"
                     class="results-table">
              <template v-slot:body-cell-property="props">
                <q-td :props="props">
                  <div class="property-cell">
                    <div class="text-weight-medium">{{ props.row.property.street_address }}</div>
                    <div class="text-caption text-grey-6">{{ props.row.property.city }}</div>
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-guess="props">
                <q-td :props="props">
                  <div class="text-weight-medium">
                    {{ formatPrice(props.row.guess * 100, props.row.property.currency) }}
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-actual="props">
                <q-td :props="props">
                  <div class="text-weight-medium">
                    {{ formatPrice(props.row.property.price_sale_current_cents, props.row.property.currency) }}
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-difference="props">
                <q-td :props="props">
                  <q-chip :color="Math.abs(props.row.difference) <= 10 ? 'positive' : Math.abs(props.row.difference) <= 25 ? 'warning' : 'negative'"
                          text-color="white"
                          size="sm">
                    {{ props.row.difference > 0 ? '+' : '' }}{{ props.row.difference.toFixed(1) }}%
                  </q-chip>
                </q-td>
              </template>

              <template v-slot:body-cell-score="props">
                <q-td :props="props">
                  <div class="score-cell">
                    <q-circular-progress :value="props.row.score"
                                         size="40px"
                                         :thickness="0.15"
                                         :color="getScoreColor(props.row.score)"
                                         track-color="grey-3"
                                         class="q-mr-sm">
                      <div class="text-caption text-weight-bold">{{ props.row.score }}</div>
                    </q-circular-progress>
                  </div>
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>

        <!-- Actions -->
        <div class="results-actions text-center">
          <q-btn color="primary"
                 label="Play Again"
                 icon="refresh"
                 size="lg"
                 rounded
                 unelevated
                 @click="handleResetGame"
                 class="q-mr-md" />
          <q-btn color="secondary"
                 label="Share Results"
                 icon="share"
                 size="lg"
                 rounded
                 outline
                 @click="shareResults" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue'
import { useQuasar } from 'quasar'
import { useRoute } from 'vue-router'
import { usePriceGuess } from '../composables/usePriceGuess'

const $q = useQuasar()
const $route = useRoute()

// Get dossier UUID from route params or use default
const dossierUuid = ref($route.params.dossierUuid || '0d10ef8f-9af3-42f5-a48b-02808ecf5f7a')

// Initialize the price guess composable
const {
  isLoading,
  error,
  currentPropertyIndex,
  currentProperty,
  totalProperties,
  isGameComplete,
  gameResults,
  totalScore,
  maxPossibleScore,
  fetchPriceGuessData,
  formatPrice,
  getPropertyImages,
  submitGuess,
  nextProperty,
  resetGame,
  getScoreColor,
  getPerformanceRating
} = usePriceGuess()

// Local reactive state
const userGuess = ref(null)
const validationErrors = ref([])
const showFeedback = ref(false)
const currentResult = ref(null)
const currentImageIndex = ref(0)

// Computed properties
const propertyImages = computed(() => {
  return currentProperty.value ? getPropertyImages(currentProperty.value) : []
})

const performanceRating = computed(() => {
  return getPerformanceRating(totalScore.value, maxPossibleScore.value)
})

const resultsColumns = computed(() => [
  {
    name: 'property',
    label: 'Property',
    field: 'property',
    align: 'left',
    style: 'width: 30%'
  },
  {
    name: 'guess',
    label: 'Your Guess',
    field: 'guess',
    align: 'right',
    style: 'width: 20%'
  },
  {
    name: 'actual',
    label: 'Actual Price',
    field: 'actual',
    align: 'right',
    style: 'width: 20%'
  },
  {
    name: 'difference',
    label: 'Difference',
    field: 'difference',
    align: 'center',
    style: 'width: 15%'
  },
  {
    name: 'score',
    label: 'Score',
    field: 'score',
    align: 'center',
    style: 'width: 15%'
  }
])

// Methods
const validateCurrentGuess = () => {
  validationErrors.value = []

  if (!userGuess.value || userGuess.value <= 0) {
    validationErrors.value.push('Please enter a positive number')
    return
  }

  if (!currentProperty.value) return

  const actualPrice = currentProperty.value.price_sale_current_cents / 100
  const maxAllowed = actualPrice * 3 // 200% too high
  const minAllowed = actualPrice * 0.1 // 90% too low

  if (userGuess.value > maxAllowed) {
    validationErrors.value.push('Guess is more than 200% too high')
  } else if (userGuess.value < minAllowed) {
    validationErrors.value.push('Guess is less than 90% of actual price')
  }
}

const handleSubmitGuess = () => {
  const result = submitGuess(userGuess.value)

  if (result.success) {
    currentResult.value = result.result
    showFeedback.value = true

    $q.notify({
      color: getScoreColor(result.result.score),
      message: `Score: ${result.result.score}/100`,
      icon: result.result.score >= 80 ? 'celebration' : 'info',
      position: 'top'
    })
  } else {
    validationErrors.value = result.errors
  }
}

const handleNextProperty = () => {
  showFeedback.value = false
  currentResult.value = null
  userGuess.value = null
  validationErrors.value = []
  currentImageIndex.value = 0

  const hasNext = nextProperty()
  if (!hasNext) {
    // Game complete
    $q.notify({
      color: 'positive',
      message: 'Challenge complete! Check your results.',
      icon: 'flag',
      position: 'top'
    })
  }
}

const handleResetGame = () => {
  resetGame()
  userGuess.value = null
  validationErrors.value = []
  showFeedback.value = false
  currentResult.value = null
  currentImageIndex.value = 0

  $q.notify({
    message: 'Game reset! Ready for a new challenge.',
    icon: 'refresh',
    position: 'top'
  })
}

const shareResults = () => {
  const text = `I just completed the Property Price Challenge! Score: ${totalScore.value}/${maxPossibleScore.value} (${performanceRating.value.rating})`

  if (navigator.share) {
    navigator.share({
      title: 'Property Price Challenge Results',
      text: text,
      url: window.location.href
    })
  } else {
    // Fallback to clipboard
    navigator.clipboard.writeText(text).then(() => {
      $q.notify({
        message: 'Results copied to clipboard!',
        icon: 'content_copy',
        color: 'positive'
      })
    })
  }
}

const initializeGame = async () => {
  try {
    await fetchPriceGuessData(dossierUuid.value)
    userGuess.value = null
    validationErrors.value = []
    showFeedback.value = false
    currentResult.value = null
    currentImageIndex.value = 0
  } catch (err) {
    $q.notify({
      color: 'negative',
      message: 'Failed to load property data',
      icon: 'error'
    })
  }
}

// Initialize on mount
onMounted(() => {
  initializeGame()
})
</script>

<style scoped>
.price-guess-page {
  background-color: #fafafa;
  min-height: 100vh;
}

.max-ctr {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container,
.error-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.game-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.property-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
}

.property-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.property-carousel {
  height: 400px;
}

.carousel-slide {
  background-size: cover;
  background-position: center;
}

.custom-caption {
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 1rem;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  background: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.guess-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
}

.feedback-card {
  border-radius: 12px;
  overflow: hidden;
}

.feedback-header {
  padding: 1.5rem;
}

.result-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.result-label {
  font-weight: 500;
  color: #666;
}

.result-value {
  font-size: 1.1rem;
}

.results-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.performance-badge {
  display: inline-block;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 16px;
  border: 2px solid #e9ecef;
}

.results-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.results-table {
  border-radius: 8px;
}

.property-cell {
  max-width: 200px;
}

.score-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.results-actions {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* Button improvements */
.q-btn {
  text-transform: none;
  font-weight: 500;
}

/* Input improvements */
.q-field--outlined .q-field__control {
  border-radius: 8px;
}

/* Progress bar improvements */
.q-linear-progress {
  border-radius: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .max-ctr {
    padding: 1rem;
  }

  .game-header,
  .results-header {
    padding: 1rem;
  }

  .property-carousel {
    height: 300px;
  }

  .guess-section {
    margin-top: 1rem;
  }

  .progress-info {
    text-align: left;
    margin-top: 1rem;
  }

  .progress-info .q-linear-progress {
    width: 100% !important;
  }
}
</style>