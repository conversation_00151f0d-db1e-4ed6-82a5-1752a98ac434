<template>
  <div class="price-guess-page">
    <!-- Loading State -->
    <div v-if="isLoading"
         class="loading-container">
      <div class="max-ctr q-pa-xl text-center">
        <q-spinner color="primary"
                   size="3em" />
        <div class="q-mt-md text-h6 text-grey-7">Loading property data...</div>
        <div class="text-body2 text-grey-6">Preparing your price guessing challenge</div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="error"
         class="error-container">
      <div class="max-ctr q-pa-xl text-center">
        <q-icon name="error_outline"
                color="negative"
                size="4em" />
        <div class="q-mt-md text-h5 text-negative">Oops! Something went wrong</div>
        <div class="q-mt-sm text-body1 text-grey-7">{{ error }}</div>
        <q-btn color="primary"
               label="Try Again"
               class="q-mt-lg"
               @click="initializeGame" />
      </div>
    </div>

    <!-- Game Content -->
    <div v-else-if="!isGameComplete"
         class="game-container">
      <div class="max-ctr q-pa-lg">
        <!-- Header -->
        <div class="game-header q-mb-xl">
          <div class="row items-center justify-between">
            <div>
              <h1 class="text-h4 text-weight-bold text-primary q-mb-sm">Property Price Challenge</h1>
              <p class="text-body1 text-grey-7">Test your property valuation skills</p>
            </div>
            <div class="progress-info text-right">
              <div class="text-h6 text-primary">Property {{ currentPropertyIndex + 1 }} of {{ totalProperties }}</div>
              <q-linear-progress :value="(currentPropertyIndex + 1) / totalProperties"
                                 color="primary"
                                 size="8px"
                                 rounded
                                 class="q-mt-sm"
                                 style="width: 200px;" />
            </div>
          </div>
        </div>

        <!-- Property Card -->
        <q-card class="property-card"
                flat
                bordered>
          <!-- Property Images -->
          <div class="property-images"
               v-if="propertyImages.length > 0">
            <q-carousel v-model="currentImageIndex"
                        thumbnails
                        infinite
                        :autoplay="false"
                        arrows
                        prev-icon="arrow_left"
                        next-icon="arrow_right"
                        class="property-carousel">
              <q-carousel-slide v-for="(image, index) in propertyImages"
                                :key="index"
                                :name="index"
                                :img-src="image.image_details?.url"
                                class="carousel-slide">
                <div class="absolute-bottom custom-caption">
                  <div class="text-subtitle2">{{ image.photo_title || `Image ${index + 1}` }}</div>
                </div>
              </q-carousel-slide>
            </q-carousel>
          </div>

          <!-- Property Details -->
          <q-card-section class="property-details q-pa-lg">
            <div class="row q-col-gutter-lg">
              <div class="col-12 col-md-8">
                <div class="property-info">
                  <h2 class="text-h5 text-weight-medium q-mb-sm">{{ currentProperty.street_address }}</h2>
                  <div class="text-body1 text-grey-7 q-mb-md">{{ currentProperty.title }}</div>

                  <div class="property-features">
                    <div class="row q-col-gutter-md">
                      <div class="col-auto"
                           v-if="currentProperty.count_bedrooms">
                        <div class="feature-item">
                          <q-icon name="bed"
                                  color="primary"
                                  size="sm" />
                          <span class="q-ml-sm">{{ currentProperty.count_bedrooms }} bed{{
                            currentProperty.count_bedrooms !== 1 ? 's' : '' }}</span>
                        </div>
                      </div>
                      <div class="col-auto"
                           v-if="currentProperty.count_bathrooms">
                        <div class="feature-item">
                          <q-icon name="bathtub"
                                  color="primary"
                                  size="sm" />
                          <span class="q-ml-sm">{{ currentProperty.count_bathrooms }} bath{{
                            currentProperty.count_bathrooms !== 1 ? 's' : '' }}</span>
                        </div>
                      </div>
                      <div class="col-auto"
                           v-if="currentProperty.count_garages">
                        <div class="feature-item">
                          <q-icon name="garage"
                                  color="primary"
                                  size="sm" />
                          <span class="q-ml-sm">{{ currentProperty.count_garages }} garage{{
                            currentProperty.count_garages !== 1 ? 's' : '' }}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="location-info q-mt-md">
                    <div class="row items-center">
                      <q-icon name="place"
                              color="grey-6"
                              size="sm" />
                      <span class="q-ml-sm text-body2 text-grey-7">
                        {{ currentProperty.city }}, {{ currentProperty.country }}
                        <span v-if="currentProperty.postal_code"> • {{ currentProperty.postal_code }}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="col-12 col-md-4">
                <div class="guess-section">
                  <div class="guess-form">
                    <div class="text-h6 q-mb-md">What's your price guess?</div>

                    <q-input v-model.number="userGuess"
                             type="number"
                             label="Enter your price guess"
                             prefix="£"
                             outlined
                             dense
                             :error="!!validationErrors.length"
                             :error-message="validationErrors.join(', ')"
                             @update:model-value="validateCurrentGuess"
                             class="q-mb-md">
                      <template v-slot:append>
                        <q-icon name="help_outline"
                                color="grey-6">
                          <q-tooltip class="bg-primary">
                            Enter your best guess for this property's sale price
                          </q-tooltip>
                        </q-icon>
                      </template>
                    </q-input>

                    <q-btn color="primary"
                           label="Submit Guess"
                           icon="send"
                           :disable="!userGuess || validationErrors.length > 0"
                           @click="handleSubmitGuess"
                           class="full-width"
                           unelevated
                           rounded />
                  </div>
                </div>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Feedback Dialog -->
        <q-dialog v-model="showFeedback"
                  persistent>
          <q-card class="feedback-card"
                  style="min-width: 400px;">
            <q-card-section class="feedback-header text-white"
                            :class="`bg-${getScoreColor(currentResult?.score || 0)}`">
              <div class="row items-center">
                <q-icon :name="currentResult?.score >= 80 ? 'celebration' : currentResult?.score >= 60 ? 'thumb_up' : 'info'"
                        size="md"
                        class="q-mr-md" />
                <div>
                  <div class="text-h6">Guess Result</div>
                  <div class="text-subtitle2">Score: {{ currentResult?.score || 0 }}/100</div>
                </div>
              </div>
            </q-card-section>

            <q-card-section class="q-pa-lg">
              <div class="result-details">
                <div class="result-row q-mb-md">
                  <div class="result-label">Your Guess:</div>
                  <div class="result-value text-weight-bold">{{ formatPrice((currentResult?.guess || 0) * 100,
                    currentProperty?.currency) }}</div>
                </div>

                <div class="result-row q-mb-md">
                  <div class="result-label">Actual Price:</div>
                  <div class="result-value text-weight-bold">{{ formatPrice(currentProperty?.price_sale_current_cents,
                    currentProperty?.currency) }}</div>
                </div>

                <div class="result-row q-mb-md">
                  <div class="result-label">Difference:</div>
                  <div class="result-value"
                       :class="currentResult?.difference > 0 ? 'text-negative' : 'text-positive'">
                    {{ currentResult?.difference > 0 ? '+' : '' }}{{ currentResult?.difference?.toFixed(1) || 0 }}%
                  </div>
                </div>

                <q-separator class="q-my-md" />

                <div class="feedback-message text-center q-pa-md">
                  <div class="text-body1">{{ currentResult?.feedback }}</div>
                </div>
              </div>
            </q-card-section>

            <q-card-actions align="right"
                            class="q-pa-lg">
              <q-btn flat
                     label="Next Property"
                     color="primary"
                     icon="arrow_forward"
                     @click="handleNextProperty"
                     :disable="isGameComplete" />
            </q-card-actions>
          </q-card>
        </q-dialog>

        <!-- User Info Dialog -->
        <q-dialog v-model="showUserInfoDialog"
                  persistent>
          <q-card style="min-width: 400px;">
            <q-card-section class="bg-primary text-white">
              <div class="text-h6">Welcome to the Property Price Challenge!</div>
            </q-card-section>

            <q-card-section class="q-pa-lg">
              <div class="text-body1 q-mb-md">
                To make the game more interesting, we'd like to know your name so we can compare your guesses with
                others.
              </div>

              <q-input v-model="userInfo.name"
                       label="Your name (optional)"
                       outlined
                       dense
                       placeholder="Enter your name or leave blank for 'Anonymous'"
                       class="q-mb-md">
                <template v-slot:prepend>
                  <q-icon name="person" />
                </template>
              </q-input>

              <div class="text-caption text-grey-6">
                Your guesses will be saved and compared with other players. No personal information is required.
              </div>
            </q-card-section>

            <q-card-actions align="right"
                            class="q-pa-lg">
              <q-btn flat
                     label="Skip"
                     color="grey-7"
                     @click="handleUserInfoSubmit" />
              <q-btn color="primary"
                     label="Start Game"
                     unelevated
                     @click="handleUserInfoSubmit" />
            </q-card-actions>
          </q-card>
        </q-dialog>
      </div>
    </div>

    <!-- Game Complete -->
    <div v-else-if="isGameComplete"
         class="results-container">
      <div class="max-ctr q-pa-lg">
        <!-- Results Header -->
        <div class="results-header q-mb-xl text-center">
          <div class="performance-badge q-mb-lg">
            <q-icon :name="performanceRating.icon"
                    :color="performanceRating.color"
                    size="4em" />
            <div class="text-h4 text-weight-bold q-mt-md"
                 :class="`text-${performanceRating.color}`">
              {{ performanceRating.rating }}
            </div>
            <div class="text-h6 text-grey-7">{{ totalScore }} / {{ maxPossibleScore }} points</div>
          </div>

          <h1 class="text-h4 text-weight-bold text-primary q-mb-sm">Challenge Complete!</h1>
          <p class="text-body1 text-grey-7">Here's how you performed on each property</p>
        </div>

        <!-- Results Table -->
        <q-card class="results-card q-mb-lg"
                flat
                bordered>
          <q-card-section class="q-pa-lg">
            <q-table :rows="gameResults"
                     :columns="resultsColumns"
                     row-key="propertyIndex"
                     flat
                     :pagination="{ rowsPerPage: 0 }"
                     class="results-table">
              <template v-slot:body-cell-property="props">
                <q-td :props="props">
                  <div class="property-cell">
                    <div class="text-weight-medium">{{ props.row.property.street_address }}</div>
                    <div class="text-caption text-grey-6">{{ props.row.property.city }}</div>
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-guess="props">
                <q-td :props="props">
                  <div class="text-weight-medium">
                    {{ formatPrice(props.row.guess * 100, props.row.property.currency) }}
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-actual="props">
                <q-td :props="props">
                  <div class="text-weight-medium">
                    {{ formatPrice(props.row.property.price_sale_current_cents, props.row.property.currency) }}
                  </div>
                </q-td>
              </template>

              <template v-slot:body-cell-difference="props">
                <q-td :props="props">
                  <q-chip :color="Math.abs(props.row.difference) <= 10 ? 'positive' : Math.abs(props.row.difference) <= 25 ? 'warning' : 'negative'"
                          text-color="white"
                          size="sm">
                    {{ props.row.difference > 0 ? '+' : '' }}{{ props.row.difference.toFixed(1) }}%
                  </q-chip>
                </q-td>
              </template>

              <template v-slot:body-cell-score="props">
                <q-td :props="props">
                  <div class="score-cell">
                    <q-circular-progress :value="props.row.score"
                                         size="40px"
                                         :thickness="0.15"
                                         :color="getScoreColor(props.row.score)"
                                         track-color="grey-3"
                                         class="q-mr-sm">
                      <div class="text-caption text-weight-bold">{{ props.row.score }}</div>
                    </q-circular-progress>
                  </div>
                </q-td>
              </template>
            </q-table>
          </q-card-section>
        </q-card>

        <!-- Comparison Summary -->
        <q-card v-if="allEstimatesData.length > 0"
                class="comparison-card q-mb-lg"
                flat
                bordered>
          <q-card-section class="q-pa-lg">
            <div class="text-h6 q-mb-md">
              <q-icon name="people"
                      color="primary"
                      size="sm"
                      class="q-mr-sm" />
              How You Compare to Other Players
            </div>

            <div v-if="isLoadingEstimates"
                 class="text-center q-pa-md">
              <q-spinner color="primary"
                         size="2em" />
              <div class="q-mt-sm text-grey-7">Loading comparison data...</div>
            </div>

            <div v-else>
              <div v-for="(propertyData, index) in allEstimatesData"
                   :key="index"
                   class="property-comparison q-mb-lg">
                <div class="comparison-header q-mb-md">
                  <div class="text-subtitle1 text-weight-medium">{{ propertyData.property.street_address }}</div>
                  <div class="text-caption text-grey-6">{{ propertyData.property.city }}</div>
                </div>

                <div v-if="propertyData.error"
                     class="text-negative q-pa-md">
                  <q-icon name="error"
                          class="q-mr-sm" />
                  Could not load estimates for this property
                </div>

                <div v-else-if="propertyData.estimates && propertyData.estimates.length > 0">
                  <!-- Your guess vs others -->
                  <div class="comparison-stats q-mb-md">
                    <div class="row q-col-gutter-md">
                      <div class="col-12 col-md-4">
                        <q-card flat
                                bordered
                                class="stat-card">
                          <q-card-section class="text-center q-pa-md">
                            <div class="text-h6 text-primary">{{
                              formatPrice(getUserGuessForProperty(propertyData.property)
                                * 100, propertyData.property.currency) }}</div>
                            <div class="text-caption text-grey-6">Your Guess</div>
                          </q-card-section>
                        </q-card>
                      </div>
                      <div class="col-12 col-md-4">
                        <q-card flat
                                bordered
                                class="stat-card">
                          <q-card-section class="text-center q-pa-md">
                            <div class="text-h6 text-secondary">{{
                              formatPrice(getAverageEstimate(propertyData.estimates),
                                propertyData.property.currency) }}</div>
                            <div class="text-caption text-grey-6">Average Guess</div>
                          </q-card-section>
                        </q-card>
                      </div>
                      <div class="col-12 col-md-4">
                        <q-card flat
                                bordered
                                class="stat-card">
                          <q-card-section class="text-center q-pa-md">
                            <div class="text-h6 text-positive">{{
                              formatPrice(propertyData.property.price_sale_current_cents,
                                propertyData.property.currency) }}
                            </div>
                            <div class="text-caption text-grey-6">Actual Price</div>
                          </q-card-section>
                        </q-card>
                      </div>
                    </div>
                  </div>

                  <!-- Estimate distribution chart -->
                  <div class="estimate-distribution q-mb-md">
                    <div class="text-subtitle2 q-mb-sm">Guess Distribution ({{ propertyData.estimates.length }} players)
                    </div>
                    <div class="distribution-bars">
                      <div v-for="bucket in getEstimateBuckets(propertyData.estimates, propertyData.property)"
                           :key="bucket.label"
                           class="distribution-bar">
                        <div class="bar-container">
                          <div class="bar-fill"
                               :style="{ width: `${bucket.percentage}%` }"
                               :class="bucket.isUserBucket ? 'user-bar' : 'other-bar'"></div>
                        </div>
                        <div class="bar-label">
                          <span class="range">{{ bucket.label }}</span>
                          <span class="count">({{ bucket.count }})</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Performance ranking -->
                  <div class="performance-ranking">
                    <div class="text-subtitle2 q-mb-sm">Your Performance</div>
                    <div class="ranking-info">
                      <q-chip :color="getUserRankingColor(propertyData.estimates, propertyData.property)"
                              text-color="white"
                              icon="emoji_events">
                        Ranked {{ getUserRanking(propertyData.estimates, propertyData.property) }} of {{
                          propertyData.estimates.length }}
                      </q-chip>
                      <span class="q-ml-md text-body2 text-grey-7">
                        {{ getUserPerformanceText(propertyData.estimates, propertyData.property) }}
                      </span>
                    </div>
                  </div>
                </div>

                <div v-else
                     class="text-grey-6 q-pa-md text-center">
                  <q-icon name="info"
                          class="q-mr-sm" />
                  No other estimates available for this property yet
                </div>

                <q-separator v-if="index < allEstimatesData.length - 1"
                             class="q-mt-lg" />
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Actions -->
        <div class="results-actions text-center">
          <q-btn color="primary"
                 label="Play Again"
                 icon="refresh"
                 size="lg"
                 rounded
                 unelevated
                 @click="handleResetGame"
                 class="q-mr-md" />
          <q-btn color="secondary"
                 label="Share Results"
                 icon="share"
                 size="lg"
                 rounded
                 outline
                 @click="shareResults" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useQuasar } from 'quasar'
import { useRoute } from 'vue-router'
import { usePriceGuess } from '../composables/usePriceGuess'

const $q = useQuasar()
const $route = useRoute()

// Get dossier UUID from route params or use default
const dossierUuid = ref($route.params.dossierUuid || '0d10ef8f-9af3-42f5-a48b-02808ecf5f7a')

// Initialize the price guess composable
const {
  isLoading,
  error,
  currentPropertyIndex,
  currentProperty,
  totalProperties,
  isGameComplete,
  gameResults,
  totalScore,
  maxPossibleScore,
  fetchPriceGuessData,
  fetchAllEstimatesForProperties,
  formatPrice,
  getPropertyImages,
  submitGuess,
  nextProperty,
  resetGame,
  getScoreColor,
  getPerformanceRating
} = usePriceGuess()

// Local reactive state
const userGuess = ref(null)
const validationErrors = ref([])
const showFeedback = ref(false)
const currentResult = ref(null)
const currentImageIndex = ref(0)
const showUserInfoDialog = ref(false)
const userInfo = ref({
  name: '',
  sessionId: null
})
const allEstimatesData = ref([])
const isLoadingEstimates = ref(false)

// Computed properties
const propertyImages = computed(() => {
  return currentProperty.value ? getPropertyImages(currentProperty.value) : []
})

const performanceRating = computed(() => {
  return getPerformanceRating(totalScore.value, maxPossibleScore.value)
})

const resultsColumns = computed(() => [
  {
    name: 'property',
    label: 'Property',
    field: 'property',
    align: 'left',
    style: 'width: 30%'
  },
  {
    name: 'guess',
    label: 'Your Guess',
    field: 'guess',
    align: 'right',
    style: 'width: 20%'
  },
  {
    name: 'actual',
    label: 'Actual Price',
    field: 'actual',
    align: 'right',
    style: 'width: 20%'
  },
  {
    name: 'difference',
    label: 'Difference',
    field: 'difference',
    align: 'center',
    style: 'width: 15%'
  },
  {
    name: 'score',
    label: 'Score',
    field: 'score',
    align: 'center',
    style: 'width: 15%'
  }
])

// Methods
const validateCurrentGuess = () => {
  validationErrors.value = []

  if (!userGuess.value || userGuess.value <= 0) {
    validationErrors.value.push('Please enter a positive number')
    return
  }

  if (!currentProperty.value) return

  const actualPrice = currentProperty.value.price_sale_current_cents / 100
  const maxAllowed = actualPrice * 3 // 200% too high
  const minAllowed = actualPrice * 0.1 // 90% too low

  if (userGuess.value > maxAllowed) {
    validationErrors.value.push('Guess is more than 200% too high')
  } else if (userGuess.value < minAllowed) {
    validationErrors.value.push('Guess is less than 90% of actual price')
  }
}

const handleSubmitGuess = async () => {
  // Show user info dialog on first guess if name not provided
  if (currentPropertyIndex.value === 0 && !userInfo.value.name) {
    showUserInfoDialog.value = true
    return
  }

  try {
    const result = await submitGuess(userGuess.value, {
      name: userInfo.value.name || 'Anonymous Player',
      sessionId: userInfo.value.sessionId,
      userUuid: null, // Could be set if user is logged in
      scootUuid: dossierUuid.value
    })

    if (result.success) {
      currentResult.value = result.result
      showFeedback.value = true

      // Show save error if there was one
      if (result.saveError) {
        $q.notify({
          color: 'warning',
          message: result.saveError,
          icon: 'warning',
          position: 'top'
        })
      }

      $q.notify({
        color: getScoreColor(result.result.score),
        message: `Score: ${result.result.score}/100`,
        icon: result.result.score >= 80 ? 'celebration' : 'info',
        position: 'top'
      })
    } else {
      validationErrors.value = result.errors
    }
  } catch (error) {
    $q.notify({
      color: 'negative',
      message: 'Failed to submit guess. Please try again.',
      icon: 'error',
      position: 'top'
    })
  }
}

const handleNextProperty = () => {
  showFeedback.value = false
  currentResult.value = null
  userGuess.value = null
  validationErrors.value = []
  currentImageIndex.value = 0

  const hasNext = nextProperty()
  if (!hasNext) {
    // Game complete
    $q.notify({
      color: 'positive',
      message: 'Challenge complete! Check your results.',
      icon: 'flag',
      position: 'top'
    })
  }
}

const handleResetGame = () => {
  resetGame()
  userGuess.value = null
  validationErrors.value = []
  showFeedback.value = false
  currentResult.value = null
  currentImageIndex.value = 0
  allEstimatesData.value = []

  $q.notify({
    message: 'Game reset! Ready for a new challenge.',
    icon: 'refresh',
    position: 'top'
  })
}

// Load comparison data when game is complete
const loadComparisonData = async () => {
  if (!isGameComplete.value) return

  isLoadingEstimates.value = true
  try {
    const estimates = await fetchAllEstimatesForProperties()
    allEstimatesData.value = estimates
  } catch (error) {
    console.error('Failed to load comparison data:', error)
    $q.notify({
      color: 'warning',
      message: 'Could not load comparison data',
      icon: 'warning',
      position: 'top'
    })
  } finally {
    isLoadingEstimates.value = false
  }
}

// Handle user info submission
const handleUserInfoSubmit = () => {
  if (!userInfo.value.name.trim()) {
    userInfo.value.name = 'Anonymous Player'
  }

  // Generate session ID if not exists
  if (!userInfo.value.sessionId) {
    userInfo.value.sessionId = `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  showUserInfoDialog.value = false

  // Now submit the guess
  handleSubmitGuess()
}

// Comparison analysis helper functions
const getUserGuessForProperty = (property) => {
  const result = gameResults.value.find(r => r.property.uuid === property.uuid)
  return result ? result.guess : 0
}

const getAverageEstimate = (estimates) => {
  if (!estimates || estimates.length === 0) return 0
  const sum = estimates.reduce((acc, est) => acc + (est.estimated_price_cents || 0), 0)
  return sum / estimates.length
}

const getEstimateBuckets = (estimates, property) => {
  const actualPrice = property.price_sale_current_cents
  const userGuess = getUserGuessForProperty(property) * 100

  // Create price buckets (ranges)
  const buckets = [
    { min: 0, max: actualPrice * 0.7, label: 'Under 70%' },
    { min: actualPrice * 0.7, max: actualPrice * 0.85, label: '70-85%' },
    { min: actualPrice * 0.85, max: actualPrice * 0.95, label: '85-95%' },
    { min: actualPrice * 0.95, max: actualPrice * 1.05, label: '95-105%' },
    { min: actualPrice * 1.05, max: actualPrice * 1.15, label: '105-115%' },
    { min: actualPrice * 1.15, max: actualPrice * 1.3, label: '115-130%' },
    { min: actualPrice * 1.3, max: Infinity, label: 'Over 130%' }
  ]

  // Count estimates in each bucket
  const bucketCounts = buckets.map(bucket => {
    const count = estimates.filter(est => {
      const price = est.estimated_price_cents
      return price >= bucket.min && price < bucket.max
    }).length

    const isUserBucket = userGuess >= bucket.min && userGuess < bucket.max

    return {
      ...bucket,
      count,
      percentage: estimates.length > 0 ? (count / estimates.length) * 100 : 0,
      isUserBucket
    }
  })

  return bucketCounts.filter(bucket => bucket.count > 0 || bucket.isUserBucket)
}

const getUserRanking = (estimates, property) => {
  const userGuess = getUserGuessForProperty(property) * 100
  const actualPrice = property.price_sale_current_cents

  const userAccuracy = Math.abs((userGuess - actualPrice) / actualPrice)

  const betterCount = estimates.filter(est => {
    const estAccuracy = Math.abs((est.estimated_price_cents - actualPrice) / actualPrice)
    return estAccuracy < userAccuracy
  }).length

  return betterCount + 1
}

const getUserRankingColor = (estimates, property) => {
  const ranking = getUserRanking(estimates, property)
  const total = estimates.length + 1 // +1 for user
  const percentile = (total - ranking) / total

  if (percentile >= 0.8) return 'positive'
  if (percentile >= 0.6) return 'warning'
  return 'negative'
}

const getUserPerformanceText = (estimates, property) => {
  const ranking = getUserRanking(estimates, property)
  const total = estimates.length + 1
  const percentile = Math.round(((total - ranking) / total) * 100)

  if (percentile >= 90) return `Excellent! You're in the top 10% of players.`
  if (percentile >= 75) return `Great job! You're in the top 25% of players.`
  if (percentile >= 50) return `Good work! You're above average.`
  if (percentile >= 25) return `Not bad, but there's room for improvement.`
  return `Keep practicing! You'll get better with experience.`
}

const shareResults = () => {
  const text = `I just completed the Property Price Challenge! Score: ${totalScore.value}/${maxPossibleScore.value} (${performanceRating.value.rating})`

  if (navigator.share) {
    navigator.share({
      title: 'Property Price Challenge Results',
      text: text,
      url: window.location.href
    })
  } else {
    // Fallback to clipboard
    navigator.clipboard.writeText(text).then(() => {
      $q.notify({
        message: 'Results copied to clipboard!',
        icon: 'content_copy',
        color: 'positive'
      })
    })
  }
}

const initializeGame = async () => {
  try {
    await fetchPriceGuessData(dossierUuid.value)
    userGuess.value = null
    validationErrors.value = []
    showFeedback.value = false
    currentResult.value = null
    currentImageIndex.value = 0
  } catch (err) {
    $q.notify({
      color: 'negative',
      message: 'Failed to load property data',
      icon: 'error'
    })
  }
}

// Watch for game completion to load comparison data
watch(isGameComplete, (newValue) => {
  if (newValue) {
    loadComparisonData()
  }
})

// Initialize on mount
onMounted(() => {
  initializeGame()
})
</script>

<style scoped>
.price-guess-page {
  background-color: #fafafa;
  min-height: 100vh;
}

.max-ctr {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container,
.error-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.game-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.property-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease;
}

.property-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.property-carousel {
  height: 400px;
}

.carousel-slide {
  background-size: cover;
  background-position: center;
}

.custom-caption {
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 1rem;
}

.feature-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  background: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

.guess-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #e9ecef;
}

.feedback-card {
  border-radius: 12px;
  overflow: hidden;
}

.feedback-header {
  padding: 1.5rem;
}

.result-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.result-label {
  font-weight: 500;
  color: #666;
}

.result-value {
  font-size: 1.1rem;
}

.results-header {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.performance-badge {
  display: inline-block;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 16px;
  border: 2px solid #e9ecef;
}

.results-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.results-table {
  border-radius: 8px;
}

.property-cell {
  max-width: 200px;
}

.score-cell {
  display: flex;
  align-items: center;
  justify-content: center;
}

.results-actions {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.comparison-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.property-comparison {
  border-left: 4px solid #e0e0e0;
  padding-left: 1rem;
}

.comparison-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

.stat-card {
  border-radius: 8px;
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.estimate-distribution {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
}

.distribution-bars {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.distribution-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.bar-container {
  flex: 1;
  height: 20px;
  background: #e0e0e0;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.bar-fill {
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.user-bar {
  background: linear-gradient(90deg, #1976d2, #42a5f5);
}

.other-bar {
  background: linear-gradient(90deg, #757575, #9e9e9e);
}

.bar-label {
  min-width: 120px;
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
}

.range {
  font-weight: 500;
}

.count {
  color: #666;
}

.performance-ranking {
  background: #f0f4f8;
  border-radius: 8px;
  padding: 1rem;
}

.ranking-info {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
}

/* Button improvements */
.q-btn {
  text-transform: none;
  font-weight: 500;
}

/* Input improvements */
.q-field--outlined .q-field__control {
  border-radius: 8px;
}

/* Progress bar improvements */
.q-linear-progress {
  border-radius: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .max-ctr {
    padding: 1rem;
  }

  .game-header,
  .results-header {
    padding: 1rem;
  }

  .property-carousel {
    height: 300px;
  }

  .guess-section {
    margin-top: 1rem;
  }

  .progress-info {
    text-align: left;
    margin-top: 1rem;
  }

  .progress-info .q-linear-progress {
    width: 100% !important;
  }
}
</style>