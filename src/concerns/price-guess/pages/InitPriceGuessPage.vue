<template>
  <q-page class="q-pa-md bg-gray-100">
    <div v-if="currentHouseIndex < houses.length"
         class="max-w-2xl mx-auto">
      <q-card class="my-card shadow-2">
        <q-card-section class="bg-blue-500 text-white">
          <div class="text-h5">Guess the House Price</div>
          <div class="text-subtitle1">Property {{ currentHouseIndex + 1 }} of {{ houses.length }}</div>
        </q-card-section>
        <q-card-section>
          <div class="q-mb-md">
            <div class="text-h6">{{ currentHouse.address }}</div>
            <div class="text-body1">{{ currentHouse.description }}</div>
            <ul class="list-disc pl-5 q-mt-sm">
              <li>Bedrooms: {{ currentHouse.bedrooms }}</li>
              <li>Bathrooms: {{ currentHouse.bathrooms }}</li>
              <li>Area: {{ currentHouse.area }} sq ft</li>
              <li>Location: {{ currentHouse.location }}</li>
            </ul>
          </div>
          <q-input v-model.number="userGuess"
                   type="number"
                   label="Your Price Guess ($)"
                   outlined
                   :error="!!errorMessage"
                   :error-message="errorMessage"
                   @update:model-value="validateGuess"
                   class="q-mb-md" />
          <q-btn color="primary"
                 label="Submit Guess"
                 :disable="!userGuess || !!errorMessage"
                 @click="submitGuess"
                 class="full-width" />
        </q-card-section>
      </q-card>
      <q-dialog v-model="showFeedback">
        <q-card>
          <q-card-section class="bg-blue-500 text-white">
            <div class="text-h6">Guess Feedback</div>
          </q-card-section>
          <q-card-section>
            <div>Your guess: ${{ userGuess.toLocaleString() }}</div>
            <div>Actual price: ${{ currentHouse.price.toLocaleString() }}</div>
            <div :class="feedbackClass">
              {{ feedbackMessage }}
            </div>
          </q-card-section>
          <q-card-actions align="right">
            <q-btn flat
                   label="Next House"
                   color="primary"
                   @click="nextHouse" />
          </q-card-actions>
        </q-card>
      </q-dialog>
    </div>
    <div v-else
         class="max-w-2xl mx-auto">
      <q-card class="my-card shadow-2">
        <q-card-section class="bg-green-500 text-white">
          <div class="text-h5">Game Over!</div>
        </q-card-section>
        <q-card-section>
          <div class="text-h6">Your Results</div>
          <div>Total Score: {{ totalScore }} / 500</div>
          <div class="q-mt-md">Guess Breakdown:</div>
          <q-table :rows="results"
                   :columns="columns"
                   row-key="house"
                   class="q-mt-md">
            <template v-slot:body-cell-difference="props">
              <q-td :props="props">
                <span :class="props.row.difference < 0 ? 'text-red-500' : 'text-green-500'">
                  {{ props.row.difference > 0 ? '+' : '' }}{{ props.row.difference.toFixed(2) }}%
                </span>
              </q-td>
            </template>
          </q-table>
          <q-btn color="primary"
                 label="Play Again"
                 @click="resetGame"
                 class="full-width q-mt-md" />
        </q-card-section>
      </q-card>
    </div>
  </q-page>
</template>

<script>
export default {
  name: 'HousePriceGuess',
  data() {
    return {
      houses: [
        {
          address: '123 Maple Street',
          description: 'A cozy family home with a large backyard.',
          bedrooms: 3,
          bathrooms: 2,
          area: 1800,
          location: 'Suburban',
          price: 350000,
        },
        {
          address: '456 Oak Avenue',
          description: 'Modern townhouse with city views.',
          bedrooms: 2,
          bathrooms: 2,
          area: 1200,
          location: 'Urban',
          price: 450000,
        },
        {
          address: '789 Pine Road',
          description: 'Spacious ranch-style house with a pool.',
          bedrooms: 4,
          bathrooms: 3,
          area: 2500,
          location: 'Suburban',
          price: 600000,
        },
        {
          address: '101 Birch Lane',
          description: 'Charming cottage near the lake.',
          bedrooms: 2,
          bathrooms: 1,
          area: 1000,
          location: 'Rural',
          price: 200000,
        },
        {
          address: '202 Cedar Court',
          description: 'Luxury condo in downtown.',
          bedrooms: 3,
          bathrooms: 2,
          area: 1600,
          location: 'Urban',
          price: 800000,
        },
      ],
      currentHouseIndex: 0,
      userGuess: null,
      errorMessage: '',
      showFeedback: false,
      feedbackMessage: '',
      feedbackClass: '',
      results: [],
      columns: [
        { name: 'house', label: 'House', field: 'house', align: 'left' },
        { name: 'guess', label: 'Your Guess ($)', field: 'guess', format: val => val.toLocaleString() },
        { name: 'actual', label: 'Actual Price ($)', field: 'actual', format: val => val.toLocaleString() },
        { name: 'difference', label: 'Difference (%)', field: 'difference' },
        { name: 'score', label: 'Score', field: 'score' },
      ],
    };
  },
  computed: {
    currentHouse() {
      return this.houses[this.currentHouseIndex];
    },
    totalScore() {
      return this.results.reduce((sum, result) => sum + result.score, 0);
    },
  },
  methods: {
    validateGuess(value) {
      this.errorMessage = '';
      if (value <= 0) {
        this.errorMessage = 'Guess must be a positive number';
        return;
      }
      const actualPrice = this.currentHouse.price;
      const maxAllowed = actualPrice * 3; // 200% too high
      const minAllowed = actualPrice * 0.1; // 10% too low
      if (value > maxAllowed) {
        this.errorMessage = 'Guess is more than 200% too high';
      } else if (value < minAllowed) {
        this.errorMessage = 'Guess is less than 10% too low';
      }
    },
    submitGuess() {
      const actualPrice = this.currentHouse.price;
      const difference = ((this.userGuess - actualPrice) / actualPrice) * 100;
      const absoluteDifference = Math.abs(difference);
      let score = 0;
      if (absoluteDifference <= 10) {
        score = 100;
        this.feedbackMessage = 'Excellent guess! Very close to the actual price.';
        this.feedbackClass = 'text-green-500';
      } else if (absoluteDifference <= 25) {
        score = 75;
        this.feedbackMessage = 'Good guess! You were fairly close.';
        this.feedbackClass = 'text-blue-500';
      } else if (absoluteDifference <= 50) {
        score = 50;
        this.feedbackMessage = 'Not bad, but a bit off.';
        this.feedbackClass = 'text-yellow-500';
      } else {
        score = 25;
        this.feedbackMessage = 'Quite far off, better luck next time!';
        this.feedbackClass = 'text-red-500';
      }
      this.results.push({
        house: this.currentHouse.address,
        guess: this.userGuess,
        actual: actualPrice,
        difference: difference,
        score: score,
      });
      this.showFeedback = true;
    },
    nextHouse() {
      this.currentHouseIndex++;
      this.userGuess = null;
      this.errorMessage = '';
      this.showFeedback = false;
    },
    resetGame() {
      this.currentHouseIndex = 0;
      this.userGuess = null;
      this.errorMessage = '';
      this.showFeedback = false;
      this.results = [];
    },
  },
};
</script>

<style scoped>
.my-card {
  @apply rounded-lg;
}
</style>