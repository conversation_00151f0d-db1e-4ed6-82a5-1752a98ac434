<template>
  <div class="editable-criteria-range-container">
    <div class="q-mb-none">
      <div style="text-transform: capitalize"
           class="q-mt-sm q-pt-sm q-ml-none text-subtitle1 text-left input-field-prompt-color">
        {{ rangeItem }}
      </div>
      <!-- <q-badge color="secondary"> {{ criteriaLabel }} </q-badge> -->
    </div>
    <!-- <q-popup-edit v-model="localContent" auto-save v-slot="scope">
      <q-input v-model="scope.value" dense autofocus counter @keyup.enter="scope.set" />
    </q-popup-edit> -->

    <EditableCriteriaRangeItem :rangeItem="rangeItem"
                               :criteriaSearchParams="criteriaSearchParams"
                               @passCriteriaUpdated="passCriteriaUpdated">
    </EditableCriteriaRangeItem>
  </div>
</template>
<script>
// import { ref, computed } from "vue"
import EditableCriteriaRangeItem from "src/concerns/quests-costa/components/criteria/EditableCriteriaRangeItem.vue"
//  "src/components/customer/criteria/EditableCriteriaRangeItem.vue"
// import { extend } from "quasar"
export default {
  components: {
    EditableCriteriaRangeItem,
  },
  setup(props) {
    // const priceModel = ref({
    //   min: 4,
    //   max: 6,
    // })
    return {
      // standardRangeVals: ref({
      //   min: 10,
      //   max: 25,
      // }),
    }
  },
  mounted() { },
  data() {
    return {
      // localContent: "dsfdsfs",
    }
  },
  computed: {
    criteriaLabel() {
      return this.rangeItem
      // if (this.rangeItem === "price") {
      //   return "Price"
      // } else if (this.rangeItem === "bedrooms") {
      //   return "Bedrooms"
      // } else {
      //   return "Bathrooms"
      // }
    },
    // maxItemLabel() {
    //   let maxItemLabel = this.maxPriceLabel
    //   switch (this.rangeItem) {
    //     case "bedrooms":
    //       maxItemLabel = `Max: ${this.criteriaSearchParams.bedrooms_max}`
    //       break
    //     case "bathrooms":
    //       maxItemLabel = `Max: ${this.criteriaSearchParams.bathrooms_max}`
    //       break
    //   }
    //   return maxItemLabel
    // },
    // minItemLabel() {
    //   let minItemLabel = this.minPriceLabel
    //   switch (this.rangeItem) {
    //     case "bedrooms":
    //       minItemLabel = `Min: ${this.criteriaSearchParams.bedrooms_min}`
    //       break
    //     case "bathrooms":
    //       minItemLabel = `Min: ${this.criteriaSearchParams.bathrooms_min}`
    //       break
    //   }
    //   return minItemLabel
    // },
    // minPriceLabel() {
    //   return `Min: ${this.getPriceText(this.criteriaSearchParams.price_min)}`
    // },
    // maxPriceLabel() {
    //   return `Max: ${this.getPriceText(this.criteriaSearchParams.price_max)}`
    // },
  },
  watch: {},
  methods: {
    passCriteriaUpdated(fieldName, fieldContent) {
      this.$emit("passCriteriaUpdated", fieldName, fieldContent)
    },
    // getPriceText(priceObject) {
    //   let originalPrice = priceObject.price_int
    //   // if (priceObject && priceObject.cents > 0) {
    //   //   originalPrice = priceObject.cents / 100
    //   // }
    //   let formatedOriginalPrice = originalPrice
    //   try {
    //     formatedOriginalPrice = new Intl.NumberFormat("en", {
    //       style: "currency",
    //       currency: priceObject.currency_iso,
    //       maximumFractionDigits: 0,
    //     }).format(originalPrice)
    //   } catch (err) {
    //     // Will result in Invalid currency code err
    //     // if invalid code returned from server
    //     console.log(err.message)
    //   }
    //   return formatedOriginalPrice
    // },
  },
  props: {
    rangeItem: {
      type: String,
    },
    criteriaSearchParams: {
      type: Object,
      default() {
        return {}
      },
    },
    // criteriaValues: {
    //   type: Object,
    //   default() {
    //     return {}
    //   },
    // },
  },
}
</script>
<style></style>
