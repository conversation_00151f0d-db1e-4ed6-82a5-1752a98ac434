<template>
  <div class="criteria-ranges EditableCriteriaRangeItem">
    <div class="row">
      <div class="flex criteria-range-label row justify-between text-h5">
        <!-- <div style="width: 20px" class="q-pt-sm" v-html="rangeItem"></div> -->
      </div>

      <q-timeline class="input-field-prompt-color">
        <!-- <q-timeline-entry heading> Timeline heading </q-timeline-entry> -->
        <q-timeline-entry class="q-pl-none q-pr-xl"
                          style="padding-left: 25px"
                          subtitle=""
                          color="rgb(10, 0, 131)">
          <template v-slot:subtitle>
            <q-btn no-caps
                   style="margin-top: -8px"
                   class="w-full text-left"
                   outline
                   color="rgb(10, 0, 131)">
              <div class="w-full text-left">
                <div v-if="isMoneyField"
                     style=""
                     class="text-gray-900 money-criteria-item">
                  <ReactivePopupEditMoney @saveContent="passMoneyCriteriaUpdated"
                                          endPointName=""
                                          prefixText="Min: "
                                          :currencyToUse="questMoneyField('search_price_min').currency"
                                          :fieldName="questMoneyField('search_price_min').name"
                                          editLabel="Min Price"
                                          defaultContent="0"
                                          :incomingContent="questMoneyField('search_price_min').val">
                  </ReactivePopupEditMoney>
                </div>
                <div class="w-full"
                     v-else>
                  Min: {{ localMinVal }}
                  <q-popup-edit v-model="localMinVal"
                                @save="passMinCriteriaUpdated"
                                v-slot="scope">
                    <div class="edit-color q-pb-sm">Min number of {{ rangeItem }}</div>

                    <q-input v-model="scope.value"
                             type="number"
                             dense
                             autofocus
                             @keyup.enter="scope.set" />
                  </q-popup-edit>
                </div>
              </div>
            </q-btn>
          </template>
          <template v-slot:title> </template>
        </q-timeline-entry>
        <q-timeline-entry class="q-pl-none q-pr-xl"
                          style="padding-left: 25px"
                          subtitle=""
                          color="rgb(10, 0, 131)">
          <template v-slot:subtitle>
            <q-btn no-caps
                   style="margin-top: -8px"
                   class="w-full text-left"
                   outline
                   color="rgb(10, 0, 131)">
              <div class="w-full text-left">
                <div v-if="isMoneyField"
                     style=""
                     class="text-gray-900 money-criteria-item">
                  <ReactivePopupEditMoney @saveContent="passMoneyCriteriaUpdated"
                                          endPointName=""
                                          prefixText="Max: "
                                          :currencyToUse="questMoneyField('search_price_max').currency"
                                          :fieldName="questMoneyField('search_price_max').name"
                                          editLabel="Max Price"
                                          defaultContent="0"
                                          :incomingContent="questMoneyField('search_price_max').val">
                  </ReactivePopupEditMoney>
                </div>
                <div class="w-full"
                     v-else>
                  Max: {{ localMaxVal }}
                  <q-popup-edit v-model="localMaxVal"
                                @save="passMaxCriteriaUpdated"
                                v-slot="scope">
                    <div class="edit-color q-pb-sm">Max number of {{ rangeItem }}</div>

                    <q-input v-model="scope.value"
                             type="number"
                             dense
                             autofocus
                             @keyup.enter="scope.set" />
                  </q-popup-edit>
                </div>
              </div>
            </q-btn>
          </template>
          <template v-slot:title> </template>
        </q-timeline-entry>
      </q-timeline>
    </div>
  </div>
</template>
<script>
import { ref, computed } from "vue"
// import { extend } from "quasar"
import ReactivePopupEditMoney from "src/components/edit/ReactivePopupEditMoney.vue"
// import ReactiveInput from "src/components/edit/ReactiveInput.vue"
export default {
  components: {
    // ReactiveInput,
    // // ReactivePopupEdit,
    ReactivePopupEditMoney,
  },
  setup(props) {
    return {}
  },
  computed: {
    isMoneyField() {
      if (this.rangeItem === "search_price") {
        return true
      } else {
        return false
      }
    },
  },
  mounted() {
    if (this.rangeItem === "search_bedrooms") {
      this.localMaxVal = this.criteriaSearchParams.search_bedrooms_max
      this.localMinVal = this.criteriaSearchParams.search_bedrooms_min
    }
    if (this.rangeItem === "search_bathrooms") {
      this.localMaxVal = this.criteriaSearchParams.search_bathrooms_max
      this.localMinVal = this.criteriaSearchParams.search_bathrooms_min
    }
  },
  methods: {
    passMoneyCriteriaUpdated(fieldName, fieldContent) {
      fieldContent = fieldContent / 100
      this.$emit("passCriteriaUpdated", fieldName, fieldContent)
    },
    passMinCriteriaUpdated(newValue, oldValue) {
      let fieldName = "search_bathrooms_min"
      if (this.rangeItem === "search_bedrooms") {
        fieldName = "search_bedrooms_min"
      }
      // if (this.rangeItem === "search_bathrooms") {
      //   fieldName = "search_bathrooms_min"
      // }
      this.$emit("passCriteriaUpdated", fieldName, newValue)
    },
    passMaxCriteriaUpdated(newValue, oldValue) {
      let fieldName = "search_bathrooms_max"
      if (this.rangeItem === "search_bedrooms") {
        fieldName = "search_bedrooms_max"
      }
      // if (this.rangeItem === "search_bathrooms") {
      //   fieldName = "search_bathrooms_max"
      // }
      this.$emit("passCriteriaUpdated", fieldName, newValue)
    },
    questMoneyField(fieldName) {
      let tVal = ""
      if (this.criteriaSearchParams && this.criteriaSearchParams[fieldName]) {
        tVal = this.criteriaSearchParams[fieldName] //.price_int
        tVal = (tVal * 100).toString()
      }
      let curr = "EUR"
      // if (this.criteriaSearchParams && this.criteriaSearchParams[fieldName]) {
      //   curr = this.criteriaSearchParams[fieldName].currency_iso || "EUR"
      // }
      // console.log(`${fieldName} is ${tVal}`)
      return {
        currency: curr,
        name: fieldName,
        // name: `${fieldName}_cents`,
        val: tVal,
      }
    },
    // questField(fieldName) {
    //   let tVal = ""
    //   if (this.criteriaSearchParams && this.criteriaSearchParams[fieldName]) {
    //     tVal = this.criteriaSearchParams[fieldName].toString()
    //   }
    //   // console.log(`${fieldName} is ${tVal}`)
    //   return {
    //     name: fieldName,
    //     val: tVal,
    //   }
    // },
  },
  data() {
    return {
      localMaxVal: "",
      localMinVal: "22",
    }
  },
  props: {
    rangeItem: {
      type: String,
    },
    criteriaSearchParams: {
      type: Object,
      default() {
        return {}
      },
    },
    // criteriaLabel: {
    //   type: String,
    // },
    // minItemLabel: {
    //   type: String,
    // },
    // maxItemLabel: {
    //   type: String,
    // },
  },
}
</script>
<style>
/* .criteria-range-item .q-slider__text {
  font-size: 18px;
} */
</style>
