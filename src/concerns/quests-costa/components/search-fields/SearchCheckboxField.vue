<template>
  <div>
    <q-checkbox
      dense
      keep-color
      class="select-image-check q-mr-sm"
      color="accent"
      :model-value="checkboxItem.isSelected"
      @update:model-value="triggerCheckboxItemChange(checkboxItem)"
      :label="checkboxItem.label || checkboxItem.locality_label"
    />
  </div>
</template>
<script>
// import { ref } from "vue"
// import useEditHelper from "src/compose/useEditHelper.js"
export default {
  // inject: ["currentSbdUserProvider"],
  data() {
    return {
      // localSearchTerm: "",
      // searchTermRef: ref(null),
      // sTermRules: [
      //   (val) => (val && val.length > 2) || "Please enter at least 3 characters",
      //   // (val) => (val && urlValidator(val)) || "Please enter a valid url",
      //   // (val) => (val && urlRegex.test(val)) || "Please enter a valid url",
      // ],
    }
  },
  watch: {
    // searchTerm: {
    //   // deep: true,
    //   immediate: true,
    //   handler: function (newVal) {
    //     if (newVal) {
    //       this.localSearchTerm = newVal
    //     }
    //   },
    // },
  },
  // setup(props) {},
  methods: {
    triggerCheckboxItemChange() {
      // this.searchTermRef.validate()
      this.$emit("triggerCheckboxItemChange", this.checkboxItem)
    },
  },
  computed: {},
  props: {
    // searchFieldLabel: {
    //   type: String,
    //   default: "",
    // },
    checkboxItem: {
      type: Object,
      default() {
        return {}
      },
    },
  },
}
</script>
<style></style>
