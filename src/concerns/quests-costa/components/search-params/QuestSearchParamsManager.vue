<template>
  <div class="q-mt-none q-mb-sm QuestSearchParamsManager row">
    <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-none">
      <div class="row">
        <div v-if="criteriaSearchParams"
             class="col-xs-12 col-sm-6 col-md-4 col-lg-3 text-center">
          <div class="q-py-none q-mb-none">
            <EditableCriteriaRangeContainer @passCriteriaUpdated="passCriteriaUpdated"
                                            rangeItem="search_price"
                                            :criteriaSearchParams="criteriaSearchParams">
            </EditableCriteriaRangeContainer>
          </div>
        </div>
        <div v-if="criteriaSearchParams"
             class="col-xs-12 col-sm-6 col-md-4 col-lg-3 text-center">
          <div class="q-py-none q-mb-none">
            <EditableCriteriaRangeContainer @passCriteriaUpdated="passCriteriaUpdated"
                                            rangeItem="search_bedrooms"
                                            :criteriaSearchParams="criteriaSearchParams">
            </EditableCriteriaRangeContainer>
          </div>
        </div>
        <div v-if="criteriaSearchParams"
             class="col-xs-12 col-sm-6 col-md-4 col-lg-3 text-center">
          <div class="q-py-none q-mb-none">
            <EditableCriteriaRangeContainer @passCriteriaUpdated="passCriteriaUpdated"
                                            rangeItem="search_bathrooms"
                                            :criteriaSearchParams="criteriaSearchParams">
            </EditableCriteriaRangeContainer>
          </div>
        </div>
      </div>
    </div>
    <!-- <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-xl">
      <CriteriaInputs :criteriaValues="criteriaValues"> </CriteriaInputs>
    </div> -->
  </div>
</template>
<script>
// // import CriteriaInputs from "src/components/forms/parts/CriteriaInputs.vue"
import EditableCriteriaRangeContainer from "src/concerns/quests-costa/components/criteria/EditableCriteriaRangeContainer.vue"
// import EditableCriteriaRangeContainer from "src/components/customer/criteria/EditableCriteriaRangeContainer.vue"
export default {
  components: {
    EditableCriteriaRangeContainer,
    // CriteriaInputs,
  },
  setup(props) { },
  mounted() { },
  data() {
    return {
      // confirmedHouseHuntingFactors: {},
    }
  },
  computed: {
    criteriaSearchParams() {
      return this.criteriaValues
      let csp = {
        price_min: {
          price_int: 75,
          currency_iso: "EUR",
        },
        price_max: {
          price_int: 200,
          currency_iso: "EUR",
        },
        bedrooms_min: "0",
        bedrooms_max: "0",
        bathrooms_min: "0",
        bathrooms_max: "0",
        // cities: "Aloha",
      }
      if (this.criteriaValues) {
        csp.bedrooms_min = this.criteriaValues.search_bedrooms_min
        csp.bedrooms_max = this.criteriaValues.search_bedrooms_max
        csp.bathrooms_min = this.criteriaValues.search_bathrooms_min
        csp.bathrooms_max = this.criteriaValues.search_bathrooms_max
        csp.price_min = {
          price_int: this.criteriaValues.search_price_min,
          currency_iso: "EUR",
        }
        csp.price_max = {
          price_int: this.criteriaValues.search_price_max,
          currency_iso: "EUR",
        }
        // this.criteriaValues.search_bedrooms_min
      }
      return csp
      //  this.questDeterminants.search_params
    },
  },
  watch: {},
  methods: {
    passCriteriaUpdated(fieldName, fieldContent) {
      this.$emit("passCriteriaUpdated", fieldName, fieldContent)
    },
  },
  props: {
    // questDeterminants: {
    //   type: Object,
    //   default() {
    //     return {}
    //   },
    // },
    criteriaValues: {
      type: Object,
      default() {
        return {}
      },
    },
  },
}
</script>
<style></style>
