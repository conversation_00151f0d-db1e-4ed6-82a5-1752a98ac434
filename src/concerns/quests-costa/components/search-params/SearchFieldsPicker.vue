<template>
  <div class="q-mt-none q-mb-sm QuestSearchParamsManager row">
    <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-none">
      <div class="row">
        <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3 text-center">
          <div class="q-py-none q-mb-none">
            <div class="q-gutter-sm">
              <q-list>
                <q-item v-for="propTypeItem in realtyPropertyTypeItems"
                        :key="propTypeItem.uuid"
                        tag="label"
                        v-ripple>
                  <q-item-section avatar>
                    <SearchCheckboxField @triggerCheckboxItemChange="triggerPropetyTypeItemChange"
                                         :checkboxItem="propTypeItem"></SearchCheckboxField>
                  </q-item-section>
                  <q-item-section> </q-item-section>
                </q-item>
              </q-list>
            </div>
          </div>
        </div>
        <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3 text-center">
          <div class="q-py-none q-mb-none">
            <div class="q-gutter-sm">
              <q-list>
                <!--
        Rendering a <label> tag (notice tag="label")
        so QCheckboxes will respond to clicks on QItems to
        change Toggle state.
      -->

                <q-item v-for="featureItem in realtyFeatureItems"
                        :key="featureItem.uuid"
                        tag="label"
                        v-ripple>
                  <q-item-section avatar>
                    <SearchCheckboxField @triggerCheckboxItemChange="triggerFeatureItemChange"
                                         :checkboxItem="featureItem"></SearchCheckboxField>
                    <!-- <q-checkbox
                      dense
                      keep-color
                      class="select-image-check q-mr-sm"
                      color="accent"
                      :model-value="featureItem.isSelected"
                      @update:model-value="triggerFeatureItemChange(featureItem)"
                      :label="featureItem.label"
                    /> -->
                  </q-item-section>
                  <q-item-section>
                    <!-- <q-item-label>Teal</q-item-label> -->
                  </q-item-section>
                </q-item>
              </q-list>
            </div>
          </div>
        </div>
        <div class="col-xs-12 col-sm-6 col-md-4 col-lg-3 text-center">
          <div class="q-py-none q-mb-none">
            <div class="q-gutter-sm">
              <q-list>
                <q-item v-for="propTypeItem in realtyLocalityItems"
                        :key="propTypeItem.uuid"
                        tag="label"
                        v-ripple>
                  <q-item-section avatar>
                    <SearchCheckboxField @triggerCheckboxItemChange="triggerLocalityItemChange"
                                         :checkboxItem="propTypeItem"></SearchCheckboxField>
                  </q-item-section>
                  <q-item-section> </q-item-section>
                </q-item>
              </q-list>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import SearchCheckboxField from "src/concerns/quests-costa/components/search-fields/SearchCheckboxField.vue"
export default {
  components: {
    SearchCheckboxField,
  },
  setup(props) { },
  watch: {
    realtyLocalityItems: {
      handler: function (newVal) {
        if (newVal && newVal.length > 0) {
          this.selectedLocalityItems = new Set()
          let requiredFeatureUuids =
            this.requiredLocalities.realty_locality_item_uuids || []
          newVal.forEach((localityItem) => {
            if (requiredFeatureUuids.includes(localityItem.uuid)) {
              localityItem.isSelected = true
              this.selectedLocalityItems.add(localityItem)
            } else {
              localityItem.isSelected = false
            }
          })
        }
      },
    },
    realtyFeatureItems: {
      handler: function (newVal) {
        if (newVal && newVal.length > 0) {
          this.selectedFeatureItems = new Set()
          let requiredFeatureUuids = this.requiredFeatures.realty_feature_item_uuids || []
          newVal.forEach((featureItem) => {
            if (requiredFeatureUuids.includes(featureItem.uuid)) {
              featureItem.isSelected = true
              this.selectedFeatureItems.add(featureItem)
            } else {
              featureItem.isSelected = false
            }
          })
        }
      },
    },
    realtyPropertyTypeItems: {
      // deep: true,
      // Apr 2024 - with above enabled, clicking the checkbox
      // would not update the ui..
      // immediate: true,
      handler: function (newVal) {
        if (newVal && newVal.length > 0) {
          this.selectedPropertyTypeItems = new Set()
          let requiredPropertyTypeUuids =
            this.requiredPropertyTypes.realty_property_type_item_uuids || []
          // this.requiredPropertyTypes.realty_property_type_item_uuids.includes("1f44dc71-5eef-4308-a4c3-85a52db4018e")
          let rfitu = []
          newVal.forEach((propTypeItem) => {
            if (requiredPropertyTypeUuids.includes(propTypeItem.uuid)) {
              propTypeItem.isSelected = true
              this.selectedPropertyTypeItems.add(propTypeItem)
            } else {
              propTypeItem.isSelected = false
            }
          })
          // let rfitu = newVal.map((item) => {
          //   return { ...item, isSelected: false } // Spread syntax to clone the item and add the extra field
          // // })
          // this.realtyPropertyTypeItemsToUse = rfitu
        }
      },
    },
  },
  data() {
    return {
      // realtyFeatureItemsToUse: [],
      selectedPropertyTypeItems: [],
      selectedFeatureItems: [],
      selectedLocalityItems: [],
    }
  },
  computed: {},
  methods: {
    triggerLocalityItemChange(fieldItem) {
      fieldItem.isSelected = !fieldItem.isSelected
      if (fieldItem.isSelected) {
        this.selectedLocalityItems.add(fieldItem)
      } else {
        this.selectedLocalityItems.delete(fieldItem)
      }
      this.$emit(
        "passSearchFieldsUpdated",
        "selectedLocalityItems",
        Array.from(this.selectedLocalityItems)
      )
    },
    triggerFeatureItemChange(fieldItem) {
      fieldItem.isSelected = !fieldItem.isSelected
      if (fieldItem.isSelected) {
        this.selectedFeatureItems.add(fieldItem)
      } else {
        this.selectedFeatureItems.delete(fieldItem)
      }
      this.$emit(
        "passSearchFieldsUpdated",
        "selectedFeatureItems",
        Array.from(this.selectedFeatureItems)
      )
    },
    triggerPropetyTypeItemChange(fieldItem) {
      fieldItem.isSelected = !fieldItem.isSelected
      if (fieldItem.isSelected) {
        this.selectedPropertyTypeItems.add(fieldItem)
      } else {
        this.selectedPropertyTypeItems.delete(fieldItem)
      }
      this.$emit(
        "passSearchFieldsUpdated",
        "selectedPropertyTypeItems",
        // this.selectedPropertyTypeItems
        Array.from(this.selectedPropertyTypeItems)
      )
    },
  },
  props: {
    realtyLocalityItems: {
      type: Array,
      default() {
        return []
      },
    },
    realtyFeatureItems: {
      type: Array,
      default() {
        return []
      },
    },
    realtyPropertyTypeItems: {
      type: Array,
      default() {
        return []
      },
    },
    requiredPropertyTypes: {
      type: Object,
      default() {
        return {}
      },
    },
    requiredFeatures: {
      type: Object,
      default() {
        return {}
      },
    },
    requiredLocalities: {
      type: Object,
      default() {
        return {}
      },
    },
  },
}
</script>
<style></style>
