<template>
  <div class="q-mb-lg q-pb-lg ai-quest-start-main">
    <div v-if="!retrievalInProgress"
         class="col-xs-12">
      <div class="quest-start-container-top row">
        <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-xl">
          <!-- <AiGetQuestParams></AiGetQuestParams> -->
          <!--  -->
        </div>

        <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-xl"></div>
        <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-xl">
          <div class="row">
            <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-sm">
              <div class="q-mt-md q-pt-md q-ml-none text-h6 text-center input-field-prompt-color">
                Your searches
              </div>
            </div>
            <div v-for="searchDetails in searchesForQuest"
                 :key="searchDetails.search_query.uuid"
                 class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-sm">
              <div class="q-mt-md q-pt-md q-ml-none text-h6 text-center input-field-prompt-color">
                Search:
                {{ searchDetails.search_query.id }}
                /
                {{ searchDetails.search_query.uuid }}

                <router-link :to="{
                  name: 'rSearchQueryForQuest',
                  params: { searchQueryUuid: searchDetails.search_query.uuid },
                }">
                  prop_search_query:{{ searchDetails.search_query.uuid }}
                </router-link>
                <QuestSearchParamsManager :criteriaValues="searchDetails.search_query"></QuestSearchParamsManager>
              </div>
            </div>
          </div>
          <!--  -->
        </div>
        <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-none text-center">
          <div class="row">
            <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-sm">
              <div class="q-mt-md q-pt-md q-ml-none text-h6 text-center input-field-prompt-color">
                Add a search:
              </div>
            </div>
          </div>
          <QuestSearchParamsManager :criteriaValues="criteriaValuesToAdd"></QuestSearchParamsManager>
          <div class="q-py-none justify-center flex w-full">
            <q-btn @click="startAddSearchToQuest"
                   color="accent"
                   label="AddSearch ...." />
          </div>
        </div>
        <!-- -->
      </div>
    </div>
  </div>
  <!--  -->
</template>
<script>
import useQuests from "src/compose/useQuests.js"
import useEditHelper from "src/compose/useEditHelper.js"
// import useLocalDataForQuest from "src/compose/useLocalDataForQuest.js"
// // import AiGetQuestParams from "src/concerns/quests-costa/components/ai/AiGetQuestParams.vue"
import QuestSearchParamsManager from "src/concerns/quests-costa/components/search-params/QuestSearchParamsManager.vue"
//  "src/concerns/quests-costa/components/search-params/QuestSearchParamsManager.vue"
export default {
  components: {
    // AiGetQuestParams,
    QuestSearchParamsManager,
  },
  setup(props) {
    // const { addToLocalQuests } = useLocalDataForQuest()
    const { makeEditCall } = useEditHelper()
    const { getAiQuestOverview, newSearchForQuest } = useQuests()
    return {
      newSearchForQuest,
      getAiQuestOverview,
      makeEditCall,
      mounted() { },
    }
  },
  data() {
    return {
      searchesForQuest: [],
      criteriaValuesToAdd: {
        // price_min: {
        //   price_int: 750000,
        //   currency_iso: "EUR",
        // },
        // price_max: {
        //   price_int: 2000000,
        //   currency_iso: "EUR",
        // },
        search_price_min: 280000,
        search_price_max: 580000,
        search_bedrooms_min: "2",
        search_bedrooms_max: "5",
        search_bathrooms_min: "2",
        search_bathrooms_max: "4",
        cities: "Aloha",
      },
      retrievalInProgress: false,
      currentQuest: {},
      questDeterminants: {},
      currentPurchaseEvaluations: [],
    }
  },
  mounted() {
    let retrievalObject = {
      // userQuestUuid: this.$route.params.userQuestUuid,
      userQuestUuid: 'a2b6d10d-f771-4284-85b5-8f58eae09263'
    }
    let helperDetails = {}
    this.makeEditCall(this.getAiQuestOverview, retrievalObject, helperDetails).then(
      (responseObject) => {
        this.retrievalInProgress = false
        if (responseObject.data && responseObject.data.quest) {
          this.searchesForQuest = responseObject.data.bvh_prop_search_queries

          this.currentQuest = responseObject.data.quest
          this.currentPurchaseEvaluations = responseObject.data.purchase_evaluations
          this.questDeterminants = responseObject.data.determinants
          // this.addToLocalQuests({
          //   uuid: this.currentQuest.uuid,
          // })
        }
      }
    )
  },
  methods: {
    // passCriteriaUpdated(fieldName, fieldContent) {
    //   if (["price_min", "price_max"].includes(fieldName)) {
    //     this.criteriaValuesToAdd[fieldName].price_int = fieldContent
    //   } else {
    //     this.criteriaValuesToAdd[fieldName] = fieldContent
    //   }
    // },
    startAddSearchToQuest() {
      let updateObject = {
        // userQuestUuid: this.$route.params.userQuestUuid,
        userQuestUuid: 'a2b6d10d-f771-4284-85b5-8f58eae09263',
        searchCriteriaValues: this.criteriaValuesToAdd,
      }
      let helperDetails = {}
      this.makeEditCall(this.newSearchForQuest, updateObject, helperDetails).then(
        (responseObject) => {
          if (responseObject.data.primary_triad_comparison) {
            // In this case start sequencing the triad
            // this.$router.push({
            //   name: "rSingleTriad",
            //   params: {
            //     userQuestUuid: this.$route.params.userQuestUuid,
            //   },
            // })
          }
        }
      )
    },
  },
  computed: {
    // gybeSpeakerAvatar() {
    //   return "Y" // this.gybeSpeakerDisplay.charAt(0)
    // },
  },
  props: {

  },
}
</script>
<style scoped>
/*
*/
</style>
