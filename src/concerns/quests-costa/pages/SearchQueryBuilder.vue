<template>
  <div class="q-mb-lg q-pb-lg ai-quest-start-main">
    <div class="col-xs-12">
      <div class="quest-start-container-top row">
        <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-xl">
          <!-- <AiGetQuestParams></AiGetQuestParams> -->
        </div>

        <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-xl"></div>
        <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-xl">
          <div class="row"></div>
        </div>
        <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-none text-center">
          <div class="row">
            <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-sm">
              <div class="q-mt-md q-pt-md q-ml-none text-h6 text-center input-field-prompt-color">
                Add a search:
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-none text-center">
              <QuestSearchParamsManager @passCriteriaUpdated="runCriteriaUpdated"
                                        :questDeterminants="{}"
                                        :criteriaValues="criteriaValuesToAdd"></QuestSearchParamsManager>
            </div>
          </div>
          <div class="row">
            <div class="col-xs-12 col-sm-12 offset-md-1 col-md-10 q-mt-none text-center">
              <SearchFieldsPicker @passSearchFieldsUpdated="runSearchFieldsUpdated"
                                  :realtyLocalityItems="realtyLocalityItems"
                                  :realtyFeatureItems="realtyFeatureItems"
                                  :realtyPropertyTypeItems="realtyPropertyTypeItems"
                                  :requiredPropertyTypes="requiredPropertyTypes"
                                  :requiredFeatures="requiredFeatures"
                                  :requiredLocalities="requiredLocalities"></SearchFieldsPicker>
            </div>
          </div>
          <div class="q-py-none justify-center flex w-full">
            <!-- <h1>dfdsfdsfdsfds</h1> -->
            <q-btn @click="startAddSearchToQuest"
                   color="accent"
                   label="Add a Search" />
          </div>
        </div>
        <!-- -->
      </div>
    </div>
  </div>
  <!--  -->
</template>
<script>
import useSearchParams from "src/compose/useSearchParams.js"
import useQuests from "src/compose/useQuests.js"
import useEditHelper from "src/compose/useEditHelper.js"
import SearchFieldsPicker from "src/concerns/quests-costa/components/search-params/SearchFieldsPicker.vue"
import QuestSearchParamsManager from "src/concerns/quests-costa/components/search-params/QuestSearchParamsManager.vue"
export default {
  components: {
    SearchFieldsPicker,
    QuestSearchParamsManager,
  },
  setup(props) {
    const { getSearchQueryWithFields } = useSearchParams()
    const { makeEditCall } = useEditHelper()
    const { newSearchForQuest } = useQuests()
    return {
      newSearchForQuest,
      getSearchQueryWithFields,
      // getAiQuestOverview,
      makeEditCall,
      mounted() { },
    }
  },
  data() {
    return {
      searchFieldValues: {},
      queryDetails: {},
      criteriaValuesToAdd: {
        search_price_min: 280000,
        search_price_max: 580000,
        search_bedrooms_min: "2",
        search_bedrooms_max: "5",
        search_bathrooms_min: "2",
        search_bathrooms_max: "4",
        // cities: "Aloha",
      },
      realtyFeatureItems: [],
      realtyLocalityItems: [],
      realtyPropertyTypeItems: [],
    }
  },
  mounted() {
    let retrievalObject = {
      searchQueryUuid: this.$route.params.searchQueryUuid,
    }
    let helperDetails = {}
    this.makeEditCall(this.getSearchQueryWithFields, retrievalObject, helperDetails).then(
      (responseObject) => {
        if (responseObject.data && responseObject.data.query_details) {
          this.queryDetails = responseObject.data.query_details
        }
        if (responseObject.data && responseObject.data.param_field_details) {
          this.realtyFeatureItems =
            responseObject.data.param_field_details.realty_feature_items
          this.realtyLocalityItems =
            responseObject.data.param_field_details.realty_locality_items
          this.realtyPropertyTypeItems =
            responseObject.data.param_field_details.realty_property_type_items
        }
      }
    )
  },
  methods: {
    runSearchFieldsUpdated(updatedFieldsKey, updatedFieldsValue) {
      this.searchFieldValues[updatedFieldsKey] = updatedFieldsValue
    },
    runCriteriaUpdated(fieldName, fieldContent) {
      if (["search_price_min", "search_price_max"].includes(fieldName)) {
        this.criteriaValuesToAdd[fieldName] = fieldContent
      } else {
        this.criteriaValuesToAdd[fieldName] = fieldContent
      }
    },
    startAddSearchToQuest() {
      let updateObject = {
        userQuestUuid: this.$route.params.userQuestUuid,
        searchCriteriaValues: this.criteriaValuesToAdd,
        searchFieldValues: this.searchFieldValues,
      }
      let helperDetails = {}
      this.makeEditCall(this.newSearchForQuest, updateObject, helperDetails).then(
        (responseObject) => {
          if (
            responseObject.data.query_details &&
            responseObject.data.query_details.uuid
          ) {
            this.$router.push({
              name: "rSearchQueryForQuest",
              params: {
                searchQueryUuid: responseObject.data.query_details.uuid,
              },
            })
          }
        }
      )
    },
  },
  computed: {
    requiredLocalities() {
      return this.queryDetails.required_localities || {}
    },
    requiredFeatures() {
      return this.queryDetails.required_features || {}
    },
    requiredPropertyTypes() {
      return this.queryDetails.required_property_types || {}
    },
  },
  props: {
    //   type: Boolean,
    //   default: true,
    // },
  },
}
</script>
<style scoped>
/*
*/
</style>
