<template>
  <div class="reference-properties q-pa-md">
    <div v-if="pageIsLoading"
         class="full-width flex justify-center items-center"
         style="height: 80vh">
      <q-circular-progress indeterminate
                           rounded
                           size="50px"
                           color="primary"
                           class="q-ma-md" />
    </div>
    <div v-else
         class="full-width">
      <div v-if="noReferenceProperties"
           class="text-center q-pa-xl">
        <q-icon name="real_estate_agent"
                size="100px"
                color="grey-4" />
        <div class="text-h4 q-mt-md">No Properties Found</div>
        <div class="text-subtitle1 q-mt-sm text-grey-7">
          Start by importing properties or creating new reference listings
        </div>
        <q-btn color="primary"
               class="q-mt-lg"
               icon="add"
               label="Add Reference Property"
               @click="addNewProperty" />
      </div>
      <div v-else>
        <div class="row items-center q-mb-lg">
          <!-- <div class="col">
            <div class="text-h4">Reference Properties</div>
            <div class="text-subtitle1 text-grey-7">
              Manage and compare your reference properties
            </div>
          </div>
          <div class="col-auto">
            <q-btn color="primary"
                   icon="add"
                   label="Add Property"
                   @click="addNewProperty" />
          </div> -->
        </div>

        <div class="row q-col-gutter-md">
          <div class="col-12 col-sm-6 col-md-6"
               v-for="(referenceProperty, index) in referenceProperties"
               :key="referenceProperty.uuid || index">
            <ReferencePropertySummary :referenceProperty="referenceProperty.attributes"
                                      detailsRouteName="rReferencePropertyDetails" />
          </div>
        </div>
        <div class="text-center width-full">
          <!-- <div class="q-pa-md q-ma-md">
            <q-btn size="md"
                   :to="{ name: 'rNewListingImportForSubdomain' }"
                   style="
                background: var(--q-pwb-buttons);
                color: var(--q-pwb-buttons-contrast);
              "
                   label="Import Another Property From A Website"
                   class="q-mb-none q-pb-none" />
          </div> -->
        </div>
        <q-space />
        <div class="q-pb-lg q-mb-lg w-full my-evs-sis-area">
          <!-- -->
        </div>
      </div>

      <!-- <div class=""
           v-if="receivedShares.length > 0">
      </div> -->
    </div>
  </div>
</template>
<script>
import ReferencePropertySummary from "src/concerns/reference-properties/components/ReferencePropertySummary.vue"
import useReferenceProperties from "src/compose/useReferenceProperties.js"
export default {
  components: {
    ReferencePropertySummary,
  },
  setup(props) {
    const { getReferencePropertysList } = useReferenceProperties()
    return {
      // svt,
      getReferencePropertysList,
      // makeEditCall,
    }
  },
  props: {
    // rootDashSvt: {
    //   type: String,
    //   default: "",
    // },
  },
  mounted() {
    this.getReferencePropertysList()
      .then((response) => {
        this.referenceProperties = response.data?.data || []
      })
      .catch((error) => { })
  },
  data() {
    return {
      pageIsLoading: false,
      referenceProperties: [],
      // receivedShares: {},
    }
  },
  methods: {
    addNewProperty() {
      this.$q.notify({
        message: 'Property creation coming soon',
        color: 'info'
      })
    }
  },
  computed: {

    noReferenceProperties() {
      return this.referenceProperties.length < 1
    },
    loginRoute() {
      return {
        name: "rPwbProLoginPage",
      }
    },
    // createAccountRoute() {
    //   return {
    //     name: "rPwbProCreateAccount",
    //   }
    // },
  },
  methods: {},
  watch: {},
}
</script>

<style scoped>
/* .reference-properties {
  background: #f5f5f5;
  min-height: 100vh;
} */

.q-card {
  transition: all 0.3s ease;
}

.q-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 25px 0 rgba(0, 0, 0, .1);
}
</style>
