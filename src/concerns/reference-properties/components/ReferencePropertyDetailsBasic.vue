<template>
  <div class="q-mx-xs q-py-md">
    <q-card class="ref-prop-details-card">
      <div class="row q-pa-md">
        <div class="col-12 text-h5 q-mb-md">{{ propertyTitle }}</div>

        <div class="col-12 q-mb-md">
          <div class="text-subtitle1">Description</div>
          <p>{{ propertyDescription }}</p>
        </div>

        <div class="col-12 col-md-6">
          <div class="text-subtitle1">Location Details</div>
          <q-list>
            <q-item>
              <q-item-section>
                <q-item-label>Address</q-item-label>
                <q-item-label caption>{{ streetName }}</q-item-label>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <q-item-label>City</q-item-label>
                <q-item-label caption>{{ city }}</q-item-label>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <q-item-label>Region</q-item-label>
                <q-item-label caption>{{ region }}</q-item-label>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <q-item-label>Postal Code</q-item-label>
                <q-item-label caption>{{ postalCode }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>

        <div class="col-12 col-md-6">
          <div class="text-subtitle1">Property Details</div>
          <q-list>
            <q-item>
              <q-item-section>
                <q-item-label>Plot Area</q-item-label>
                <q-item-label caption>{{ plotArea }} m²</q-item-label>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <q-item-label>Constructed Area</q-item-label>
                <q-item-label caption>{{ constructedArea }} m²</q-item-label>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <q-item-label>Bedrooms</q-item-label>
                <q-item-label caption>{{ bedrooms }}</q-item-label>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <q-item-label>Bathrooms</q-item-label>
                <q-item-label caption>{{ bathrooms }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>

        <div class="col-12 q-mt-md">
          <div class="text-subtitle1">Pricing Information</div>
          <q-list>
            <q-item>
              <q-item-section>
                <q-item-label>Price Range</q-item-label>
                <q-item-label caption>{{ priceRange }}</q-item-label>
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>
                <q-item-label>Rental Potential (Monthly)</q-item-label>
                <q-item-label caption>{{ monthlyRental }}</q-item-label>
              </q-item-section>
            </q-item>
          </q-list>
        </div>
      </div>
    </q-card>
  </div>
</template>

<script>
export default {
  name: 'ReferencePropertyDetails',
  props: {
    propertyDetails: {
      type: Object,
      required: true
    }
  },
  computed: {
    propertyTitle() {
      return this.propertyDetails?.attributes?.title || 'No Title'
    },
    propertyDescription() {
      return this.propertyDetails?.attributes?.description || 'No description available'
    },
    streetName() {
      return this.propertyDetails?.attributes?.location?.street_name || 'N/A'
    },
    city() {
      return this.propertyDetails?.attributes?.location?.city || 'N/A'
    },
    region() {
      return this.propertyDetails?.attributes?.location?.region || 'N/A'
    },
    postalCode() {
      return this.propertyDetails?.attributes?.location?.postal_code || 'N/A'
    },
    plotArea() {
      return this.propertyDetails?.attributes?.areas?.plot || 0
    },
    constructedArea() {
      return this.propertyDetails?.attributes?.areas?.constructed || 0
    },
    bedrooms() {
      return this.propertyDetails?.attributes?.bedrooms || 0
    },
    bathrooms() {
      return this.propertyDetails?.attributes?.bathrooms || 0
    },
    priceRange() {
      const low = this.propertyDetails?.attributes?.pricing?.price_range?.low || '£0.00'
      const high = this.propertyDetails?.attributes?.pricing?.price_range?.high || '£0.00'
      return `${low} - ${high}`
    },
    monthlyRental() {
      return this.propertyDetails?.attributes?.pricing?.rental_potential?.monthly || '£0.00'
    }
  }
}
</script>

<style scoped>
.ref-prop-details-card {
  border-radius: 8px;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
}
</style>
