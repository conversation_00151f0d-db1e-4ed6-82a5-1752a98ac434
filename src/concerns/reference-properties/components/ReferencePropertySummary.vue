<template>
  <div class="q-mx-xs q-py-md">
    <q-card class="ref-prop-summ-card">
      <div class="row"
           style="width: 100%; cursor: pointer; overflow: auto">
        <div></div>
        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12">
          <router-link style="height: 100%; display: flex"
                       class="ignore-link q-px-none"
                       :to="routeSingleDetails">
            <q-responsive class="col sing-summ-responsive"
                          :ratio="16 / 9"
                          style="max-width: 100%">
              <q-img fit="cover"
                     :src="singleColImage"
                     class="sing-summ-det-img"> </q-img>
            </q-responsive>
          </router-link>
        </div>
        <div class="col-lg-6 col-md-6 col-sm-12 col-xs-12"
             style="cursor: pointer">
          <div class="text-center"
               style="cursor: pointer">
            <div class="q-my-none sing-summ-texts"
                 style="">
              <router-link style="display: contents"
                           class="ignore-link q-px-md"
                           :to="routeSingleDetails">
                <q-scroll-area :visible="false"
                               style="height: 80px">
                  <div class="q-pt-sm">
                    {{ teaserTitle }}
                  </div>
                </q-scroll-area>

                <div class="text-right link-tag q-px-md text-body2"></div>
                <PurchaseEvaluationBlock2 :showDescription="false"
                                          :evaluationDetails="referenceProperty"></PurchaseEvaluationBlock2>
                <!-- <div></div> -->
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </q-card>
  </div>
</template>
<script>
// import { name } from "lodash/sortBy";
import PurchaseEvaluationBlock2 from "src/components/listing-blocks/PurchaseEvaluationBlock2.vue"
export default {
  // inject: ["configAndLocalData"],
  components: {
    PurchaseEvaluationBlock2,
  },
  setup(props) {
    // return {
    // }
  },
  data() {
    return {}
  },
  computed: {
    singleColImage() {
      let firstPic = this.referenceProperty.property_pics?.[0]
      if (firstPic) {
        return firstPic.image_details.url
      } else {
        return ""
      }
    },
    teaserTitle() {
      return this.referenceProperty.title
    },
    routeSingleDetails() {
      let routeSingleDetails = {
        // name: this.detailsRouteName,
        name: "rReferencePropertyDetails",
        params: {
          refPropUuid: this.referenceProperty.uuid,
        },
      }
      return routeSingleDetails
    },
  },
  methods: {},
  props: {
    referenceProperty: {
      type: Object,
      default: () => { },
    },
    // detailsRouteName: {
    //   type: String,
    //   default: "rSingleEvaluationView",
    // },
  },
}
</script>
<style>
.dtp-summ-card {
  border-top: 3px solid #9acd32;
}
</style>
