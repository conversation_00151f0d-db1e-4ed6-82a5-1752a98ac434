<template>
  <q-page :style-fn="myTweak">
    <div class="single-bex-edit-layout-ctr">
      <div v-if="isGuestRoute">
        <q-breadcrumbs class="q-py-md q-px-sm text-blue">
          <template v-slot:separator>
            <!-- <q-icon size="1.5em" name="chevron_right" color="primary" /> -->
          </template>
          <q-breadcrumbs-el :to="{ name: 'rMyPurchaseEvaluations' }"
                            label="Back To Your Listings"
                            icon="widgets" />
          <q-icon size="1.5em"
                  name="chevron_left"
                  color="primary" />
          <!-- <q-breadcrumbs-el label="" icon="chevron_left" /> -->
        </q-breadcrumbs>
      </div>
      <div v-if="true"
           class="q-mt-lg SingleSubdomainListingLayout-tabs">
        <q-tabs v-model="activeTab"
                dense
                mobile-arrows
                class="text-grey"
                active-color="primary"
                indicator-color="primary"
                align="justify"
                narrow-indicator
                outside-arrows>
          <q-route-tab name="overview"
                       :to="viewRouteDetails"
                       label="Preview" />
          <q-route-tab name="edit-panel"
                       label="Edit"
                       :to="editEvaluationRouteDetails"
                       :key="$route.fullPath"
                       :exact="false" />
          <q-route-tab name="viewInNewWin-panel"
                       label="Open In New Window"
                       @click="viewInNewWin"
                       :key="$route.fullPath"
                       :exact="true" />
        </q-tabs>

        <q-separator />

        <q-tab-panels transition-next="fade"
                      transition-duration="1000"
                      transition-prev="slide-left"
                      :infinite="false"
                      v-model="activeTab"
                      animated>
          <q-tab-panel class="q-px-xs"
                       name="overview">
            <router-view :refPropDetails="refPropDetails" />
          </q-tab-panel>
          <q-tab-panel class="q-px-none"
                       name="edit-panel">
            <router-view :refPropDetails="refPropDetails" />
          </q-tab-panel>
        </q-tab-panels>
      </div>
      <div v-else>
        <router-view :refPropDetails="refPropDetails" />
      </div>
    </div>
  </q-page>
</template>
<script>
import useReferenceProperties from "src/compose/useReferenceProperties.js"
export default {
  components: {},
  methods: {
    viewInNewWin(navEvent) {
      let mainShareLink = this.refPropDetails.share_links_for_purchase_evaluation[0]
      if (mainShareLink) {
        let shareRoute = {
          name: "rFeedbackForEvalGeneric",
          params: {
            evaluationShareLinkUuid: mainShareLink.uuid,
          },
        }
        let fullPath = `${location.origin}${this.$router.resolve(shareRoute).href}`
        window.open(fullPath, "_blank")
      }
    },

    myTweak(offset) {
      offset = 50
      return { minHeight: offset ? `calc(100vh - ${offset}px)` : "100vh" }
    },
  },
  setup(props) {
    const { getReferenceProperty } = useReferenceProperties()
    // const { makeEditCall } = useEditHelper()
    return {
      getReferenceProperty,
      // makeEditCall,
    }
  },
  props: {
    rootDashSvt: {
      type: String,
      default: "",
    },
  },
  mounted() {
    let retrievalObject = {
      svt: this.rootDashSvt,
      refPropUuid: this.$route.params.refPropUuid
    }
    // let helperDetails = {}
    this.getReferenceProperty(retrievalObject).then((responseObject) => {
      this.refPropDetails = responseObject.data.reference_property || {}
    })
  },
  computed: {
    isGuestRoute() {
      return true
    },
    editEvaluationRouteDetails() {
      let routeName = "rSingleEvaluationEdit"
      let editEvaluationRouteDetails = {
        name: routeName,
        params: {
          // editToken: this.editToken,
        },
      }
      return editEvaluationRouteDetails
    },
    viewRouteDetails() {
      let routeName = "rReferencePropertyDetails"
      // if (
      //   ["rGuestEditComparison", "rGuestSideBySideCompare"].includes(this.$route.name)
      // ) {
      //   routeName = "rGuestSideBySideCompare"
      // }
      let viewRouteDetails = {
        name: routeName,
        params: {},
      }
      return viewRouteDetails
    },
  },
  data() {
    return {
      // mainFab: false,
      editToken: null,
      activeTab: null,
      // pageReady:
      refPropDetails: {},
    }
  },
}
</script>
<style></style>
