<template>
  <div class="reference-properties q-pa-md">
    <ReferencePropertiesList></ReferencePropertiesList>
  </div>
</template>
<script>
import ReferencePropertiesList from "src/concerns/reference-properties/components/ReferencePropertiesList.vue"
import useReferenceProperties from "src/compose/useReferenceProperties.js"
import { useRoute } from "vue-router"
export default {
  inject: ["currentSbdUserProvider"],
  components: {
    ReferencePropertiesList,
  },
  setup(props) {
    // const { makeEditCall } = useEditHelper()
    const { getReferencePropertysList } = useReferenceProperties()
    return {
      // svt,
      getReferencePropertysList,
      // makeEditCall,
    }
  },
  props: {
    rootDashSvt: {
      type: String,
      default: "",
    },
  },
}
</script>

<style scoped>
.reference-properties {
  background: #f5f5f5;
  min-height: 100vh;
}

.q-card {
  transition: all 0.3s ease;
}

.q-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 25px 0 rgba(0, 0, 0, .1);
}
</style>
