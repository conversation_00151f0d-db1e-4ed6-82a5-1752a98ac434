<template>
  <div v-if="refPropDetails"
       class="q-mx-xs q-py-md ref-prop-details">
    <ReferencePropertyDetails :propertyDetails="refPropDetails"></ReferencePropertyDetails>
    <div class="lst-car-ctr">
      <ListingCarousel :carouselSlides="carouselSlides"> </ListingCarousel>
      <!-- <q-img fit="cover" :src="singleColImage" class="sing-lst-det-img">
      </q-img> -->
    </div>
    <div class="peb-ctr">
      <ReferencePropertyBlock :refPropDetails="refPropDetails"></ReferencePropertyBlock>
    </div>
    <div class="sed-map-ctr">
      <!-- <ExpandableContainer :expansionDetails="mapExpansionDetails">
        <PoiMap :activerefPropDetails="refPropDetails"
                :listingSummaries="listingSummaries"></PoiMap>
      </ExpandableContainer> -->
    </div>

    <!-- <div class="row q-col-gutter-md sl-sum-thb-ctr"
         v-if="listingSummaries.length > 0">
      <div class="col-xs-12 lst-subsect-txt-ctr">
        <h5 class="font-medium text-center title-font text-gray-900 q-py-sm q-my-xs other-props-txt">
          Other Properties
          <br />
        </h5>
        <hr class="custom-hr-listing" />
      </div>
    </div> -->
  </div>
</template>
<script>
import ReferencePropertyDetails from "src/concerns/reference-properties/components/ReferencePropertyDetails.vue"
import ReferencePropertyBlock from "src/concerns/reference-properties/components/ReferencePropertyBlock.vue"
import ListingCarousel from "src/components/pics/ListingCarousel.vue"
// import PoiMap from "src/components/maps/PoiMap.vue"
// import ExpandableContainer from "src/components/content/ExpandableContainer.vue"
// import ShareLinkThumb from "src/components/summary-widgets/ShareLinkThumb.vue"
export default {
  components: {
    ReferencePropertyDetails,
    // ExpandableContainer,
    // ShareLinkThumb,
    // PoiMap,
    ReferencePropertyBlock,
    ListingCarousel,
  },
  // setup(props) {},
  data() {
    return {
      mapExpansionDetails: {
        expTitle: "Location",
        expModel: false,
      },
    }
  },
  computed: {
    carouselSlides() {
      let carouselSlides = []
      let picsColl = []
      let picsOrder = []
      if (this.refPropDetails?.attributes?.property_pics) {
        picsColl = this.refPropDetails.attributes?.property_pics
        picsOrder = this.refPropDetails.attributes?.evaluation_pics_order || []
      }

      picsColl.forEach(function (picObject, index) {
        let imageUrl = picObject.image_details.url
        // if (imageUrl[0] === "/") {
        //   // imageUrl = `${dataApiBase}${picObject.image_details.url}`
        // }
        if (!!!picObject.flag_is_hidden) {
          // having picObject.sort_order fallback below can lead to errors as
          // 0 gets interpretted as false
          let sortOrder = picsOrder[picObject.uuid] // || picObject.sort_order
          carouselSlides.push({
            thumb: imageUrl,
            src: imageUrl,
            altText: picObject.photo_title,
            // sortOrder: picObject.sort_order,
            sortOrder: sortOrder,
          })
        }
      })
      return carouselSlides.sort((a, b) => a.sortOrder - b.sortOrder)
    },
  },
  methods: {},
  props: {
    refPropDetails: {
      type: Object,
      default: () => { },
    },
    currQuest: {
      type: Object,
      default: () => { },
    },
  },
}
</script>
<style>
.dtp-summ-card {
  border-top: 3px solid #9acd32;
}
</style>
