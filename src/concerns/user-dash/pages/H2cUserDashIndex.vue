<template>
  <q-page class="once-bggrey2">
    <!-- -->
    <div class="container q-mx-auto q-px-md">
      <h1 class="text-h4 text-center q-mb-lg">Welcome, {{ userName }}</h1>

      <q-tabs v-model="activeTab"
              class="text-primary"
              align="justify">
        <q-tab name="compare"
               label="Compare Properties"
               icon="compare" />
        <q-tab name="saved"
               label="Saved Comparisons"
               icon="bookmark" />
        <q-tab name="profile"
               label="Profile"
               icon="person" />
      </q-tabs>

      <q-tab-panels v-model="activeTab"
                    animated>
        <!-- Compare Properties Section -->
        <q-tab-panel name="compare">
          <div class="q-mt-lg">
            <h2 class="text-h5">Compare Hypothetical Properties</h2>
            <p class="text-body1">
              Use our AI-powered tools to explore and compare hypothetical properties based on your selected location
              and preferences.
            </p>
            <q-btn icon="search"
                   label="Start Comparing"
                   color="primary"
                   @click="startComparison" />
          </div>
        </q-tab-panel>

        <!-- Saved Comparisons Section -->
        <q-tab-panel name="saved">
          <div class="q-mt-lg">
            <h2 class="text-h5">Your Saved Comparisons</h2>
            <p class="text-body1">
              Review the property comparisons you’ve saved for future reference.
            </p>
            <q-list bordered
                    separator>
              <q-item v-for="(comparison, index) in savedComparisons"
                      :key="index">
                <q-item-section>
                  <div class="text-subtitle2">{{ comparison.title }}</div>
                  <div class="text-caption text-grey">{{ comparison.date }}</div>
                </q-item-section>
                <q-item-section side>
                  <q-btn flat
                         icon="open_in_new"
                         @click="viewComparison(comparison)" />
                </q-item-section>
              </q-item>
            </q-list>
          </div>
        </q-tab-panel>

        <!-- Profile Section -->
        <q-tab-panel name="profile">
          <div class="q-mt-lg">
            <h2 class="text-h5">Profile Settings</h2>
            <p class="text-body1">Update your preferences, email, or password.</p>
            <q-btn icon="settings"
                   label="Edit Profile"
                   color="primary"
                   @click="editProfile" />
          </div>
        </q-tab-panel>
      </q-tab-panels>
    </div>
  </q-page>
</template>
<script>
export default {
  // inject: ["currentSbdUserProvider"],
  components: {
  },
  setup(props) {

  },
  methods: {
    editProfile() {
    },
    startComparison() {
      // this.$router.push({ name: "h2cUserDashCompare" })
    },
  },
  watch: {},
  data() {
    return {
      activeTab: "compare",
      userName: "User",
      savedComparisons: [
        {
          title: "Property Comparison 1",
          date: "June 1, 2021",
        },
        {
          title: "Property Comparison 2",
          date: "June 15, 2021",
        },
        {
          title: "Property Comparison 3",
          date: "July 1, 2021",
        },
      ],
    }
  },
}
</script>
