<template>
  <q-page class="profile-page q-py-lg">
    <div class="container q-mx-auto q-px-md">
      <h1 class="text-h4 text-center q-mb-lg">Your Profile</h1>

      <!-- Profile Overview Section -->
      <q-card bordered
              class="q-mb-lg">
        <q-card-section>
          <div class="row items-center">
            <div class="col-auto">
              <q-avatar size="80px">
                <img :src="user.avatarUrl"
                     alt="User Avatar" />
              </q-avatar>
            </div>
            <div class="col">
              <div class="text-h6">{{ user.name }}</div>
              <div class="text-subtitle2 text-grey">{{ user.email }}</div>
            </div>
            <div class="col-auto">
              <q-btn label="Edit Profile"
                     icon="edit"
                     color="primary"
                     @click="editProfile" />
            </div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Tabs for Profile Details -->
      <q-tabs v-model="activeTab"
              class="text-primary"
              align="justify">
        <q-tab name="details"
               label="Details"
               icon="person" />
        <q-tab name="settings"
               label="Settings"
               icon="settings" />
        <q-tab name="history"
               label="Activity History"
               icon="history" />
      </q-tabs>

      <q-tab-panels v-model="activeTab"
                    animated>
        <!-- Details Tab -->
        <q-tab-panel name="details">
          <q-card bordered
                  class="q-mt-lg">
            <q-card-section>
              <h2 class="text-h6">Profile Details</h2>
              <q-list bordered
                      separator>
                <q-item>
                  <q-item-section>
                    <div class="text-subtitle2">Name</div>
                    <div class="text-body1">{{ user.name }}</div>
                  </q-item-section>
                </q-item>
                <q-item>
                  <q-item-section>
                    <div class="text-subtitle2">Email</div>
                    <div class="text-body1">{{ user.email }}</div>
                  </q-item-section>
                </q-item>
                <q-item>
                  <q-item-section>
                    <div class="text-subtitle2">Account Created</div>
                    <div class="text-body1">{{ user.createdDate }}</div>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </q-tab-panel>

        <!-- Settings Tab -->
        <q-tab-panel name="settings">
          <q-card bordered
                  class="q-mt-lg">
            <q-card-section>
              <h2 class="text-h6">Account Settings</h2>
              <q-list bordered
                      separator>
                <q-item clickable
                        @click="changePassword">
                  <q-item-section>
                    <div class="text-subtitle2">Change Password</div>
                  </q-item-section>
                  <q-item-section side>
                    <q-icon name="chevron_right" />
                  </q-item-section>
                </q-item>
                <q-item clickable
                        @click="manageNotifications">
                  <q-item-section>
                    <div class="text-subtitle2">Manage Notifications</div>
                  </q-item-section>
                  <q-item-section side>
                    <q-icon name="chevron_right" />
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </q-tab-panel>

        <!-- Activity History Tab -->
        <q-tab-panel name="history">
          <q-card bordered
                  class="q-mt-lg">
            <q-card-section>
              <h2 class="text-h6">Activity History</h2>
              <q-list bordered
                      separator>
                <q-item v-for="(activity, index) in user.activityHistory"
                        :key="index">
                  <q-item-section>
                    <div class="text-body1">{{ activity.action }}</div>
                    <div class="text-caption text-grey">{{ activity.date }}</div>
                  </q-item-section>
                </q-item>
              </q-list>
            </q-card-section>
          </q-card>
        </q-tab-panel>
      </q-tab-panels>
    </div>
  </q-page>
</template>

<script setup>
import { ref } from "vue";

// Mock User Data
const user = ref({
  name: "John Doe",
  email: "<EMAIL>",
  avatarUrl: "https://via.placeholder.com/150",
  createdDate: "2024-01-01",
  activityHistory: [
    { action: "Compared properties in NYC", date: "2024-11-29" },
    { action: "Saved a comparison for San Francisco", date: "2024-11-25" },
    { action: "Updated profile details", date: "2024-11-20" },
  ],
});

// State for active tab
const activeTab = ref("details");

// Methods
const editProfile = () => {
  console.log("Navigating to Edit Profile");
};

const changePassword = () => {
  console.log("Navigating to Change Password");
};

const manageNotifications = () => {
  console.log("Navigating to Manage Notifications");
};
</script>

<style scoped>
.profile-page {
  background-color: #f9f9f9;
  min-height: 100vh;
}

.container {
  max-width: 800px;
}
</style>
