<template>
  <q-no-ssr>
    <q-layout class="pwb-pro-auth-layout"
              view="lHh Lpr lFf">
      <q-header elevated>
        <q-toolbar class="max-ctr">
          <!-- <q-btn flat
               dense
               round
               @click="toggleLeftDrawer"
               icon="menu"
               aria-label="Menu" /> -->
          <!-- <q-toolbar-title class="">
            <strong>Property</strong><span style="color: black">Web</span><strong
                    class="navy--text text--darken-1">Builder</strong>
          </q-toolbar-title> -->
          <q-toolbar-title>
            <q-btn align="left"
                   flat
                   :to="{
                    name: 'rAdminRoot', params: {},
                  }"
                   class="flex items-left no-uppercase font-bold text-2xl lg:text-4xl text-gray-800">
              <span style="height: 100%; display: inline-flex;font-size: large;">
                Homes<span class="text-secondary">To</span>Compare</span>
            </q-btn>
          </q-toolbar-title>
          <!-- <div class="q-gutter-sm row items-center no-wrap">
          </div> -->
        </q-toolbar>
      </q-header>

      <q-page-container class="once-bggrey2">
        <router-view />
      </q-page-container>
    </q-layout>
  </q-no-ssr>
</template>
<script>
import { currentSbdUserProvider } from "src/compose/providers/user-from-subdomain-provider.js"
import { defineComponent, ref } from "vue"
export default defineComponent({
  name: "AuthLayout",
  // inject: ["websiteProvider"],
  components: {
    // Messages
  },
  async beforeRouteEnter(to, from, next) {
    // https://github.com/gautemo/Vue-guard-routes-with-Firebase-Authentication?tab=readme-ov-file
    // used above to help me figure this out
    // const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

    // Asynchronous authentication check
    const checkAuth = async () => {
      const providerDetails = await currentSbdUserProvider.getCurrentUser()
      // let isAuthenticatedAndAuthorized = false
      let isAuthenticated = false
      if (providerDetails.fbAuthenticatedUser.uid) {
        // this.userUuid = providerDetails.fbAuthenticatedUser.uid
        // Unfortunately I don't have access to this.userUuid here
        isAuthenticated = true
        //  providerDetails.userData.tenant_permissions.permissions === "full"
      }
      return isAuthenticated // Returns true if user is authenticated
    }
    await checkAuth().then(isAuthenticated => {
      next()
    }).catch((error) => {
      next()
    })
  },
  computed: {},
  data() {
    return {
      userUuid: "",
      // currentWebsite: {},
      // currentAgency: {
      //   attributes: {},
      // },
    }
  },
  setup() {
    // const leftDrawerOpen = ref(false)
    // const { getAgency } = useAgency()
    // const { getAdminTranslations } = useTranslations()
    return {
      // getAgency,
      // getAdminTranslations,
      // leftDrawerOpen,
      // toggleLeftDrawer() {
      //   leftDrawerOpen.value = !leftDrawerOpen.value
      // },
    }
  },
})
</script>
