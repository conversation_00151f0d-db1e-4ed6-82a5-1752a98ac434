<template>
  <q-no-ssr>
    <q-layout class="pwb-pro-main-layout"
              view="lHh Lpr lFf">
      <q-header elevated>
        <q-toolbar>
          <q-btn flat
                 dense
                 round
                 @click="toggleLeftDrawer"
                 icon="menu"
                 aria-label="Menu" />
          <q-btn align="left"
                 flat
                 :to="{ name: 'rAdminRootIndex' }"
                 class="flex items-left no-uppercase font-bold text-2xl lg:text-4xl text-gray-800">
            <img src="~assets/pwb-logo-vertical.png"
                 alt="PropertyWebBuilder Logo"
                 class="top_logo hidden-sm-and-down"
                 style="height: 45px; display: inline-flex;" />
            <span style="height: 100%; display: inline-flex;font-size: large;">
              Property <span class="text-secondary">Web</span> Builder</span>
          </q-btn>
          <q-space />
          <div class="q-gutter-sm row items-center no-wrap">
            <q-btn round
                   dense
                   flat
                   color="white"
                   :icon="$q.fullscreen.isActive ? 'fullscreen_exit' : 'fullscreen'"
                   @click="$q.fullscreen.toggle()"
                   v-if="$q.screen.gt.sm">
            </q-btn>
            <q-btn round
                   dense
                   flat
                   color="white"
                   icon="person"
                   :to="{ name: 'rCurrentUser' }">
            </q-btn>
          </div>
        </q-toolbar>
      </q-header>

      <!-- spt 2023 - previously had margin-top: -50px above to fix gap above drawer -->
      <q-drawer v-model="leftDrawerOpen"
                fix
                show-if-above
                bordered
                class="bg-primary text-white h2c-gpt-drawer"
                style="height: 100vh">
        <!-- <q-page-sticky position="left" :offset="[18, 0]">
          <q-btn round color="accent" icon="arrow_back" />
        </q-page-sticky> -->
        <q-img class="absolute-top"
               src="data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs="
               style="height: 160px">
          <div class="absolute-full flex flex-center text-white">
            <div class="bg-transparent">
              <router-link style="font-size: medium"
                           :to="dashRootRoute">
                <q-avatar text-color="white"
                          color="accent"
                          size="56px"
                          class="q-mb-sm">
                  {{ avatarChar }}
                </q-avatar>
                <div class="text-weight-bold text-white">Your Dashboard</div>
              </router-link>
              <div v-if="weHaveAValidSignedInUser">
                {{ currentSbdUserProvider.state.currentFbUser.email }}
                <!-- <div class="htoc-sign-out-ctr text-white"
                     @click="startSignOut">
                  <div style="font-size: small">
                    <span class="text-white"> (Sign Out) </span>
                  </div>
                </div> -->
              </div>
              <div v-else>
                @guest_user
                <div class="htoc-sign-in-ctr text-white">
                  <!-- <router-link style="font-size: small" :to="loginRoute">
                    <span class="text-white"> (Sign In) </span>
                  </router-link> -->
                </div>
              </div>
              <!-- <div>
                @guest_user
                <div class="text-white">
                  <router-link style="font-size: small" :to="loginRoute">
                    <span class="text-white"> (Sign In) </span>
                  </router-link>
                </div>
              </div> -->
            </div>
          </div>
        </q-img>
        <q-scroll-area class="absolute-top h2c-drawer-scroll-area bg-gray text-white"
                       style="height: 100%; margin-top: 160px; border-right: 1px solid #ddd">
          <q-list class="h2c-drawer-inner-list">
            <!-- <q-item :to="{ name: 'rSubdomainManageLanding' }"
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Landing Page</q-item-label>
              </q-item-section>
            </q-item> -->
            <q-item :to="{ name: 'rProPropertiesList' }"
                    active-class="q-item-h2c-active"
                    :exact="false"
                    :class="propertiesRouteClass"
                    clickable
                    v-ripple>
              <q-item-section avatar>
                <q-icon name="location_city" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Properties</q-item-label>
              </q-item-section>
            </q-item>
            <!-- <q-item :to="{ name: 'rMySubdomainEvalComparisonsContainer' }"
                    active-class="q-item-h2c-active"
                    :exact="false"
                    :class="comparisonsRouteClass">
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Comparisons</q-item-label>
              </q-item-section>
            </q-item>
            <q-item :to="{ name: 'rExtraServices' }"
                    active-class="q-item-h2c-active"
                    :exact="false">
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Services</q-item-label>
              </q-item-section>
            </q-item> -->
            <q-item v-if="showAllDashTabs"
                    :to="{ name: 'rMapsLanding' }"
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Maps</q-item-label>
              </q-item-section>
            </q-item>
            <q-item v-if="showAllDashTabs"
                    :to="{ name: 'rMyBexAlerts' }"
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Sell Your Home</q-item-label>
              </q-item-section>
            </q-item>
            <q-item v-if="showAllDashTabs"
                    :to="{ name: 'rChecklists' }"
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Checklists</q-item-label>
              </q-item-section>
            </q-item>
            <q-item v-if="showAllDashTabs"
                    :to="{ name: 'rCalendar' }"
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Calendar</q-item-label>
              </q-item-section>
            </q-item>
            <q-item v-if="showAllDashTabs"
                    :to="{ name: 'rSheets' }"
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Sheets</q-item-label>
              </q-item-section>
            </q-item>
            <q-item v-if="showAllDashTabs"
                    :to="{ name: 'rCollaborate' }"
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Artificial Intelligence</q-item-label>
              </q-item-section>
            </q-item>
            <!-- <q-item :to="{ name: 'rDashInfoPage', params: { page_slug: 'faqs' } }"
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>FAQS </q-item-label>
              </q-item-section>
            </q-item>
            <q-item :to="{ name: 'rFeedback', params: {} }"
                    active-class="q-item-h2c-active">
              <q-item-section avatar>
                <q-icon name="dashboard" />
              </q-item-section>
              <q-item-section>
                <q-item-label>Feedback</q-item-label>
              </q-item-section>
            </q-item> -->
          </q-list>
        </q-scroll-area>
      </q-drawer>
      <!-- <q-drawer v-model="leftDrawerOpen"
                show-if-above
                bordered
                class="bg-primary text-white">
        <q-list class="q-pt-xl q-mt-xl">
          <q-item :exact="true"
                  :to="{ name: 'rAdminRootIndex' }"
                  active-class="q-item-no-link-highlighting">
            <q-item-section avatar>
              <q-icon name="real_estate_agent" />
            </q-item-section>
            <q-item-section>
              <q-item-label>Home</q-item-label>
            </q-item-section>
          </q-item>
          <q-expansion-item :default-opened="true"
                            aria-expanded="true"
                            icon="location_city"
                            label="Properties">
            <q-list class="q-pl-lg">
              <q-item :exact="true"
                      :to="{ name: 'rPropertiesList' }"
                      active-class="q-item-no-link-highlighting">
                <q-item-section avatar>
                  <q-icon name="list" />
                </q-item-section>
                <q-item-section>
                  <q-item-label>List All</q-item-label>
                </q-item-section>
              </q-item>
            </q-list>
          </q-expansion-item>
        </q-list>
      </q-drawer> -->

      <q-page-container class="once-bggrey2">

        <router-view :currentWebsite="currentWebsite"
                     :currentAgency="currentAgency" />
      </q-page-container>
    </q-layout>
  </q-no-ssr>

</template>
<script>
import { currentSbdUserProvider } from "src/compose/providers/user-from-subdomain-provider.js"
import useAgency from "src/compose/useAgency.js"
import useTranslations from "src/compose/useTranslations.js"
import { defineComponent, ref } from "vue"
export default defineComponent({
  name: "MainLayout",
  inject: ["websiteProvider", "currentSbdUserProvider"],
  components: {
    // Messages
  },
  async beforeRouteEnter(to, from, next) {
    // https://github.com/gautemo/Vue-guard-routes-with-Firebase-Authentication?tab=readme-ov-file
    // used above to help me figure this out
    // const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

    // Asynchronous authentication check
    const checkAuth = async () => {
      const providerDetails = await currentSbdUserProvider.getCurrentUser()
      let isAuthenticatedAndAuthorized = false
      if (providerDetails.fbAuthenticatedUser.uid) {
        isAuthenticatedAndAuthorized = providerDetails.userData.tenant_permissions.permissions === "full"
      }
      return isAuthenticatedAndAuthorized // Returns true if user is authenticated
    }

    // if (requiresAuth) {
    await checkAuth().then(isAuthenticatedAndAuthorized => {
      if (isAuthenticatedAndAuthorized) {
        next() // Continue to the route
      } else {
        next('/auth/login') // Redirect to login page if not authenticated
      }
    }).catch((error) => {
      // In case of an error, redirect to login
      next('/auth/login')
    })

  },
  computed: {
    avatarChar() {
      if (this.currentSbdUserProvider.state.currentFbUser.email) {
        return this.currentSbdUserProvider.state.currentFbUser.email.charAt(0).toUpperCase()
      } else {
        return "G"
      }
    },
    showAllDashTabs() {
      if (this.$route.query.showall && this.$route.query.showall === "1") {
        return true
      } else {
        // Todo: change to false before deploy:
        return false
      }
    },
    propertiesRouteClass() {
      if (this.$route.path.includes("my-properties")) {
        return "q-item-h2c-active"
      } else {
        return ""
      }
    },
    comparisonsRouteClass() {
      if (this.$route.path.includes("my-comparisons")) {
        return "q-item-h2c-active"
      } else {
        return ""
      }
    },
    dashRootRoute() {
      return {
        name: "rAdminRoot",
        params: {
          // dashParam: dashParam,
        },
      }
    },
    loginRoute() {
      return {
        name: "rPwbProLoginPage",
      }
    },
    weHaveAValidSignedInUser() {
      return this.currentSbdUserProvider.state.currentFbUser
      // // had previously been using currentSbdUserProvider.state.currentFbUser.email
      // return !!this.currentSbdUserProvider.state.currentFbUser.accessToken
    },
  },
  mounted: function () {
    this.getAgency()
      .then((response) => {
        this.currentAgency = response.data.agency
        this.currentAgency.primaryAddress = response.data.primary_address
        this.currentWebsite = response.data.website
        this.websiteProvider.setCurrentWebsite(response.data.website)
      })
      .catch((error) => { })
    let adminLocale = "en"
    // will only support English for admin from now forward
    this.getAdminTranslations(adminLocale)
      .then((response) => {
        this.adminTranslations = response.data[adminLocale]
        this.websiteProvider.setAdminTranslations(response.data[adminLocale])
      })
      .catch((error) => { })
  },
  data() {
    return {
      adminTranslations: {},
      currentWebsite: {},
      currentAgency: {
        attributes: {},
      },
      activeTab: null,
    }
  },
  setup() {
    const leftDrawerOpen = ref(false)
    const { getAgency } = useAgency()
    const { getAdminTranslations } = useTranslations()
    return {
      getAgency,
      getAdminTranslations,
      leftDrawerOpen,
      toggleLeftDrawer() {
        leftDrawerOpen.value = !leftDrawerOpen.value
      },
      pagesToEdit: [
        {
          label: "Sell",
          pageName: "sell",
        },
        {
          label: "About Us",
          pageName: "about-us",
        },
        {
          label: "Contact Us",
          pageName: "contact-us",
        },
        {
          label: "Legal Notice",
          pageName: "legal",
        },
        {
          label: "Privacy Policy",
          pageName: "privacy",
        },
      ],
    }
  },
})
</script>
