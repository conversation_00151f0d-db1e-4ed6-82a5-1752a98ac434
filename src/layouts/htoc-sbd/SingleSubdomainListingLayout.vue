<template>
  <q-page :style-fn="myTweak">
    <div class="single-bex-edit-layout-ctr">
      <div v-if="isGuestRoute">
        <q-breadcrumbs class="q-py-md q-px-sm text-blue">
          <template v-slot:separator>
            <!-- <q-icon size="1.5em" name="chevron_right" color="primary" /> -->
          </template>
          <q-breadcrumbs-el :to="{ name: 'rMyPurchaseEvaluations' }"
                            label="Back To Your Listings"
                            icon="widgets" />
          <q-icon size="1.5em"
                  name="chevron_left"
                  color="primary" />
          <!-- <q-breadcrumbs-el label="" icon="chevron_left" /> -->
        </q-breadcrumbs>
      </div>
      <div v-if="true"
           class="q-mt-lg SingleSubdomainListingLayout-tabs">
        <q-tabs v-model="activeTab"
                dense
                mobile-arrows
                class="text-grey"
                active-color="primary"
                indicator-color="primary"
                align="justify"
                narrow-indicator
                outside-arrows>
          <q-route-tab name="overview"
                       :to="viewRouteDetails"
                       label="Preview" />
          <q-route-tab name="edit-panel"
                       label="Edit"
                       :to="editEvaluationRouteDetails"
                       :key="$route.fullPath"
                       :exact="false" />
          <q-route-tab name="viewInNewWin-panel"
                       label="Open In New Window"
                       @click="viewInNewWin"
                       :key="$route.fullPath"
                       :exact="true" />
        </q-tabs>

        <q-separator />

        <q-tab-panels transition-next="fade"
                      transition-duration="1000"
                      transition-prev="slide-left"
                      :infinite="false"
                      v-model="activeTab"
                      animated>
          <q-tab-panel class="q-px-xs"
                       name="overview">
            <router-view :evaluationDetails="evaluationDetails" />
          </q-tab-panel>
          <q-tab-panel class="q-px-none"
                       name="edit-panel">
            <router-view :evaluationDetails="evaluationDetails" />
          </q-tab-panel>
        </q-tab-panels>
      </div>
      <div v-else>
        <router-view :evaluationDetails="evaluationDetails" />
      </div>
    </div>
    <!-- <q-page-sticky position="bottom-left" :offset="[58, 18]">
      <q-fab
        v-model="mainFab"
        vertical-actions-align="left"
        glossy
        icon="electric_bolt"
        direction="up"
        color="accent"
      >
        <ChangeCurrencyNavBtn :isFab="true"></ChangeCurrencyNavBtn>
        <q-fab-action
          :to="{ name: 'rNewComparison' }"
          color="accent"
          icon="add"
          label="Create a comparison"
        />
      </q-fab>
    </q-page-sticky> -->
  </q-page>
</template>
<script>
// import useLocalData from "src/compose/useLocalData.js"
import useEditHelper from "src/compose/useEditHelper.js"
import usePwbSaleListings from "src/compose/usePwbSaleListings.js"
export default {
  components: {},
  methods: {
    viewInNewWin(navEvent) {
      let mainShareLink = this.evaluationDetails.share_links_for_purchase_evaluation[0]
      if (mainShareLink) {
        let shareRoute = {
          name: "rFeedbackForEvalGeneric",
          params: {
            evaluationShareLinkUuid: mainShareLink.uuid,
          },
        }
        let fullPath = `${location.origin}${this.$router.resolve(shareRoute).href}`
        window.open(fullPath, "_blank")
      }
    },

    myTweak(offset) {
      offset = 50
      // This was an attempt to fix the edit tab not being visible
      // on nav.  Ended up hard coding 50px padding for
      // .main-layout-h2c-gpt
      // https://quasar.dev/layout/page#style-fn
      // "offset" is a Number (pixels) that refers to the total
      // height of header + footer that occupies on screen,
      // based on the QLayout "view" prop configuration
      // this is actually what the default style-fn does in Quasar
      return { minHeight: offset ? `calc(100vh - ${offset}px)` : "100vh" }
    },
  },
  setup(props) {
    const { getPwbSaleListing } = usePwbSaleListings()
    const { makeEditCall } = useEditHelper()
    return {
      getPwbSaleListing,
      makeEditCall,
    }
  },
  props: {
    rootDashSvt: {
      type: String,
      default: "",
    },
  },
  mounted() {
    let retrievalObject = {
      svt: this.rootDashSvt,
      listingUuid: this.$route.params.listingUuid,
    }
    // let helperDetails = {}
    this.getPwbSaleListing(retrievalObject).then((responseObject) => {
      this.evaluationDetails = responseObject.data.sale_listing || {}
      //  responseObject.data.listing_details || {}
    })
    // this.makeEditCall(
    //   this.getPwbSaleListing,
    //   retrievalObject,
    //   helperDetails
    // ).then((responseObject) => {
    //   this.evaluationDetails = responseObject.data.sale_listing || {}
    //   //  responseObject.data.listing_details || {}
    // })
  },
  computed: {
    isGuestRoute() {
      return true
    },
    editEvaluationRouteDetails() {
      let routeName = "rSingleEvaluationEdit"
      let editEvaluationRouteDetails = {
        name: routeName,
        params: {
          // editToken: this.editToken,
        },
      }
      return editEvaluationRouteDetails
    },
    viewRouteDetails() {
      let routeName = "rSingleEvaluationView"
      // if (
      //   ["rGuestEditComparison", "rGuestSideBySideCompare"].includes(this.$route.name)
      // ) {
      //   routeName = "rGuestSideBySideCompare"
      // }
      let viewRouteDetails = {
        name: routeName,
        params: {},
      }
      return viewRouteDetails
    },
  },
  data() {
    return {
      // mainFab: false,
      editToken: null,
      activeTab: null,
      // pageReady:
      evaluationDetails: {},
    }
  },
}
</script>
<style></style>
