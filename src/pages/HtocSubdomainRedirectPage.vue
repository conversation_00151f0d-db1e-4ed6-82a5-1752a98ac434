<template>
  <q-page class="bg-grey-1">
    <div class="hero-section bg-gradient text-white text-center q-pa-xl"
         style="height:100vh;">
      <div class="max-ctr">
        <section class="hero">
          <div class="hero-overlay"></div>
          <div class="container row">
            <div class="hero-content col-12">
              <div class="justify-center flex">
                <h5 v-if="notificationMessage"
                    class="animate-pop-in">
                  Sorry, your dossier is not quite ready yet. Please check back later.
                </h5>
                <h1 v-else
                    class="hero-title">
                  <q-spinner v-if="true"
                             class="q-uploader__spinner" />

                  <!-- Make Smarter Real Estate Choices -->
                </h1>
              </div>

            </div>
          </div>
        </section>
      </div>
    </div>
  </q-page>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue';
import { useQuasar } from 'quasar'; // Optional: for notifications
import useScoots from "src/compose/useScoots.js"
import { pwbFlexConfig } from 'boot/pwb-flex-conf'
import { useRouter } from 'vue-router'

export default defineComponent({
  name: 'HtocSubdomainRedirectPage',
  methods: {
    endSession() {
      localStorage.removeItem('userAccessCode');
      // this.hasAccess = false;
      this.accessCode = '';
    }
  },
  setup() {
    const { checkScoot } = useScoots()
    const $q = useQuasar(); // Optional: for notifications
    const $router = useRouter()

    const accessCode = ref('');
    // const hasAccess = ref(false);
    // const isLoading = ref(false);
    const notificationMessage = ref('');




    const checkLocalStorageForAccess = () => {
      const storedAccessCode = localStorage.getItem('userAccessCode');
      if (storedAccessCode) {
        checkScoot(pwbFlexConfig.subdomainName, storedAccessCode).then((response) => {
          if (response.data?.status === "awaiting_dossier") {
            // console.log(response.data?.message)
            notificationMessage.value = response.data?.message
            return
          }
          if (response.data.main_dossier) {
            // localStorage.setItem('userAccessCode', response.data.main_dossier);
            let myDsr = response.data.main_dossier
            //  response.data.realty_dossiers[0] || {}
            if (myDsr.uuid) {
              $router.push({ name: 'rDossierHome', params: { dossierUuid: myDsr.uuid } })
            } else {
              notificationMessage.value = 'No Dossier Found';
            }
          } else {
            localStorage.removeItem('userAccessCode');
            $router.push({ name: 'rSubdomainRoot', params: {} })
          }
        }).catch((error) => {
          localStorage.removeItem('userAccessCode');
          $router.push({ name: 'rSubdomainRoot', params: {} })
        })


      }
      else {
        localStorage.removeItem('userAccessCode');
        $router.push({ name: 'rSubdomainRoot', params: {} })
      }
    };

    onMounted(() => {
      checkLocalStorageForAccess();
    });

    return {
      checkScoot,
      accessCode,
      // hasAccess,
      // isLoading,
      notificationMessage,
    };
  },
});
</script>

<style>
.bg-gradient {
  background: linear-gradient(135deg, #2196f3 0%, #673ab7 100%);
}

.hero-section {
  min-height: 80vh;
  /* Ensure it's at least 80vh as per original */
  height: 100vh;
  /* Maintain 100vh if that was the intent */
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

/* ... (keep all your existing styles) ... */

.process-steps {
  display: flex;
  justify-content: space-between;
  gap: 3rem;
  margin-top: 3rem;
}

.process-step {
  flex: 1;
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.process-step:hover {
  transform: translateY(-10px);
}

.step-number {
  font-size: 3.5rem;
  font-weight: bold;
  color: var(--q-primary);
  /* Ensure --q-primary is defined or replace */
  margin-bottom: 1rem;
  opacity: 0.2;
}

.step-icon-container {
  /* background: var(--q-primary-light);  Ensure --q-primary-light is defined or replace */
  background: #e3f2fd;
  /* Example fallback if --q-primary-light is not globally set */
  border-radius: 50%;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.feature-card {
  border-radius: 1rem;
  transition: transform 0.3s ease;
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-10px);
}

.feature-icon-container {
  /* background: var(--q-primary-light); Ensure --q-primary-light is defined or replace */
  background: #e3f2fd;
  /* Example fallback */
  border-radius: 50%;
  padding: 1.5rem;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.feature-list {
  text-align: left;
}

.audience-card {
  height: 100%;
  border-radius: 1rem;
}

.audience-icon-container {
  /* background: var(--q-primary-light); Ensure --q-primary-light is defined or replace */
  background: #e3f2fd;
  /* Example fallback */
  border-radius: 50%;
  padding: 1.5rem;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.faq-list {
  border-radius: 1rem;
  overflow: hidden;
}

.cta-section {
  background: linear-gradient(135deg, #2196f3 0%, #673ab7 100%);
}

.animate-pop-in {
  animation: pop-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) forwards;
}

.animate-fade-in {
  animation: fade-in 0.8s ease-out forwards;
}

@keyframes pop-in {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }

  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.no-bullets {
  list-style-type: none;
  padding-left: 0;
}

@media (max-width: 768px) {
  .process-steps {
    flex-direction: column;
    gap: 2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }
}
</style>