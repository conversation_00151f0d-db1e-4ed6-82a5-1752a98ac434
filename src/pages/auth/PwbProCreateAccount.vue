<template>
  <q-page class="">
    <div class="p-0 mx-auto">
      <div class="row mt-10">
        <div class="col-xs-12">
          <q-no-ssr>
            <div class="h2c-create-account-page"
                 style="min-height: 100vh">
              <div class="column justify-center items-center">
                <a class="ignore-link"
                   href="/">
                  <h4 class="mobile-only text-bold text-center">
                    {{ $pwbFlexConfig.whitelabelNameDisplay }}
                  </h4>
                  <h3 class="mobile-hide text-bold text-center">
                    {{ $pwbFlexConfig.whitelabelNameDisplay }}
                  </h3>
                </a>
                <div v-if="weHaveAValidSignedInUser">
                  <SignedInStatusBlock></SignedInStatusBlock>
                </div>
                <div class="flex-center"
                     v-else>
                  <div class="q-mb-lg q-pb-lg text-center">
                    <strong class="text-h4">Create your account</strong>
                  </div>
                  <div class="row">
                    <form class="h2c-create-account-form"
                          v-on:submit.prevent="onSubmit()">
                      <q-card square
                              class="flex-center text-center q-mx-md q-mb-xl"
                              style="padding: 50px">
                        <q-card-section class="text-center q-pa-none">
                          <h5 class="text-grey-6">Please enter an email and password</h5>
                        </q-card-section>
                        <q-card-section class="text-center q-pa-none">
                          <p class=""></p>
                        </q-card-section>

                        <q-separator />
                        <div class="text-left">
                          <q-input id="email"
                                   type="email"
                                   v-model.trim="form.email"
                                   label="Email"
                                   ref="emailRef"
                                   :rules="emailRules"
                                   required
                                   autofocus />
                          <q-input id="password"
                                   v-bind:type="isPwd ? 'password' : ''"
                                   ref="pwdRef"
                                   :rules="pwdRules"
                                   v-model="form.password"
                                   label="Password"
                                   required>
                            <template v-slot:append>
                              <q-icon :name="isPwd ? 'visibility_off' : 'visibility'"
                                      class="cursor-pointer"
                                      v-on:click="isPwd = !isPwd"></q-icon> </template></q-input>
                          <q-input id="password_confirmation"
                                   v-bind:type="isPwd ? 'password' : ''"
                                   v-model="form.password_confirmation"
                                   label="Password Confirmation"
                                   :rules="[
                                    (val) => val == form.password || 'Password is not matched',
                                  ]"
                                   required>
                            <template v-slot:append>
                              <q-icon :name="isPwd ? 'visibility_off' : 'visibility'"
                                      class="cursor-pointer"
                                      v-on:click="isPwd = !isPwd"></q-icon>
                            </template>
                          </q-input>
                          <br />
                        </div>
                        <q-card-actions>
                          <q-btn type="submit"
                                 class="fit"
                                 color="primary">Create Account</q-btn>
                          <div class="q-pa-md q-ma-md flex justify-center items-center full-width">
                            <router-link style="font-size: medium"
                                         :to="loginRoute">
                              Sign in here if you already have an account
                            </router-link>
                          </div>
                        </q-card-actions>
                      </q-card>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </q-no-ssr>
        </div>
      </div>
    </div>
  </q-page>
</template>
<script>
// https://raw.githubusercontent.com/neatpro/Quasar-JWT/master/src/pages/login.vue
import { ref } from "vue"
import { useQuasar, Cookies } from "quasar"
import SignedInStatusBlock from "src/components/auth/SignedInStatusBlock.vue"
import useAuthStore from "src/compose/useAuthStore.js"
export default {
  inject: ["currentSbdUserProvider"],
  components: {
    SignedInStatusBlock,
  },
  data() {
    return {
      // localBoardRequests: [],
      isPwd: true,
      // rememberMe: true,
      form: {
        email: "",
        password: "",
        password_confirmation: "",
      },
    }
  },
  mounted: function () { },
  setup() {
    // const { locale } = useI18n({ useScope: "global" })
    const $q = useQuasar()
    // const { getLocalPropertyBoards } = usePropertyBoard()
    // const authStore = useAuthStore()
    const { authStoreRegister } = useAuthStore()
    const emailRef = ref(null)
    const emailRegex = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/
    const pwdRef = ref(null)

    return {
      // authStore,
      authStoreRegister,
      emailRef,
      emailRegex,
      emailRules: [
        (val) => (val && val.length > 0) || "Please type something",
        (val) => (val && emailRegex.test(val)) || "Please enter a valid email",
      ],
      pwdRef,
      pwdRules: [
        (val) => val.length >= 6 || "Please use minimum 6 characters",
        (val) => val.length <= 20 || "Please use maximum 20 characters",
      ],
      // getLocalPropertyBoards,
      qNotify: $q.notify,
    }
  },
  computed: {
    afterSignInRoute() {
      return { name: "rAdminRoot" }
    },
    loginRoute() {
      // currentSbdUserProvider.state.signInRoute
      return {
        name: "rPwbProLoginPage",
      }
    },
    weHaveAValidSignedInUser() {
      // had previously been using currentSbdUserProvider.state.currentBeUser.email
      return !!this.currentSbdUserProvider.state.currentFbUser.accessToken
    },
    // loggedIn() {
    //   let sppUser = null
    //   if (process.env.CLIENT) {
    //     sppUser = this.$q.cookies.get("psq_ut")
    //   }
    //   if (sppUser) {
    //     return true
    //   } else {
    //     return false
    //   }
    //   // Jan 2022 - below does not work
    //   // return this.$store.state.auth.status.loggedIn
    // },
  },
  created() { },
  methods: {
    onSubmit() {
      let userParams = this.form
      // this.emailRef.validate()
      // this.pwdRef.validate()
      // if (this.emailRef.hasError) {
      //   // don't need to do anything - error message will show
      // } else {
      // https://www.bezkoder.com/vue-3-authentication-jwt/#Authentication_service
      // this.authStore.register(userParams).then(
      this.authStoreRegister(userParams).then(
        (authResponseSummary) => {
          if (authResponseSummary.success) {
            this.qNotify({
              color: "positive",
              position: "top",
              message: "Successfully created account.",
              icon: "check_circle",
            })
            // important to reload here so that the
            // SignedInStatusBlock is displayed correctly
            location.reload()
            // this.$router.push(this.afterSignInRoute)
          } else {
            let errorMessage =
              authResponseSummary.error.message || "Sorry, unknown authentication error"
            this.qNotify({
              color: "negative",
              position: "top",
              message: errorMessage,
              icon: "report_problem",
            })
          }
        },
        (error) => { }
      )
      // }
    },
  },
}
</script>
