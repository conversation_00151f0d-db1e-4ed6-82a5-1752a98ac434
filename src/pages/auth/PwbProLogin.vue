<template>
  <q-page class="">
    <div class="p-0 mx-auto">
      <div class="row mt-10">
        <div class="col-xs-12">
          <q-no-ssr>
            <div class="login-page"
                 style="min-height: 100vh">
              <div class="column justify-center items-center">
                <h4 class="mobile-only text-bold text-center">
                  {{ $pwbFlexConfig.whitelabelNameDisplay }}
                </h4>
                <h3 class="mobile-hide text-bold text-center">
                  {{ $pwbFlexConfig.whitelabelNameDisplay }}
                </h3>
                <div v-if="weHaveAValidSignedInUser">
                  <SignedInStatusBlock></SignedInStatusBlock>
                </div>
                <div v-else>
                  <div class="row justify-center items-center">
                    <strong class="text-h4 q-mb-lg q-pb-lg">Sign in to your account</strong>
                  </div>
                  <div class="row">
                    <form v-on:submit.prevent="onSubmit()">
                      <q-card square
                              class="flex-center text-center q-mx-md q-mb-xl"
                              style="padding: 50px">
                        <q-card-section class="text-center q-pa-none">
                          <h5 class="text-grey-6"></h5>
                        </q-card-section>
                        <q-card-section v-if="showDemoAct"
                                        class="text-center q-pa-none">
                          <p class=""></p>
                        </q-card-section>

                        <q-separator />
                        <div class="text-left">
                          <q-input id="email"
                                   type="email"
                                   v-model.trim="form.email"
                                   label="Email"
                                   required
                                   autofocus />
                          <q-input id="password"
                                   v-bind:type="isPwd ? 'password' : ''"
                                   v-model="form.password"
                                   label="Password"
                                   required>
                            <template v-slot:append>
                              <q-icon :name="isPwd ? 'visibility_off' : 'visibility'"
                                      class="cursor-pointer"
                                      v-on:click="isPwd = !isPwd"></q-icon>
                            </template>
                          </q-input>
                          <br />
                        </div>
                        <q-card-actions>
                          <q-btn type="submit"
                                 class="fit"
                                 color="primary">Sign In</q-btn>
                          <div class="q-pa-md q-ma-md flex justify-center items-center full-width">
                            <router-link style="font-size: medium"
                                         :to="createAccountRoute">
                              Or create an account here
                            </router-link>
                          </div>
                        </q-card-actions>
                      </q-card>
                    </form>
                  </div>
                </div>
              </div>
            </div>
          </q-no-ssr>
        </div>
      </div>
    </div>
  </q-page>
</template>
<script>
// https://raw.githubusercontent.com/neatpro/Quasar-JWT/master/src/pages/login.vue
import { useQuasar, Cookies } from "quasar"
// import { Buffer } from "buffer"
import SignedInStatusBlock from "src/components/auth/SignedInStatusBlock.vue"
import useAuthStore from "src/compose/useAuthStore.js"
export default {
  inject: ["currentSbdUserProvider"],
  components: {
    SignedInStatusBlock,
  },
  data() {
    return {
      showDemoAct: true,
      isPwd: true,
      rememberMe: true,
      form: {
        email: "", // "", // "<EMAIL>",
        password: "", // "spp123456",
      },
    }
  },
  setup() {
    const $q = useQuasar()
    const { authStoreLogin } = useAuthStore()
    return {
      authStoreLogin,
      qNotify: $q.notify,
      // authStore,
    }
  },
  computed: {
    weHaveAValidSignedInUser() {
      // had previously been using currentSbdUserProvider.state.currentBeUser.email
      return !!this.currentSbdUserProvider.state.currentFbUser.accessToken
    },
    createAccountRoute() {
      return {
        name: "rPwbProCreateAccount",
      }
    },
    afterSignInRoute() {
      return { name: "rAdminRoot" }
    },
  },
  watch: {},
  mounted() {
    if (process.env.CLIENT) {
      // const userEmailBase64 = Cookies.get("psq_ue")
      // if (userEmailBase64 && userEmailBase64.length > 1) {
      //   this.form.password = ""
      //   let userEmail = Buffer.from(userEmailBase64, "base64").toString("ascii")
      //   this.form.email = userEmail
      //   this.showDemoAct = false
      // }
    }
  },
  created() { },
  methods: {
    onSubmit() {
      // this.loading = true
      let userParams = this.form
      // https://www.bezkoder.com/vue-3-authentication-jwt/#Authentication_service
      // this.$store.dispatch("auth/login", userParams).then(
      this.authStoreLogin(userParams).then(
        // this.authStore.login(userParams).then(
        async (authResponseSummary) => {
          if (authResponseSummary.success) {
            this.qNotify({
              color: "positive",
              position: "top",
              message: "Successfully signed in.",
              icon: "check_circle",
            })
            this.$router.push(this.afterSignInRoute)
          } else {
            // let errorMessage =
            //   authResponseSummary.error.message || "Sorry, unknown authentication error"
            // authResponseSummary.error.message would give too much info to users
            // good for debugging though
            let errorMessage = "Sorry, it has not been possible to log you in."
            this.qNotify({
              color: "negative",
              position: "top",
              message: errorMessage,
              icon: "report_problem",
            })
          }
        },
        (error) => { }
      )
    },
  },
}
</script>
