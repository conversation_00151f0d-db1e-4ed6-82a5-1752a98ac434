<template>
  <q-page class="htoc-gate-costag-hp q-mb-lg q-pb-lg max-ctr">
    <!-- <div class="row mt-10">
      <HtocGateCostaLandingCta @blurbCta="blurbCta"></HtocGateCostaLandingCta>
    </div>

    <div class="row max-ctr">
      <div class="col-xs-12 col-sm-12 col-md-12">
        <AiParamsAndTextSearch> </AiParamsAndTextSearch>
      </div>
    </div>

    <div class="row mt-10 q-mt-lg">
      <HowAiHouseHuntingWorks></HowAiHouseHuntingWorks>
    </div>
    <div class="row mt-10 q-mb-xl">
      <AiHouseHuntingFaqs></AiHouseHuntingFaqs>
    </div> -->

    <div class="row max-ctr">
      <div class="col-xs-12 col-sm-12 col-md-12"
           v-for="questItem in localQuestUrls"
           :key="questItem.uuid">
        <a :href="questItem.url">{{ questItem.url }}</a>
      </div>
    </div>
    <q-no-ssr class="q-mb-lg q-pb-lg">
      <!-- <div>
        <SocialSharing
          socialSharingPrompt=""
          socialSharingTitle=""
          urlProp=""
        ></SocialSharing>
      </div>
      <div>
        <QrCodeShare urlProp="" qrCodeTitle=""></QrCodeShare>
      </div> -->
    </q-no-ssr>
    <!-- place QPageScroller at end of page -->
    <q-page-scroller class="h2c-showc-scroller"
                     position="bottom-right"
                     :scroll-offset="150"
                     :offset="[18, 18]">
      <q-btn fab
             icon="keyboard_arrow_up"
             color="accent" />
    </q-page-scroller>
  </q-page>
</template>
<script>
import { defineComponent, ref, inject } from "vue"
// import QrCodeShare from "src/components/sharing/QrCodeShare.vue"
// import SocialSharing from "src/components/sharing/SocialSharing.vue"
// import HtocGateCostaLandingCta from "src/concerns/quests-costa/components/ctas/HtocGateCostaLandingCta.vue"
// import HowAiHouseHuntingWorks from "src/concerns/quests-costa/components/content/HowAiHouseHuntingWorks.vue"
// import AiHouseHuntingFaqs from "src/concerns/quests-costa/components/content/AiHouseHuntingFaqs.vue"
// import AiParamsAndTextSearch from "src/concerns/quests-costa/components/ai/AiParamsAndTextSearch.vue"
// import useLocalDataForQuest from "src/compose/useLocalDataForQuest.js"
export default defineComponent({
  name: "HtocGateCostaHomePage",
  // inject: ["configAndLocalData"],
  components: {
    // AiParamsAndTextSearch,
    // QrCodeShare,
    // SocialSharing,
    // HtocGateCostaLandingCta,
    // HowAiHouseHuntingWorks,
    // AiHouseHuntingFaqs,
  },
  methods: {
    blurbCta(selectedAccountPlan) {
      this.$emit("blurbCta", selectedAccountPlan)
    },
  },
  // mounted() {
  //   this.localQuests = this.getLocalQuests() || {}
  // },
  // setup(props) {
  //   const { getLocalQuests } = useLocalDataForQuest()
  //   return {
  //     getLocalQuests,
  //   }
  // },
  props: {},
  computed: {
    localQuestUrls() {
      let lqu = []
      lqu = Object.values(this.localQuests).map((quest) => {
        return {
          uuid: quest.uuid,
          url: `/quest/s/${quest.uuid}`,
        }
      })
      return lqu
    },
  },
  data: () => {
    return {
      localQuests: {},
    }
  },
})
</script>
<style scoped></style>
