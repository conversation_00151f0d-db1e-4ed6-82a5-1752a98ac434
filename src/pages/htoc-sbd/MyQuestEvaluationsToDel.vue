<template>
  <div class="my-evaluations q-ma-xs"
       style="height: 100vh">
    <div v-if="pageIsLoading"
         class="full-height w-full flex justify-center">
      <q-circular-progress indeterminate
                           rounded
                           size="50px"
                           color="purple"
                           class="q-ma-md" />
    </div>
    <div v-else
         class="full-height">
      <div class=""
           v-if="noPurchaseEvaluations">
        <div class="full-height justify-between column"
             style="">
          <div class="text-center">
            <div class="q-mt-lg q-pt-lg q-pb-lg text-h4">
              You have not imported any properties
            </div>
            <div class="q-pb-lg q-pt-sm text-h6">
              (Properties imported by you or shared privately with you will appear here )
            </div>

            <div class="q-pa-lg q-ma-lg">
              <q-btn size="md"
                     :to="{ name: 'rNewListingImportForSubdomain' }"
                     style="
                  background: var(--q-pwb-buttons);
                  color: var(--q-pwb-buttons-contrast);
                "
                     label="Import A Property From A Website"
                     class="q-mb-none q-pb-none" />
            </div>
          </div>
        </div>
        <q-space />
      </div>
      <div class="row"
           v-else>
        <div class="text-center col-xs-12">
          <div class="q-mt-md q-pt-lg q-pb-sm text-h4">
            Properties you are interested in
          </div>
          <div class="q-pb-lg q-pt-sm text-body2 text-weight-regular">
            The details of these properties have been imported here so you can keep track
            of any changes.
            <br />
            You can also add notes and make edits which will be saved even when the
            original property goes off the market.
          </div>
        </div>
        <div class="col-xs-12">
          <div class="row">
            <div class="col-xs-12 col-md-12 col-lg-6 eval-single-sum-ctr"
                 v-for="(purchaseEvaluation, index) in purchaseEvaluations"
                 :key="index">
              <!-- <h4>{{ index }}</h4> -->
              <SingleEvaluationSummary detailsRouteName="rSingleEvaluationView"
                                       :purchaseEvaluation="purchaseEvaluation"></SingleEvaluationSummary>
            </div>
          </div>
        </div>
        <div class="text-center width-full">
          <div class="q-pa-md q-ma-md">
            <q-btn size="md"
                   :to="{ name: 'rNewListingImportForSubdomain' }"
                   style="
                background: var(--q-pwb-buttons);
                color: var(--q-pwb-buttons-contrast);
              "
                   label="Import Another Property From A Website"
                   class="q-mb-none q-pb-none" />
          </div>
        </div>
        <q-space />
        <div class="q-pb-lg q-mb-lg w-full my-evs-sis-area">
          <q-card square
                  class="flex-center text-center q-mx-md q-mb-xl"
                  style="padding: 50px"
                  v-if="currentSbdUserProvider.state.currentBeUser.email">
            <SignedInStatusBlock></SignedInStatusBlock>
          </q-card>
          <div v-else
               class="q-pb-lg q-mb-lg q-link rounded-borders q-pa-xs colum edit-info-coln justify-center bg-grey-3 col edit-info-col">
            <div class="row">
              <q-icon v-ripple
                      clickable
                      name="info"
                      class="col-shrink q-px-sm q-py-md text-blue q-hoverable"
                      style="font-size: 1.5rem" />
              <div class="col"
                   style="margin-top: 10px; font-size: medium">
                Content here is only available on your current device.
                <div>
                  <router-link style=""
                               :to="createAccountRoute">
                    Create an account
                  </router-link>
                  to be able to see it on other devices.
                </div>
                <div>
                  <router-link style="font-size: medium"
                               :to="loginRoute">
                    Or sign in here
                  </router-link>
                  if you already have an account
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class=""
           v-if="receivedShares.length > 0">
        <div class="text-center col-xs-12">
          <div class="q-mt-md q-pt-lg q-pb-sm text-h4">Properties shared with you</div>
          <div class="q-pb-lg q-pt-sm text-body2 text-weight-regular">
            You can provide feedback about these but cannot edit them.
          </div>
        </div>
        <div class="col-xs-12">
          <div class="row">
            <div class="col-xs-12 col-md-12 col-lg-6 eval-single-sum-ctr"
                 v-for="(shareInvite, index) in receivedShares"
                 :key="index">
              <!-- <SharedEvaluationSummary
                detailsRouteName="rSingleEvaluationView"
                :purchaseEvaluation="shareInvite.purchase_evaluation"
                :receivedShare="shareInvite.received_share"
              ></SharedEvaluationSummary> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// import useEditHelper from "src/compose/useEditHelper.js"
import SignedInStatusBlock from "src/components/auth/SignedInStatusBlock.vue"
import SingleEvaluationSummary from "src/components/htoc-sbd/listing-containers/SingleEvaluationSummary.vue"
import usePurchaseEvaluations from "src/compose/usePurchaseEvaluations.js"
import { useRoute } from "vue-router"
export default {
  inject: ["currentSbdUserProvider"],
  components: {
    SingleEvaluationSummary,
    SignedInStatusBlock,
  },
  setup(props) {
    // const { makeEditCall } = useEditHelper()
    const { getPurchaseEvaluations } = usePurchaseEvaluations()
    return {
      // svt,
      getPurchaseEvaluations,
      // makeEditCall,
    }
  },
  props: {
    rootDashSvt: {
      type: String,
      default: "",
    },
    // purchaseEvaluations: {
    //   type: Array,
    //   default: () => [],
    // },
  },
  mounted() {
    this.getPurchaseEvaluations()
      .then((response) => {
        this.purchaseEvaluations = response.data?.content?.purchase_evaluations || []
      })
      .catch((error) => { })
  },
  data() {
    return {
      pageIsLoading: false,
      purchaseEvaluations: [],
      receivedShares: {},
    }
  },
  computed: {
    receivedSharesList() {
      // let receivedSharesList = []
      // if (Object.keys(this.receivedShares).length > 0) {
      //   Object.keys(this.receivedShares).forEach((linkKey) => {
      //     let shareLink = this.receivedShares[linkKey]
      //     receivedSharesList.push(shareLink)
      //   })
      // }
      // below much better than above
      let rsl = Object.entries(this.receivedShares).map(([key, value]) => ({
        key,
        value,
      }))
      return rsl
    },
    noPurchaseEvaluations() {
      return this.purchaseEvaluations.length < 1
    },
    loginRoute() {
      return {
        name: "rPwbProLoginPage",
      }
    },
    createAccountRoute() {
      return {
        name: "rPwbProCreateAccount",
      }
    },
  },
  methods: {},
  watch: {},
}
</script>
