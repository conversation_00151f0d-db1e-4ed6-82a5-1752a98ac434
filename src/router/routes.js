const routes = [
  {
    path: '/',
    component: () => import('layouts/htoc-gate/HtocGateLayout.vue'),
    children: [
      {
        path: '',
        name: 'rSubdomainRoot',
        component: () => import('src/pages/HtocSubdomainLandingPage.vue'),
      },
      {
        path: 'buying-steps',
        name: 'rBuyingsteps',
        component: () =>
          import('src/concerns/buyers-steps/pages/BuyersStepsLandingPage.vue'),
      },
      {
        path: 'price-guess',
        name: 'rPriceGuess',
        component: () =>
          import('src/concerns/price-guess/pages/InitPriceGuessPage.vue'),
      },
      {
        path: 'access-error',
        name: 'rSubdomainAccessError',
        component: () => import('src/pages/HtocSubdomainLandingPage.vue'),
      },
      {
        path: 'my-dossier',
        name: 'rSubdomainRedir',
        component: () => import('src/pages/HtocSubdomainRedirectPage.vue'),
      },
      {
        path: '0',
        name: 'rUnusedRoot',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-experiments/HtocLandingPage.vue'
          ),
      },
      {
        path: 'charts',
        name: 'rInitialCharts',
        component: () => import('src/concerns/charts/pages/ChartTestsPage.vue'),
      },
      {
        path: 'epcs',
        name: 'rInitialEpcs',
        component: () => import('src/concerns/epcs/pages/EpcTestsPage.vue'),
      },
      {
        path: 'chart_examples',
        name: 'rExampleCharts',
        component: () =>
          import('src/concerns/charts/pages/ChartExamplesPage.vue'),
      },
      {
        path: 'chart_examples_2',
        name: 'rExampleCharts2',
        component: () =>
          import('src/concerns/charts/pages/ChartExamplesPage2.vue'),
      },
      {
        path: 'chart_examples_3',
        name: 'rExampleCharts3',
        component: () =>
          import('src/concerns/charts/pages/ChartExamplesPage3.vue'),
      },
      {
        path: 'sold_homes_charts/:targetChartName',
        name: 'rSoldHomesCharts',
        component: () =>
          import('src/concerns/charts/pages/SoldHomesCharts.vue'),
      },
      {
        path: '2',
        name: 'rUnusedRoot2',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-experiments/HtocLandingPage2.vue'
          ),
      },
      {
        path: '3',
        name: 'rUnusedRoot3',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-experiments/HtocLandingPage3.vue'
          ),
      },
      {
        path: '4',
        name: 'rUnusedRoot4',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-experiments/HtocGate2025LandingCta.vue'
          ),
      },
      {
        path: '5',
        name: 'rUnusedRoot5',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-experiments/HtocLandingPageBasicFromGpt.vue'
          ),
      },
      {
        path: '6',
        name: 'rUnusedRoot6',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-experiments/HtocLandingPage_Dossier_1.vue'
          ),
      },
      {
        path: '7',
        name: 'rUnusedRoot7',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-experiments/HtocLandingPage7.vue'
          ),
      },
      // 25 may 2025 - tried creating landing page with augment and
      // it was in a league of its own.
      // So much so that I will add new ones from now in landing-contenders folder
      {
        path: '10',
        name: 'rUnusedRoot10',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-contenders/HtocLandingPage10.vue'
          ),
      },
      {
        path: '11',
        name: 'rUnusedRoot11',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-contenders/HtocLandingPage11.vue'
          ),
      },
      {
        path: '12',
        name: 'rUnusedRoot12',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-contenders/HtocLandingPage12.vue'
          ),
      },
      {
        path: '13',
        name: 'rUnusedRoot13',
        component: () =>
          import(
            'src/concerns/dossiers/pages/landing-contenders/HtocLandingPage13.vue'
          ),
      },
      {
        path: 'reference-properties',
        name: 'rReferenceProperties',
        component: () =>
          import(
            'src/concerns/reference-properties/pages/ReferencePropertiesList.vue'
          ),
        // component: () =>
        //   import('pages/htoc-comparables/HtocGateCostaHomePage.vue'),
      },
      {
        path: 'for-sale-evaluation/:saleListingUuid',
        name: 'rForSaleEvalContainer',
        component: () =>
          import(
            'src/concerns/for-sale-evaluations/layouts/ForSaleEvaluationLayout.vue'
          ),
        children: [
          // 23 mar 2025 - can remove this page as it is no longer useful
          // was the starting point for what are now the dossier routes
          {
            path: '',
            name: 'rForSaleEvalDetails',
            component: () =>
              import(
                'src/concerns/for-sale-evaluations/pages/ForSaleEvaluationDetails.vue'
              ),
          },
        ],
      },
      // dossiers below was initally based on for-sale-evaluations above
      {
        path: 'reference-properties/:refPropUuid',
        name: 'rReferencePropertyContainer',
        component: () =>
          import(
            'src/concerns/reference-properties/layouts/SingleReferencePropertyLayout.vue'
          ),
        children: [
          {
            path: '',
            name: 'rReferencePropertyDetails',
            component: () =>
              import(
                'src/concerns/reference-properties/pages/ReferencePropertyDetails.vue'
              ),
          },
        ],
      },
    ],
  },
  {
    path: '/buyers-steps-fulla',
    name: 'rBuyersstepsFulla',
    component: () =>
      import('src/concerns/buyers-steps/pages/BuyersStepsLandingPage.vue'),
  },
  {
    path: '/buyers-steps',
    name: 'rBuyerssteps',
    component: () =>
      import('src/concerns/buyers-steps/layouts/BuyersStepsLayout.vue'),
    // import('src/concerns/buyers-steps/pages/BuyersStepsLandingPage.vue'),
  },
  {
    path: '/superwiser/dossiers',
    name: 'rDossierSuperwiser',
    component: () =>
      import(
        'src/concerns/dossiers/containers/SuperwiserDossiersMainContainer.vue'
      ),
  },
  {
    path: '/superwiser/dossier-assets/:dossierAssetId',
    // for viewing listing photos of a dossier asset - in case I need to run plan_b
    name: 'rDossierAssetSuperwiser',
    component: () =>
      import(
        'src/concerns/dossiers/containers/SuperwiserDossierAssetMainContainer.vue'
      ),
  },
  {
    path: '/rawdossiers/:dossierUuid',
    name: 'rRawDossierContainer',
    component: () =>
      import('src/concerns/dossiers/containers/DossiersMainContainer.vue'),
    // These raw routes needed initially so I can create a screenshot
    // using playwright that can be passed to an LLM
    children: [
      {
        path: 'photos',
        name: 'rRawDossierPhotos',
        component: () =>
          import('src/concerns/dossiers/pages/RawDossierPhotos.vue'),
      },
      {
        path: 'listing_photos/:listingUuid',
        name: 'rRawDossierListingPhotos',
        component: () =>
          import('src/concerns/dossiers/pages/RawDossierListingPhotos.vue'),
      },
      {
        path: 'overview',
        name: 'rRawDossierOverview',
        component: () =>
          import('src/concerns/dossiers/pages/RawDossierSections.vue'),
      },
    ],
  },
  {
    path: '/dossiers/:dossierUuid',
    name: 'rDossierContainer',
    component: () =>
      import('src/concerns/dossiers/containers/DossiersMainContainer.vue'),
    children: [
      {
        path: '',
        name: 'rDossierDetails',
        component: () =>
          import(
            'src/concerns/dossiers/containers/DossiersDrawerContainer.vue'
          ),
        redirect: { name: 'rDossierHome' }, // Add this line to redirect

        // 20 mar 2025 - replacing tabs nav with drawer nav
        // component: () =>
        //   import(
        //     'src/concerns/dossiers/containers/DossiersTabsContainer.vue'
        //   ),
        children: [
          {
            path: 'home',
            name: 'rDossierHome',
            component: () =>
              import('src/concerns/dossiers/pages/DossierHome.vue'),
          },
          {
            path: 'overview',
            name: 'rDossierOverview',
            component: () =>
              import('src/concerns/dossiers/pages/DossierDetails.vue'),
          },
          {
            path: 'photos',
            name: 'rDossierPhotos',
            component: () =>
              import('src/concerns/dossiers/pages/DossierPhotos.vue'),
          },
          {
            path: 'photos/edit',
            name: 'rDossierPhotosEdit',
            component: () =>
              import('src/concerns/dossiers/pages/DossierPhotos.vue'),
          },
          {
            path: 'ai-feedback',
            name: 'rDossierLlmFeedback',
            component: () =>
              import('src/concerns/dossiers/pages/AiFeedbackPanel.vue'),
          },
          {
            path: 'calendar',
            name: 'rTaskCalendar',
            component: () =>
              import('src/concerns/dossiers/components/Calendar.vue'),
          },
          {
            path: 'notifications',
            name: 'rNotifications',
            component: () =>
              import('src/concerns/dossiers/components/Notifications.vue'),
          },
          {
            path: 'ai-chat',
            name: 'rConversations',
            component: () =>
              import('src/concerns/dossiers/components/AiChat.vue'),
          },
          {
            path: 'profile',
            name: 'rHunterProfile',
            component: () =>
              import('src/concerns/dossiers/pages/HunterProfile.vue'),
          },
          {
            path: 'notes-and-queries',
            name: 'rNotesAndQueries',
            component: () =>
              import('src/concerns/dossiers/components/NotesAndQueries.vue'),
            children: [
              {
                path: 'create',
                name: 'rCreateNoteOrQuery',
                component: () =>
                  import('src/concerns/dossiers/components/jots/CreateJot.vue'),
                children: [
                  {
                    path: 'primary',
                    name: 'rCreateNoteOrQueryPrimary',
                    component: () =>
                      import(
                        'src/concerns/dossiers/components/jots/CreateJot.vue'
                      ),
                    meta: { preSelectProperty: 'primary' },
                  },
                  {
                    path: 'comparison/:comparisonUuid',
                    name: 'rCreateNoteOrQueryComparison',
                    component: () =>
                      import(
                        'src/concerns/dossiers/components/jots/CreateJot.vue'
                      ),
                    props: true,
                    meta: { preSelectProperty: 'comparison' },
                  },
                ],
              },
              {
                path: 'primary',
                name: 'rNotesAndQueriesPrimary',
                component: () =>
                  import(
                    'src/concerns/dossiers/components/NotesAndQueries.vue'
                  ),
                meta: { filterType: 'primary' },
              },
              {
                path: 'comparison/:comparisonUuid',
                name: 'rNotesAndQueriesComparison',
                component: () =>
                  import(
                    'src/concerns/dossiers/components/NotesAndQueries.vue'
                  ),
                props: true,
                meta: { filterType: 'comparison' },
              },
            ],
          },
          {
            path: 'tasks',
            name: 'rTasks',
            component: () =>
              import('src/concerns/dossiers/components/Tasks.vue'),
            children: [
              {
                path: 'view/:taskId',
                name: 'rTaskDetails',
                component: () =>
                  import(
                    'src/concerns/dossiers/components/tasks/TaskDetails.vue'
                  ),
                props: true,
              },
              {
                path: 'create',
                name: 'rCreateTask',
                component: () =>
                  import('src/concerns/dossiers/components/CreateTask.vue'),
              },
            ],
          },
          {
            path: 'Charts',
            name: 'rDossierCharts',
            component: () =>
              import('src/concerns/dossiers/pages/DossierChartsPanel.vue'),
          },
          {
            path: 'location',
            name: 'rDossierLocation',
            component: () =>
              import('src/concerns/dossiers/pages/DossierLocation.vue'),
          },
          {
            path: 'neighbourhood',
            name: 'rDossierNeighbourhood',
            component: () =>
              import('src/concerns/dossiers/pages/DossierNeighbourhood.vue'),
          },
          {
            path: 'recent-sales',
            name: 'rRecentSales',
            component: () =>
              import('src/concerns/dossiers/pages/RecentSalesPanel.vue'),
          },
          {
            path: 'comparables',
            name: 'rComparables',
            component: () =>
              import('src/concerns/dossiers/pages/ComparablesPanel.vue'),
          },
          {
            path: 'comparables/:assetsComparisonUuid',
            name: 'rComparableLaunch',
            component: () =>
              import(
                'src/concerns/dossiers/containers/DossiersComparableContainer.vue'
              ),
            redirect: { name: 'rComparableSbs' }, // Add this line to redirect

            // path: '/dossiers/:dossierUuid',
            // name: 'rDossierContainer',
            // component: () =>
            //   import('src/concerns/dossiers/containers/DossiersMainContainer.vue'),
            children: [
              {
                path: 'solo/test',
                name: 'rComparableSoloTest',
                component: () =>
                  import(
                    'src/concerns/dossiers/components/SingleComparisonTest.vue'
                  ),
              },
              {
                path: 'solo/test2',
                name: 'rComparableSoloTest2',
                component: () =>
                  import(
                    'src/concerns/dossiers/components/SingleComparisonTest2.vue'
                  ),
              },
              {
                path: 'sbs/overview',
                name: 'rComparableSbs',
                component: () =>
                  import(
                    'src/concerns/dossiers/pages/ComparableFocusPanel.vue'
                  ),
              },
              {
                path: 'solo/overview',
                name: 'rComparableSolo',
                component: () =>
                  import(
                    'src/concerns/dossiers/pages/ComparableFocusPanel.vue'
                  ),
              },
              {
                path: 'solo/photos',
                name: 'rComparableSoloPhotos',
                component: () =>
                  import(
                    'src/concerns/dossiers/pages/ComparableFocusPanel.vue'
                  ),
              },
              {
                path: 'sbs/parts',
                name: 'rComparableSbsParts',
                component: () =>
                  import(
                    'src/concerns/dossiers/pages/ComparableFocusPanel.vue'
                  ),
              },
            ],
          },
          {
            path: 'comparables/:assetsComparisonUuid/sbs/photos',
            name: 'rComparableSbsPhotos',
            component: () =>
              import('src/concerns/dossiers/pages/ComparableFocusPanel.vue'),
          },
          {
            path: 'comparables/:assetsComparisonUuid/map',
            name: 'rComparableSbsMap',
            component: () =>
              import('src/concerns/dossiers/pages/ComparableFocusPanel.vue'),
          },
          // other potential routes:
          // epc, audio-summary, neighbourhood, floorplans, photos, etc
          // neighbourhood would have the most potential
          // Would be great to be able to do some google earth or streetview
          // cast recorded as a video...
        ],
      },
    ],
  },
  {
    path: '/curated-neighbourhoods',
    component: () => import('layouts/htoc-gate/HtocGateLayout.vue'),
    // import('src/concerns/user-dash/layouts/H2cUserDashLayout.vue'),
    children: [
      {
        path: '',
        name: 'rCuratedNeighbourhoods',
        component: () =>
          import(
            'src/concerns/postcode-clusters/pages/PostcodeClustersPage.vue'
          ),
      },
      {
        path: 'sold',
        name: 'rSoldProperties',
        component: () =>
          // import('src/concerns/gmaps/pages/PostCodeMapThird.vue'),
          import(
            'src/concerns/postcode-clusters/pages/SoldTransactionsPage.vue'
            // 'src/concerns/postcode-clusters/components/SoldTransactionsMap.vue'
          ),
      },
      {
        path: 'a/:areaClusterUuid',
        name: 'rAreaCluster',
        component: () =>
          // import('src/concerns/gmaps/pages/PostCodeMapThird.vue'),
          import(
            'src/concerns/postcode-clusters/pages/PostcodeClusterDetailsPage.vue'
          ),
      },
    ],
  },
  {
    path: '/bolt',
    // some example pages created by AIs such as bolt...
    name: 'rBoltRoot',
    component: () =>
      import('src/concerns/gpt-experiments/layouts/MainLayout.vue'),
    children: [
      {
        path: '',
        name: 'rBoltRootIndex',
        component: () =>
          import('src/concerns/gpt-experiments/pages/IndexPage.vue'),
      },
      {
        path: 'SyntheticComparablesByChatGptPage',
        name: 'rBoltGptIndex1',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/SyntheticComparablesByChatGptPage.vue'
          ),
      },
      {
        path: 'SyntheticComparablesByChatGptPage2',
        name: 'rBoltGptIndex2',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/SyntheticComparablesByChatGptPage2.vue'
          ),
      },
      {
        path: 'SyntheticComparablesForHomebuyers1',
        name: 'rSyntheticComparablesForHomebuyers1',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/SyntheticComparablesForHomebuyers1.vue'
          ),
      },
      {
        path: 'SyntheticComparablesForHomebuyers2',
        name: 'rSyntheticComparablesForHomebuyers2',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/SyntheticComparablesForHomebuyers2.vue'
          ),
      },
      {
        path: 'Comparablesforsellers2a',
        name: 'rComparablesforsellers2a',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/comparablesforsellers2a.vue'
          ),
      },
      {
        path: 'Comparablesforbuyers2a',
        name: 'rComparablesforbuyers2a',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/comparablesforbuyers2a.vue'
          ),
      },
      {
        path: 'Comparablesforsellers2b',
        name: 'rComparablesforsellers2b',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/comparablesforsellers2b.vue'
          ),
      },
      {
        path: 'Comparablesforbuyers2b',
        name: 'rComparablesforbuyers2b',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/comparablesforbuyers2b.vue'
          ),
      },
      {
        path: 'Comparablesforagents2a',
        name: 'rComparablesforagents2a',
        component: () =>
          import(
            'src/concerns/gpt-experiments/pages/comparablesforagents2a.vue'
          ),
      },
    ],
  },
  {
    path: '/gmaps',
    name: 'rgmaps',
    component: () =>
      import('src/concerns/user-dash/layouts/H2cUserDashLayout.vue'),
    children: [
      // {
      //   path: '',
      //   name: 'rgmapsIndex',
      //   component: () =>
      //     import('src/concerns/gmaps/pages/PostCodeMapFirst.vue'),
      // },
      // {
      //   path: 'second',
      //   name: 'rgmapsSecond',
      //   component: () =>
      //     import('src/concerns/gmaps/pages/PostCodeMapSecond.vue'),
      // },
      {
        path: 'third',
        name: 'rgmapsThird',
        component: () =>
          import('src/concerns/gmaps/pages/PostCodeMapThird.vue'),
      },
    ],
  },
  {
    path: '/maps',
    name: 'rMaps',
    component: () =>
      import('src/concerns/user-dash/layouts/H2cUserDashLayout.vue'),
    children: [
      // get this error on dokku server so commenting out below component:
      // Cannot find module '@vue-leaflet/vue-leaflet
      // {
      //   path: '',
      //   name: 'rMapsIndex',
      //   component: () => import('src/concerns/maps/pages/PostCodeMapFirst.vue'),
      // },
      // {
      //   path: 'second',
      //   name: 'rMapsSecond',
      //   component: () =>
      //     import('src/concerns/maps/pages/PostCodeMapSecond.vue'),
      // },
      {
        path: 'third',
        name: 'rMapsThird',
        component: () => import('src/concerns/maps/pages/PostCodeMapThird.vue'),
      },
      {
        path: 'fourth',
        name: 'rMapsFourth',
        component: () =>
          import('src/concerns/maps/pages/PostCodeMapFourth.vue'),
      },
      {
        path: 'fifth',
        name: 'rMapsFifth',
        component: () => import('src/concerns/maps/pages/PostCodeMapFifth.vue'),
      },
      {
        path: 'sixth',
        name: 'rMapsSixth',
        // below fails with ReferenceError: Worker is not defined
        // - made me decide to give up on openlayers
        component: () => import('src/concerns/maps/pages/PostCodeMapSixth.vue'),
      },
      {
        path: 'cv11',
        name: 'rMapsCV11',
        component: () => import('src/concerns/maps/pages/PostcodeCV11Map.vue'),
      },
    ],
  },
  {
    path: '/quests-costa',
    name: 'rUserAdminQuestsCosta',
    component: () =>
      import('src/concerns/user-dash/layouts/H2cUserDashLayout.vue'),
    children: [
      // {
      //   path: '',
      //   name: 'rQuestOverview',
      //   component: () =>
      // },
      {
        path: 'quest-start',
        name: 'rQuestStart',
        component: () =>
          import('src/concerns/quests-costa/pages/AiQuestStart.vue'),
      },
      {
        path: 'search-query/:searchQueryUuid',
        name: 'rSearchQueryForQuest',
        component: () =>
          import('src/concerns/quests-costa/pages/SearchQueryBuilder.vue'),
      },
    ],
  },
  {
    path: '/admin',
    name: 'rAdminRoot',
    component: () =>
      import('src/concerns/user-dash/layouts/H2cUserDashLayout.vue'),
  },
  {
    path: '/u/:userUuid/admin',
    name: 'rUserAdminRoot',
    component: () =>
      import('src/concerns/user-dash/layouts/H2cUserDashLayout.vue'),
    // below is from older htoc-sbd (like lynette.pwb....)
    // might check it out every so often for ideas:
    // component: () => import('layouts/H2cSubdomainsDashLayout.vue'),
    children: [
      {
        path: '',
        name: 'rAdminRootIndex',
        component: () =>
          import('src/concerns/user-dash/pages/H2cUserDashIndex.vue'),
      },
      {
        path: 'profile',
        name: 'rUserAdminProfile',
        component: () =>
          import('src/concerns/user-dash/pages/H2cUserDashProfileView.vue'),
      },
      {
        // Nov 2024 : experiment from older htoc-sbd:
        path: 'myproperties',
        name: 'rProPropertiesList',
        component: () => import('src/pages/ProPropertiesList.vue'),
      },
      {
        path: 'myproperties/:listingUuid',
        name: 'rSingleSubdomainListingContainer',
        component: () =>
          import('src/layouts/htoc-sbd/SingleSubdomainListingLayout.vue'),
        children: [
          {
            path: '',
            name: 'rSingleEvaluationView',
            component: () =>
              import(
                'src/components/listing-containers/SingleEvaluationDetails.vue'
              ),
            // component: () =>
            //   import('src/apps/htoc-sbd/pages/dash/SingleEvaluationView.vue'),
          },
          {
            path: 'e',
            name: 'rSingleEvaluationEdit',
            component: () =>
              import(
                'src/components/listing-containers/SingleEvaluationEdit.vue'
              ),
            // component: () =>
            //   import('src/apps/htoc-sbd/pages/dash/SingleEvaluationView.vue'),
          },
          // {
          //   path: 'ebob2',
          //   name: 'rSingleEvaluationEditTest',
          //   component: () =>
          //     'src/components/listing-containers/SingleEvaluationEdit.vue',
          //   // import('src/apps/htoc-sbd/pages/dash/SingleEvaluationEdit.vue'),
          // },
          // {
          //   path: "html-supply",
          //   name: "rSupplyEvaluationHtml",
          //   component: () => import("src/shared/pages/SupplyEvaluationHtml.vue"),
          // },
          // {
          //   path: "html-verify",
          //   name: "rVerifyEvaluationHtml",
          //   component: () => import("src/shared/pages/VerifyEvaluationHtml.vue"),
          // },
        ],
      },
      {
        path: 'user',
        name: 'rCurrentUser',
        component: () => import('src/pages/auth/PwbProLogin.vue'),
      },
      {
        path: 'agency',
        name: 'rAgencyEdit',
        component: () => import('pages/AgencyEdit.vue'),
        children: [
          {
            path: 'general',
            name: 'rAgencyEditGeneral',
            component: () => import('components/website/EditAgencyGeneral.vue'),
          },
          {
            path: 'location',
            name: 'rAgencyEditLocation',
            component: () => import('components/website/EditAgencyGeneral.vue'),
          },
        ],
      },
      {
        path: 'pages/:pageName',
        name: 'rPagesEdit',
        component: () => import('../pages/PagesEdit.vue'),
        children: [
          {
            path: ':pageTabName',
            name: 'rPagesEditTab',
            component: () => import('../components/pages/EditPageTab.vue'),
          },
          // {
          //   path: '',
          //   name: "rPagesEditSingle",
          //   component: () => import("../components/translations/EditTranslationBatch.vue"),
          //   children: [
          //   ]
          // },
        ],
      },
      {
        path: 'translations',
        name: 'rTranslationsEdit',
        component: () => import('../pages/TranslationsEdit.vue'),
        children: [
          {
            path: ':tBatchId',
            name: 'rTranslationsEditBatch',
            component: () =>
              import('../components/translations/EditTranslationBatch.vue'),
          },
        ],
      },
      {
        path: 'website/footer',
        name: 'rWebsiteEditFooter',
        component: () => import('../pages/WebsiteEdit.vue'),
      },
      {
        path: 'website/settings',
        name: 'rWebsiteEdit',
        component: () => import('../pages/WebsiteEdit.vue'),
        children: [
          {
            path: 'general',
            name: 'rWebsiteEditGeneral',
            component: () =>
              import('../components/website/EditWebsiteGeneral.vue'),
          },
          {
            path: 'appearance',
            name: 'rWebsiteEditAppearance',
            component: () =>
              import('../components/website/EditWebsiteGeneral.vue'),
          },
          {
            path: 'navigation',
            name: 'rWebsiteEditNavigation',
            component: () =>
              import('../components/website/EditWebsiteNavigation.vue'),
          },
        ],
      },
      {
        path: 'properties/list/all',
        name: 'rPropertiesList',
        component: () => import('../pages/PropertiesList.vue'),
      },
      {
        path: 'properties/s/:prop_id',
        name: 'rPropertyEdit',
        component: () => import('../pages/PropertyEdit.vue'),
        children: [
          {
            path: ':editTabName',
            name: 'rPropertyEditTab',
            component: () =>
              import('../components/properties/EditPropertyGeneral.vue'),
          },
        ],
      },
    ],
  },
  {
    path: '/auth',
    component: () => import('layouts/AuthLayout.vue'),
    children: [
      {
        path: 'login',
        name: 'rPwbProLoginPage',
        component: () => import('src/pages/auth/PwbProLogin.vue'),
      },
      // {
      //   path: 'logout',
      //   name: 'rPwbProLogoutPage',
      //   component: () => import('src/pages/auth/H2cLogout.vue'),
      // },
      {
        path: 'create-account',
        name: 'rPwbProCreateAccount',
        component: () => import('src/pages/auth/PwbProCreateAccount.vue'),
        // component: () => import("src/pages/CreateAccount.vue"),
      },
    ],
  },

  {
    path: '/i',
    name: 'rInfoPages',
    component: () => import('src/layouts/htoc-gate/HtocGateLayout.vue'),
    // component: () => import('src/layouts/EmptyContainer.vue'),
    children: [
      {
        path: ':page_slug',
        name: 'rInfoPage',
        component: () =>
          import(
            'src/concerns/dossiers/pages/content/Htoc2025ContentPagesContainer.vue'
          ),
        // above copied from
        // /dev/sites-2023-spt/htoc-fe-2023-spt/src/pages/content/Htoc2025ContentPagesContainer.vue
      },
    ],
  },
  // Always leave this as last one,
  // but you can also remove it
  {
    path: '/:catchAll(.*)*',
    component: () => import('pages/ErrorNotFound.vue'),
  },
]

export default routes
